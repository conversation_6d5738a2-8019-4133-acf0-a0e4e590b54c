name: Security Scanning

on:
  push:
    branches: [ main, development ]
  pull_request:
    branches: [ main, development ]
  schedule:
    # Run weekly security scans
    - cron: '0 2 * * 1'

jobs:
  # Dependency vulnerability scanning
  dependency-scan:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4

    - name: Run Trivy vulnerability scanner for backend
      uses: aquasecurity/trivy-action@master
      with:
        scan-type: 'fs'
        scan-ref: './backend'
        format: 'sarif'
        output: 'backend-trivy-results.sarif'

    - name: Upload Trivy scan results for backend
      uses: github/codeql-action/upload-sarif@v2
      with:
        sarif_file: 'backend-trivy-results.sarif'
        category: 'backend-dependencies'

    - name: Setup Node.js for frontend scan
      uses: actions/setup-node@v4
      with:
        node-version: '18'

    - name: Setup pnpm
      uses: pnpm/action-setup@v2
      with:
        version: latest

    - name: Install frontend dependencies
      run: pnpm install --frozen-lockfile
      working-directory: ./frontend

    - name: Run npm audit for frontend
      run: pnpm audit --audit-level=high
      working-directory: ./frontend
      continue-on-error: true

  # Container image scanning
  container-scan:
    runs-on: ubuntu-latest
    if: github.event_name == 'push'
    steps:
    - uses: actions/checkout@v4

    - name: Build backend image for scanning
      run: docker build -f infra/docker/backend.Dockerfile -t ngora-backend:scan .

    - name: Run Trivy vulnerability scanner for backend image
      uses: aquasecurity/trivy-action@master
      with:
        image-ref: 'ngora-backend:scan'
        format: 'sarif'
        output: 'backend-image-trivy-results.sarif'

    - name: Upload backend image scan results
      uses: github/codeql-action/upload-sarif@v2
      with:
        sarif_file: 'backend-image-trivy-results.sarif'
        category: 'backend-image'

    - name: Build frontend image for scanning
      run: docker build -f infra/docker/frontend.Dockerfile -t ngora-frontend:scan .

    - name: Run Trivy vulnerability scanner for frontend image
      uses: aquasecurity/trivy-action@master
      with:
        image-ref: 'ngora-frontend:scan'
        format: 'sarif'
        output: 'frontend-image-trivy-results.sarif'

    - name: Upload frontend image scan results
      uses: github/codeql-action/upload-sarif@v2
      with:
        sarif_file: 'frontend-image-trivy-results.sarif'
        category: 'frontend-image'

  # Secret scanning
  secret-scan:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
      with:
        fetch-depth: 0

    - name: Run TruffleHog OSS
      uses: trufflesecurity/trufflehog@main
      with:
        path: ./
        base: main
        head: HEAD
        extra_args: --debug --only-verified

  # SAST (Static Application Security Testing)
  sast-scan:
    runs-on: ubuntu-latest
    permissions:
      actions: read
      contents: read
      security-events: write

    steps:
    - name: Checkout repository
      uses: actions/checkout@v4

    - name: Initialize CodeQL
      uses: github/codeql-action/init@v2
      with:
        languages: python, javascript

    - name: Autobuild
      uses: github/codeql-action/autobuild@v2

    - name: Perform CodeQL Analysis
      uses: github/codeql-action/analyze@v2
      with:
        category: "/language:python,javascript"

  # Python security linting
  bandit-scan:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.12'

    - name: Install bandit
      run: pip install bandit[toml]

    - name: Run bandit security linter
      run: |
        bandit -r backend/src -f json -o bandit-report.json || true
        bandit -r backend/src || true

    - name: Upload bandit results
      uses: actions/upload-artifact@v3
      with:
        name: bandit-report
        path: bandit-report.json