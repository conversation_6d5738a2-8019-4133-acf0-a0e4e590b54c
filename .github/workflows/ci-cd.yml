name: CI/CD Pipeline

on:
  push:
    branches: [ main, development ]
  pull_request:
    branches: [ main, development ]

env:
  REGISTRY: ghcr.io
  BACKEND_IMAGE_NAME: ${{ github.repository }}/backend
  FRONTEND_IMAGE_NAME: ${{ github.repository }}/frontend

jobs:
  # Test Backend
  test-backend:
    runs-on: ubuntu-latest
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: test_password
          POSTGRES_USER: test_user
          POSTGRES_DB: test_db
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
      
      redis:
        image: redis:7-alpine
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
    - uses: actions/checkout@v4

    - name: Install uv
      uses: astral-sh/setup-uv@v3
      with:
        version: "latest"

    - name: Set up Python
      run: uv python install 3.12

    - name: Create virtual environment
      run: uv venv
      working-directory: ./backend

    - name: Install dependencies
      run: uv sync
      working-directory: ./backend

    - name: Run linting
      run: uv run ruff check --fix
      working-directory: ./backend

    - name: Run formatting check
      run: uv run ruff format --check
      working-directory: ./backend

    - name: Run tests
      run: uv run pytest --cov --cov-report=xml
      working-directory: ./backend
      env:
        POSTGRES_HOST: localhost
        POSTGRES_PORT: 5432
        POSTGRES_DB: test_db
        POSTGRES_USER: test_user
        POSTGRES_PASSWORD: test_password
        REDIS_HOST: localhost
        REDIS_PORT: 6379
        SECRET_KEY: test_secret_key_that_is_long_enough
        ENVIRONMENT: testing

    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./backend/coverage.xml
        fail_ci_if_error: true

  # Test Frontend
  test-frontend:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'

    - name: Setup pnpm
      uses: pnpm/action-setup@v2
      with:
        version: latest

    - name: Install dependencies
      run: pnpm install --frozen-lockfile
      working-directory: ./frontend

    - name: Run linting
      run: pnpm run lint
      working-directory: ./frontend

    - name: Run type checking
      run: pnpm run type-check
      working-directory: ./frontend
      continue-on-error: true

    - name: Build application
      run: pnpm run build
      working-directory: ./frontend
      env:
        NEXT_PUBLIC_API_BASE_URL: http://localhost:8000

  # Build and Push Docker Images
  build-and-push:
    needs: [test-backend, test-frontend]
    runs-on: ubuntu-latest
    if: github.event_name == 'push' && (github.ref == 'refs/heads/main' || github.ref == 'refs/heads/development')
    
    permissions:
      contents: read
      packages: write

    steps:
    - name: Checkout repository
      uses: actions/checkout@v4

    - name: Log in to Container Registry
      uses: docker/login-action@v3
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Extract metadata for backend
      id: meta-backend
      uses: docker/metadata-action@v5
      with:
        images: ${{ env.REGISTRY }}/${{ env.BACKEND_IMAGE_NAME }}
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=sha,prefix={{branch}}-
          type=raw,value=latest,enable={{is_default_branch}}

    - name: Extract metadata for frontend
      id: meta-frontend
      uses: docker/metadata-action@v5
      with:
        images: ${{ env.REGISTRY }}/${{ env.FRONTEND_IMAGE_NAME }}
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=sha,prefix={{branch}}-
          type=raw,value=latest,enable={{is_default_branch}}

    - name: Build and push backend image
      uses: docker/build-push-action@v5
      with:
        context: .
        file: ./infra/docker/backend.Dockerfile
        push: true
        tags: ${{ steps.meta-backend.outputs.tags }}
        labels: ${{ steps.meta-backend.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max

    - name: Build and push frontend image
      uses: docker/build-push-action@v5
      with:
        context: .
        file: ./infra/docker/frontend.Dockerfile
        push: true
        tags: ${{ steps.meta-frontend.outputs.tags }}
        labels: ${{ steps.meta-frontend.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max

  # Deploy to Staging (development branch)
  deploy-staging:
    needs: build-and-push
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/development'
    environment: staging

    steps:
    - name: Checkout repository
      uses: actions/checkout@v4

    - name: Setup SSH
      uses: webfactory/ssh-agent@v0.8.0
      with:
        ssh-private-key: ${{ secrets.STAGING_SSH_PRIVATE_KEY }}

    - name: Deploy to staging server
      run: |
        ssh -o StrictHostKeyChecking=no ${{ secrets.STAGING_USER }}@${{ secrets.STAGING_HOST }} << 'EOF'
          cd /opt/ngora-staging
          echo "${{ secrets.GITHUB_TOKEN }}" | docker login ghcr.io -u ${{ github.actor }} --password-stdin
          
          # Pull latest images
          docker pull ${{ env.REGISTRY }}/${{ env.BACKEND_IMAGE_NAME }}:development
          docker pull ${{ env.REGISTRY }}/${{ env.FRONTEND_IMAGE_NAME }}:development
          
          # Update docker-compose and restart services
          docker-compose -f docker-compose.staging.yml down
          docker-compose -f docker-compose.staging.yml up -d
          
          # Clean up unused images
          docker image prune -f
        EOF

  # Deploy to Production (main branch)
  deploy-production:
    needs: build-and-push
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    environment: production

    steps:
    - name: Checkout repository
      uses: actions/checkout@v4

    - name: Setup SSH
      uses: webfactory/ssh-agent@v0.8.0
      with:
        ssh-private-key: ${{ secrets.PRODUCTION_SSH_PRIVATE_KEY }}

    - name: Deploy to production server
      run: |
        ssh -o StrictHostKeyChecking=no ${{ secrets.PRODUCTION_USER }}@${{ secrets.PRODUCTION_HOST }} << 'EOF'
          cd /opt/ngora-production
          echo "${{ secrets.GITHUB_TOKEN }}" | docker login ghcr.io -u ${{ github.actor }} --password-stdin
          
          # Pull latest images
          docker pull ${{ env.REGISTRY }}/${{ env.BACKEND_IMAGE_NAME }}:latest
          docker pull ${{ env.REGISTRY }}/${{ env.FRONTEND_IMAGE_NAME }}:latest
          
          # Create backup of current deployment
          docker-compose -f docker-compose.prod.yml down
          
          # Update and restart services
          docker-compose -f docker-compose.prod.yml up -d
          
          # Health check
          sleep 30
          if ! curl -f http://localhost/health; then
            echo "Health check failed, rolling back..."
            docker-compose -f docker-compose.prod.yml down
            # Add rollback logic here
            exit 1
          fi
          
          # Clean up unused images
          docker image prune -f
        EOF

    - name: Notify deployment success
      if: success()
      run: |
        echo "Production deployment successful!"
        # Add notification logic here (Slack, email, etc.)

    - name: Notify deployment failure
      if: failure()
      run: |
        echo "Production deployment failed!"
        # Add failure notification logic here