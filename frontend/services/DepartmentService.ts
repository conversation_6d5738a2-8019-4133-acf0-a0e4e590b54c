import api from "@/config/api.config";
import http from "@/config/http";
import { DepartmentDto, DepartmentFilter, DepartmentRequest, HttpResponse } from "@/types";
import { httpResponse, jsonToQueryParams } from "@/utils/common";

export const fetchAll = async (filter: Partial<DepartmentFilter>): Promise<HttpResponse<DepartmentDto[]>> => {
    const url = `${api.v1.departments.root}?${jsonToQueryParams(filter)}`;

    return httpResponse<DepartmentDto[]>(await http.get(url));
};

export const create = async (data: DepartmentRequest): Promise<HttpResponse<DepartmentDto>> => {
    return httpResponse<DepartmentDto>(await http.post(api.v1.departments.root, data));
};

export const update = async (id: string, data: DepartmentRequest): Promise<HttpResponse<DepartmentDto>> => {
    return httpResponse<DepartmentDto>(await http.put(api.v1.departments.action(id), data));
};

export const voidDepartment = async (id: string, voidReason: string): Promise<HttpResponse<boolean>> => {
    return httpResponse<boolean>(await http.delete(api.v1.departments.action(id) + `?void_reason=${voidReason}`));
};
