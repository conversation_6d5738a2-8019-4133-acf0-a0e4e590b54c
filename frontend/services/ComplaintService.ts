import api from "@/config/api.config";
import http from "@/config/http";
import {
    HttpResponse,
    ComplaintDTO,
    ComplaintRequest,
    PublicComplaintRequest,
    ComplaintStatusUpdateRequest,
    ComplaintFilter,
    ComplaintStatsDto,
    ComplaintDashboardFilter,
    ComplaintTrendDto,
    ComplaintSummaryDto,
    ComplaintSubmissionResponse,
    ComplaintCategory,
} from "@/types";
import { httpResponse, jsonToQueryParams } from "@/utils/common";

export const fetchComplaints = async (filter: Partial<ComplaintFilter> = {}): Promise<HttpResponse<ComplaintDTO[]>> => {
    try {
        const queryString = jsonToQueryParams(filter);
        const url = `${api.v1.complaints.root}${queryString ? `?${queryString}` : ""}`;
        const response = await http.get(url);

        return httpResponse<ComplaintDTO[]>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

export const getComplaintById = async (id: string): Promise<HttpResponse<ComplaintDTO>> => {
    try {
        const response = await http.get(api.v1.complaints.action(id));

        return httpResponse<ComplaintDTO>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

export const fetchComplaintById = async (id: string): Promise<HttpResponse<ComplaintDTO>> => {
    try {
        const response = await http.get(api.v1.complaints.action(id));

        return httpResponse<ComplaintDTO>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

export const createComplaint = async (data: ComplaintRequest): Promise<HttpResponse<ComplaintDTO>> => {
    try {
        const response = await http.post(api.v1.complaints.root, data);

        return httpResponse<ComplaintDTO>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

export const createPublicComplaint = async (
    data: PublicComplaintRequest,
): Promise<HttpResponse<ComplaintSubmissionResponse>> => {
    try {
        const response = await http.post(api.v1.complaints.public, data);

        return httpResponse<ComplaintSubmissionResponse>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

export const trackComplaint = async (trackingCode: string): Promise<HttpResponse<ComplaintDTO>> => {
    try {
        const response = await http.get(api.v1.complaints.track(trackingCode));

        return httpResponse<ComplaintDTO>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

export const updateComplaint = async (id: string, data: ComplaintRequest): Promise<HttpResponse<ComplaintDTO>> => {
    try {
        const response = await http.put(api.v1.complaints.action(id), data);

        return httpResponse<ComplaintDTO>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

export const updateComplaintStatus = async (
    id: string,
    data: ComplaintStatusUpdateRequest,
): Promise<HttpResponse<ComplaintDTO>> => {
    try {
        const response = await http.put(api.v1.complaints.status(id), data);

        return httpResponse<ComplaintDTO>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

export const voidComplaint = async (id: string, voidReason: string): Promise<HttpResponse<boolean>> => {
    try {
        const response = await http.delete(api.v1.complaints.void(id), {
            data: { void_reason: voidReason },
        });

        return httpResponse<boolean>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

export const fetchComplaintStatistics = async (): Promise<HttpResponse<ComplaintStatsDto>> => {
    try {
        const response = await http.get(api.v1.complaints.statistics);

        return httpResponse<ComplaintStatsDto>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

export const fetchComplaintTrends = async (period: string = "6months"): Promise<HttpResponse<ComplaintTrendDto[]>> => {
    try {
        const response = await http.get(`${api.v1.complaints.trends}?period=${period}`);

        return httpResponse<ComplaintTrendDto[]>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

export const fetchComplaintDashboard = async (
    filter: Partial<ComplaintDashboardFilter> = {},
): Promise<HttpResponse<ComplaintSummaryDto[]>> => {
    try {
        const queryString = jsonToQueryParams(filter);
        const url = `${api.v1.complaints.dashboard}${queryString ? `?${queryString}` : ""}`;
        const response = await http.get(url);

        return httpResponse<ComplaintSummaryDto[]>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

export const fetchComplaintCategories = async (): Promise<HttpResponse<ComplaintCategory[]>> => {
    try {
        const response = await http.get(`${api.v1.settings.loadable_items.root}?type=COMPLAINT_CATEGORY`);

        return httpResponse<ComplaintCategory[]>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

export const fetchComplaintsWithPagination = async (
    filter: Partial<ComplaintFilter> = {},
): Promise<HttpResponse<{ data: ComplaintDTO[]; total: number; page: number; size: number }>> => {
    try {
        const queryString = jsonToQueryParams(filter);
        const url = `${api.v1.complaints.root}${queryString ? `?${queryString}` : ""}`;
        const response = await http.get(url);

        return httpResponse<{ data: ComplaintDTO[]; total: number; page: number; size: number }>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};
