import api from "@/config/api.config";
import http from "@/config/http";
import { HttpResponse } from "@/types";

export type InvitationStatus = "PENDING" | "ACCEPTED" | "REJECTED" | "CANCELLED";
export type InviteeType = "INTERNAL" | "EXTERNAL";
export type AccountType = "ORG" | "USER";

export interface ActivityInvitationDto {
  id: string;
  activity_id: string;
  status: InvitationStatus;
  type: InviteeType;
  account_id?: string;
  email?: string;
  name?: string;
  account_type?: AccountType;
  created_at?: string;
  updated_at?: string;
}

export type InternalInvitationRequest = {
  type: "INTERNAL";
  account_id?: string;
  user_id?: string;
  organization_id?: string;
};

export type ExternalInvitationRequest = {
  type: "EXTERNAL";
  email: string;
  name?: string;
  account_type?: AccountType;
};

export type ActivityInvitationRequest = InternalInvitationRequest | ExternalInvitationRequest;

export const listInvitations = async (
  activityId: string,
  params: { status?: InvitationStatus; search?: string } = {}
): Promise<HttpResponse<ActivityInvitationDto[]>> => {
  try {
    const url = api.v1.activities.invitations.root(activityId);
    const response = await http.get(url, { params });
    return { success: true, data: response.data, status: response.status };
  } catch (e: any) {
    const r = e.response || { data: null, status: 500 };
    return { success: false, data: r.data, status: r.status };
  }
};

export const resendInvitationEmail = async (
  activityId: string,
  invitationId: string
) => {
  const url = api.v1.activities.invitations.action(activityId, invitationId);
  const response = await http.post(url + "/resend");
  return { success: true, data: response.data, status: response.status };
};

export const createInvitations = async (
  activityId: string,
  body: ActivityInvitationRequest[]
): Promise<HttpResponse<ActivityInvitationDto[]>> => {
  try {
    const url = api.v1.activities.invitations.root(activityId);
    const response = await http.post(url, body);
    return { success: true, data: response.data, status: response.status };
  } catch (e: any) {
    const r = e.response || { data: null, status: 500 };
    return { success: false, data: r.data, status: r.status };
  }
};

export const updateInvitationStatus = async (
  activityId: string,
  invitationId: string,
  status: InvitationStatus
): Promise<HttpResponse<ActivityInvitationDto>> => {
  try {
    const url = api.v1.activities.invitations.status(activityId, invitationId, status);
    const response = await http.put(url);
    return { success: true, data: response.data, status: response.status };
  } catch (e: any) {
    const r = e.response || { data: null, status: 500 };
    return { success: false, data: r.data, status: r.status };
  }
};

export const deleteInvitation = async (
  activityId: string,
  invitationId: string
): Promise<HttpResponse<boolean>> => {
  try {
    const url = api.v1.activities.invitations.action(activityId, invitationId);
    const response = await http.delete(url);
    return { success: true, data: true, status: response.status };
  } catch (e: any) {
    const r = e.response || { data: null, status: 500 };
    return { success: false, data: false as any, status: r.status };
  }
};
