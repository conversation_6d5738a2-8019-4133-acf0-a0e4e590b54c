import { AuditD<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, HttpResponse } from "@/types";
import http from "@/config/http";
import routes from "@/config/api.config";
import { httpResponse, jsonToQueryParams } from "@/utils/common";

export const fetchAudits = async (filter: Partial<AuditFilter> = {}): Promise<HttpResponse<AuditDto[]>> => {
    try {
        const response = await http.get(`${routes.v1.management.audits.root}?${jsonToQueryParams(filter)}`);

        return httpResponse<AuditDto[]>(response);
    } catch (error) {
        return httpResponse(error);
    }
};

export const fetchActions = async (): Promise<HttpResponse<string[]>> => {
    try {
        const response = await http.get(routes.v1.management.audits.actions);

        return httpResponse<string[]>(response);
    } catch (error) {
        return httpResponse(error);
    }
};
