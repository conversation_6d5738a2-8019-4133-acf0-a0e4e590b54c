import api from "@/config/api.config";
import http from "@/config/http";
import {
    CountryDto,
    CountryFilter,
    CurrencyDto,
    DistrictDto,
    DistrictFilter,
    HttpResponse,
    LoadableItemDto,
    LoadableItemFilter,
    RegionDto,
    RegionFilter,
} from "@/types";
import { httpResponse, jsonToQueryParams } from "@/utils/common";

export const fetchLoadableItems = async (
    filter: Partial<LoadableItemFilter>,
): Promise<HttpResponse<LoadableItemDto[]>> => {
    try {
        const queryString = jsonToQueryParams(filter);
        const url = `${api.v1.settings.loadable_items.root}${queryString ? `?${queryString}` : ""}`;

        const response = await http.get(url);

        return httpResponse<LoadableItemDto[]>(response);
    } catch (error) {
        return httpResponse(error);
    }
};

export const fetchLoadableItemTypes = async (params: Partial<LoadableItemFilter>): Promise<HttpResponse<string[]>> => {
    try {
        const url = `${api.v1.settings.loadable_items.root}/types?${jsonToQueryParams(params)}`;
        const response = await http.get(url);

        return httpResponse<string[]>(response);
    } catch (error) {
        return httpResponse(error);
    }
};

export const createLoadableItem = async (
    data: Omit<LoadableItemDto, "id" | "created_at" | "updated_at">,
): Promise<HttpResponse<LoadableItemDto>> => {
    try {
        const response = await http.post(api.v1.settings.loadable_items.root, data);

        return httpResponse<LoadableItemDto>(response);
    } catch (error) {
        return httpResponse(error);
    }
};

export const updateLoadableItem = async (
    id: string,
    data: Omit<LoadableItemDto, "id" | "created_at" | "updated_at">,
): Promise<HttpResponse<LoadableItemDto>> => {
    try {
        const response = await http.put(api.v1.settings.loadable_items.action(id), data);

        return httpResponse<LoadableItemDto>(response);
    } catch (error) {
        return httpResponse(error);
    }
};

export const deleteLoadableItem = async (id: string, voidReason: string): Promise<HttpResponse<boolean>> => {
    try {
        const response = await http.delete(
            api.v1.settings.loadable_items.action(id) + `?void_reason=${encodeURIComponent(voidReason)}`,
        );

        return httpResponse<boolean>(response);
    } catch (error) {
        return httpResponse(error);
    }
};

export const fetchCurrencies = async (filter: Partial<CountryFilter> = {}): Promise<HttpResponse<CurrencyDto[]>> => {
    try {
        const queryString = jsonToQueryParams(filter);
        const url = `${api.v1.financial.currencies}${queryString ? `?${queryString}` : ""}`;

        const response = await http.get(url);

        return httpResponse<CurrencyDto[]>(response);
    } catch (error) {
        return httpResponse(error);
    }
};

export const fetchCountries = async (filter: Partial<CountryFilter> = {}): Promise<HttpResponse<CountryDto[]>> => {
    try {
        const queryString = jsonToQueryParams(filter);
        const url = `${api.v1.settings.countries}${queryString ? `?${queryString}` : ""}`;

        const response = await http.get(url);

        return httpResponse<CountryDto[]>(response);
    } catch (error) {
        return httpResponse(error);
    }
};

export const fetchDistricts = async (filter: Partial<DistrictFilter> = {}): Promise<HttpResponse<DistrictDto[]>> => {
    try {
        const queryString = jsonToQueryParams(filter);
        const url = `${api.v1.settings.districts.root}${queryString ? `?${queryString}` : ""}`;

        const response = await http.get(url);

        return httpResponse<DistrictDto[]>(response);
    } catch (error) {
        return httpResponse(error);
    }
};

export const fetchRegions = async (filter: Partial<RegionFilter> = {}): Promise<HttpResponse<RegionDto[]>> => {
    try {
        const queryString = jsonToQueryParams(filter);
        const url = `${api.v1.settings.regions}${queryString ? `?${queryString}` : ""}`;

        const response = await http.get(url);

        return httpResponse<RegionDto[]>(response);
    } catch (error) {
        return httpResponse(error);
    }
};
