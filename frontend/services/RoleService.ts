import api from "@/config/api.config";
import http from "@/config/http";
import { HttpResponse, Pagination, RoleDto, RoleFilter, RolePermissionDto } from "@/types";
import { httpResponse, jsonToQueryParams } from "@/utils/common";

type RoleRequest = {} & Pagination & RolePermissionDto;

export const fetchAll = async (filter: Partial<RoleFilter>): Promise<HttpResponse<RolePermissionDto[]>> => {
    try {
        const url = `${api.v1.roles.root}?${jsonToQueryParams(filter)}`;
        const response = await http.get(url);

        return httpResponse<RolePermissionDto[]>(response);
    } catch (e: any) {
        throw new Error("Failed to fetch roles");
    }
};

export const createRole = async (role: Partial<RoleRequest>): Promise<HttpResponse<RoleDto[]>> => {
    try {
        const url = `${api.v1.roles.root}`;
        const response = await http.post(url, role);

        return httpResponse<RoleDto[]>(response);
    } catch (e: any) {
        throw new Error("Failed to create role");
    }
};

export const updateRole = async (data: Partial<RolePermissionDto>): Promise<HttpResponse<RoleDto>> => {
    try {
        const response = await http.put(api.v1.roles.root + `/${data.id}`, data);

        return httpResponse<RoleDto>(response);
    } catch (e: any) {
        throw new Error("Failed to update role");
    }
};

export const deleteRole = async (roleId: string): Promise<HttpResponse<boolean>> => {
    try {
        const response = await http.delete(api.v1.roles.delete(roleId));

        return httpResponse<boolean>(response);
    } catch (e: any) {
        throw new Error("Failed to delete role");
    }
};
