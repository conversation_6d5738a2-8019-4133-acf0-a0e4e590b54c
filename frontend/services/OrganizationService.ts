import { AxiosProgressEvent } from "axios";

import api from "@/config/api.config";
import http from "@/config/http";
import { HttpResponse } from "@/types";
import {
    BankDetailsDto,
    DirectorDto,
    FundingSourceDto,
    LocationActivityDto,
    OrganizationAuditorDto,
    OrganizationDonorDto,
    OrganizationDto,
    OrganizationFilter,
    OrganizationProjectDto,
    OrganizationSectorDto,
    OrganizationStaffDto,
    OrganizationVerificationDto,
    TargetGroupDto,
} from "@/types/organization.dto";
import { httpResponse, jsonToQueryParams } from "@/utils/common";

export const fetchOrganizations = async (
    filter: Partial<OrganizationFilter>,
): Promise<HttpResponse<OrganizationDto[]>> => {
    try {
        const url = `${api.v1.organizations.root}?${jsonToQueryParams(filter)}`;
        const response = await http.get(url);

        return httpResponse<OrganizationDto[]>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

export const getOrganization = async (id: string): Promise<HttpResponse<OrganizationDto>> => {
    try {
        const url = `${api.v1.organizations.root}/${id}`;
        const response = await http.get(url);

        return httpResponse<OrganizationDto>(response);
    } catch (e: any) {
        return httpResponse<OrganizationDto>(e.response);
    }
};

export const create = async (
    data: FormData,
    onUploadProgress?: (e: AxiosProgressEvent) => void,
): Promise<HttpResponse<OrganizationDto>> => {
    try {
        const sectors = JSON.parse((data.get("sectors") as string) || "[]");
        const cleaned = [];

        for (const sector of sectors) {
            if (typeof sector === "object" && sector.id) {
                cleaned.push(sector.id);
            } else {
                cleaned.push(sector);
            }
        }

        data.delete("sectors");
        data.append("sectors", JSON.stringify(cleaned));

        const response = await http.post(api.v1.organizations.root, data, {
            onUploadProgress,
        });

        return httpResponse<OrganizationDto>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

export const getDirectors = async (organizationId: string): Promise<HttpResponse<DirectorDto[]>> => {
    try {
        const url = `${api.v1.organizations.root}/${organizationId}/directors`;
        const response = await http.get(url);

        return httpResponse<DirectorDto[]>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

export const getSectors = async (organizationId: string): Promise<HttpResponse<OrganizationSectorDto[]>> => {
    try {
        const url = `${api.v1.organizations.root}/${organizationId}/sectors`;
        const response = await http.get(url);

        return httpResponse<OrganizationSectorDto[]>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

export const getStaff = async (organizationId: string): Promise<HttpResponse<OrganizationStaffDto[]>> => {
    try {
        const url = `${api.v1.organizations.root}/${organizationId}/staff`;
        const response = await http.get(url);

        return httpResponse<OrganizationStaffDto[]>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

export const getProjects = async (organizationId: string): Promise<HttpResponse<OrganizationStaffDto[]>> => {
    try {
        const url = `${api.v1.organizations.root}/${organizationId}/projects`;
        const response = await http.get(url);

        return httpResponse<OrganizationStaffDto[]>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

export const submitForReview = async (organizationId: string) => {
    try {
        const url = `/api/v1/applications/submit/${organizationId}`;
        const response = await http.post(url);

        return httpResponse(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

// Directors CRUD
export const addDirectors = async (organizationId: string, directors: any[]): Promise<HttpResponse<DirectorDto[]>> => {
    try {
        const url = api.v1.organizations.directors.root(organizationId);
        const response = await http.post(url, directors);

        return httpResponse<DirectorDto[]>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

export const updateDirector = async (
    organizationId: string,
    directorId: string,
    director: any,
): Promise<HttpResponse<DirectorDto>> => {
    try {
        const url = api.v1.organizations.directors.action(organizationId, directorId);
        const response = await http.put(url, director);

        return httpResponse<DirectorDto>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

export const deleteDirector = async (organizationId: string, directorId: string): Promise<HttpResponse<boolean>> => {
    try {
        const url = api.v1.organizations.directors.action(organizationId, directorId);
        const response = await http.delete(url);

        return httpResponse<boolean>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

// Sectors CRUD
export const addSectors = async (
    organizationId: string,
    sectors: any[],
): Promise<HttpResponse<OrganizationSectorDto[]>> => {
    try {
        const url = api.v1.organizations.sectors.root(organizationId);
        const response = await http.post(url, sectors);

        return httpResponse<OrganizationSectorDto[]>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

export const updateSector = async (
    organizationId: string,
    sectorId: string,
    sectorData: any,
): Promise<HttpResponse<OrganizationSectorDto>> => {
    try {
        const url = api.v1.organizations.sectors.action(organizationId, sectorId);
        const response = await http.put(url, sectorData);

        return httpResponse<OrganizationSectorDto>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

export const deleteSector = async (organizationId: string, sectorId: string): Promise<HttpResponse<boolean>> => {
    try {
        const url = api.v1.organizations.sectors.action(organizationId, sectorId);
        const response = await http.delete(url);

        return httpResponse<boolean>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

// Location Activities CRUD
export const getLocationActivities = async (organizationId: string): Promise<HttpResponse<LocationActivityDto[]>> => {
    try {
        const url = api.v1.organizations.locationActivities.root(organizationId);
        const response = await http.get(url);

        return httpResponse<LocationActivityDto[]>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

export const addLocationActivities = async (
    organizationId: string,
    activities: any[],
): Promise<HttpResponse<LocationActivityDto[]>> => {
    try {
        const url = api.v1.organizations.locationActivities.root(organizationId);
        const response = await http.post(url, activities);

        return httpResponse<LocationActivityDto[]>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

export const updateLocationActivity = async (
    organizationId: string,
    activityId: string,
    activity: any,
): Promise<HttpResponse<LocationActivityDto>> => {
    try {
        const url = api.v1.organizations.locationActivities.action(organizationId, activityId);
        const response = await http.put(url, activity);

        return httpResponse<LocationActivityDto>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

export const deleteLocationActivity = async (
    organizationId: string,
    activityId: string,
): Promise<HttpResponse<boolean>> => {
    try {
        const url = api.v1.organizations.locationActivities.action(organizationId, activityId);
        const response = await http.delete(url);

        return httpResponse<boolean>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

// Target Groups CRUD
export const getTargetGroups = async (organizationId: string): Promise<HttpResponse<TargetGroupDto[]>> => {
    try {
        const url = api.v1.organizations.targetGroups.root(organizationId);
        const response = await http.get(url);

        return httpResponse<TargetGroupDto[]>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

export const addTargetGroups = async (
    organizationId: string,
    groups: any[],
): Promise<HttpResponse<TargetGroupDto[]>> => {
    try {
        const url = api.v1.organizations.targetGroups.root(organizationId);
        const response = await http.post(url, groups);

        return httpResponse<TargetGroupDto[]>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

export const updateTargetGroup = async (
    organizationId: string,
    groupId: string,
    group: any,
): Promise<HttpResponse<TargetGroupDto>> => {
    try {
        const url = api.v1.organizations.targetGroups.action(organizationId, groupId);
        const response = await http.put(url, group);

        return httpResponse<TargetGroupDto>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

export const deleteTargetGroup = async (organizationId: string, groupId: string): Promise<HttpResponse<boolean>> => {
    try {
        const url = api.v1.organizations.targetGroups.action(organizationId, groupId);
        const response = await http.delete(url);

        return httpResponse<boolean>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

// Projects CRUD
export const addProjects = async (
    organizationId: string,
    projects: any[],
): Promise<HttpResponse<OrganizationProjectDto[]>> => {
    try {
        const url = api.v1.organizations.projects.root(organizationId);
        const response = await http.post(url, projects);

        return httpResponse<OrganizationProjectDto[]>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

export const updateProject = async (
    organizationId: string,
    projectId: string,
    project: any,
): Promise<HttpResponse<OrganizationProjectDto>> => {
    try {
        const url = api.v1.organizations.projects.action(organizationId, projectId);
        const response = await http.put(url, project);

        return httpResponse<OrganizationProjectDto>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

export const deleteProject = async (organizationId: string, projectId: string): Promise<HttpResponse<boolean>> => {
    try {
        const url = api.v1.organizations.projects.action(organizationId, projectId);
        const response = await http.delete(url);

        return httpResponse<boolean>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

// Donors CRUD
export const getDonors = async (organizationId: string): Promise<HttpResponse<OrganizationDonorDto[]>> => {
    try {
        const url = api.v1.organizations.donors.root(organizationId);
        const response = await http.get(url);

        return httpResponse<OrganizationDonorDto[]>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

export const addDonors = async (
    organizationId: string,
    donors: any[],
): Promise<HttpResponse<OrganizationDonorDto[]>> => {
    try {
        const url = api.v1.organizations.donors.root(organizationId);
        const response = await http.post(url, donors);

        return httpResponse<OrganizationDonorDto[]>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

export const updateDonor = async (
    organizationId: string,
    donorId: string,
    donor: any,
): Promise<HttpResponse<OrganizationDonorDto>> => {
    try {
        const url = api.v1.organizations.donors.action(organizationId, donorId);
        const response = await http.put(url, donor);

        return httpResponse<OrganizationDonorDto>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

export const deleteDonor = async (organizationId: string, donorId: string): Promise<HttpResponse<boolean>> => {
    try {
        const url = api.v1.organizations.donors.action(organizationId, donorId);
        const response = await http.delete(url);

        return httpResponse<boolean>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

// Funding Sources CRUD
export const getFundingSources = async (organizationId: string): Promise<HttpResponse<FundingSourceDto[]>> => {
    try {
        const url = api.v1.organizations.fundingSources.root(organizationId);
        const response = await http.get(url);

        return httpResponse<FundingSourceDto[]>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

export const addFundingSources = async (
    organizationId: string,
    sources: any[],
): Promise<HttpResponse<FundingSourceDto[]>> => {
    try {
        const url = api.v1.organizations.fundingSources.root(organizationId);
        const response = await http.post(url, sources);

        return httpResponse<FundingSourceDto[]>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

export const updateFundingSource = async (
    organizationId: string,
    sourceId: string,
    source: any,
): Promise<HttpResponse<FundingSourceDto>> => {
    try {
        const url = api.v1.organizations.fundingSources.action(organizationId, sourceId);
        const response = await http.put(url, source);

        return httpResponse<FundingSourceDto>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

export const deleteFundingSource = async (organizationId: string, sourceId: string): Promise<HttpResponse<boolean>> => {
    try {
        const url = api.v1.organizations.fundingSources.action(organizationId, sourceId);
        const response = await http.delete(url);

        return httpResponse<boolean>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

// Auditors CRUD
export const getAuditors = async (organizationId: string): Promise<HttpResponse<OrganizationAuditorDto[]>> => {
    try {
        const url = api.v1.organizations.auditors.root(organizationId);
        const response = await http.get(url);

        return httpResponse<OrganizationAuditorDto[]>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

export const addAuditors = async (
    organizationId: string,
    auditors: any[],
): Promise<HttpResponse<OrganizationAuditorDto[]>> => {
    try {
        const url = api.v1.organizations.auditors.root(organizationId);
        const response = await http.post(url, auditors);

        return httpResponse<OrganizationAuditorDto[]>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

export const updateAuditor = async (
    organizationId: string,
    auditorId: string,
    auditor: any,
): Promise<HttpResponse<OrganizationAuditorDto>> => {
    try {
        const url = api.v1.organizations.auditors.action(organizationId, auditorId);
        const response = await http.put(url, auditor);

        return httpResponse<OrganizationAuditorDto>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

export const deleteAuditor = async (organizationId: string, auditorId: string): Promise<HttpResponse<boolean>> => {
    try {
        const url = api.v1.organizations.auditors.action(organizationId, auditorId);
        const response = await http.delete(url);

        return httpResponse<boolean>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

// Bank Details CRUD
export const getBankDetails = async (organizationId: string): Promise<HttpResponse<BankDetailsDto[]>> => {
    try {
        const url = api.v1.organizations.bankDetails.root(organizationId);
        const response = await http.get(url);

        return httpResponse<BankDetailsDto[]>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

export const addBankDetails = async (
    organizationId: string,
    details: any[],
): Promise<HttpResponse<BankDetailsDto[]>> => {
    try {
        const url = api.v1.organizations.bankDetails.root(organizationId);
        const response = await http.post(url, details);

        return httpResponse<BankDetailsDto[]>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

export const updateBankDetail = async (
    organizationId: string,
    detailId: string,
    detail: any,
): Promise<HttpResponse<BankDetailsDto>> => {
    try {
        const url = api.v1.organizations.bankDetails.action(organizationId, detailId);
        const response = await http.put(url, detail);

        return httpResponse<BankDetailsDto>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

export const deleteBankDetail = async (organizationId: string, detailId: string): Promise<HttpResponse<boolean>> => {
    try {
        const url = api.v1.organizations.bankDetails.action(organizationId, detailId);
        const response = await http.delete(url);

        return httpResponse<boolean>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

// Staff CRUD
export const addStaff = async (organizationId: string, staff: any[]): Promise<HttpResponse<OrganizationStaffDto[]>> => {
    try {
        const url = api.v1.organizations.staff.root(organizationId);
        const response = await http.post(url, staff);

        return httpResponse<OrganizationStaffDto[]>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

export const updateStaff = async (
    organizationId: string,
    staffId: string,
    staff: any,
): Promise<HttpResponse<OrganizationStaffDto>> => {
    try {
        const url = api.v1.organizations.staff.action(organizationId, staffId);
        const response = await http.put(url, staff);

        return httpResponse<OrganizationStaffDto>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

export const deleteStaff = async (organizationId: string, staffId: string): Promise<HttpResponse<boolean>> => {
    try {
        const url = api.v1.organizations.staff.action(organizationId, staffId);
        const response = await http.delete(url);

        return httpResponse<boolean>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

/**
 * Verify an organization by registration number (public endpoint)
 */
export const verifyOrganization = async (
    registrationNumber: string,
): Promise<HttpResponse<OrganizationVerificationDto>> => {
    try {
        const url = `${api.v1.organizations.root}/verify/${encodeURIComponent(registrationNumber)}`;
        const response = await http.get(url);

        return httpResponse<OrganizationVerificationDto>(response);
    } catch (e: any) {
        return httpResponse<OrganizationVerificationDto>(e.response);
    }
};
