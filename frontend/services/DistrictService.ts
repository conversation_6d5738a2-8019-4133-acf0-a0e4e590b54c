import api from "@/config/api.config";
import http from "@/config/http";
import { HttpResponse, DistrictDto, DistrictFilter } from "@/types";
import { httpResponse, jsonToQueryParams } from "@/utils/common";

export interface DistrictRequest {
    name: string;
    code: string;
    region_id: string;
}

export const fetchAll = async (filter: Partial<DistrictFilter> = {}): Promise<HttpResponse<DistrictDto[]>> => {
    try {
        const queryString = jsonToQueryParams(filter);
        const url = `${api.v1.settings.districts.root}${queryString ? `?${queryString}` : ""}`;
        const response = await http.get(url);

        return httpResponse<DistrictDto[]>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

export const getById = async (id: string): Promise<HttpResponse<DistrictDto>> => {
    try {
        const response = await http.get(api.v1.settings.districts.action(id));

        return httpResponse<DistrictDto>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

export const create = async (data: DistrictRequest): Promise<HttpResponse<DistrictDto>> => {
    try {
        const response = await http.post(api.v1.settings.districts.root, data);

        return httpResponse<DistrictDto>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

export const update = async (id: string, data: DistrictRequest): Promise<HttpResponse<DistrictDto>> => {
    try {
        const response = await http.put(api.v1.settings.districts.action(id), data);

        return httpResponse<DistrictDto>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

export const deleteDistrict = async (id: string): Promise<HttpResponse<boolean>> => {
    try {
        const response = await http.delete(api.v1.settings.districts.action(id));

        return httpResponse<boolean>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};
