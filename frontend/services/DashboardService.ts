import api from "@/config/api.config";
import http from "@/config/http";
import { HttpResponse, OrganizationStatistics, RecentOrganization } from "@/types";
import { httpResponse } from "@/utils/common";

export const getOrganizationStatistics = async (): Promise<HttpResponse<OrganizationStatistics>> => {
    try {
        const url = `${api.v1.dashboard.root}/organizations/statistics`;
        const response = await http.get(url);

        return httpResponse<OrganizationStatistics>(response);
    } catch (e: any) {
        return httpResponse<OrganizationStatistics>(e.response);
    }
};

export const getRecentOrganizations = async (limit: number = 10): Promise<HttpResponse<RecentOrganization[]>> => {
    try {
        const url = `${api.v1.dashboard.root}/organizations/recent?limit=${limit}`;
        const response = await http.get(url);

        return httpResponse<RecentOrganization[]>(response);
    } catch (e: any) {
        return httpResponse<RecentOrganization[]>(e.response);
    }
};
