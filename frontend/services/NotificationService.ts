import api from "@/config/api.config";
import http from "@/config/http";
import {
    HttpResponse,
    NotificationWithRecipientDto,
    NotificationDto,
    NotificationFilter,
    CreateNotificationRequest,
    UpdateNotificationStatusRequest,
} from "@/types";
import { httpResponse, jsonToQueryParams } from "@/utils/common";

export const fetchNotifications = async (
    filter: Partial<NotificationFilter>,
): Promise<HttpResponse<NotificationWithRecipientDto[]>> => {
    try {
        const url = `${api.v1.notifications.root}?${jsonToQueryParams(filter)}`;
        const response = await http.get(url);

        return httpResponse<NotificationWithRecipientDto[]>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

export const fetchAllNotifications = async (
    filter: Partial<NotificationFilter>,
): Promise<HttpResponse<NotificationDto[]>> => {
    try {
        const url = `${api.v1.notifications.all}?${jsonToQueryParams(filter)}`;
        const response = await http.get(url);

        return httpResponse<NotificationDto[]>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

export const getNotification = async (notificationId: string): Promise<HttpResponse<NotificationDto>> => {
    try {
        const response = await http.get(api.v1.notifications.action(notificationId));

        return httpResponse<NotificationDto>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

export const createNotification = async (data: CreateNotificationRequest): Promise<HttpResponse<NotificationDto>> => {
    try {
        const response = await http.post(api.v1.notifications.root, data);

        return httpResponse<NotificationDto>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

export const updateNotificationStatus = async (
    notificationId: string,
    data: UpdateNotificationStatusRequest,
): Promise<HttpResponse<NotificationWithRecipientDto>> => {
    try {
        const response = await http.patch(api.v1.notifications.updateStatus(notificationId), data);

        return httpResponse<NotificationWithRecipientDto>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

export const deleteNotification = async (notificationId: string): Promise<HttpResponse<boolean>> => {
    try {
        const response = await http.delete(api.v1.notifications.action(notificationId));

        return httpResponse<boolean>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

export const markAllAsRead = async (): Promise<HttpResponse<boolean>> => {
    try {
        const response = await http.post(api.v1.notifications.markAllRead);

        return httpResponse<boolean>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

export const getUnreadCount = async (): Promise<HttpResponse<number>> => {
    try {
        const response = await http.get(api.v1.notifications.unreadCount);

        return httpResponse<number>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};
