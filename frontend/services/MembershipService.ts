import api from "@/config/api.config";
import http from "@/config/http";
import {
    AcceptInvitationRequest,
    AcceptInvitationWithAccountRequest,
    HttpResponse,
    MemberDto,
    MemberFilter,
    MemberInvitationDto,
    MemberInvitationFilter,
    MemberInvitationRequest,
    OrganizationMembershipStats,
    RejectInvitationRequest,
    ValidateInvitationTokenRequest,
    ValidateInvitationTokenResponse,
} from "@/types";
import { httpResponse, jsonToQueryParams } from "@/utils/common";

export const getOrganizationMembers = async (
    organizationId: string, 
    filter: Partial<MemberFilter> = {}
): Promise<HttpResponse<MemberDto[]>> => {
    try {
        const queryString = jsonToQueryParams(filter);
        const url = `${api.v1.membership.organizations.members(organizationId)}${queryString ? `?${queryString}` : ""}`;
        const response = await http.get(url);

        return httpResponse<MemberDto[]>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

export const inviteMember = async (
    organizationId: string,
    request: MemberInvitationRequest,
): Promise<HttpResponse<MemberInvitationDto>> => {
    try {
        const response = await http.post(api.v1.membership.organizations.invite(organizationId), request);

        return httpResponse<MemberInvitationDto>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

export const getMemberInvitations = async (
    organizationId: string, 
    filter: Partial<MemberInvitationFilter> = {}
): Promise<HttpResponse<MemberInvitationDto[]>> => {
    try {
        const queryString = jsonToQueryParams(filter);
        const url = `${api.v1.membership.organizations.invitations(organizationId)}${queryString ? `?${queryString}` : ""}`;
        const response = await http.get(url);

        return httpResponse<MemberInvitationDto[]>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

export const cancelInvitation = async (organizationId: string, invitationId: string): Promise<HttpResponse<boolean>> => {
    try {
        const response = await http.delete(api.v1.membership.organizations.cancelInvitation(organizationId, invitationId));

        return httpResponse<boolean>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

export const removeMember = async (organizationId: string, memberId: string): Promise<HttpResponse<boolean>> => {
    try {
        const response = await http.delete(api.v1.membership.organizations.removeMember(organizationId, memberId));

        return httpResponse<boolean>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

export const acceptInvitation = async (
    invitationCode: string,
    request: AcceptInvitationRequest,
): Promise<HttpResponse<MemberDto>> => {
    try {
        const response = await http.post(api.v1.membership.invitations.accept(invitationCode), request);

        return httpResponse<MemberDto>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

export const rejectInvitation = async (
    invitationCode: string,
    request: RejectInvitationRequest,
): Promise<HttpResponse<boolean>> => {
    try {
        const response = await http.post(api.v1.membership.invitations.reject(invitationCode), request);

        return httpResponse<boolean>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

export const validateInvitationToken = async (
    request: ValidateInvitationTokenRequest,
): Promise<HttpResponse<ValidateInvitationTokenResponse>> => {
    try {
        const response = await http.post(api.v1.membership.invitations.validate, request);

        return httpResponse<ValidateInvitationTokenResponse>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

export const acceptInvitationWithAccount = async (
    token: string,
    request: AcceptInvitationWithAccountRequest,
): Promise<HttpResponse<MemberDto>> => {
    try {
        const response = await http.post(api.v1.membership.invitations.acceptWithAccount(token), request);

        return httpResponse<MemberDto>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

export const changeMemberRole = async (
    organizationId: string,
    memberId: string,
    role: "OWNER" | "MEMBER"
): Promise<HttpResponse<MemberDto>> => {
    try {
        const response = await http.patch(api.v1.membership.organizations.changeMemberRole(organizationId, memberId), {
            role,
        });

        return httpResponse<MemberDto>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

export const retryInvitation = async (
    organizationId: string,
    email: string,
    role: "OWNER" | "MEMBER"
): Promise<HttpResponse<MemberInvitationDto>> => {
    try {
        const response = await http.post(api.v1.membership.organizations.invite(organizationId), {
            email,
            role,
        });

        return httpResponse<MemberInvitationDto>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

export const removeInvitation = async (organizationId: string, invitationId: string): Promise<HttpResponse<boolean>> => {
    try {
        const response = await http.delete(api.v1.membership.organizations.removeInvitation(organizationId, invitationId));

        return httpResponse<boolean>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

export const getOrganizationMembershipStats = async (
    organizationId: string
): Promise<HttpResponse<OrganizationMembershipStats>> => {
    try {
        const response = await http.get(api.v1.membership.organizations.stats(organizationId));

        return httpResponse<OrganizationMembershipStats>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};
