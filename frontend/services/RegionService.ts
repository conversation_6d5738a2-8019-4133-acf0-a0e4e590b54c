import api from "@/config/api.config";
import http from "@/config/http";
import { HttpResponse, RegionDto, RegionFilter } from "@/types";
import { httpResponse, jsonToQueryParams } from "@/utils/common";

export interface RegionRequest {
    name: string;
    code: string;
    description: string;
}

export const fetchAll = async (filter: Partial<RegionFilter> = {}): Promise<HttpResponse<RegionDto[]>> => {
    try {
        const queryString = jsonToQueryParams(filter);
        const url = `${api.v1.settings.regions}${queryString ? `?${queryString}` : ""}`;
        const response = await http.get(url);

        return httpResponse<RegionDto[]>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

export const getById = async (id: string): Promise<HttpResponse<RegionDto>> => {
    try {
        const response = await http.get(`${api.v1.settings.regions}/${id}`);

        return httpResponse<RegionDto>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

export const create = async (data: RegionRequest): Promise<HttpResponse<RegionDto>> => {
    try {
        const response = await http.post(api.v1.settings.regions, data);

        return httpResponse<RegionDto>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

export const update = async (id: string, data: RegionRequest): Promise<HttpResponse<RegionDto>> => {
    try {
        const response = await http.put(`${api.v1.settings.regions}/${id}`, data);

        return httpResponse<RegionDto>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

export const deleteRegion = async (id: string): Promise<HttpResponse<boolean>> => {
    try {
        const response = await http.delete(`${api.v1.settings.regions}/${id}`);

        return httpResponse<boolean>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};
