import api from "@/config/api.config";
import http from "@/config/http";
import { HttpResponse, LoadableItemDto, LoadableItemFilter } from "@/types";
import { httpResponse, jsonToQueryParams } from "@/utils/common";

export interface ZoneRequest {
    code: string;
    display_value: string;
    description?: string;
}

// Convert ZoneRequest to LoadableItemRequest format
const toLoadableItemRequest = (zone: ZoneRequest) => ({
    type: "ZONE",
    code: zone.code,
    display_value: zone.display_value,
    description: zone.description || "",
});

export const fetchAll = async (filter: Partial<LoadableItemFilter> = {}): Promise<HttpResponse<LoadableItemDto[]>> => {
    try {
        const zoneFilter = { ...filter, type: "ZONE" };
        const queryString = jsonToQueryParams(zoneFilter);
        const url = `${api.v1.settings.loadable_items.root}${queryString ? `?${queryString}` : ""}`;
        const response = await http.get(url);

        return httpResponse<LoadableItemDto[]>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

export const getById = async (id: string): Promise<HttpResponse<LoadableItemDto>> => {
    try {
        const response = await http.get(api.v1.settings.loadable_items.action(id));

        return httpResponse<LoadableItemDto>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

export const create = async (data: ZoneRequest): Promise<HttpResponse<LoadableItemDto>> => {
    try {
        const loadableItemData = toLoadableItemRequest(data);
        const response = await http.post(api.v1.settings.loadable_items.root, loadableItemData);

        return httpResponse<LoadableItemDto>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

export const update = async (id: string, data: ZoneRequest): Promise<HttpResponse<LoadableItemDto>> => {
    try {
        const loadableItemData = { ...toLoadableItemRequest(data), id };
        const response = await http.put(api.v1.settings.loadable_items.action(id), loadableItemData);

        return httpResponse<LoadableItemDto>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

export const deleteZone = async (id: string): Promise<HttpResponse<boolean>> => {
    try {
        const response = await http.delete(api.v1.settings.loadable_items.action(id));

        return httpResponse<boolean>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};
