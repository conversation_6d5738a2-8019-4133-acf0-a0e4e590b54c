import api from "@/config/api.config";
import http from "@/config/http";
import {
    FeeEffectivenessAnalysis,
    FinanceWorkflowStage,
    FinancialDashboardData,
    HttpResponse,
    InvoiceDto,
    InvoiceFilter,
    OrganizationFinancialProfile,
    PaymentAnalyticsData,
    PaymentDiscrepancy,
    PaymentDto,
    PaymentReconciliation,
    PaymentVerificationRequest,
    WorkflowStageFilter,
} from "@/types";
import { httpResponse, jsonToQueryParams } from "@/utils/common";

export const fetchFinancialDashboard = async (
    dateRange: string = "30d",
): Promise<HttpResponse<FinancialDashboardData>> => {
    try {
        const startDate = getDateFromRange(dateRange);
        const endDate = new Date().toISOString().split("T")[0];

        const response = await http.get(
            `${api.v1.financial.root}/dashboard?start_date=${startDate}&end_date=${endDate}`,
        );

        return httpResponse<FinancialDashboardData>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

// Payment Analytics
export const fetchPaymentAnalytics = async (
    period: string = "monthly",
): Promise<HttpResponse<PaymentAnalyticsData>> => {
    try {
        const response = await http.get(`${api.v1.financial.root}/analytics/payments?period=${period}`);

        return httpResponse<PaymentAnalyticsData>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

// Organization Financial Profile
export const fetchOrganizationFinancialProfile = async (
    organizationId: string,
): Promise<HttpResponse<OrganizationFinancialProfile>> => {
    try {
        const response = await http.get(`${api.v1.financial.root}/organizations/${organizationId}/profile`);

        return httpResponse<OrganizationFinancialProfile>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

// Organization Payment Timeline
export const fetchOrganizationPaymentTimeline = async (
    organizationId: string,
): Promise<
    HttpResponse<
        Array<{
            date: string;
            amount: number;
            transaction_number: string;
            payment_method: string;
            invoice_id?: string;
        }>
    >
> => {
    try {
        const response = await http.get(`${api.v1.financial.root}/organizations/${organizationId}/timeline`);

        return httpResponse(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

// Payment Reconciliation
export const reconcilePaymentWithFees = async (applicationId: string): Promise<HttpResponse<PaymentReconciliation>> => {
    try {
        const response = await http.get(`${api.v1.financial.root}/reconciliation/application/${applicationId}`);

        return httpResponse<PaymentReconciliation>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

// Payment Discrepancies
export const fetchPaymentDiscrepancies = async (
    organizationId?: string,
): Promise<HttpResponse<PaymentDiscrepancy[]>> => {
    try {
        const params = organizationId ? `?organization_id=${organizationId}` : "";
        const response = await http.get(`${api.v1.financial.root}/reconciliation/discrepancies${params}`);

        return httpResponse<PaymentDiscrepancy[]>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

// Payment Reconciliation Summary
export const fetchPaymentReconciliationSummary = async (
    organizationId?: string,
): Promise<
    HttpResponse<{
        total_discrepancies: number;
        overpayments_count: number;
        underpayments_count: number;
        total_overpayment_amount: number;
        total_underpayment_amount: number;
        requires_immediate_attention: number;
        discrepancies: PaymentDiscrepancy[];
    }>
> => {
    try {
        const params = organizationId ? `?organization_id=${organizationId}` : "";
        const response = await http.get(`${api.v1.financial.root}/reconciliation/summary${params}`);

        return httpResponse(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

// Fee Effectiveness Analysis
export const fetchFeeEffectivenessAnalysis = async (): Promise<HttpResponse<FeeEffectivenessAnalysis>> => {
    try {
        const response = await http.get(`${api.v1.financial.root}/fees/effectiveness`);

        return httpResponse<FeeEffectivenessAnalysis>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

// Simulate Fee Change Impact
export const simulateFeeChangeImpact = async (
    feeId: string,
    newAmount: number,
): Promise<
    HttpResponse<{
        fee_id: string;
        current_amount: number;
        new_amount: number;
        affected_applications_count: number;
        revenue_impact: number;
        revenue_change_percentage: number;
        organization_impact: Array<{
            application_id: string;
            organization_id: string;
            current_fee: number;
            new_fee: number;
            impact: number;
            percentage_change: number;
        }>;
        recommendation: string;
    }>
> => {
    try {
        const response = await http.get(
            `${api.v1.financial.root}/fees/${feeId}/simulate-change?new_amount=${newAmount}`,
        );

        return httpResponse(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

// Fee Utilization Report
export const fetchFeeUtilizationReport = async (): Promise<
    HttpResponse<{
        total_active_fees: number;
        total_applications_processed: number;
        total_revenue_generated: number;
        fee_utilization: Array<{
            fee_id: string;
            fee_name: string;
            fee_amount: number;
            category: string;
            usage_count: number;
            revenue_generated: number;
            is_income_based: boolean;
            income_range: string;
        }>;
        most_used_fee?: any;
        least_used_fees: any[];
        revenue_concentration: {
            top_3_concentration: number;
            top_5_concentration: number;
        };
    }>
> => {
    try {
        const response = await http.get(`${api.v1.financial.root}/fees/utilization-report`);

        return httpResponse(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

// Invoice PDF Generation
export const generateInvoicePDF = async (
    invoiceId: string,
): Promise<
    HttpResponse<{
        invoice_id: string;
        pdf_content: string;
        content_type: string;
        filename: string;
    }>
> => {
    try {
        const response = await http.get(`${api.v1.financial.invoices.action(invoiceId)}/pdf`);

        return httpResponse(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

// Reports
export const generateMonthlyRevenueReport = async (month: number, year: number): Promise<HttpResponse<any>> => {
    try {
        const response = await http.get(`${api.v1.financial.root}/reports/revenue/monthly?month=${month}&year=${year}`);

        return httpResponse(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

export const generatePaymentReconciliationReport = async (
    startDate: string,
    endDate: string,
    organizationId?: string,
): Promise<HttpResponse<any>> => {
    try {
        let url = `${api.v1.financial.root}/reports/reconciliation?start_date=${startDate}&end_date=${endDate}`;

        if (organizationId) {
            url += `&organization_id=${organizationId}`;
        }
        const response = await http.get(url);

        return httpResponse(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

export const generateOrganizationPaymentHistoryReport = async (
    organizationId: string,
    includePending: boolean = true,
): Promise<HttpResponse<any>> => {
    try {
        const response = await http.get(
            `${api.v1.financial.root}/reports/organizations/${organizationId}/payment-history?include_pending=${includePending}`,
        );

        return httpResponse(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

export const generateOutstandingReceivablesReport = async (asOfDate?: string): Promise<HttpResponse<any>> => {
    try {
        const params = asOfDate ? `?as_of_date=${asOfDate}` : "";
        const response = await http.get(`${api.v1.financial.root}/reports/outstanding-receivables${params}`);

        return httpResponse(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

// Utility functions
function getDateFromRange(range: string): string {
    const now = new Date();
    const days = parseInt(range.replace("d", ""));
    const startDate = new Date(now.getTime() - days * 24 * 60 * 60 * 1000);

    return startDate.toISOString().split("T")[0];
}

export const formatCurrency = (amount: number, currency: string = "MWK"): string => {
    return `${currency} ${amount.toLocaleString()}`;
};

export const formatPercentage = (value: number, decimals: number = 1): string => {
    return `${value.toFixed(decimals)}%`;
};

export const getStatusColor = (status: string): string => {
    const colors: Record<string, string> = {
        SUCCESS: "text-green-600",
        FAILED: "text-red-600",
        PENDING: "text-yellow-600",
        PAID: "text-green-600",
        PARTIALLY_PAID: "text-yellow-600",
        ACTIVE: "text-green-600",
        INACTIVE: "text-gray-600",
        SCHEDULED: "text-blue-600",
        EXPIRED: "text-red-600",
    };

    return colors[status] || "text-gray-600";
};

// Invoice Management API Functions
export const fetchInvoices = async (filter: Partial<InvoiceFilter> = {}): Promise<HttpResponse<InvoiceDto[]>> => {
    try {
        const queryString = jsonToQueryParams(filter);
        const url = `${api.v1.financial.invoices.root}${queryString ? `?${queryString}` : ""}`;
        const response = await http.get(url);

        return httpResponse<InvoiceDto[]>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

export const getInvoiceByApplication = async (applicationId: string): Promise<HttpResponse<InvoiceDto>> => {
    try {
        const response = await http.get(`${api.v1.financial.invoices.root}/application/${applicationId}`);

        return httpResponse<InvoiceDto>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

export const updateInvoiceStatus = async (invoiceId: string, status: string): Promise<HttpResponse<InvoiceDto>> => {
    try {
        const response = await http.put(`${api.v1.financial.invoices.action(invoiceId)}`, { status });

        return httpResponse<InvoiceDto>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

export const markInvoiceAsPaid = async (
    invoiceId: string,
    paymentModeId: string,
): Promise<HttpResponse<InvoiceDto>> => {
    try {
        const response = await http.post(`${api.v1.financial.invoices.action(invoiceId)}/mark-as-paid`, {
            payment_mode_id: paymentModeId,
        });

        return httpResponse<InvoiceDto>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

// Payment Management API Functions
export const fetchPayments = async (filter: any = {}): Promise<HttpResponse<PaymentDto[]>> => {
    try {
        const queryString = jsonToQueryParams(filter);
        const url = `${api.v1.financial.payments.root}${queryString ? `?${queryString}` : ""}`;
        const response = await http.get(url);

        return httpResponse<PaymentDto[]>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

export const getPaymentsByInvoice = async (invoiceId: string): Promise<HttpResponse<PaymentDto[]>> => {
    try {
        const response = await http.get(`${api.v1.financial.payments.root}/invoice/${invoiceId}`);

        return httpResponse<PaymentDto[]>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

export const verifyPayment = async (request: PaymentVerificationRequest): Promise<HttpResponse<PaymentDto>> => {
    try {
        const response = await http.post(`${api.v1.financial.payments.root}/verify`, request);

        return httpResponse<PaymentDto>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

export const fetchFinanceWorkflowStages = async (
    filter: WorkflowStageFilter,
): Promise<HttpResponse<FinanceWorkflowStage[]>> => {
    try {
        const queryString = jsonToQueryParams(filter);
        const url = `${api.v1.workflows.root}/stages/finance${queryString ? `?${queryString}` : ""}`;
        const response = await http.get(url);

        return httpResponse<FinanceWorkflowStage[]>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};
