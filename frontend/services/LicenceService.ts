import { AxiosProgressEvent } from "axios";

import api from "@/config/api.config";
import http from "@/config/http";
import {
    ApplicationDto,
    HttpResponse,
    LicenceDto,
    LicenceRenewalDto,
    LicenceValidityCheckDto,
    LicenceVerificationDto,
} from "@/types";
import { httpResponse } from "@/utils/common";

/**
 * Get all licences for an organization
 */
export const getOrganizationLicences = async (organizationId: string): Promise<HttpResponse<LicenceDto[]>> => {
    try {
        const url = `${api.v1.licences.organizations.licences(organizationId)}`;
        const response = await http.get(url);

        return httpResponse<LicenceDto[]>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

/**
 * Get the active licence for an organization
 */
export const getActiveLicence = async (organizationId: string): Promise<HttpResponse<LicenceDto>> => {
    try {
        const url = `${api.v1.licences.organizations.activeLicence(organizationId)}`;
        const response = await http.get(url);

        return httpResponse<LicenceDto>(response);
    } catch (e: any) {
        return httpResponse<LicenceDto>(e.response);
    }
};

/**
 * Create a licence renewal application
 */
export const createLicenceRenewal = async (
    organizationId: string,
    data: FormData,
    onUploadProgress?: (e: AxiosProgressEvent) => void,
): Promise<HttpResponse<ApplicationDto>> => {
    try {
        const url = `${api.v1.licences.organizations.renewal.create(organizationId)}`;

        const sectors = JSON.parse((data.get("sectors") as string) || "[]");
        const cleaned = [];

        for (const sector of sectors) {
            if (typeof sector === "object" && sector.id) {
                cleaned.push(sector.id);
            } else {
                cleaned.push(sector);
            }
        }

        data.delete("sectors");
        data.append("sectors", JSON.stringify(cleaned));

        const response = await http.post(url, data, { onUploadProgress });

        return httpResponse<ApplicationDto>(response);
    } catch (e: any) {
        return httpResponse<ApplicationDto>(e.response);
    }
};

/**
 * Update organization data for licence renewal
 */
export const updateOrganizationForRenewal = async (
    organizationId: string,
    formData: FormData,
    onUploadProgress?: (e: AxiosProgressEvent) => void,
): Promise<HttpResponse<ApplicationDto>> => {
    try {
        const url = `${api.v1.licences.organizations.renewal.update(organizationId)}`;
        const response = await http.put(url, formData, {
            onUploadProgress,
        });

        return httpResponse<ApplicationDto>(response);
    } catch (e: any) {
        return httpResponse<ApplicationDto>(e.response);
    }
};

/**
 * Submit licence renewal application for review
 */
export const submitLicenceRenewal = async (organizationId: string): Promise<HttpResponse<ApplicationDto>> => {
    try {
        const url = `${api.v1.licences.organizations.renewal.submit(organizationId)}`;
        const response = await http.post(url);

        return httpResponse<ApplicationDto>(response);
    } catch (e: any) {
        return httpResponse<ApplicationDto>(e.response);
    }
};

/**
 * Get the current licence renewal application
 */
export const getLicenceRenewalApplication = async (
    organizationId: string,
): Promise<HttpResponse<LicenceRenewalDto>> => {
    try {
        const url = `${api.v1.licences.organizations.renewal.get(organizationId)}`;
        const response = await http.get(url);

        return httpResponse<LicenceRenewalDto>(response);
    } catch (e: any) {
        return httpResponse<LicenceRenewalDto>(e.response);
    }
};

/**
 * Get staged licence renewal form data for organizations in RENEWAL_DRAFT or RENEWAL_IN_REVIEW status
 */
export const getStagedLicenceRenewalData = async (organizationId: string): Promise<HttpResponse<any>> => {
    try {
        const url = `${api.v1.licences.organizations.renewal.staged(organizationId)}`;
        const response = await http.get(url);

        return httpResponse<any>(response);
    } catch (e: any) {
        return httpResponse<any>(e.response);
    }
};

/**
 * Check if organization can create licence renewal
 */
export const canCreateLicenceRenewal = async (organizationId: string): Promise<HttpResponse<boolean>> => {
    try {
        const url = `${api.v1.licences.organizations.renewal.eligible(organizationId)}`;
        const response = await http.get(url);

        return httpResponse<boolean>(response);
    } catch (e: any) {
        return httpResponse<boolean>(e.response);
    }
};

/**
 * Verify a licence by licence number (authenticated)
 */
export const verifyLicence = async (licenceNumber: string): Promise<HttpResponse<LicenceVerificationDto>> => {
    try {
        const url = `${api.v1.licences.verify(licenceNumber)}`;
        const response = await http.get(url);

        return httpResponse<LicenceVerificationDto>(response);
    } catch (e: any) {
        return httpResponse<LicenceVerificationDto>(e.response);
    }
};

/**
 * Quick check if licence is valid
 */
export const checkLicenceValidity = async (licenceNumber: string): Promise<HttpResponse<LicenceValidityCheckDto>> => {
    try {
        const url = `${api.v1.licences.check(licenceNumber)}`;
        const response = await http.get(url);

        return httpResponse<LicenceValidityCheckDto>(response);
    } catch (e: any) {
        return httpResponse<LicenceValidityCheckDto>(e.response);
    }
};

/**
 * Public licence verification (no authentication required)
 */
export const publicVerifyLicence = async (licenceNumber: string): Promise<HttpResponse<LicenceVerificationDto>> => {
    try {
        const url = `${api.v1.licences.publicVerify(licenceNumber)}`;
        const response = await http.get(url);

        return httpResponse<LicenceVerificationDto>(response);
    } catch (e: any) {
        return httpResponse<LicenceVerificationDto>(e.response);
    }
};
