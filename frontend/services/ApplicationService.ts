import api from "@/config/api.config";
import http from "@/config/http";
import { ApplicationDocumentDto, CreatePermitApplicationsRequest, HttpResponse } from "@/types";
import { ApplicationDto, ApplicationStatsDto, ApplicationType } from "@/types/application.dto";
import { httpResponse } from "@/utils/common";

export const submitForReview = async (id: string): Promise<HttpResponse<ApplicationDto>> => {
    try {
        const url = api.v1.applications.submit(id);
        const response = await http.post(url, { id });

        return httpResponse<ApplicationDto>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

export const getApplicationDocuments = async (
    applicationId: string,
): Promise<HttpResponse<ApplicationDocumentDto[]>> => {
    try {
        const url = api.v1.applications.documents(applicationId);
        const response = await http.get(url);

        return httpResponse<ApplicationDocumentDto[]>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

export const getApplicationHistory = async (
    organizationId: string,
    type?: ApplicationType,
): Promise<HttpResponse<ApplicationDto[]>> => {
    try {
        let url = api.v1.applications.history(organizationId);

        if (type) {
            url += `?type=${type}`;
        }

        const response = await http.get(url);

        return httpResponse<ApplicationDto[]>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

export const getApplicationStats = async (organizationId: string): Promise<HttpResponse<ApplicationStatsDto>> => {
    try {
        const url = api.v1.applications.stats(organizationId);
        const response = await http.get(url);

        return httpResponse<ApplicationStatsDto>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

export const getApplications = async (type?: ApplicationType): Promise<HttpResponse<ApplicationDto[]>> => {
    try {
        let url = api.v1.applications.root;

        if (type) {
            url += `?type=${type}`;
        }

        const response = await http.get(url);

        return httpResponse<ApplicationDto[]>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};

export const createPermitApplications = async (
    request: CreatePermitApplicationsRequest,
): Promise<HttpResponse<ApplicationDto[]>> => {
    try {
        const formData = new FormData();

        formData.append("applicants_data", JSON.stringify(request.applicants));
        formData.append("organization_id", request.organization_id);

        request.supporting_documents.forEach((docs, index) => {
            Object.entries(docs).forEach(([key, file]) => {
                if (file) {
                    const filename = `${key}_${index}_${file.name}`;
                    formData.append("supporting_documents", new File([file], filename, { type: file.type }));
                }
            });
        });

        const url = api.v1.applications.permits.create();
        const response = await http.post(url, formData, {
            headers: {
                "Content-Type": "multipart/form-data",
            },
        });

        return httpResponse<ApplicationDto[]>(response);
    } catch (e: any) {
        return httpResponse(e.response);
    }
};
