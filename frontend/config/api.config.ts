const baseUrl = process.env.NEXT_PUBLIC_API_BASE_URL;
const bucketUrl = process.env.NEXT_PUBLIC_STORAGE_URL;

const v1 = `${baseUrl}/v1`;
const roots = {
    auth: v1 + "/auth",
    roles: v1 + "/roles",
    permissions: v1 + "/permissions",
    settings: v1 + "/settings",
    users: v1 + "/users",
    workflows: v1 + "/workflows",
    organizations: v1 + "/organizations",
    membership: v1 + "/membership",
    departments: v1 + "/departments",
    complaints: v1 + "/complaints",
    activities: v1 + "/activities",
    applications: v1 + "/applications",
    financial: v1 + "/financials",
    dashboard: v1 + "/dashboard",
    notifications: v1 + "/notifications",
    licences: v1 + "/licences",
};

const settings = {
    root: roots.settings,
    countries: roots.settings + "/countries",
    districts: {
        root: roots.settings + "/districts",
        action: (id: string) => roots.settings + "/districts/" + id,
    },
    regions: roots.settings + "/regions",
    system_configuration: roots.settings + "/system-configuration",
    loadable_items: {
        root: roots.settings + "/loadable_items",
        action: (id: string) => roots.settings + "/loadable_items/" + id,
    },
};

const management = {
    audits: {
        root: v1 + "/audits",
        actions: v1 + "/audits/actions",
    },
};

const departments = {
    root: roots.departments,
    action: (id: string) => roots.departments + "/" + id,
};

const complaints = {
    root: roots.complaints,
    action: (id: string) => roots.complaints + "/" + id,
    public: roots.complaints + "/public",
    track: (trackingCode: string) => roots.complaints + "/track/" + trackingCode,
    status: (id: string) => roots.complaints + "/" + id + "/status",
    void: (id: string) => roots.complaints + "/" + id + "/void",
    statistics: roots.complaints + "/statistics",
    trends: roots.complaints + "/trends",
    dashboard: roots.complaints + "/dashboard",
};

const activities = {
    root: roots.activities,
    action: (id: string) => roots.activities + "/" + id,
    invitations: {
        root: (activityId: string) => `${roots.activities}/${activityId}/invitations`,
        action: (activityId: string, invitationId: string) => `${roots.activities}/${activityId}/invitations/${invitationId}`,
        status: (activityId: string, invitationId: string, status: string) => `${roots.activities}/${activityId}/invitations/${invitationId}/status/${status}`,
    },
};

const applications = {
    root: roots.applications,
    action: (id: string) => roots.applications + "/" + id,
    submit: (id: string) => roots.applications + "/" + id + "/submit",
    documents: (id: string) => roots.applications + "/" + id + "/documents",
    history: (orgId: string) => roots.applications + "/history/" + orgId,
    stats: (orgId: string) => roots.applications + "/stats/" + orgId,
    permits: {
        create: () => roots.applications + "/permits",
    },
};

const organizations = {
    root: roots.organizations,
    action: (id: string) => roots.organizations + "/" + id,
    directors: {
        root: (orgId: string) => roots.organizations + "/" + orgId + "/directors",
        action: (orgId: string, directorId: string) => roots.organizations + "/" + orgId + "/directors/" + directorId,
    },
    sectors: {
        root: (orgId: string) => roots.organizations + "/" + orgId + "/sectors",
        action: (orgId: string, sectorId: string) => roots.organizations + "/" + orgId + "/sectors/" + sectorId,
    },
    locationActivities: {
        root: (orgId: string) => roots.organizations + "/" + orgId + "/location_activities",
        action: (orgId: string, activityId: string) =>
            roots.organizations + "/" + orgId + "/location_activities/" + activityId,
    },
    targetGroups: {
        root: (orgId: string) => roots.organizations + "/" + orgId + "/target_groups",
        action: (orgId: string, groupId: string) => roots.organizations + "/" + orgId + "/target_groups/" + groupId,
    },
    projects: {
        root: (orgId: string) => roots.organizations + "/" + orgId + "/projects",
        action: (orgId: string, projectId: string) => roots.organizations + "/" + orgId + "/projects/" + projectId,
    },
    donors: {
        root: (orgId: string) => roots.organizations + "/" + orgId + "/donors",
        action: (orgId: string, donorId: string) => roots.organizations + "/" + orgId + "/donors/" + donorId,
    },
    fundingSources: {
        root: (orgId: string) => roots.organizations + "/" + orgId + "/funding_sources",
        action: (orgId: string, sourceId: string) => roots.organizations + "/" + orgId + "/funding_sources/" + sourceId,
    },
    auditors: {
        root: (orgId: string) => roots.organizations + "/" + orgId + "/auditors",
        action: (orgId: string, auditorId: string) => roots.organizations + "/" + orgId + "/auditors/" + auditorId,
    },
    bankDetails: {
        root: (orgId: string) => roots.organizations + "/" + orgId + "/bank_details",
        action: (orgId: string, detailId: string) => roots.organizations + "/" + orgId + "/bank_details/" + detailId,
    },
    staff: {
        root: (orgId: string) => roots.organizations + "/" + orgId + "/staff",
        action: (orgId: string, staffId: string) => roots.organizations + "/" + orgId + "/staff/" + staffId,
    },
};

const financial = {
    root: roots.financial,
    currencies: roots.financial + "/currencies",
    fees: {
        root: roots.financial + "/fees",
        action: (id: string) => roots.financial + "/fees/" + id,
        schedule: (id: string) => roots.financial + "/fees/" + id + "/schedule",
        activate: (id: string) => roots.financial + "/fees/" + id + "/activate",
        history: (categoryId: string) => roots.financial + "/fees/history/" + categoryId,
    },
    invoices: {
        root: roots.financial + "/invoices",
        action: (id: string) => roots.financial + "/invoices/" + id,
    },
    payments: {
        root: roots.financial + "/payments",
        action: (id: string) => roots.financial + "/payments/" + id,
    },
};

const auth = {
    login: v1 + "/auth/login",
    refreshToken: v1 + "/auth/refresh-token",
    register: v1 + "/auth/register",
    logout: v1 + "/auth/logout",
    resendAccountVerification: v1 + "/auth/resend-account-verification",
    requestPasswordReset: v1 + "/auth/request-password-reset",
    resetPassword: v1 + "/auth/reset-password",
    two_factor: v1 + "/auth/2fa",
};

const templates = {
    root: v1 + "/workflows/templates",
    action: (id: string) => `${v1}/workflows/templates/${id}`,
    void: (id: string) => `${v1}/workflows/templates/${id}/void`,
    stages: {
        root: (templateId: string) => `${v1}/workflows/templates/${templateId}/stages`,
        action: (templateId: string, stageId: string) => `${v1}/workflows/templates/${templateId}/stages/${stageId}`,
        roles: {
            root: (templateId: string, stageId: string) =>
                `${v1}/workflows/templates/${templateId}/stages/${stageId}/roles`,
            action: (templateId: string, stageId: string, roleId: string) =>
                `${v1}/workflows/templates/${templateId}/stages/${stageId}/roles/${roleId}`,
        },
        triggers: {
            root: (templateId: string, stageId: string) =>
                `${v1}/workflows/templates/${templateId}/stages/${stageId}/triggers`,
            action: (templateId: string, stageId: string, triggerId: string) =>
                `${v1}/workflows/templates/${templateId}/stages/${stageId}/triggers/${triggerId}`,
        },
    },
};

const workflows = {
    root: v1 + "/workflows",
    dashboard: v1 + "/workflows/dashboard",
    stats: v1 + "/workflows/stats",
    action: (id: string) => `${v1}/workflows/${id}`,
    delete: (id: string) => `${v1}/workflows/${id}`,
    stages: {
        approve: (workflowId: string, stageId: string) => `${v1}/workflows/${workflowId}/stages/${stageId}/approve`,
        reject: (workflowId: string, stageId: string) => `${v1}/workflows/${workflowId}/stages/${stageId}/reject`,
    },
    templates,
};

const dashboard = {
    root: roots.dashboard,
    organizations: {
        statistics: roots.dashboard + "/organizations/statistics",
        recent: roots.dashboard + "/organizations/recent",
    },
};

const notifications = {
    root: roots.notifications,
    all: roots.notifications + "/all",
    markAllRead: roots.notifications + "/mark-all-read",
    unreadCount: roots.notifications + "/unread-count",
    action: (id: string) => roots.notifications + "/" + id,
    updateStatus: (id: string) => roots.notifications + "/" + id + "/status",
};

const permissions = {
    root: roots.permissions,
};

const membership = {
    root: roots.membership,
    organizations: {
        members: (organizationId: string) => `${roots.membership}/organizations/${organizationId}/members`,
        invite: (organizationId: string) => `${roots.membership}/organizations/${organizationId}/members/invite`,
        invitations: (organizationId: string) => `${roots.membership}/organizations/${organizationId}/members/invitations`,
        stats: (organizationId: string) => `${roots.membership}/organizations/${organizationId}/members/stats`,
        cancelInvitation: (organizationId: string, invitationId: string) => `${roots.membership}/organizations/${organizationId}/members/invitations/${invitationId}/cancel`,
        removeInvitation: (organizationId: string, invitationId: string) => `${roots.membership}/organizations/${organizationId}/members/invitations/${invitationId}`,
        removeMember: (organizationId: string, memberId: string) => `${roots.membership}/organizations/${organizationId}/members/${memberId}`,
        changeMemberRole: (organizationId: string, memberId: string) => `${roots.membership}/organizations/${organizationId}/members/${memberId}/role`,
    },
    invitations: {
        accept: (invitationCode: string) => `${roots.membership}/invitations/${invitationCode}/accept`,
        reject: (invitationCode: string) => `${roots.membership}/invitations/${invitationCode}/reject`,
        validate: `${roots.membership}/invitations/validate`,
        acceptWithAccount: (token: string) => `${roots.membership}/invitations/${token}/accept-with-account`,
    },
};

const licences = {
    root: roots.licences,
    organizations: {
        licences: (organizationId: string) => roots.licences + "/organizations/" + organizationId + "/licences",
        activeLicence: (organizationId: string) =>
            roots.licences + "/organizations/" + organizationId + "/licences/active",
        renewal: {
            create: (organizationId: string) => roots.licences + "/organizations/" + organizationId + "/renewal",
            update: (organizationId: string) => roots.licences + "/organizations/" + organizationId + "/renewal/update",
            submit: (organizationId: string) => roots.licences + "/organizations/" + organizationId + "/renewal/submit",
            get: (organizationId: string) => roots.licences + "/organizations/" + organizationId + "/renewal",
            eligible: (organizationId: string) =>
                roots.licences + "/organizations/" + organizationId + "/renewal/eligible",
            staged: (organizationId: string) => roots.licences + "/organizations/" + organizationId + "/renewal/staged",
        },
    },
    verify: (licenceNumber: string) => roots.licences + "/verify/" + licenceNumber,
    check: (licenceNumber: string) => roots.licences + "/check/" + licenceNumber,
    publicVerify: (licenceNumber: string) => roots.licences + "/public/verify/" + licenceNumber,
};

const api = {
    bucket: (location: string) => `${bucketUrl}/${location}`,
    v1: {
        auth,
        roles: {
            root: v1 + "/roles",
            delete: (id: string) => `${v1}/roles/${id}`,
        },
        settings,
        permissions,
        organizations,
        membership,
        departments,
        complaints,
        activities,
        financial,
        applications,
        users: {
            root: v1 + "/users",
            delete: (id: string) => `${v1}/users/${id}`,
        },
        workflows,
        dashboard,
        notifications,
        management,
        licences,
    },
};

export default api;
