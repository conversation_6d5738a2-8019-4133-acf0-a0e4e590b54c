import { create } from "zustand";
import { createJSONStorage, persist } from "zustand/middleware";

import { siteConfig } from "@/config/site";
import { AuthDto, MembershipSummaryDto } from "@/types";

interface AppState {
    session: AuthDto | null;
    isAuthenticated: boolean;
    isLoading: boolean;
    context: MembershipSummaryDto | null;

    sidebarExpanded: boolean;
    darkMode: boolean;
    currentPage: string;

    setUser: (session: AuthDto | null) => void;
    setContext: (context: MembershipSummaryDto | null) => void;
    destroySession: () => void;
    initializeAuth: () => void;
    toggleSidebar: () => void;
    setSidebarExpanded: (expanded: boolean) => void;
    toggleDarkMode: () => void;
    setCurrentPage: (page: string) => void;
}

export const useStore = create<AppState>()(
    persist(
        (set, get) => ({
            session: null,
            isAuthenticated: false,
            isLoading: true,
            context: null,

            sidebarExpanded: true,
            darkMode: false,
            currentPage: "",

            setUser: (session) =>
                set({
                    session,
                    isAuthenticated: !!session,
                    isLoading: false,
                }),

            setContext: (context: MembershipSummaryDto | null) =>
                set({
                    context,
                }),

            destroySession: () =>
                set({
                    session: null,
                    isAuthenticated: false,
                    isLoading: false,
                }),

            initializeAuth: () => {
                const state = get();

                if (state.session) {
                    set({ isAuthenticated: true, isLoading: false });
                } else {
                    set({ isAuthenticated: false, isLoading: false });
                }
            },

            toggleSidebar: () =>
                set((state) => ({
                    sidebarExpanded: !state.sidebarExpanded,
                })),

            setSidebarExpanded: (expanded) =>
                set({
                    sidebarExpanded: expanded,
                }),

            toggleDarkMode: () =>
                set((state) => ({
                    darkMode: !state.darkMode,
                })),

            setCurrentPage: (page) =>
                set(
                    {
                        currentPage: page,
                    },
                    false,
                ),
        }),
        {
            name: siteConfig.APP_STATE,
            storage: createJSONStorage(() => localStorage),
            partialize: (state) => ({
                session: state.session,
                context: state.context,
                isAuthenticated: state.isAuthenticated,
                sidebarExpanded: state.sidebarExpanded,
                darkMode: state.darkMode,
                currentPage: state.currentPage,
            }),
        },
    ),
);

export const useAuth = () => {
    const { session, context, isAuthenticated, isLoading, destroySession, setUser, setContext, initializeAuth } =
        useStore();

    return {
        session,
        context,
        isAuthenticated,
        isLoading,
        destroySession,
        setUser,
        initializeAuth,
        setContext,
    };
};

export const useUI = () => {
    const {
        sidebarExpanded,
        currentPage,
        darkMode,
        toggleSidebar,
        setSidebarExpanded,
        toggleDarkMode,
        setCurrentPage,
    } = useStore();

    return {
        sidebarExpanded,
        currentPage,
        darkMode,
        toggleSidebar,
        setSidebarExpanded,
        toggleDarkMode,
        setCurrentPage,
    };
};
