"use client";

import { useQuery } from "@tanstack/react-query";
import { ColumnDef } from "@tanstack/react-table";
import {
    ArrowUpDown,
    Calendar,
    CheckCircle,
    Clock,
    Eye,
    FileText,
    Grid3X3,
    List,
    Plus,
    User,
    UserCheck,
    XCircle,
} from "lucide-react";
import Link from "next/link";
import { useMemo, useState } from "react";
import { toast } from "sonner";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { DataTable } from "@/components/ui/data-table";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useAuth } from "@/composables/useStore";
import { getApplications } from "@/services/ApplicationService";
import { ApplicationDto } from "@/types";

type ViewMode = "table" | "cards";

const getStatusConfig = (status: string) => {
    const configs = {
        DRAFT: {
            color: "bg-yellow-100 text-yellow-800",
            label: "Draft",
            icon: <FileText className="w-4 h-4" />,
        },
        IN_REVIEW: {
            color: "bg-blue-100 text-blue-800",
            label: "In Review",
            icon: <Clock className="w-4 h-4" />,
        },
        APPROVED: {
            color: "bg-green-100 text-green-800",
            label: "Approved",
            icon: <CheckCircle className="w-4 h-4" />,
        },
        REJECTED: {
            color: "bg-red-100 text-red-800",
            label: "Rejected",
            icon: <XCircle className="w-4 h-4" />,
        },
    };

    return (
        configs[status as keyof typeof configs] || {
            color: "bg-gray-100 text-gray-800",
            label: status,
            icon: <FileText className="w-4 h-4" />,
        }
    );
};

const permitColumns: ColumnDef<ApplicationDto>[] = [
    {
        accessorKey: "code",
        header: ({ column }) => {
            return (
                <Button
                    className="h-auto p-0 hover:bg-transparent"
                    variant="ghost"
                    onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
                >
                    Application Code
                    <ArrowUpDown className="ml-2 h-4 w-4" />
                </Button>
            );
        },
        cell: ({ row }) => {
            const application = row.original;

            return (
                <div className="flex items-center gap-3">
                    <div className="p-2 rounded-full bg-blue-500/20 text-blue-600">
                        <UserCheck className="h-4 w-4" />
                    </div>
                    <div>
                        <div className="font-medium text-foreground">
                            <Link className="hover:underline" href={`/permits/${application.id}`}>
                                {application.code}
                            </Link>
                        </div>
                        <div className="text-sm text-muted-foreground">Employment Permit</div>
                    </div>
                </div>
            );
        },
    },
    {
        accessorKey: "status",
        header: "Status",
        cell: ({ row }) => {
            const status = row.getValue("status") as string;
            const config = getStatusConfig(status);

            return (
                <Badge className={`${config.color} border-none flex items-center gap-1`}>
                    {config.icon}
                    {config.label}
                </Badge>
            );
        },
    },
    {
        accessorKey: "created_at",
        header: ({ column }) => {
            return (
                <Button
                    className="h-auto p-0 hover:bg-transparent"
                    variant="ghost"
                    onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
                >
                    Submitted
                    <ArrowUpDown className="ml-2 h-4 w-4" />
                </Button>
            );
        },
        cell: ({ row }) => {
            const date = new Date(row.getValue("created_at"));

            return (
                <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4 text-muted-foreground" />
                    <span>{date.toLocaleDateString()}</span>
                </div>
            );
        },
    },
    {
        accessorKey: "creator",
        header: "Submitted By",
        cell: ({ row }) => {
            const creator = row.original.creator;

            if (!creator) return <span className="text-muted-foreground">-</span>;

            return (
                <div className="flex items-center gap-2">
                    <User className="h-4 w-4 text-muted-foreground" />
                    <span>
                        {creator.first_name} {creator.last_name}
                    </span>
                </div>
            );
        },
    },
    {
        id: "actions",
        header: "Actions",
        cell: ({ row }) => {
            const application = row.original;

            return (
                <div className="flex items-center gap-2">
                    <Link href={`/permits/${application.id}`}>
                        <Button size="sm" variant="outline">
                            <Eye className="h-4 w-4 mr-1" />
                            View
                        </Button>
                    </Link>
                </div>
            );
        },
    },
];

export default function PermitsPage() {
    if (typeof document !== "undefined") {
        document.title = "Employment Permits - myNGO";
    }

    const { isAuthenticated } = useAuth();
    const [viewMode, setViewMode] = useState<ViewMode>("table");
    const [searchQuery, setSearchQuery] = useState("");
    const [statusFilter, setStatusFilter] = useState("all");

    const {
        data: applicationsResponse,
        isLoading,
        error: httpError,
    } = useQuery({
        queryKey: ["type", "PERMIT_APPLICATION"],
        queryFn: async () => {
            const response = await getApplications("PERMIT_APPLICATION");

            if (response.success && response.data) {
                return response.data;
            }

            for (const error of response.errors) {
                toast.error(error.message);
            }

            return [];
        },
        enabled: isAuthenticated,
        staleTime: 2 * 60 * 1000,
        gcTime: 5 * 60 * 1000,
    });

    const permitApplications = applicationsResponse || [];

    const filteredApplications = useMemo(() => {
        return permitApplications.filter((app) => {
            const matchesSearch = searchQuery === "" || app.code.toLowerCase().includes(searchQuery.toLowerCase());

            const matchesStatus = statusFilter === "all" || app.status === statusFilter;

            return matchesSearch && matchesStatus;
        });
    }, [permitApplications, searchQuery, statusFilter]);

    // Calculate stats
    const stats = useMemo(() => {
        const total = permitApplications.length;
        const draft = permitApplications.filter((app) => app.status === "DRAFT").length;
        const in_review = permitApplications.filter((app) => app.status === "REVIEW").length;
        const approved = permitApplications.filter((app) => app.status === "REGISTERED").length;
        const rejected = permitApplications.filter((app) => app.status === "REJECTED").length;

        return { total, draft, in_review, approved, rejected };
    }, [permitApplications]);

    if (!isAuthenticated) {
        return (
            <div className="flex justify-center items-center h-screen">
                <div className="grid gap-3">
                    <Button>
                        <Link href="/auth/login">Please log in to view permits</Link>
                    </Button>
                </div>
            </div>
        );
    }

    if (httpError) {
        return (
            <div className="max-w-7xl mx-auto p-6 space-y-6">
                <Card>
                    <CardContent className="text-center py-12">
                        <XCircle className="h-12 w-12 mx-auto text-red-400 mb-4" />
                        <h3 className="text-lg font-semibold mb-2">Error Loading Permits</h3>
                        <p className="text-muted-foreground mb-4">
                            Something went wrong while loading permit applications
                        </p>
                        <Button onClick={() => window.location.reload()}>Try Again</Button>
                    </CardContent>
                </Card>
            </div>
        );
    }

    return (
        <div className="space-y-6">
            {/* Header */}
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div>
                    <h1 className="text-2xl font-bold text-foreground">Employment Permits</h1>
                    <p className="text-muted-foreground">Manage employment permit applications for your organization</p>
                </div>
                <div className="flex items-center gap-3">
                    <div className="flex items-center gap-1 bg-card/30 rounded-lg p-1">
                        <Button
                            size="sm"
                            variant={viewMode === "table" ? "default" : "ghost"}
                            onClick={() => setViewMode("table")}
                        >
                            <List className="h-4 w-4" />
                        </Button>
                        <Button
                            size="sm"
                            variant={viewMode === "cards" ? "default" : "ghost"}
                            onClick={() => setViewMode("cards")}
                        >
                            <Grid3X3 className="h-4 w-4" />
                        </Button>
                    </div>
                    <Link href="/permits/create">
                        <Button className="bg-blue-600 hover:bg-blue-700 text-white">
                            <Plus className="h-4 w-4 mr-2" />
                            Apply for TEP
                        </Button>
                    </Link>
                </div>
            </div>

            {/* Stats Cards */}
            <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
                <Card className="border-border/50 bg-card/30">
                    <CardContent className="p-6">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm font-medium text-muted-foreground">Total Applications</p>
                                <p className="text-2xl font-bold text-foreground">{stats.total}</p>
                            </div>
                            <div className="p-3 rounded-full bg-blue-500/20 text-blue-600">
                                <UserCheck className="w-6 h-6" />
                            </div>
                        </div>
                    </CardContent>
                </Card>

                <Card className="border-border/50 bg-card/30">
                    <CardContent className="p-6">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm font-medium text-muted-foreground">Draft</p>
                                <p className="text-2xl font-bold text-foreground">{stats.draft}</p>
                            </div>
                            <div className="p-3 rounded-full bg-yellow-500/20 text-yellow-600">
                                <FileText className="w-6 h-6" />
                            </div>
                        </div>
                    </CardContent>
                </Card>

                <Card className="border-border/50 bg-card/30">
                    <CardContent className="p-6">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm font-medium text-muted-foreground">In Review</p>
                                <p className="text-2xl font-bold text-foreground">{stats.in_review}</p>
                            </div>
                            <div className="p-3 rounded-full bg-blue-500/20 text-blue-600">
                                <Clock className="w-6 h-6" />
                            </div>
                        </div>
                    </CardContent>
                </Card>

                <Card className="border-border/50 bg-card/30">
                    <CardContent className="p-6">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm font-medium text-muted-foreground">Approved</p>
                                <p className="text-2xl font-bold text-foreground">{stats.approved}</p>
                            </div>
                            <div className="p-3 rounded-full bg-green-500/20 text-green-600">
                                <CheckCircle className="w-6 h-6" />
                            </div>
                        </div>
                    </CardContent>
                </Card>

                <Card className="border-border/50 bg-card/30">
                    <CardContent className="p-6">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm font-medium text-muted-foreground">Rejected</p>
                                <p className="text-2xl font-bold text-foreground">{stats.rejected}</p>
                            </div>
                            <div className="p-3 rounded-full bg-red-500/20 text-red-600">
                                <XCircle className="w-6 h-6" />
                            </div>
                        </div>
                    </CardContent>
                </Card>
            </div>

            {/* Filters */}
            <Card className="border-border/50 bg-card/30">
                <CardContent className="p-6">
                    <div className="flex flex-col sm:flex-row gap-4">
                        <div className="flex-1">
                            <Input
                                className="w-full"
                                placeholder="Search by application code..."
                                value={searchQuery}
                                onChange={(e) => setSearchQuery(e.target.value)}
                            />
                        </div>
                        <div className="sm:w-48">
                            <Select value={statusFilter} onValueChange={setStatusFilter}>
                                <SelectTrigger>
                                    <SelectValue placeholder="Filter by status" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="all">All Statuses</SelectItem>
                                    <SelectItem value="DRAFT">Draft</SelectItem>
                                    <SelectItem value="IN_REVIEW">In Review</SelectItem>
                                    <SelectItem value="APPROVED">Approved</SelectItem>
                                    <SelectItem value="REJECTED">Rejected</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>
                    </div>
                </CardContent>
            </Card>

            {/* Content */}
            <Card className="border-border/50 bg-card/30">
                <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                        <UserCheck className="w-5 h-5 text-primary" />
                        Employment Permit Applications
                    </CardTitle>
                    <CardDescription>
                        {filteredApplications.length} of {permitApplications.length} permit applications
                    </CardDescription>
                </CardHeader>
                <CardContent>
                    {isLoading ? (
                        <div className="flex items-center justify-center py-12">
                            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary" />
                            <span className="ml-4 text-lg text-foreground">Loading permits...</span>
                        </div>
                    ) : filteredApplications.length === 0 && permitApplications.length === 0 ? (
                        <div className="text-center py-12">
                            <UserCheck className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                            <p className="text-muted-foreground mb-4">No permit applications found</p>
                            <p className="text-sm text-muted-foreground mb-6">
                                Start by applying for employment permits for your organization&apos;s staff
                            </p>
                            <Link href="/permits/create">
                                <Button className="bg-blue-600 hover:bg-blue-700 text-white">
                                    <Plus className="w-4 h-4 mr-2" />
                                    Apply for TEP
                                </Button>
                            </Link>
                        </div>
                    ) : filteredApplications.length === 0 ? (
                        <div className="text-center py-12">
                            <UserCheck className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                            <p className="text-muted-foreground mb-4">No permits match your search criteria</p>
                            <Button
                                variant="outline"
                                onClick={() => {
                                    setSearchQuery("");
                                    setStatusFilter("all");
                                }}
                            >
                                Clear Filters
                            </Button>
                        </div>
                    ) : viewMode === "table" ? (
                        <DataTable columns={permitColumns} data={filteredApplications} loading={isLoading} />
                    ) : (
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                            {filteredApplications.map((application) => {
                                const statusConfig = getStatusConfig(application.status);

                                return (
                                    <Card
                                        key={application.id}
                                        className="border-border/30 bg-card/20 hover:bg-card/30 transition-colors"
                                    >
                                        <CardContent className="p-6">
                                            <div className="flex items-start justify-between mb-4">
                                                <div className="p-2 rounded-full bg-blue-500/20 text-blue-600">
                                                    <UserCheck className="h-5 w-5" />
                                                </div>
                                                <Badge
                                                    className={`${statusConfig.color} border-none flex items-center gap-1`}
                                                >
                                                    {statusConfig.icon}
                                                    {statusConfig.label}
                                                </Badge>
                                            </div>

                                            <div className="space-y-2 mb-4">
                                                <h3 className="font-semibold text-foreground">{application.code}</h3>
                                                <p className="text-sm text-muted-foreground">
                                                    Employment Permit Application
                                                </p>
                                            </div>

                                            <div className="space-y-2 mb-4 text-sm text-muted-foreground">
                                                <div className="flex items-center gap-2">
                                                    <Calendar className="w-4 h-4" />
                                                    <span>
                                                        Submitted:{" "}
                                                        {new Date(application.created_at).toLocaleDateString()}
                                                    </span>
                                                </div>
                                                {application.creator && (
                                                    <div className="flex items-center gap-2">
                                                        <User className="w-4 h-4" />
                                                        <span>
                                                            By: {application.creator.first_name}{" "}
                                                            {application.creator.last_name}
                                                        </span>
                                                    </div>
                                                )}
                                            </div>

                                            <Link href={`/permits/${application.id}`}>
                                                <Button className="w-full" size="sm">
                                                    <Eye className="w-4 h-4 mr-2" />
                                                    View Details
                                                </Button>
                                            </Link>
                                        </CardContent>
                                    </Card>
                                );
                            })}
                        </div>
                    )}
                </CardContent>
            </Card>
        </div>
    );
}
