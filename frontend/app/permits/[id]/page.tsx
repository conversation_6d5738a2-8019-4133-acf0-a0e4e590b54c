"use client";

import { useQuery } from "@tanstack/react-query";
import {
    AlertCircle,
    ArrowLeft,
    Calendar,
    CheckCircle,
    Clock,
    Download,
    Eye,
    FileText,
    Globe,
    MapPin,
    User,
    UserCheck,
} from "lucide-react";
import { use } from "react";
import { toast } from "sonner";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useAuth } from "@/composables/useStore";
import { getApplicationDocuments, getApplicationHistory } from "@/services/ApplicationService";
import { ApplicationDto, ApplicationDocumentDto } from "@/types";
import Link from "next/link";

const getStatusConfig = (status: string) => {
    switch (status) {
        case "DRAFT":
            return {
                color: "bg-yellow-100 text-yellow-800",
                borderColor: "border-yellow-200",
                bgColor: "bg-yellow-500/20",
                iconColor: "text-yellow-600 dark:text-yellow-400",
                icon: <FileText className="w-4 h-4" />,
                label: "Draft",
                description: "Application is being prepared",
            };
        case "IN_REVIEW":
            return {
                color: "bg-blue-100 text-blue-800",
                borderColor: "border-blue-200",
                bgColor: "bg-blue-500/20",
                iconColor: "text-blue-600 dark:text-blue-400",
                icon: <Clock className="w-4 h-4" />,
                label: "In Review",
                description: "Application is being reviewed by authorities",
            };
        case "APPROVED":
            return {
                color: "bg-green-100 text-green-800",
                borderColor: "border-green-200",
                bgColor: "bg-green-500/20",
                iconColor: "text-green-600 dark:text-green-400",
                icon: <CheckCircle className="w-4 h-4" />,
                label: "Approved",
                description: "Application has been approved",
            };
        case "REJECTED":
            return {
                color: "bg-red-100 text-red-800",
                borderColor: "border-red-200",
                bgColor: "bg-red-500/20",
                iconColor: "text-red-600 dark:text-red-400",
                icon: <AlertCircle className="w-4 h-4" />,
                label: "Rejected",
                description: "Application has been rejected",
            };
        default:
            return {
                color: "bg-gray-100 text-gray-800",
                borderColor: "border-gray-200",
                bgColor: "bg-gray-500/20",
                iconColor: "text-gray-600 dark:text-gray-400",
                icon: <FileText className="w-4 h-4" />,
                label: status,
                description: "Unknown status",
            };
    }
};

export default function PermitApplicationDetails({ params }: { params: Promise<{ id: string }> }) {
    const { id } = use(params);
    const { isAuthenticated, user } = useAuth();

    if (typeof document !== "undefined") {
        document.title = "Permit Application Details - myNGO";
    }

    // Get all applications and filter for this permit application
    const { data: applicationsResponse, isLoading: applicationsLoading } = useQuery({
        queryKey: ["organization-applications", user?.selected_organization?.id],
        queryFn: async () => {
            if (!user?.selected_organization?.id) return [];
            
            const response = await getApplicationHistory(user.selected_organization.id);
            if (response.success && response.data) {
                // Filter for permit applications and find the specific one
                const permitApplications = response.data.filter(app => app.type === "PERMIT_APPLICATION");
                return permitApplications.find(app => app.id === id) || null;
            }
            return null;
        },
        enabled: !!id && !!user?.selected_organization?.id && isAuthenticated,
    });

    const { data: documentsResponse } = useQuery({
        queryKey: ["application-documents", id],
        queryFn: async () => {
            const response = await getApplicationDocuments(id);
            if (response.success && response.data) {
                return response.data;
            }
            return [];
        },
        enabled: !!id && isAuthenticated,
    });

    const application: ApplicationDto | null = applicationsResponse || null;
    const documents: ApplicationDocumentDto[] = documentsResponse || [];

    if (!isAuthenticated) {
        return (
            <div className="flex justify-center items-center h-screen">
                <div className="grid gap-3">
                    <Button>
                        <ArrowLeft />
                        <Link href={`/organizations/${user?.selected_organization?.id}/applications`}>
                            Back to applications
                        </Link>
                    </Button>
                </div>
            </div>
        );
    }

    if (applicationsLoading) {
        return (
            <div className="space-y-6">
                <div className="flex items-center justify-center py-16">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary" />
                    <span className="ml-4 text-lg text-foreground">Loading permit application...</span>
                </div>
            </div>
        );
    }

    if (!application) {
        return (
            <div className="space-y-6">
                <div className="flex items-center gap-4">
                    <Link href={`/organizations/${user?.selected_organization?.id}/applications`}>
                        <Button variant="outline" size="sm">
                            <ArrowLeft className="w-4 h-4 mr-2" />
                            Back to Applications
                        </Button>
                    </Link>
                    <div>
                        <h1 className="text-2xl font-bold text-foreground">Permit Application Not Found</h1>
                        <p className="text-muted-foreground">The requested permit application could not be found</p>
                    </div>
                </div>
            </div>
        );
    }

    const statusConfig = getStatusConfig(application.status);

    return (
        <div className="space-y-6">
            {/* Header */}
            <div className="flex items-center gap-4">
                <Link href={`/organizations/${user?.selected_organization?.id}/applications`}>
                    <Button variant="outline" size="sm">
                        <ArrowLeft className="w-4 h-4 mr-2" />
                        Back to Applications
                    </Button>
                </Link>
                <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                        <h1 className="text-2xl font-bold text-foreground">Employment Permit Application</h1>
                        <Badge className={`${statusConfig.color} border-none`}>
                            {statusConfig.icon}
                            <span className="ml-1">{statusConfig.label}</span>
                        </Badge>
                    </div>
                    <p className="text-muted-foreground">Application Code: {application.code}</p>
                </div>
                {application.status === "APPROVED" && (
                    <Button className="bg-green-600 hover:bg-green-700 text-white">
                        <Download className="w-4 h-4 mr-2" />
                        Download Permit
                    </Button>
                )}
            </div>

            <Tabs defaultValue="overview" className="w-full">
                <TabsList className="grid w-full grid-cols-3">
                    <TabsTrigger value="overview">Overview</TabsTrigger>
                    <TabsTrigger value="documents">Documents</TabsTrigger>
                    <TabsTrigger value="workflow">Workflow</TabsTrigger>
                </TabsList>

                <TabsContent value="overview" className="space-y-6">
                    {/* Application Status */}
                    <Card className="border-border/50 bg-card/30">
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                {statusConfig.icon}
                                Application Status
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="flex items-center gap-4">
                                <div className={`p-3 rounded-full ${statusConfig.bgColor}`}>
                                    <div className={statusConfig.iconColor}>
                                        {statusConfig.icon}
                                    </div>
                                </div>
                                <div>
                                    <h3 className="font-semibold text-foreground">{statusConfig.label}</h3>
                                    <p className="text-sm text-muted-foreground">{statusConfig.description}</p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Application Details */}
                    <Card className="border-border/50 bg-card/30">
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <FileText className="w-5 h-5 text-primary" />
                                Application Information
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div className="space-y-2">
                                    <Label className="text-sm font-medium text-foreground">Application Code</Label>
                                    <p className="text-sm text-muted-foreground">{application.code}</p>
                                </div>
                                <div className="space-y-2">
                                    <Label className="text-sm font-medium text-foreground">Application Type</Label>
                                    <p className="text-sm text-muted-foreground">Employment Permit Application</p>
                                </div>
                                <div className="space-y-2">
                                    <Label className="text-sm font-medium text-foreground">Submitted Date</Label>
                                    <p className="text-sm text-muted-foreground">
                                        {new Date(application.created_at).toLocaleDateString()}
                                    </p>
                                </div>
                                <div className="space-y-2">
                                    <Label className="text-sm font-medium text-foreground">Last Updated</Label>
                                    <p className="text-sm text-muted-foreground">
                                        {new Date(application.updated_at).toLocaleDateString()}
                                    </p>
                                </div>
                                {application.creator && (
                                    <div className="space-y-2">
                                        <Label className="text-sm font-medium text-foreground">Submitted By</Label>
                                        <p className="text-sm text-muted-foreground">
                                            {application.creator.first_name} {application.creator.last_name}
                                        </p>
                                    </div>
                                )}
                            </div>
                        </CardContent>
                    </Card>

                    {/* Organization Information */}
                    <Card className="border-border/50 bg-card/30">
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <UserCheck className="w-5 h-5 text-primary" />
                                Organization Information
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div className="space-y-2">
                                    <Label className="text-sm font-medium text-foreground">Organization Name</Label>
                                    <p className="text-sm text-muted-foreground">
                                        {user?.selected_organization?.name || "Not specified"}
                                    </p>
                                </div>
                                <div className="space-y-2">
                                    <Label className="text-sm font-medium text-foreground">Registration Number</Label>
                                    <p className="text-sm text-muted-foreground">
                                        {user?.selected_organization?.registration_number || "Not specified"}
                                    </p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                </TabsContent>

                <TabsContent value="documents" className="space-y-6">
                    <Card className="border-border/50 bg-card/30">
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <FileText className="w-5 h-5 text-primary" />
                                Supporting Documents
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            {documents.length === 0 ? (
                                <div className="text-center py-8">
                                    <FileText className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                                    <p className="text-muted-foreground mb-4">No documents found</p>
                                    <p className="text-sm text-muted-foreground">
                                        Documents will appear here once uploaded
                                    </p>
                                </div>
                            ) : (
                                <div className="space-y-4">
                                    {documents.map((document) => (
                                        <Card
                                            key={document.id}
                                            className="border-border/30 bg-card/20"
                                        >
                                            <CardContent className="p-4">
                                                <div className="flex items-center justify-between">
                                                    <div className="flex items-center gap-3">
                                                        <div className="p-2 rounded-md bg-blue-500/20">
                                                            <FileText className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                                                        </div>
                                                        <div>
                                                            <h4 className="font-medium text-foreground">
                                                                {document.document?.filename || "Document"}
                                                            </h4>
                                                            <p className="text-sm text-muted-foreground">
                                                                Uploaded on{" "}
                                                                {new Date(document.created_at).toLocaleDateString()}
                                                            </p>
                                                        </div>
                                                    </div>
                                                    <Button size="sm" variant="outline">
                                                        <Eye className="w-4 h-4 mr-2" />
                                                        View
                                                    </Button>
                                                </div>
                                            </CardContent>
                                        </Card>
                                    ))}
                                </div>
                            )}
                        </CardContent>
                    </Card>
                </TabsContent>

                <TabsContent value="workflow" className="space-y-6">
                    <Card className="border-border/50 bg-card/30">
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Clock className="w-5 h-5 text-primary" />
                                Application Workflow
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="text-center py-8">
                                <Clock className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                                <p className="text-muted-foreground mb-4">Workflow information not available</p>
                                <p className="text-sm text-muted-foreground">
                                    Workflow details will be displayed here once the application enters the review process
                                </p>
                            </div>
                        </CardContent>
                    </Card>
                </TabsContent>
            </Tabs>
        </div>
    );
}

function Label({ children, className, ...props }: { children: React.ReactNode; className?: string; [key: string]: any }) {
    return (
        <label className={`block text-sm font-medium ${className}`} {...props}>
            {children}
        </label>
    );
}