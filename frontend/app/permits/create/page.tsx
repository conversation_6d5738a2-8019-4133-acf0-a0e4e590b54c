"use client";

import { useMutation } from "@tanstack/react-query";
import { Plus, Trash2, User<PERSON>he<PERSON>, Users } from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { toast } from "sonner";

import { ContextSetter } from "@/components/auth/context-setter";
import CountryInput from "@/components/inputs/country";
import { ManagerInput } from "@/components/inputs/manager";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { DatePicker } from "@/components/ui/date-picker";
import { FileUpload } from "@/components/ui/file-upload";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useAuth } from "@/composables/useStore";
import { createPermitApplications } from "@/services/ApplicationService";
import { CreatePermitApplicationsRequest, PermitApplicantData } from "@/types";

interface ApplicantFormData extends PermitApplicantData {
    application_letter?: File[];
    passport_picture?: File[];
}

export default function CreatePermitApplication() {
    const router = useRouter();
    const { context } = useAuth();

    if (typeof document !== "undefined") {
        document.title = "Apply for TEP - myNGO";
    }

    const [applicants, setApplicants] = useState<ApplicantFormData[]>([
        {
            full_name: "",
            nationality_id: "",
            passport_number: "",
            position: "",
            department: "",
            employment_start_date: "",
            employment_end_date: "",
        },
    ]);

    const createPermitMutation = useMutation({
        mutationFn: createPermitApplications,
        onSuccess: (response) => {
            if (response.success && response.data) {
                toast.success(`Successfully created ${response.data.length} permit application(s)`);
                router.push(`/organizations/${context?.organization.id}/applications`);
            }

            for (const error of response.errors) {
                toast.error(error.message);
            }
        },
    });

    const handleAddApplicant = () => {
        setApplicants([
            ...applicants,
            {
                full_name: "",
                nationality_id: "",
                passport_number: "",
                position: "",
                department: "",
                employment_start_date: "",
                employment_end_date: "",
            },
        ]);
    };

    const handleRemoveApplicant = (index: number) => {
        if (applicants.length > 1) {
            const newApplicants = applicants.filter((_, i) => i !== index);

            setApplicants(newApplicants);
        }
    };

    const handleApplicantChange = (index: number, field: string, value: any) => {
        const newApplicants = [...applicants];

        newApplicants[index] = { ...newApplicants[index], [field]: value };
        setApplicants(newApplicants);
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();

        if (!context?.organization?.id) {
            toast.error("Please select an organization first");

            return;
        }

        for (const applicant of applicants) {
            if (
                !applicant.full_name ||
                !applicant.nationality_id ||
                !applicant.passport_number ||
                !applicant.position
            ) {
                toast.error("Please fill in all required fields for all applicants");

                return;
            }
        }

        const supporting_documents: Record<string, File>[] = applicants.map((applicant) => {
            const docs: Record<string, File> = {};

            if (applicant.application_letter && applicant.application_letter[0]) {
                docs.application_letter = applicant.application_letter[0];
            }
            if (applicant.passport_picture && applicant.passport_picture[0]) {
                docs.passport_picture = applicant.passport_picture[0];
            }

            return docs;
        });

        const cleanApplicants: PermitApplicantData[] = applicants.map(
            ({ application_letter, passport_picture, ...rest }) => rest,
        );

        const request: CreatePermitApplicationsRequest = {
            organization_id: context.organization.id,
            applicants: cleanApplicants,
            supporting_documents,
        };

        createPermitMutation.mutate(request);
    };

    if (!context) {
        return <ContextSetter />;
    }

    return (
        <div className="space-y-6 mt-3">
            {/* Header */}
            <div className="flex items-center justify-between">
                <div className="flex items-left gap-4">
                    <div>
                        <h1 className="text-2xl font-bold text-foreground">Apply for Temporary Employment Permit</h1>
                        <p className="text-muted-foreground">
                            Submit applications for employment permits for your organization&apos;s staff
                        </p>
                    </div>
                </div>
            </div>

            <form className="space-y-6" onSubmit={handleSubmit}>
                {/* Organization Info */}
                <Card className="border-border/50 bg-card/30">
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <Users className="w-5 h-5 text-primary" />
                            Organization Information
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div className="space-y-2">
                                <ManagerInput
                                    isDisabled
                                    label="Organization"
                                    placeholder="Select organization"
                                    type="organization"
                                    value={context?.organization?.id}
                                />
                            </div>
                            <div>
                                <Label className="text-sm font-medium text-foreground">Registration Number</Label>
                                <p className="text-sm text-muted-foreground mt-1">
                                    {context?.organization?.registration_number || "Not specified"}
                                </p>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                {/* Applicants */}
                <Card className="border-border/50 bg-card/30">
                    <CardHeader>
                        <div className="flex items-center justify-between">
                            <CardTitle className="flex items-center gap-2">
                                <UserCheck className="w-5 h-5 text-primary" />
                                Employment Permit Applicants
                                <Badge className="bg-orange-100 text-orange-800">{applicants.length}</Badge>
                            </CardTitle>
                            <Button size="sm" type="button" onClick={handleAddApplicant}>
                                <Plus className="w-4 h-4 mr-2" />
                                Add Applicant
                            </Button>
                        </div>
                    </CardHeader>
                    <CardContent className="space-y-6">
                        {applicants.map((applicant, index) => (
                            <Card key={index} className="border-border/30 bg-card/20">
                                <CardHeader>
                                    <div className="flex items-center justify-between">
                                        <CardTitle className="text-lg">Applicant {index + 1}</CardTitle>
                                        {applicants.length > 1 && (
                                            <Button
                                                size="sm"
                                                type="button"
                                                variant="outline"
                                                onClick={() => handleRemoveApplicant(index)}
                                            >
                                                <Trash2 className="w-4 h-4" />
                                            </Button>
                                        )}
                                    </div>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    {/* Personal Information */}
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <Input
                                            required
                                            id={`full_name_${index}`}
                                            label="Full Name *"
                                            placeholder="Enter full name"
                                            value={applicant.full_name}
                                            onChange={(e) => handleApplicantChange(index, "full_name", e.target.value)}
                                        />
                                        <CountryInput
                                            isRequired
                                            label="Nationality"
                                            placeholder="Select nationality"
                                            value={applicant.nationality_id}
                                            onSelectionChange={(value) =>
                                                handleApplicantChange(index, "nationality_id", value)
                                            }
                                        />
                                    </div>

                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <Input
                                            required
                                            id={`passport_${index}`}
                                            label="Passport Number *"
                                            placeholder="Enter passport number"
                                            value={applicant.passport_number}
                                            onChange={(e) =>
                                                handleApplicantChange(index, "passport_number", e.target.value)
                                            }
                                        />
                                        <Input
                                            required
                                            id={`position_${index}`}
                                            label="Position *"
                                            placeholder="Enter job position"
                                            value={applicant.position}
                                            onChange={(e) => handleApplicantChange(index, "position", e.target.value)}
                                        />
                                    </div>

                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <Input
                                            id={`department_${index}`}
                                            label="Department"
                                            placeholder="Enter department"
                                            value={applicant.department}
                                            onChange={(e) => handleApplicantChange(index, "department", e.target.value)}
                                        />
                                        <DatePicker
                                            id={`start_date_${index}`}
                                            label="Employment Start Date"
                                            placeholder="Select start date"
                                            value={applicant.employment_start_date}
                                            onChange={(date) =>
                                                handleApplicantChange(index, "employment_start_date", date || "")
                                            }
                                        />
                                    </div>

                                    <DatePicker
                                        id={`end_date_${index}`}
                                        label="Employment End Date"
                                        placeholder="Select end date"
                                        value={applicant.employment_end_date}
                                        onChange={(date) =>
                                            handleApplicantChange(index, "employment_end_date", date || "")
                                        }
                                    />

                                    {/* Supporting Documents */}
                                    <div className="space-y-4 pt-4 border-t border-border/30">
                                        <Label className="text-sm font-medium text-foreground">
                                            Supporting Documents
                                        </Label>
                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                            <FileUpload
                                                label="Application Letter"
                                                acceptedFileTypes={[".pdf", ".doc", ".docx"]}
                                                allowMultiple={false}
                                                maxFiles={1}
                                                maxSize={5 * 1024 * 1024}
                                                files={applicant.application_letter || []}
                                                onFilesChange={(files) =>
                                                    handleApplicantChange(index, "application_letter", files)
                                                }
                                                showPreview
                                            />
                                            <FileUpload
                                                label="Passport Picture"
                                                acceptedFileTypes={["image/*"]}
                                                allowMultiple={false}
                                                maxFiles={1}
                                                maxSize={2 * 1024 * 1024}
                                                files={applicant.passport_picture || []}
                                                onFilesChange={(files) =>
                                                    handleApplicantChange(index, "passport_picture", files)
                                                }
                                                showPreview
                                            />
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>
                        ))}
                    </CardContent>
                </Card>

                {/* Submit Button */}
                <div className="flex items-center justify-end gap-4">
                    <Link href={`/organizations/${context?.organization?.id}/applications`}>
                        <Button type="button" variant="outline">
                            Cancel
                        </Button>
                    </Link>
                    <Button
                        className="bg-orange-600 hover:bg-orange-700 text-white"
                        disabled={createPermitMutation.isPending}
                        type="submit"
                    >
                        {createPermitMutation.isPending ? (
                            <>
                                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                                Creating Applications...
                            </>
                        ) : (
                            <>
                                <UserCheck className="w-4 h-4 mr-2" />
                                Submit {applicants.length} Application{applicants.length !== 1 ? "s" : ""}
                            </>
                        )}
                    </Button>
                </div>
            </form>
        </div>
    );
}
