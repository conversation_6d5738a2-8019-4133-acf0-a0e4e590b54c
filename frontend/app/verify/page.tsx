"use client";

import { useMutation } from "@tanstack/react-query";
import {
    AlertCircle,
    Award,
    Building2,
    Calendar,
    CheckCircle,
    Copy,
    ExternalLink,
    FileText,
    Hash,
    Search,
    Shield,
    XCircle,
} from "lucide-react";
import { useRouter, useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";
import { toast } from "sonner";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select } from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import * as licenceService from "@/services/LicenceService";
import * as organizationService from "@/services/OrganizationService";

type VerificationType = "licence" | "ngo" | "cert";

export default function VerificationPage() {
    if (typeof document !== "undefined") {
        document.title = "Verification - myNGO";
    }

    const router = useRouter();
    const searchParams = useSearchParams();
    const [inputNumber, setInputNumber] = useState("");
    const [verificationType, setVerificationType] = useState<VerificationType>("licence");
    const [verificationResult, setVerificationResult] = useState<any>(null);
    const [hasUrlQuery, setHasUrlQuery] = useState(false);

    // Get verification query from URL query string
    useEffect(() => {
        const licenceQuery = searchParams.get("licence");
        const ngoQuery = searchParams.get("ngo");
        const certQuery = searchParams.get("cert");

        if (licenceQuery) {
            setInputNumber(licenceQuery);
            setVerificationType("licence");
            setHasUrlQuery(true);
            verifyMutation.mutate({ number: licenceQuery, type: "licence" });
        } else if (ngoQuery) {
            setInputNumber(ngoQuery);
            setVerificationType("ngo");
            setHasUrlQuery(true);
            verifyMutation.mutate({ number: ngoQuery, type: "ngo" });
        } else if (certQuery) {
            setInputNumber(certQuery);
            setVerificationType("cert");
            setHasUrlQuery(true);
            verifyMutation.mutate({ number: certQuery, type: "cert" });
        } else {
            setHasUrlQuery(false);
        }
    }, [searchParams]);

    const verifyMutation = useMutation({
        mutationFn: async ({ number, type }: { number: string; type: VerificationType }) => {
            switch (type) {
                case "licence":
                    return licenceService.publicVerifyLicence(number);
                case "cert":
                    return licenceService.publicVerifyLicence(number);
                case "ngo":
                    // For organization verification, use the new verification endpoint
                    return organizationService.verifyOrganization(number);
                default:
                    throw new Error("Invalid verification type");
            }
        },
        onSuccess: (response, variables) => {
            if (response.success && response.data) {
                setVerificationResult({
                    ...response.data,
                    verificationType: variables.type,
                });

                const url = new URL(window.location.href);

                for (const key of Array.from(url.searchParams.keys())) {
                    url.searchParams.delete(key);
                }
                url.searchParams.set(variables.type, variables.number);
                router.replace(url.pathname + url.search, { scroll: false });
            } else {
                setVerificationResult({
                    is_valid: false,
                    message: `Failed to verify ${variables.type}`,
                    verificationType: variables.type,
                    [variables.type === "ngo" ? "registration_number" : "licence_number"]: variables.number,
                });
            }
        },
        onError: (error: any) => {
            const errorMessage =
                error?.response?.data?.message ||
                error?.message ||
                `${
                    variables.type === "ngo" ? "Organization" : variables.type === "cert" ? "Certificate" : "Licence"
                } not found`;

            setVerificationResult({
                is_valid: false,
                message: errorMessage,
                verificationType: variables.type,
                [variables.type === "ngo" ? "registration_number" : "licence_number"]: variables.number,
            });
        },
    });

    const handleVerify = () => {
        if (!inputNumber.trim()) {
            const typeLabel =
                verificationType === "ngo"
                    ? "organization number"
                    : verificationType === "cert"
                        ? "certificate number"
                        : "licence number";

            // Show validation error in verification result instead of toast
            setVerificationResult({
                is_valid: false,
                message: `Please enter a ${typeLabel}`,
                verificationType: verificationType,
                [verificationType === "ngo" ? "registration_number" : "licence_number"]: "",
            });

            return;
        }
        verifyMutation.mutate({ number: inputNumber.trim(), type: verificationType });
    };

    const handleCopyToClipboard = (text: string) => {
        navigator.clipboard.writeText(text);
        toast.success("Copied to clipboard");
    };

    const handleShareUrl = () => {
        const url = new URL(window.location.href);

        // Clear all existing search params
        for (const key of Array.from(url.searchParams.keys())) {
            url.searchParams.delete(key);
        }
        url.searchParams.set(verificationType, inputNumber);
        navigator.clipboard.writeText(url.toString());
        toast.success("Verification URL copied to clipboard");
    };

    const getStatusIcon = (isValid: boolean) => {
        return isValid ? (
            <CheckCircle className="w-6 h-6 text-green-600" />
        ) : (
            <XCircle className="w-6 h-6 text-red-600" />
        );
    };

    const getStatusBadge = (isValid: boolean) => {
        return isValid ? (
            <Badge className="bg-green-100 text-green-800 border-green-200">
                <CheckCircle className="w-3 h-3 mr-1" />
                Valid
            </Badge>
        ) : (
            <Badge className="bg-red-100 text-red-800 border-red-200">
                <XCircle className="w-3 h-3 mr-1" />
                Invalid
            </Badge>
        );
    };

    return (
        <div className="min-h-screen bg-background">
            {/* Header */}
            <div className="border-b bg-card">
                <div className="max-w-4xl mx-auto px-6 py-4">
                    <div className="flex items-center gap-3">
                        <div className="p-2 rounded-lg bg-primary/10 text-primary">
                            <Shield className="w-6 h-6" />
                        </div>
                        <div>
                            <h1 className="text-xl font-semibold text-foreground">NGO Licence Verification</h1>
                            <p className="text-sm text-muted-foreground">
                                Verify the authenticity of NGO certificates and licences
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <div className="max-w-4xl mx-auto px-6 py-8 space-y-8">
                {/* Search Section */}
                <Card className="border-border/50 bg-card/50 backdrop-blur-sm">
                    <CardHeader className="text-center pb-6">
                        <div className="mx-auto p-4 rounded-full bg-primary/10 text-primary w-fit mb-4">
                            <FileText className="w-8 h-8" />
                        </div>
                        <CardTitle className="text-2xl text-foreground">NGO Verification System</CardTitle>
                        <CardDescription className="text-muted-foreground max-w-md mx-auto">
                            Verify NGO organizations, certificates, and licences by entering the relevant number
                        </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-6">
                        {!hasUrlQuery && (
                            <div className="space-y-2">
                                <Select
                                    label="Select verification type"
                                    options={[
                                        { label: "Licence", value: "licence" },
                                        { label: "Certificate", value: "cert" },
                                        { label: "NGO", value: "ngo" },
                                    ]}
                                    value={verificationType}
                                    onValueChange={(value: VerificationType) => setVerificationType(value)}
                                />
                            </div>
                        )}

                        <div className="space-y-2">
                            <Label className="text-sm font-medium text-foreground" htmlFor="input-number">
                                {verificationType === "ngo"
                                    ? "Organization Number"
                                    : verificationType === "cert"
                                        ? "Certificate Number"
                                        : "Licence Number"}
                            </Label>
                            <div className="flex gap-3">
                                <div className="relative flex-1">
                                    <Hash className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                                    <Input
                                        className="pl-10 h-12 text-base"
                                        id="input-number"
                                        placeholder={
                                            verificationType === "ngo"
                                                ? "Enter organization number (e.g., NGO/01/2015/05)"
                                                : verificationType === "cert"
                                                    ? "Enter certificate number (e.g., CERT2024ABC123)"
                                                    : "Enter licence number (e.g., LIC2024XYZ789)"
                                        }
                                        value={inputNumber}
                                        onChange={(e) => setInputNumber(e.target.value)}
                                        onKeyDown={(e) => e.key === "Enter" && handleVerify()}
                                    />
                                </div>
                                <Button
                                    className="h-12 px-8"
                                    disabled={verifyMutation.isPending}
                                    onClick={handleVerify}
                                >
                                    {verifyMutation.isPending ? (
                                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary-foreground mr-2" />
                                    ) : (
                                        <Search className="w-4 h-4 mr-2" />
                                    )}
                                    Verify
                                </Button>
                            </div>
                        </div>

                        {/* Example Format */}
                        <div className="text-center text-sm text-muted-foreground">
                            <p>
                                {verificationType === "ngo"
                                    ? "Example: NGO/01/2015/05 (Organization registration number)"
                                    : verificationType === "cert"
                                        ? "Example: CERT2024ABC123 (Certificate number)"
                                        : "Example: LIC2024XYZ789 (Licence number)"}
                            </p>
                        </div>
                    </CardContent>
                </Card>

                {/* Verification Result */}
                {verificationResult && (
                    <Card className="border-border/50 bg-card/50 backdrop-blur-sm">
                        <CardHeader>
                            <div className="flex items-center justify-between">
                                <div className="flex items-center gap-3">
                                    {getStatusIcon(
                                        verificationResult.is_valid ||
                                            (verificationResult.verificationType === "ngo" && verificationResult.name),
                                    )}
                                    <div>
                                        <CardTitle className="text-xl">
                                            {verificationType === "ngo"
                                                ? "Organization"
                                                : verificationType === "cert"
                                                    ? "Certificate"
                                                    : "Licence"}{" "}
                                            Verification Result
                                        </CardTitle>
                                        <CardDescription>
                                            {verificationResult.message ||
                                                (verificationResult.verificationType === "ngo" &&
                                                verificationResult.name
                                                    ? "Organization found and verified"
                                                    : "Verification completed")}
                                        </CardDescription>
                                    </div>
                                </div>
                                {getStatusBadge(
                                    verificationResult.is_valid ||
                                        (verificationResult.verificationType === "ngo" && verificationResult.name),
                                )}
                            </div>
                        </CardHeader>
                        <CardContent className="space-y-6">
                            {/* Basic Info */}
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div className="space-y-3">
                                    <div className="flex items-center gap-2 text-sm font-medium text-foreground">
                                        <Hash className="w-4 h-4" />
                                        {verificationType === "ngo"
                                            ? "Organization Number"
                                            : verificationType === "cert"
                                                ? "Certificate Number"
                                                : "Licence Number"}
                                    </div>
                                    <div className="flex items-center gap-2">
                                        <code className="px-3 py-2 bg-muted rounded-lg font-mono text-sm flex-1">
                                            {verificationResult.licence_number ||
                                                verificationResult.registration_number ||
                                                inputNumber}
                                        </code>
                                        <Button
                                            size="sm"
                                            variant="outline"
                                            onClick={() =>
                                                handleCopyToClipboard(
                                                    verificationResult.licence_number ||
                                                        verificationResult.registration_number ||
                                                        inputNumber,
                                                )
                                            }
                                        >
                                            <Copy className="w-3 h-3" />
                                        </Button>
                                    </div>
                                </div>

                                {(verificationResult.organization_name || verificationResult.name) && (
                                    <div className="space-y-3">
                                        <div className="flex items-center gap-2 text-sm font-medium text-foreground">
                                            <Building2 className="w-4 h-4" />
                                            Organization
                                        </div>
                                        <div className="px-3 py-2 bg-muted/50 rounded-lg">
                                            <p className="font-medium text-foreground">
                                                {verificationResult.organization_name || verificationResult.name}
                                            </p>
                                            {verificationResult.abbreviation && (
                                                <p className="text-sm text-muted-foreground">
                                                    ({verificationResult.abbreviation})
                                                </p>
                                            )}
                                        </div>
                                    </div>
                                )}
                            </div>

                            {/* Detailed Information */}
                            {verificationResult.is_valid && verificationResult.licence && (
                                <>
                                    <Separator />
                                    <div className="space-y-4">
                                        <h3 className="text-lg font-semibold text-foreground flex items-center gap-2">
                                            <FileText className="w-5 h-5" />
                                            Licence Details
                                        </h3>
                                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                            <div className="p-4 rounded-lg bg-primary/10 border border-primary/20">
                                                <div className="flex items-center gap-2 text-primary mb-2">
                                                    <Award className="w-4 h-4" />
                                                    <span className="text-sm font-medium">Type</span>
                                                </div>
                                                <p className="font-semibold text-foreground">
                                                    {verificationResult.licence.type === "CERTIFICATE"
                                                        ? "Registration Certificate"
                                                        : "Operating Licence"}
                                                </p>
                                            </div>

                                            <div className="p-4 rounded-lg bg-green-500/10 border border-green-500/20">
                                                <div className="flex items-center gap-2 text-green-600 dark:text-green-400 mb-2">
                                                    <Calendar className="w-4 h-4" />
                                                    <span className="text-sm font-medium">Expires</span>
                                                </div>
                                                <p className="font-semibold text-foreground">
                                                    {new Date(verificationResult.licence.expires_at).toLocaleDateString(
                                                        "en-US",
                                                        {
                                                            year: "numeric",
                                                            month: "long",
                                                            day: "numeric",
                                                        },
                                                    )}
                                                </p>
                                            </div>

                                            <div className="p-4 rounded-lg bg-emerald-500/10 border border-emerald-500/20">
                                                <div className="flex items-center gap-2 text-emerald-600 dark:text-emerald-400 mb-2">
                                                    <CheckCircle className="w-4 h-4" />
                                                    <span className="text-sm font-medium">Status</span>
                                                </div>
                                                <p className="font-semibold text-foreground">
                                                    {new Date(verificationResult.licence.expires_at) > new Date()
                                                        ? "Active"
                                                        : "Expired"}
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                </>
                            )}

                            {/* Actions */}
                            <Separator />
                            <div className="flex flex-wrap gap-3">
                                <Button variant="outline" onClick={handleShareUrl}>
                                    <ExternalLink className="w-4 h-4 mr-2" />
                                    Share Verification URL
                                </Button>
                                <Button
                                    variant="outline"
                                    onClick={() => {
                                        setInputNumber("");
                                        setVerificationResult(null);
                                        setHasUrlQuery(false);
                                        setVerificationType("licence");
                                        router.replace("/verify", { scroll: false });
                                    }}
                                >
                                    Verify Another
                                </Button>
                            </div>
                        </CardContent>
                    </Card>
                )}

                {/* Information Section */}
                <Card className="border-border/50 bg-card/50 backdrop-blur-sm">
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2 text-foreground">
                            <AlertCircle className="w-5 h-5 text-primary" />
                            How NGO Verification Works
                        </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div className="space-y-3">
                                <h4 className="font-semibold text-foreground">What You Can Verify</h4>
                                <ul className="space-y-2 text-sm text-muted-foreground">
                                    <li className="flex items-start gap-2">
                                        <CheckCircle className="w-4 h-4 text-green-600 mt-0.5 flex-shrink-0" />
                                        NGO organizations by registration number
                                    </li>
                                    <li className="flex items-start gap-2">
                                        <CheckCircle className="w-4 h-4 text-green-600 mt-0.5 flex-shrink-0" />
                                        Registration certificates
                                    </li>
                                    <li className="flex items-start gap-2">
                                        <CheckCircle className="w-4 h-4 text-green-600 mt-0.5 flex-shrink-0" />
                                        Operating licences
                                    </li>
                                    <li className="flex items-start gap-2">
                                        <CheckCircle className="w-4 h-4 text-green-600 mt-0.5 flex-shrink-0" />
                                        Licence expiry dates
                                    </li>
                                    <li className="flex items-start gap-2">
                                        <CheckCircle className="w-4 h-4 text-green-600 mt-0.5 flex-shrink-0" />
                                        Organization authenticity
                                    </li>
                                </ul>
                            </div>
                            <div className="space-y-3">
                                <h4 className="font-semibold text-foreground">Security Features</h4>
                                <ul className="space-y-2 text-sm text-muted-foreground">
                                    <li className="flex items-start gap-2">
                                        <Shield className="w-4 h-4 text-primary mt-0.5 flex-shrink-0" />
                                        Real-time database verification
                                    </li>
                                    <li className="flex items-start gap-2">
                                        <Shield className="w-4 h-4 text-primary mt-0.5 flex-shrink-0" />
                                        Unique licence numbering system
                                    </li>
                                    <li className="flex items-start gap-2">
                                        <Shield className="w-4 h-4 text-primary mt-0.5 flex-shrink-0" />
                                        Shareable verification URLs
                                    </li>
                                    <li className="flex items-start gap-2">
                                        <Shield className="w-4 h-4 text-primary mt-0.5 flex-shrink-0" />
                                        No authentication required
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </CardContent>
                </Card>
            </div>
        </div>
    );
}
