"use client";
import { useEffect, useState } from "react";

import { DashboardPageContent } from "@/components/pages/DashboardPageContent";
import { useAuth } from "@/composables/useStore";

interface User {
    name: string;
    role: string;
    lastLogin?: string;
}

export default function Page() {
    const [loading, setLoading] = useState<boolean>(true);
    const { session } = useAuth();

    if (typeof document !== "undefined") {
        document.title = "Dashboard - myNGO";
    }

    useEffect(() => {
        const timer = setTimeout(() => setLoading(false), 1500);

        return () => clearTimeout(timer);
    }, []);

    const user: User = {
        name: `${session?.first_name} ${session?.last_name}`,
        role: "System Administrator",
        lastLogin: "Yesterday at 4:30 PM",
    };

    return <DashboardPageContent loading={loading} user={user} />;
}
