"use client";

import { useQuery } from "@tanstack/react-query";
import { ArrowLeft, Building2, FileText } from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { use, useMemo, useState } from "react";
import { toast } from "sonner";

import OrganizationFormWizard from "@/components/organization/organization-form-wizard";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import * as licenceService from "@/services/LicenceService";
import * as organizationService from "@/services/OrganizationService";
import { useOrganizationFormStore } from "@/stores/organization-form";
import { OrganizationDto } from "@/types/organization.dto";

export default function OrganizationReportingPage({ params }: { params: Promise<{ id: string }> }) {
    const { id } = use(params);

    if (typeof document !== "undefined") {
        document.title = "Report Details - myNGO";
    }
    const router = useRouter();
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [uploadProgress, setUploadProgress] = useState(0);
    const { resetForm } = useOrganizationFormStore();

    const {
        data: organizationData,
        isLoading: organizationLoading,
        error: organizationError,
        refetch,
    } = useQuery({
        queryKey: ["organization-full", id],
        queryFn: () => organizationService.getOrganization(id),
        enabled: !!id,
        select: (response) => response.data,
    });

    const { data: stagedData, isLoading: stagedDataLoading } = useQuery({
        queryKey: ["staged-licence-renewal-data", id],
        queryFn: () => licenceService.getLicenceRenewalApplication(id),
        enabled: !!organizationData,
        select: (response) => response.data,
        retry: false,
    });

    const organization = organizationData as OrganizationDto;
    const directors = organizationData?.directors || [];
    const sectors = organizationData?.sectors || [];
    const locationActivities = organizationData?.location_activities || [];
    const targetGroups = organizationData?.target_groups || [];
    const projects = organizationData?.projects || [];
    const bankDetails = organizationData?.bank_details || [];
    const auditors = organizationData?.auditors || [];
    const donors = organizationData?.donors || [];
    const fundingSources = organizationData?.funding_sources || [];
    const staff = organizationData?.staff || [];

    const initialFormData = useMemo(() => {
        if (!organization) return null;

        if (stagedData?.form_data && Object.keys(stagedData.form_data).length > 0) {
            return {
                name: stagedData.form_data.name || organization.name,
                abbreviation: stagedData.form_data.abbreviation || organization.abbreviation,
                organization_type_id: stagedData.form_data.organization_type_id || organization.organization_type_id,
                district_id: stagedData.form_data.district_id || organization.district_id,
                financial_start_month: stagedData.form_data.financial_start_month || organization.financial_start_month,
                financial_end_month: stagedData.form_data.financial_end_month || organization.financial_end_month,
                registration_type_id: stagedData.form_data.registration_type_id || organization.registration_type_id,
                charity_number: stagedData.form_data.charity_number || organization.charity_number || "",
                annual_income: stagedData.form_data.annual_income || organization.annual_income || 0,
                biography: stagedData.form_data.biography || organization.biography || "",
                vision: stagedData.form_data.vision || organization.vision || "",
                motto: stagedData.form_data.motto || organization.motto || "",

                objectives: stagedData.form_data.objectives || organization.objectives || [],
                directors: stagedData.form_data.directors || directors,
                sectors: stagedData.form_data.sectors || sectors,
                locationActivities: stagedData.form_data.location_activities || locationActivities,
                targetGroups: stagedData.form_data.target_groups || targetGroups,
                fundingSources: stagedData.form_data.funding_sources || fundingSources,
                auditors: stagedData.form_data.auditors || auditors,
                bankDetails: stagedData.form_data.bank_details || bankDetails,
                contacts: stagedData.form_data.contacts || [],
                staff: stagedData.form_data.staff || staff,
                donors: stagedData.form_data.donors || donors,
                projects: stagedData.form_data.projects || projects,

                supporting_documents: [],
                document_types: stagedData.form_data.document_types || [],
                existing_documents: stagedData.application_documents || [],
            };
        } else {
            return {
                name: organization.name,
                abbreviation: organization.abbreviation,
                organization_type_id: organization.organization_type_id,
                district_id: organization.district_id,
                financial_start_month: organization.financial_start_month,
                financial_end_month: organization.financial_end_month,
                registration_type_id: organization.registration_type_id,
                charity_number: organization.charity_number || "",
                annual_income: organization.annual_income || 0,
                biography: organization.biography || "",
                vision: organization.vision || "",
                motto: organization.motto || "",

                objectives: organization.objectives || [],
                directors: directors,
                sectors: sectors,
                locationActivities,
                targetGroups,
                fundingSources,
                auditors: auditors,
                bankDetails,
                contacts: [],
                staff,
                donors: donors,
                projects: projects,

                supporting_documents: [],
                document_types: [],
                existing_documents: [],
            };
        }
    }, [
        organization,
        stagedData,
        directors,
        sectors,
        locationActivities,
        targetGroups,
        fundingSources,
        auditors,
        bankDetails,
        staff,
        donors,
        projects,
    ]);

    const handleFormSubmit = async (formData: FormData) => {
        setIsSubmitting(true);
        setUploadProgress(0);

        try {
            const response = await licenceService.createLicenceRenewal(id, formData, (e) => {
                if (e.lengthComputable) {
                    const progress = Math.round((e.loaded * 100) / (e?.total || 0));

                    setUploadProgress(progress);
                }
            });

            for (const error of response.errors || []) {
                toast.error(error.message);
            }

            if (response.success) {
                toast.success("Licence renewal application submitted successfully!");
                // Reset the form after successful submission
                resetForm();
                // Navigate to organization page
                router.push(`/organizations/${id}`);
            }
        } catch (error) {
            toast.error("Failed to submit licence renewal application");
        } finally {
            setIsSubmitting(false);
            setUploadProgress(0);
        }
    };

    if (organizationLoading || stagedDataLoading || !organization) {
        return (
            <div className="max-w-7xl mx-auto p-6 space-y-6 min-h-screen">
                <div className="flex items-center justify-center min-h-[60vh]">
                    <div className="text-center">
                        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4" />
                        <span className="text-lg text-foreground">
                            {organizationLoading ? "Loading organization data..." : "Loading renewal data..."}
                        </span>
                    </div>
                </div>
            </div>
        );
    }

    if (organizationError) {
        return (
            <div className="max-w-7xl mx-auto p-6 space-y-6 min-h-screen">
                <Button variant="outline" onClick={() => router.push("/reporting")}>
                    <ArrowLeft className="w-4 h-4 mr-2" />
                    Back to Reporting
                </Button>
                <div className="flex items-center justify-center py-16">
                    <div className="text-center">
                        <Building2 className="h-16 w-16 text-destructive mx-auto mb-4" />
                        <h3 className="text-xl font-semibold text-foreground mb-2">Error loading organization</h3>
                        <p className="text-muted-foreground mb-4">
                            {organizationError?.message || "Failed to load organization data"}
                        </p>
                        <Button onClick={() => refetch()}>Try Again</Button>
                    </div>
                </div>
            </div>
        );
    }

    if (organization.status === "RENEWAL_IN_REVIEW") {
        return (
            <div className="max-w-7xl mx-auto p-6 space-y-6 min-h-screen">
                <Button variant="outline" onClick={() => router.push("/reporting")}>
                    <ArrowLeft className="w-4 h-4 mr-2" />
                    Back to Reporting
                </Button>
                <div className="flex items-center justify-center min-h-[60vh]">
                    <Card className="border-border/50 bg-card/50 backdrop-blur-sm max-w-md w-full">
                        <CardContent className="p-8 text-center">
                            <FileText className="h-16 w-16 text-blue-500 mx-auto mb-4" />
                            <h3 className="text-xl font-semibold text-foreground mb-2">Application Under Review</h3>
                            <p className="text-muted-foreground mb-6">
                                Your licence renewal application is currently under review. You cannot make changes at
                                this time.
                            </p>
                            <Button onClick={() => router.push(`/organizations/${id}`)}>
                                <Building2 className="w-4 h-4 mr-2" />
                                View Organization
                            </Button>
                        </CardContent>
                    </Card>
                </div>
            </div>
        );
    }

    if (!["RENEWAL_DRAFT", "REGISTERED"].includes(organization.status)) {
        return (
            <div className="max-w-7xl mx-auto p-6 space-y-6 min-h-screen">
                <Button variant="outline" onClick={() => router.push("/reporting")}>
                    <ArrowLeft className="w-4 h-4 mr-2" />
                    Back to Reporting
                </Button>
                <Card className="border-border/50 bg-card/50 backdrop-blur-sm">
                    <CardContent className="p-8 text-center">
                        <FileText className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
                        <h3 className="text-xl font-semibold text-foreground mb-2">Licence Renewal Not Available</h3>
                        <p className="text-muted-foreground mb-4">
                            This organization is not eligible for licence renewal at this time. Only active (registered)
                            organizations can apply for licence renewal.
                        </p>
                        <p className="text-sm text-muted-foreground">
                            Current Status: <span className="font-medium">{organization.status}</span>
                        </p>
                    </CardContent>
                </Card>
            </div>
        );
    }

    return (
        <div className="min-h-screen">
            <div className="max-w-6xl mx-auto p-6 space-y-6">
                {/* Header */}
                <div className="grid space-y-6">
                    <div>
                        <Button variant="outline" onClick={() => router.push("/reporting")}>
                            <ArrowLeft className="w-4 h-4 mr-2" />
                            Back to Reporting
                        </Button>
                    </div>

                    <div className="flex items-center gap-4">
                        <div>
                            <h1 className="text-3xl font-bold tracking-tight text-foreground">
                                Annual Reporting - {organization.name}
                            </h1>
                            <p className="text-muted-foreground mt-1">
                                Update your organization information for licence renewal
                            </p>
                        </div>
                    </div>
                </div>
                {/* Organization Info Card */}
                <Card className="border-border/50 bg-card/50 backdrop-blur-sm">
                    <CardHeader>
                        <CardTitle>
                            <Link
                                className="flex items-center gap-2 hover:underline"
                                href={`/organizations/${organization.id}`}
                            >
                                <Building2 className="w-5 h-5" />
                                {organization.name}
                            </Link>
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                            <div>
                                <span className="text-muted-foreground">Registration Number:</span>
                                <p className="font-medium">{organization.registration_number}</p>
                            </div>
                            <div>
                                <span className="text-muted-foreground">Current Status:</span>
                                <p className="font-medium">{organization.status}</p>
                            </div>
                            <div>
                                <span className="text-muted-foreground">Annual Income:</span>
                                <p className="font-medium">${organization.annual_income?.toLocaleString() || "0"}</p>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                {/* Form Wizard */}
                {initialFormData && (
                    <>
                        {stagedData?.form_data && Object.keys(stagedData.form_data).length > 0 && (
                            <Card className="border-blue-200 dark:border-blue-800 bg-blue-50 dark:bg-blue-950/50 backdrop-blur-sm mb-6">
                                <CardContent className="p-4">
                                    <div className="flex items-center gap-2 text-blue-700 dark:text-blue-300">
                                        <FileText className="w-4 h-4" />
                                        <p className="text-sm font-medium">
                                            Draft renewal data restored from your previous session.
                                        </p>
                                    </div>
                                </CardContent>
                            </Card>
                        )}

                        {/* Upload Progress Indicator */}
                        {isSubmitting && (
                            <Card className="border-blue-200 dark:border-blue-800 bg-blue-50 dark:bg-blue-950/50 backdrop-blur-sm mb-6">
                                <CardContent className="p-4">
                                    <div className="space-y-3">
                                        <div className="flex items-center justify-between">
                                            <p className="text-sm font-medium text-blue-700 dark:text-blue-300">
                                                Submitting licence renewal application...
                                            </p>
                                            <span className="text-sm text-blue-600 dark:text-blue-400">
                                                {uploadProgress}%
                                            </span>
                                        </div>
                                        <Progress className="h-2" value={uploadProgress} />
                                    </div>
                                </CardContent>
                            </Card>
                        )}

                        <OrganizationFormWizard
                            initialData={initialFormData}
                            isSubmitting={isSubmitting}
                            mode="renewal"
                            onSubmit={handleFormSubmit}
                        />
                    </>
                )}
            </div>
        </div>
    );
}
