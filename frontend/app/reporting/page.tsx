"use client";

import { useQuery } from "@tanstack/react-query";
import { ColumnDef } from "@tanstack/react-table";
import { Activity, ArrowUpDown, Building2, Calendar, FileText, Grid3X3, List, TrendingUp } from "lucide-react";
import Link from "next/link";
import { useState } from "react";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { DataTable } from "@/components/ui/data-table";
import { Select } from "@/components/ui/select";
import { Skeleton } from "@/components/ui/skeleton";
import * as dashboardService from "@/services/DashboardService";
import * as organizationService from "@/services/OrganizationService";
import { OrganizationDto, OrganizationStatus } from "@/types/organization.dto";

type ViewMode = "table" | "cards";

const getStatusConfig = (status: string) => {
    const configs = {
        DRAFT: { color: "bg-yellow-100 text-yellow-800", label: "Draft" },
        PENDING: { color: "bg-blue-100 text-blue-800", label: "Pending" },
        REVIEW: { color: "bg-purple-100 text-purple-800", label: "Under Review" },
        REGISTERED: { color: "bg-green-100 text-green-800", label: "Active" },
        ACTIVE: { color: "bg-green-100 text-green-800", label: "Active" },
        SUSPENDED: { color: "bg-red-100 text-red-800", label: "Suspended" },
        INACTIVE: { color: "bg-gray-100 text-gray-800", label: "Inactive" },
    };

    return configs[status as keyof typeof configs] || { color: "bg-gray-100 text-gray-800", label: status };
};

const organizationColumns: ColumnDef<OrganizationDto>[] = [
    {
        accessorKey: "name",
        header: ({ column }) => {
            return (
                <Button
                    className="h-auto p-0 hover:bg-transparent"
                    variant="ghost"
                    onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
                >
                    Organization
                    <ArrowUpDown className="ml-2 h-4 w-4" />
                </Button>
            );
        },
        cell: ({ row }) => {
            const organization = row.original;

            return (
                <div className="flex items-center gap-3">
                    <div className="p-2 rounded-full bg-primary/10 text-primary">
                        <Building2 className="h-4 w-4" />
                    </div>
                    <div>
                        <div className="font-medium text-foreground">
                            <Link className="hover:underline" href={`/reporting/${organization.id}`}>
                                {organization.name}
                            </Link>
                        </div>
                        <div className="text-sm text-muted-foreground">{organization.abbreviation}</div>
                    </div>
                </div>
            );
        },
    },
    {
        accessorKey: "registration_number",
        header: "Registration #",
        cell: ({ row }) => <div className="font-mono text-sm">{row.getValue("registration_number")}</div>,
    },
    {
        accessorKey: "status",
        header: "Status",
        cell: ({ row }) => {
            const status = row.getValue("status") as string;
            const config = getStatusConfig(status);

            return <Badge className={config.color}>{config.label}</Badge>;
        },
    },
    {
        accessorKey: "annual_income",
        header: ({ column }) => {
            return (
                <Button
                    className="h-auto p-0 hover:bg-transparent"
                    variant="ghost"
                    onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
                >
                    Annual Income
                    <ArrowUpDown className="ml-2 h-4 w-4" />
                </Button>
            );
        },
        cell: ({ row }) => {
            const income = row.getValue("annual_income") as number;

            return <div className="font-medium">${income?.toLocaleString() || "0"}</div>;
        },
    },
    {
        accessorKey: "created_at",
        header: ({ column }) => {
            return (
                <Button
                    className="h-auto p-0 hover:bg-transparent"
                    variant="ghost"
                    onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
                >
                    Created Date
                    <ArrowUpDown className="ml-2 h-4 w-4" />
                </Button>
            );
        },
        cell: ({ row }) => {
            const date = row.getValue("created_at") as string;

            return <div className="text-sm">{new Date(date).toLocaleDateString()}</div>;
        },
    },
    {
        id: "actions",
        header: "Actions",
        cell: ({ row }) => {
            const organization = row.original;

            return (
                <Button asChild size="sm" variant="ghost">
                    <Link href={`/reporting/${organization.id}`}>
                        <FileText className="h-4 w-4" />
                    </Link>
                </Button>
            );
        },
    },
];

export default function ReportingPage() {
    if (typeof document !== "undefined") {
        document.title = "Reports - myNGO";
    }

    const [viewMode, setViewMode] = useState<ViewMode>("table");
    const [statusFilter, setStatusFilter] = useState<string>("REGISTERED,RENEWAL_DRAFT");

    const {
        data: organizationsData,
        isLoading: organizationsLoading,
        error: organizationsError,
    } = useQuery({
        queryKey: ["organizations", { status: statusFilter }],
        queryFn: () =>
            organizationService.fetchOrganizations({
                status: statusFilter,
            }),
        select: (response) => response.data || [],
    });

    const { data: statisticsData, isLoading: statisticsLoading } = useQuery({
        queryKey: ["organization-statistics"],
        queryFn: () => dashboardService.getOrganizationStatistics(),
        select: (response) => response.data,
    });

    const organizations = organizationsData || [];
    const statistics = statisticsData;

    return (
        <div className="min-h-screen">
            <div className="max-w-7xl mx-auto p-6 space-y-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold tracking-tight text-foreground">Annual Reporting</h1>
                        <p className="text-muted-foreground mt-1">
                            Manage organization licence renewals and annual reports
                        </p>
                    </div>
                </div>

                {/* Statistics Cards */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    {statisticsLoading ? (
                        <>
                            {[...Array(4)].map((_, index) => (
                                <Card key={index} className="border-border/50 bg-card/50 backdrop-blur-sm">
                                    <CardContent className="p-6">
                                        <div className="flex items-center justify-between">
                                            <div className="space-y-2">
                                                <Skeleton className="h-4 w-24" />
                                                <Skeleton className="h-8 w-16" />
                                            </div>
                                            <Skeleton className="h-12 w-12 rounded-full" />
                                        </div>
                                    </CardContent>
                                </Card>
                            ))}
                        </>
                    ) : (
                        <>
                            <Card className="border-border/50 bg-card/50 backdrop-blur-sm">
                                <CardContent className="p-6">
                                    <div className="flex items-center justify-between">
                                        <div>
                                            <p className="text-sm font-medium text-muted-foreground">
                                                Active Organizations
                                            </p>
                                            <p className="text-2xl font-bold text-foreground">
                                                {organizations.filter((org) => org.status === "REGISTERED").length}
                                            </p>
                                        </div>
                                        <div className="p-3 rounded-full bg-green-500/20 text-green-600 dark:text-green-400">
                                            <Building2 className="w-6 h-6" />
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>

                            <Card className="border-border/50 bg-card/50 backdrop-blur-sm">
                                <CardContent className="p-6">
                                    <div className="flex items-center justify-between">
                                        <div>
                                            <p className="text-sm font-medium text-muted-foreground">Due for Renewal</p>
                                            <p className="text-2xl font-bold text-foreground">
                                                {statistics?.renewal_statistics?.due_for_renewal || 0}
                                            </p>
                                        </div>
                                        <div className="p-3 rounded-full bg-yellow-500/20 text-yellow-600 dark:text-yellow-400">
                                            <Calendar className="w-6 h-6" />
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>

                            <Card className="border-border/50 bg-card/50 backdrop-blur-sm">
                                <CardContent className="p-6">
                                    <div className="flex items-center justify-between">
                                        <div>
                                            <p className="text-sm font-medium text-muted-foreground">
                                                Pending Renewals
                                            </p>
                                            <p className="text-2xl font-bold text-foreground">
                                                {statistics?.renewal_statistics?.pending_renewals || 0}
                                            </p>
                                        </div>
                                        <div className="p-3 rounded-full bg-blue-500/20 text-blue-600 dark:text-blue-400">
                                            <Activity className="w-6 h-6" />
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>

                            <Card className="border-border/50 bg-card/50 backdrop-blur-sm">
                                <CardContent className="p-6">
                                    <div className="flex items-center justify-between">
                                        <div>
                                            <p className="text-sm font-medium text-muted-foreground">
                                                Completed This Year
                                            </p>
                                            <p className="text-2xl font-bold text-foreground">
                                                {statistics?.renewal_statistics?.completed_this_year || 0}
                                            </p>
                                        </div>
                                        <div className="p-3 rounded-full bg-emerald-500/20 text-emerald-600 dark:text-emerald-400">
                                            <TrendingUp className="w-6 h-6" />
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>
                        </>
                    )}
                </div>

                {/* Main Content */}
                <Card className="border-border/50 bg-card/50 backdrop-blur-sm">
                    <CardHeader>
                        <div className="flex items-center justify-between">
                            <div>
                                <CardTitle className="flex items-center gap-2 text-foreground">
                                    <FileText className="w-5 h-5" />
                                    Organizations for Reporting
                                </CardTitle>
                                <CardDescription>
                                    {organizations.length} organization{organizations.length !== 1 ? "s" : ""} available
                                    for reporting
                                </CardDescription>
                            </div>
                            <div className="flex items-center gap-2">
                                <Select
                                    options={[
                                        { label: "Active Organizations", value: "ACTIVE" },
                                        { label: "All Statuses", value: "all" },
                                        { label: "Draft", value: "DRAFT" },
                                        { label: "Pending", value: "PENDING" },
                                        { label: "Review", value: "REVIEW" },
                                        { label: "Suspended", value: "SUSPENDED" },
                                        { label: "Inactive", value: "INACTIVE" },
                                    ]}
                                    value={statusFilter}
                                    onValueChange={(e) => setStatusFilter(e as OrganizationStatus)}
                                />
                                <div className="flex rounded-lg border border-border/50 p-1">
                                    <Button
                                        className="h-8 w-8 p-0"
                                        size="sm"
                                        variant={viewMode === "table" ? "default" : "ghost"}
                                        onClick={() => setViewMode("table")}
                                    >
                                        <List className="h-4 w-4" />
                                    </Button>
                                    <Button
                                        className="h-8 w-8 p-0"
                                        size="sm"
                                        variant={viewMode === "cards" ? "default" : "ghost"}
                                        onClick={() => setViewMode("cards")}
                                    >
                                        <Grid3X3 className="h-4 w-4" />
                                    </Button>
                                </div>
                            </div>
                        </div>
                    </CardHeader>
                    <CardContent>
                        {organizationsLoading ? (
                            <div className="flex items-center justify-center py-16">
                                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary" />
                                <span className="ml-4 text-lg text-foreground">Loading organizations...</span>
                            </div>
                        ) : organizationsError ? (
                            <div className="text-center py-16">
                                <div className="text-destructive mb-4">Failed to load organizations</div>
                                <Button variant="outline">Try Again</Button>
                            </div>
                        ) : viewMode === "table" ? (
                            <DataTable
                                columns={organizationColumns}
                                data={organizations}
                                loading={organizationsLoading}
                                pageSize={15}
                                searchKey="name"
                                searchPlaceholder="Search organizations..."
                            />
                        ) : (
                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                {organizations.map((org) => {
                                    const statusConfig = getStatusConfig(org.status);

                                    return (
                                        <Card
                                            key={org.id}
                                            className="border-border/50 bg-card/20 backdrop-blur-sm hover:shadow-lg transition-all duration-300"
                                        >
                                            <CardContent className="p-4">
                                                <div className="flex items-start justify-between mb-3">
                                                    <div className="p-2 rounded-full bg-primary/10 text-primary">
                                                        <Building2 className="h-5 w-5" />
                                                    </div>
                                                    <Badge className={statusConfig.color}>{statusConfig.label}</Badge>
                                                </div>
                                                <div className="space-y-2">
                                                    <Link className="block" href={`/reporting/${org.id}`}>
                                                        <h3 className="font-semibold text-foreground hover:underline">
                                                            {org.name}
                                                        </h3>
                                                    </Link>
                                                    <p className="text-sm text-muted-foreground">{org.abbreviation}</p>
                                                    <div className="flex items-center justify-between text-xs text-muted-foreground">
                                                        <span className="font-mono">{org.registration_number}</span>
                                                        <span>${(org.annual_income || 0).toLocaleString()}</span>
                                                    </div>
                                                    <div className="flex items-center gap-1 text-xs text-muted-foreground">
                                                        <Calendar className="h-3 w-3" />
                                                        {new Date(org.created_at).toLocaleDateString()}
                                                    </div>
                                                    <div className="pt-2">
                                                        <Button asChild className="w-full" size="sm" variant="outline">
                                                            <Link
                                                                className="flex item-center gap-2"
                                                                href={`/reporting/${org.id}`}
                                                            >
                                                                <FileText className="h-4 w-4 mr-2" />
                                                                Start Reporting
                                                            </Link>
                                                        </Button>
                                                    </div>
                                                </div>
                                            </CardContent>
                                        </Card>
                                    );
                                })}
                                {organizations.length === 0 && (
                                    <div className="col-span-full text-center py-16">
                                        <Building2 className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                                        <p className="text-muted-foreground">No organizations found for reporting</p>
                                    </div>
                                )}
                            </div>
                        )}
                    </CardContent>
                </Card>
            </div>
        </div>
    );
}
