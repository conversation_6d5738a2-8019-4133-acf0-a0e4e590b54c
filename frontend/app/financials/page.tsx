"use client";

import { useQuery } from "@tanstack/react-query";
import {
    AlertTriangle,
    ArrowRight,
    Check,
    CheckCircle,
    Clock,
    CreditCard,
    DollarSign,
    Eye,
    FileText,
} from "lucide-react";
import Link from "next/link";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { cn } from "@/lib/utils";
import { fetchFinanceWorkflowStages, fetchInvoices, fetchPayments } from "@/services/FinancialService";
import { FinanceWorkflowStage } from "@/types";
import { formatFileSize } from "@/utils/common";

export default function FinancialsPage() {
    if (typeof document !== "undefined") {
        document.title = "Financials - myNGO";
    }
    
    const {
        data: workflowStages,
        isLoading: stagesLoading,
        error: stagesError,
    } = useQuery({
        queryKey: ["finance-workflow-stages"],
        queryFn: () => fetchFinanceWorkflowStages({ page: 1, size: 20, role_code: "FINANCE_OFFICER" }),
    });

    const { data: invoicesData, isLoading: invoicesLoading } = useQuery({
        queryKey: ["invoices-stats"],
        queryFn: () => fetchInvoices({ size: 10 }),
    });

    const { data: paymentsData, isLoading: paymentsLoading } = useQuery({
        queryKey: ["payments-stats"],
        queryFn: () => fetchPayments({ size: 10 }),
    });

    const stages = workflowStages?.data || [];
    const invoices = invoicesData?.data || [];
    const payments = paymentsData?.data || [];

    const pendingStages = stages.filter((stage) => stage.status === "PENDING" || stage.status === "IN_REVIEW");
    const pendingInvoices = invoices.filter((inv) => inv.status === "PENDING");
    const paidInvoices = invoices.filter((inv) => inv.status === "PAID");
    const overdueInvoices = invoices.filter((inv) => inv.status === "OVERDUE");

    const totalRevenue = paidInvoices.reduce((sum, inv) => sum + inv.total_amount, 0);

    return (
        <div className="space-y-6">
            {/* Header */}
            <div className="flex items-center justify-between">
                <div>
                    <h1 className="text-3xl font-bold tracking-tight">Financial Management</h1>
                    <p className="text-muted-foreground">
                        Review workflow stages, manage invoices, and verify payments
                    </p>
                </div>
            </div>

            {/* Quick Statistics */}
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Pending Reviews</CardTitle>
                        <Clock className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold text-yellow-600">
                            {stagesLoading ? <Skeleton className="h-8 w-12" /> : pendingStages.length}
                        </div>
                        <p className="text-xs text-muted-foreground">Finance stages awaiting review</p>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
                        <DollarSign className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold text-green-600">
                            {invoicesLoading ? (
                                <Skeleton className="h-8 w-20" />
                            ) : (
                                `MWK ${totalRevenue.toLocaleString()}`
                            )}
                        </div>
                        <p className="text-xs text-muted-foreground">From {paidInvoices.length} paid invoices</p>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Active Invoices</CardTitle>
                        <FileText className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold text-blue-600">
                            {invoicesLoading ? <Skeleton className="h-8 w-8" /> : pendingInvoices.length}
                        </div>
                        <p className="text-xs text-muted-foreground">Awaiting payment</p>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Overdue</CardTitle>
                        <AlertTriangle className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold text-red-600">
                            {invoicesLoading ? <Skeleton className="h-8 w-8" /> : overdueInvoices.length}
                        </div>
                        <p className="text-xs text-muted-foreground">Require immediate attention</p>
                    </CardContent>
                </Card>
            </div>

            {/* Quick Actions */}
            <div className="flex flex-wrap gap-4">
                <Button asChild>
                    <Link className="flex items-center" href="/financials">
                        <FileText className="h-4 w-4 mr-2" />
                        Approvals
                    </Link>
                </Button>
                <Button asChild variant="outline">
                    <Link className="flex items-center" href="/financials/payments">
                        <CreditCard className="h-4 w-4 mr-2" />
                        Verify Payments
                    </Link>
                </Button>
                <Button asChild variant="outline">
                    <Link className="flex items-center" href="/financials/invoices">
                        <CreditCard className="h-4 w-4 mr-2" />
                        Manage Invoices
                    </Link>
                </Button>
                <Button asChild variant="outline">
                    <Link className="flex items-center" href="/workflows?assigned_to_me=true">
                        <Clock className="h-4 w-4 mr-2" />
                        All Workflow Reviews
                    </Link>
                </Button>
            </div>

            {/* Finance Workflow Stages */}
            <div className="space-y-6">
                <div className="flex items-center justify-between">
                    <div>
                        <span className="text-lg font-bold">Finance Workflow Stages</span>
                        <p className="text-sm text-muted-foreground">
                            Applications requiring finance officer review and payment verification
                        </p>
                    </div>
                    {stages.length > 0 && <Badge variant="secondary">{stages.length} stages</Badge>}
                </div>
                <div className="space-y-4">
                    {stagesLoading ? (
                        <div className="space-y-4">
                            {Array.from({ length: 3 }).map((_, i) => (
                                <div key={i} className="flex items-start justify-between p-4 border rounded-lg">
                                    <div className="flex items-start gap-3">
                                        <Skeleton className="h-4 w-4 rounded" />
                                        <div className="space-y-2">
                                            <Skeleton className="h-4 w-48" />
                                            <Skeleton className="h-3 w-32" />
                                            <Skeleton className="h-3 w-64" />
                                        </div>
                                    </div>
                                    <div className="flex gap-2">
                                        <Skeleton className="h-8 w-16" />
                                        <Skeleton className="h-8 w-20" />
                                    </div>
                                </div>
                            ))}
                        </div>
                    ) : stagesError ? (
                        <div className="text-center py-8 text-red-600">
                            <AlertTriangle className="h-12 w-12 mx-auto mb-4" />
                            <p>Error loading workflow stages</p>
                            <p className="text-sm">{stagesError.message}</p>
                        </div>
                    ) : stages.length === 0 ? (
                        <div className="text-center py-12 text-muted-foreground">
                            <CheckCircle className="h-12 w-12 mx-auto mb-4 opacity-50" />
                            <p className="text-lg font-medium">No finance stages pending review</p>
                            <p className="text-sm">All applications are up to date</p>
                        </div>
                    ) : (
                        stages.map((stage) => <WorkflowStageCard key={stage.id} stage={stage} />)
                    )}
                </div>
            </div>
        </div>
    );
}

interface WorkflowStageCardProps {
    stage: FinanceWorkflowStage;
}

function WorkflowStageCard({ stage }: WorkflowStageCardProps) {
    const getStatusColor = (status: string) => {
        switch (status) {
            case "PENDING":
            case "IN_REVIEW":
                return "text-yellow-600 bg-yellow-100";
            case "COMPLETED":
            case "APPROVED":
                return "text-green-600 bg-green-100";
            case "REJECTED":
                return "text-red-600 bg-red-100";
            default:
                return "text-gray-600 bg-gray-100";
        }
    };

    const getStatusIcon = (status: string) => {
        switch (status) {
            case "PENDING":
            case "IN_REVIEW":
                return <Clock className="h-4 w-4" />;
            case "COMPLETED":
            case "APPROVED":
                return <CheckCircle className="h-4 w-4" />;
            case "REJECTED":
                return <AlertTriangle className="h-4 w-4" />;
            default:
                return <FileText className="h-4 w-4" />;
        }
    };

    return (
        <div className="flex items-start justify-between p-4 border rounded-lg hover:bg-muted/50 transition-colors">
            <div className="flex items-start gap-3 flex-1">
                {getStatusIcon(stage.status)}
                <div className="space-y-2 flex-1">
                    <div className="flex items-center gap-2">
                        <h4 className="font-medium text-md">{stage.organization?.name}</h4>
                        <Badge className={cn("text-xs", getStatusColor(stage.status))}>{stage.status}</Badge>
                    </div>

                    <div className="text-xs text-muted-foreground space-y-1">
                        <p>
                            <strong>Application:</strong> {stage.application.code} • {stage.application.type}
                        </p>
                        <p>
                            <strong>Stage:</strong> {stage.template_stage.name} (Position{" "}
                            {stage.template_stage.position})
                        </p>
                        {stage.template_stage.description && (
                            <p className="italic">{stage.template_stage.description}</p>
                        )}
                    </div>

                    {stage.invoice && (
                        <div className="text-xs bg-blue-600 p-2 rounded border-l-2 border-blue-800">
                            <p className="text-white flex items-center gap-1">
                                Invoice: {stage.invoice.reference_number}
                            </p>
                            <p className="text-white/80">
                                Amount: MWK {stage.invoice.total_amount.toLocaleString()} • Status:{" "}
                                <span className="font-medium">{stage.invoice.status}</span> • Due:{" "}
                                {new Date(stage.invoice.due_date).toLocaleDateString()}
                            </p>
                        </div>
                    )}

                    {stage.proof_of_payment_document && (
                        <div className="text-xs bg-green-600 p-2 rounded border-l-2 border-green-800 flex items-center gap-2 text-white">
                            <Check />
                            <div className="grid">
                                <p className="text-white flex items-center gap-1">
                                    <strong>Proof of Payment:</strong> {stage.proof_of_payment_document.original_name}
                                </p>
                                <p className="text-white/80">
                                    Size: {formatFileSize(stage.proof_of_payment_document.size)} • Uploaded:{" "}
                                    {new Date(stage.proof_of_payment_document.uploaded_at).toLocaleDateString()}
                                </p>
                            </div>
                        </div>
                    )}

                    {/* {stage.organization && (
                        <div className="text-xs text-muted-foreground">
                            <p>
                                <strong>Contact:</strong> {stage.organization.email} • {stage.organization.phone_number}
                            </p>
                        </div>
                    )} */}
                </div>
            </div>

            <div className="flex items-center gap-2 ml-4">
                <Button asChild size="sm" variant="outline">
                    <Link className="flex items-center" href={`/workflows/${stage.workflow_id}`}>
                        <Eye className="h-3 w-3 mr-1" />
                        View Details
                    </Link>
                </Button>

                {stage.status === "PENDING" || stage.status === "IN_REVIEW" ? (
                    <Button asChild size="sm">
                        <Link className="flex items-center" href={`/workflows/${stage.workflow_id}`}>
                            <ArrowRight className="h-3 w-3 mr-1" />
                            Review
                        </Link>
                    </Button>
                ) : null}
            </div>
        </div>
    );
}
