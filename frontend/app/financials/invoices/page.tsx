"use client";

import { Suspense } from "react";

import { InvoiceManagement } from "@/components/financial/InvoiceManagement";
import { LoadingSkeleton } from "@/components/forms/LoadingSkeleton";

export default function InvoicesPage() {
    if (typeof document !== "undefined") {
        document.title = "Invoices - myNGO";
    }
    
    return (
        <div className="space-y-6 max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex items-center justify-between">
                <div>
                    <h1 className="text-3xl font-bold tracking-tight">Invoice Management</h1>
                    <p className="text-muted-foreground">
                        Create, manage, and track invoices for organization registrations and renewals
                    </p>
                </div>
            </div>

            <Suspense fallback={<LoadingSkeleton />}>
                <InvoiceManagement />
            </Suspense>
        </div>
    );
}
