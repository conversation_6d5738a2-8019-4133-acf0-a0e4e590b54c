"use client";

import { Suspense } from "react";

import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { PaymentReconciliation } from "@/components/financial/PaymentReconciliation";
import { PaymentVerification } from "@/components/financial/PaymentVerification";
import { LoadingSkeleton } from "@/components/forms/LoadingSkeleton";

export default function PaymentsPage() {
    if (typeof document !== "undefined") {
        document.title = "Payments - myNGO";
    }
    
    return (
        <div className="space-y-6">
            <div className="flex items-center justify-between">
                <div>
                    <h1 className="text-3xl font-bold tracking-tight">Payment Management</h1>
                    <p className="text-muted-foreground">
                        Monitor payments, verify transactions, and manage payment discrepancies
                    </p>
                </div>
            </div>

            <Tabs className="space-y-4" defaultValue="verification">
                <TabsList>
                    <TabsTrigger value="verification">Payment Verification</TabsTrigger>
                    <TabsTrigger value="reconciliation">Payment Reconciliation</TabsTrigger>
                </TabsList>

                <TabsContent value="verification">
                    <Suspense fallback={<LoadingSkeleton />}>
                        <PaymentVerification />
                    </Suspense>
                </TabsContent>

                <TabsContent value="reconciliation">
                    <Suspense fallback={<LoadingSkeleton />}>
                        <PaymentReconciliation />
                    </Suspense>
                </TabsContent>
            </Tabs>
        </div>
    );
}
