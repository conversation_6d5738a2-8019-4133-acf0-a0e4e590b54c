"use client";

import {
    BarChart3,
    Calculator,
    CheckCircle,
    CreditCard,
    FileText,
    Receipt,
    Settings,
    TrendingUp,
    Users,
} from "lucide-react";

import DefaultLayout from "@/layouts/DefaultLayout";
import { LayoutProps } from "@/types";

const navigationItems = [
    {
        title: "Overview",
        href: "/financials",
        icon: BarChart3,
        description: "Financial dashboard and key metrics",
    },
    {
        title: "Revenue Analytics",
        href: "/financials/analytics",
        icon: TrendingUp,
        description: "Revenue trends and performance insights",
    },
    {
        title: "Payment Management",
        href: "/financials/payments",
        icon: CreditCard,
        description: "Payment processing and reconciliation",
        badge: "Live",
    },
    {
        title: "Invoice Management",
        href: "/financials/invoices",
        icon: Receipt,
        description: "Invoice generation and tracking",
    },
    {
        title: "Organizations",
        href: "/financials/organizations",
        icon: Users,
        description: "Organization financial profiles",
    },
    {
        title: "Fee Management",
        href: "/financials/fees",
        icon: Calculator,
        description: "Fee structure and optimization",
    },
    {
        title: "Reconciliation",
        href: "/financials/reconciliation",
        icon: CheckCircle,
        description: "Payment verification and discrepancy management",
        badge: "Important",
    },
    {
        title: "Reports",
        href: "/financials/reports",
        icon: FileText,
        description: "Comprehensive financial reporting",
    },
    {
        title: "Settings",
        href: "/financials/settings",
        icon: Settings,
        description: "Financial system configuration",
    },
];

export default function FinancialsLayout({ children }: LayoutProps) {
    return <DefaultLayout>{children}</DefaultLayout>;
}
