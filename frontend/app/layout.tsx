import "@/styles/globals.css";
import clsx from "clsx";
import { Metadata, Viewport } from "next";
import * as React from "react";

import Providers from "./providers";
import { ThemeClient } from "./theme-client";

import { siteConfig } from "@/config/site";

export const metadata: Metadata = {
    title: {
        default: siteConfig.name,
        template: `%s - ${siteConfig.name}`,
    },
    description: siteConfig.description,
    icons: {
        icon: [
            { url: "/favicon.svg", type: "image/svg+xml" },
            { url: "/favicon.ico", type: "image/x-icon" },
        ],
        apple: "/favicon.svg",
    },
};

export const viewport: Viewport = {
    themeColor: [
        { media: "(prefers-color-scheme: light)", color: "#f97316" },
        { media: "(prefers-color-scheme: dark)", color: "#f97316" },
    ],
};

export default function RootLayout({ children }: { children: React.ReactNode }) {
    return (
        <html suppressHydrationWarning lang="en">
            <head />
            <body className={clsx("min-h-screen antialiased")}>
                <Providers>
                    <ThemeClient />
                    {children}
                </Providers>
            </body>
        </html>
    );
}
