"use client";

import { AlertTriangleIcon, ArrowLeftIcon, FileQuestionIcon, HomeIcon, SearchIcon } from "lucide-react";
import Link from "next/link";

import { Logo } from "@/components/icons";
import { Button } from "@/components/ui/button";

export default function NotFound() {
    return (
        <div className="min-h-screen bg-background flex items-center justify-center p-4 relative overflow-hidden">
            {/* Background Pattern */}
            <div className="absolute inset-0 opacity-5">
                <div className="absolute top-10 left-10 text-9xl font-bold text-muted-foreground select-none">404</div>
                <div className="absolute top-32 right-20 text-6xl font-bold text-muted-foreground select-none rotate-12">
                    404
                </div>
                <div className="absolute bottom-20 left-32 text-7xl font-bold text-muted-foreground select-none -rotate-6">
                    404
                </div>
                <div className="absolute bottom-32 right-10 text-5xl font-bold text-muted-foreground select-none rotate-45">
                    404
                </div>
            </div>

            <div className="max-w-4xl w-full text-center relative z-10">
                {/* NGORA Logo */}
                <div className="mb-8">
                    <div className="flex justify-center mb-4">
                        <Logo />
                    </div>
                </div>

                {/* Main 404 Section */}
                <div className="mb-12">
                    {/* Large 404 with animation */}
                    <div className="relative mb-8">
                        <h1 className="text-8xl md:text-9xl font-black text-primary/20 select-none">404</h1>
                        <div className="absolute inset-0 flex items-center justify-center">
                            <FileQuestionIcon className="h-24 w-24 md:h-32 md:w-32 text-primary animate-pulse" />
                        </div>
                    </div>

                    {/* Error Message */}
                    <div className="space-y-4 mb-8">
                        <div className="flex items-center justify-center gap-2 mb-4">
                            <AlertTriangleIcon className="h-6 w-6 text-destructive" />
                            <h2 className="text-2xl md:text-3xl font-bold text-foreground">Oops! Page Not Found</h2>
                        </div>

                        <p className="text-lg text-muted-foreground max-w-2xl mx-auto leading-relaxed">
                            We couldn&apos;t find the page you&apos;re looking for. It might have been moved, renamed,
                            or doesn&apos;t exist.
                        </p>

                        <div className="bg-muted/50 border-l-4 border-primary rounded-r-lg p-4 max-w-xl mx-auto mt-6">
                            <p className="text-sm text-muted-foreground text-left">
                                <strong className="text-foreground">Suggestion:</strong> Check the URL for typos, or use
                                the navigation below to find what you&apos;re looking for.
                            </p>
                        </div>
                    </div>
                </div>

                {/* Action Buttons */}
                <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-12">
                    <Button asChild className="w-full sm:w-auto" size="lg">
                        <Link className="flex items-center" href="/">
                            <HomeIcon className="mr-2 h-5 w-5" />
                            Back to Home
                        </Link>
                    </Button>

                    <Button asChild className="w-full sm:w-auto" size="lg" variant="outline">
                        <Link className="flex items-center" href="/organizations">
                            <SearchIcon className="mr-2 h-5 w-5" />
                            Browse Organizations
                        </Link>
                    </Button>

                    <Button
                        className="w-full sm:w-auto flex items-center justify-center"
                        size="lg"
                        variant="ghost"
                        onClick={() => window.history.back()}
                    >
                        <ArrowLeftIcon className="mr-2 h-5 w-5" />
                        Go Back
                    </Button>
                </div>

                {/* Quick Links */}
                <div className="border-t border-border pt-8">
                    <h3 className="text-lg font-semibold text-foreground mb-4">Or try one of these popular pages:</h3>
                    <div className="flex flex-wrap items-center justify-center gap-2">
                        <Button asChild size="sm" variant="link">
                            <Link href="/organizations/create">Register Your NGO</Link>
                        </Button>
                        <span className="text-muted-foreground">•</span>
                        <Button asChild size="sm" variant="link">
                            <Link href="/auth/login">Login</Link>
                        </Button>
                        <span className="text-muted-foreground">•</span>
                        <Button asChild size="sm" variant="link">
                            <Link href="/dashboard">Dashboard</Link>
                        </Button>
                        <span className="text-muted-foreground">•</span>
                        <Button asChild size="sm" variant="link">
                            <Link href="/workflows">Applications</Link>
                        </Button>
                    </div>
                </div>

                {/* Footer */}
                <div className="mt-12 pt-6 border-t border-border text-center">
                    <p className="text-xs text-muted-foreground">
                        Need help? Contact us at{" "}
                        <a className="text-primary hover:underline" href="mailto:<EMAIL>">
                            <EMAIL>
                        </a>{" "}
                        or visit{" "}
                        <a
                            className="text-primary hover:underline"
                            href="https://ngora.mw"
                            rel="noopener noreferrer"
                            target="_blank"
                        >
                            www.ngora.mw
                        </a>
                    </p>
                </div>
            </div>
        </div>
    );
}
