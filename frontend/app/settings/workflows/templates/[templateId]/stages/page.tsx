"use client";

import React, { useEffect, useState } from "react";
import { useParams, useRouter } from "next/navigation";
import { useQuery } from "@tanstack/react-query";
import { Plus, Settings, GripVertical, Edit, Trash2, Users, Zap } from "lucide-react";
import { toast } from "sonner";
import { DragDropContext, Droppable, Draggable, DropResult } from "@hello-pangea/dnd";

import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { StageConfigurationModal } from "@/components/workflows/StageConfigurationModal";
import * as workflowService from "@/services/WorkflowService";
import { TemplateStageDto, StageFormData } from "@/types";
import { useDeleteConfirmation } from "@/context/DeleteItemContext";
import { useError } from "@/context/ErrorContext";

export default function TemplateStagesPage() {
    if (typeof document !== "undefined") {
        document.title = "Workflow Template Stages - myNGO";
    }
    
    const params = useParams();
    const router = useRouter();
    const templateId = params.templateId as string;
    const [isStageModalOpen, setIsStageModalOpen] = useState(false);
    const [editingStage, setEditingStage] = useState<TemplateStageDto | null>(null);

    const { openDeleteDialog, closeDeleteDialog, error: deleteError } = useDeleteConfirmation();
    const { showError, hideError } = useError();

    useEffect(() => {
        if (deleteError) {
            showError(deleteError, closeDeleteDialog);
        } else {
            hideError();
        }
    }, [deleteError]);

    // Fetch template details
    const {
        data: template,
        isLoading: templateLoading,
        error: templateError,
    } = useQuery({
        queryKey: ["workflow-template", templateId],
        queryFn: async () => {
            if (!templateId) return null;

            const response = await workflowService.getTemplateById(templateId);

            if (response.errors && response.errors.length > 0) {
                for (const error of response.errors) {
                    toast.error(error.message);
                }

                return null;
            }

            return response.data || null;
        },
        enabled: !!templateId,
    });

    // Fetch template stages
    const {
        data: stages = [],
        isLoading: stagesLoading,
        error: stagesError,
        refetch: refetchStages,
    } = useQuery({
        queryKey: ["template-stages", templateId],
        queryFn: async () => {
            if (!templateId) return [];

            const response = await workflowService.fetchTemplateStages(templateId);

            if (response.errors && response.errors.length > 0) {
                for (const error of response.errors) {
                    toast.error(error.message);
                }

                return [];
            }

            return response.data || [];
        },
        enabled: !!templateId,
    });

    const handleAddStage = () => {
        setEditingStage(null);
        setIsStageModalOpen(true);
    };

    const handleEditStage = (stage: TemplateStageDto) => {
        setEditingStage(stage);
        setIsStageModalOpen(true);
    };

    const onDeleteConfirmed = async (stage: TemplateStageDto) => {
        try {
            const response = await workflowService.voidTemplateStage(templateId, stage.id, "Deleted by user");

            if (response.errors && response.errors.length > 0) {
                for (const error of response.errors) {
                    toast.error(error.message);
                }

                return;
            }

            toast.success("Stage deleted successfully");
            closeDeleteDialog();
            refetchStages();
        } catch {
            toast.error("Failed to delete stage");
        }
    };

    const handleDeleteStage = async (stage: TemplateStageDto) => {
        openDeleteDialog({
            onConfirm: () => onDeleteConfirmed(stage),
            itemName: stage.name,
            title: "Delete Stage",
            description: `This will permanently delete the stage "${stage.name}". This action cannot be undone.`,
        });
    };

    const handleStageSubmit = async (data: StageFormData) => {
        try {
            const stageData = {
                name: data.name,
                description: data.description,
                position: data.position,
                is_active: true,
                roles: data.selectedRoles,
                triggers: data.selectedTriggers,
            };

            let response;

            if (editingStage) {
                response = await workflowService.updateTemplateStage(templateId, editingStage.id, stageData);
            } else {
                response = await workflowService.createTemplateStage(templateId, stageData);
            }

            if (response.errors && response.errors.length > 0) {
                for (const error of response.errors) {
                    toast.error(error.message);
                }

                return;
            }

            toast.success(editingStage ? "Stage updated successfully" : "Stage created successfully");
            setIsStageModalOpen(false);
            setEditingStage(null);
            refetchStages();
        } catch {
            toast.error("Failed to save stage");
        }
    };

    const handleDragEnd = (result: DropResult) => {
        if (!result.destination) return;

        const items = Array.from(stages);
        const [reorderedItem] = items.splice(result.source.index, 1);

        items.splice(result.destination.index, 0, reorderedItem);

        // TODO: Implement batch update for stage positions
        // For now, we'll just show a toast
        toast.info("Drag and drop reordering will be implemented in the next update");
    };

    const isLoading = templateLoading || stagesLoading;
    const error = templateError || stagesError;

    useEffect(() => {
        if (error) {
            showError(error?.message);
        } else {
            hideError();
        }
    }, [error]);

    if (isLoading) {
        return (
            <div className="flex flex-col space-y-6 p-6">
                <div className="flex items-center space-x-4">
                    <Skeleton className="h-8 w-8" />
                    <Skeleton className="h-8 w-32" />
                </div>
                <div className="space-y-2">
                    <Skeleton className="h-8 w-64" />
                    <Skeleton className="h-4 w-96" />
                </div>
                <div className="space-y-4">
                    {[...Array(3)].map((_, i) => (
                        <Skeleton key={i} className="h-32 w-full" />
                    ))}
                </div>
            </div>
        );
    }

    return (
        <div className="flex flex-col py-6">
            {/* Template Info */}
            <div className="space-y-2">
                <div className="flex flex-col-2 justify-between">
                    <div>
                        <h1 className="text-3xl font-bold tracking-tight">{template?.name}</h1>
                        <p className="text-muted-foreground">
                            {template?.description || "Manage the stages for this workflow template"}
                        </p>
                    </div>
                    <div className="flex items-center justify-end">
                        <Button className="flex items-center space-x-2" onClick={handleAddStage}>
                            <Plus className="h-4 w-4" />
                            <span>Add Stage </span>
                        </Button>
                    </div>
                </div>
                <div className="flex items-center space-x-2">
                    <Badge variant="outline">{template?.code}</Badge>
                    <Badge variant={template?.is_active ? "default" : "secondary"}>
                        {template?.is_active ? "Active" : "Inactive"}
                    </Badge>
                </div>
            </div>

            {/* Stages List */}
            <div className="space-y-4">
                <div className="flex items-center justify-between">
                    <h2 className="text-xl font-semibold">Workflow Stages</h2>
                    <p className="text-sm text-muted-foreground">
                        {stages.length} stage{stages.length !== 1 ? "s" : ""} configured
                    </p>
                </div>

                {stages.length === 0 ? (
                    <Card>
                        <CardContent className="flex flex-col items-center justify-center py-12">
                            <Settings className="h-12 w-12 text-muted-foreground mb-4" />
                            <h3 className="text-lg font-semibold mb-2">No Stages Configured</h3>
                            <p className="text-muted-foreground text-center mb-4">
                                Add stages to define the approval workflow for this template.
                            </p>
                            <Button onClick={handleAddStage}>
                                <Plus className="h-4 w-4 mr-2" />
                                Add First Stage
                            </Button>
                        </CardContent>
                    </Card>
                ) : (
                    <DragDropContext onDragEnd={handleDragEnd}>
                        <Droppable droppableId="stages">
                            {(provided) => (
                                <div {...provided.droppableProps} ref={provided.innerRef} className="space-y-3">
                                    {stages
                                        .sort((a, b) => a.position - b.position)
                                        .map((stage, index) => (
                                            <Draggable key={stage.id} draggableId={stage.id} index={index}>
                                                {(provided, snapshot) => (
                                                    <Card
                                                        ref={provided.innerRef}
                                                        {...provided.draggableProps}
                                                        className={`${
                                                            snapshot.isDragging ? "shadow-lg" : ""
                                                        } transition-shadow`}
                                                    >
                                                        <CardHeader className="pb-3">
                                                            <div className="flex items-center justify-between">
                                                                <div className="flex items-center space-x-3">
                                                                    <div
                                                                        {...provided.dragHandleProps}
                                                                        className="cursor-grab active:cursor-grabbing"
                                                                    >
                                                                        <GripVertical className="h-5 w-5 text-muted-foreground" />
                                                                    </div>
                                                                    <div>
                                                                        <CardTitle className="text-lg">
                                                                            {stage.position}. {stage.name}
                                                                        </CardTitle>
                                                                        <CardDescription>
                                                                            {stage.description ||
                                                                                "No description provided"}
                                                                        </CardDescription>
                                                                    </div>
                                                                </div>
                                                                <div className="flex items-center space-x-2">
                                                                    <Button
                                                                        size="sm"
                                                                        variant="ghost"
                                                                        onClick={() => handleEditStage(stage)}
                                                                    >
                                                                        <Edit className="h-4 w-4" />
                                                                    </Button>
                                                                    <Button
                                                                        className="text-destructive hover:text-destructive"
                                                                        size="sm"
                                                                        variant="ghost"
                                                                        onClick={() => handleDeleteStage(stage)}
                                                                    >
                                                                        <Trash2 className="h-4 w-4" />
                                                                    </Button>
                                                                </div>
                                                            </div>
                                                        </CardHeader>
                                                        <CardContent className="pt-0">
                                                            <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                                                                <div className="flex items-center space-x-1">
                                                                    <Users className="h-3 w-3" />
                                                                    <span>{stage.roles.length} roles</span>
                                                                </div>
                                                                <div className="flex items-center space-x-1">
                                                                    <Zap className="h-3 w-3" />
                                                                    <span>{stage.triggers.length} triggers</span>
                                                                </div>
                                                                <Badge
                                                                    className="text-xs"
                                                                    variant={stage.is_active ? "default" : "secondary"}
                                                                >
                                                                    {stage.is_active ? "Active" : "Inactive"}
                                                                </Badge>
                                                            </div>
                                                        </CardContent>
                                                    </Card>
                                                )}
                                            </Draggable>
                                        ))}
                                    {provided.placeholder}
                                </div>
                            )}
                        </Droppable>
                    </DragDropContext>
                )}
            </div>

            {/* Stage Configuration Modal */}
            <StageConfigurationModal
                isOpen={isStageModalOpen}
                nextPosition={stages.length + 1}
                stage={editingStage}
                onClose={() => {
                    setIsStageModalOpen(false);
                    setEditingStage(null);
                }}
                onSubmit={handleStageSubmit}
            />
        </div>
    );
}
