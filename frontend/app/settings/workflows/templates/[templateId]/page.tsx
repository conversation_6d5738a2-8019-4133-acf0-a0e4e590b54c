"use client";

import React from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { useQuery } from "@tanstack/react-query";
import { Settings, Edit, Users, Clock, CheckCircle, XCircle, Zap } from "lucide-react";
import { toast } from "sonner";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { StageCard } from "@/components/workflows/StageCard";
import * as workflowService from "@/services/WorkflowService";

export default function TemplateDetailPage() {
    if (typeof document !== "undefined") {
        document.title = "Workflow Template - myNGO";
    }
    
    const params = useParams();
    const router = useRouter();
    const templateId = params.templateId as string;

    // Fetch template details
    const {
        data: template,
        isLoading,
        error,
    } = useQuery({
        queryKey: ["workflow-template", templateId],
        queryFn: async () => {
            if (!templateId) return null;

            const response = await workflowService.getTemplateById(templateId);

            if (response.errors && response.errors.length > 0) {
                for (const error of response.errors) {
                    toast.error(error.message);
                }

                return null;
            }

            return response.data || null;
        },
        enabled: !!templateId,
    });

    const getTemplateIcon = (code: string) => {
        switch (code) {
            case "ORGANIZATION_REGISTRATION":
                return "🏢";
            case "LICENCE_RENEWAL":
                return "📋";
            case "PERMIT_APPLICATION":
                return "📄";
            default:
                return "⚙️";
        }
    };

    const getStatusBadge = (isActive: boolean) => {
        return isActive ? (
            <Badge className="flex items-center space-x-1" variant="default">
                <CheckCircle className="h-3 w-3" />
                <span>Active</span>
            </Badge>
        ) : (
            <Badge className="flex items-center space-x-1" variant="secondary">
                <XCircle className="h-3 w-3" />
                <span>Inactive</span>
            </Badge>
        );
    };

    if (isLoading) {
        return (
            <div className="flex flex-col space-y-6 p-6">
                <div className="flex items-center space-x-4">
                    <Skeleton className="h-8 w-8" />
                    <Skeleton className="h-8 w-32" />
                </div>
                <div className="space-y-2">
                    <Skeleton className="h-8 w-64" />
                    <Skeleton className="h-4 w-96" />
                </div>
                <Skeleton className="h-64 w-full" />
            </div>
        );
    }

    if (error || !template) {
        return (
            <div className="flex flex-col space-y-6 p-6">
                <Alert>
                    <AlertDescription>Failed to load template details. Please try again.</AlertDescription>
                </Alert>
            </div>
        );
    }

    const totalRoles = template.stages.reduce((acc, stage) => acc + stage.roles.length, 0);
    const totalTriggers = template.stages.reduce((acc, stage) => acc + stage.triggers.length, 0);

    return (
        <div className="flex flex-col py-6">
            {/* Header */}
            <div className="flex items-center justify-end py-6">
                <div className="flex items-center space-x-4">
                    <Button
                        variant="outline"
                        onClick={() => router.push(`/settings/workflows/templates/${templateId}/edit`)}
                    >
                        <Edit className="h-4 w-4 mr-2" />
                        Edit Template
                    </Button>
                    <Button onClick={() => router.push(`/settings/workflows/templates/${templateId}/stages`)}>
                        <Settings className="h-4 w-4 mr-2" />
                        Manage Stages
                    </Button>
                </div>
            </div>

            {/* Template Overview */}
            <Card>
                <CardHeader>
                    <div className="flex items-start justify-between">
                        <div className="flex items-center space-x-4">
                            <div className="text-4xl">{getTemplateIcon(template.code)}</div>
                            <div>
                                <CardTitle className="text-2xl">{template.name}</CardTitle>
                                <CardDescription className="text-base mt-1">
                                    {template.description || "No description provided"}
                                </CardDescription>
                            </div>
                        </div>
                        <div className="flex items-center space-x-2">
                            <Badge variant="outline">{template.code}</Badge>
                            {getStatusBadge(template.is_active)}
                        </div>
                    </div>
                </CardHeader>
                <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div className="flex items-center space-x-3">
                            <div className="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
                                <Clock className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                            </div>
                            <div>
                                <p className="text-sm text-muted-foreground">Total Stages</p>
                                <p className="text-2xl font-semibold">{template.stages.length}</p>
                            </div>
                        </div>
                        <div className="flex items-center space-x-3">
                            <div className="p-2 bg-green-100 dark:bg-green-900 rounded-lg">
                                <Users className="h-5 w-5 text-green-600 dark:text-green-400" />
                            </div>
                            <div>
                                <p className="text-sm text-muted-foreground">Total Roles</p>
                                <p className="text-2xl font-semibold">{totalRoles}</p>
                            </div>
                        </div>
                        <div className="flex items-center space-x-3">
                            <div className="p-2 bg-purple-100 dark:bg-purple-900 rounded-lg">
                                <Zap className="h-5 w-5 text-purple-600 dark:text-purple-400" />
                            </div>
                            <div>
                                <p className="text-sm text-muted-foreground">Total Triggers</p>
                                <p className="text-2xl font-semibold">{totalTriggers}</p>
                            </div>
                        </div>
                    </div>
                </CardContent>
            </Card>

            {/* Workflow Stages */}
            <div>
                <div className="flex items-center justify-between py-6">
                    <h2 className="text-xl font-semibold">Workflow Stages</h2>
                    <Button
                        variant="outline"
                        onClick={() => router.push(`/settings/workflows/templates/${templateId}/stages`)}
                    >
                        <Settings className="h-4 w-4 mr-2" />
                        Manage Stages
                    </Button>
                </div>

                {template.stages.length === 0 ? (
                    <Card>
                        <CardContent className="flex flex-col items-center justify-center py-12">
                            <Settings className="h-12 w-12 text-muted-foreground mb-4" />
                            <h3 className="text-lg font-semibold mb-2">No Stages Configured</h3>
                            <p className="text-muted-foreground text-center mb-4">
                                This workflow template doesn&apos;t have any stages configured yet.
                            </p>
                            <Button onClick={() => router.push(`/settings/workflows/templates/${templateId}/stages`)}>
                                <Settings className="h-4 w-4 mr-2" />
                                Configure Stages
                            </Button>
                        </CardContent>
                    </Card>
                ) : (
                    <div className="space-y-3">
                        {template.stages
                            .sort((a, b) => a.position - b.position)
                            .map((stage) => (
                                <StageCard
                                    key={stage.id}
                                    showDragHandle={false}
                                    stage={stage}
                                    onEdit={() => router.push(`/settings/workflows/templates/${templateId}/stages`)}
                                />
                            ))}
                    </div>
                )}
            </div>
        </div>
    );
}
