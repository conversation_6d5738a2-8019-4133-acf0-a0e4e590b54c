"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";

import { WorkflowTemplatesList } from "@/components/workflows/WorkflowTemplatesList";
import { CreateTemplateModal } from "@/components/workflows/CreateTemplateModal";
import { WorkflowTemplateCode } from "@/types";

export default function WorkflowSettingsPage() {
    if (typeof document !== "undefined") {
        document.title = "Workflow Settings - myNGO";
    }
    
    const router = useRouter();
    const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
    const [selectedTemplateType, setSelectedTemplateType] = useState<WorkflowTemplateCode | null>(null);

    const handleTemplateCreated = () => {
        setIsCreateModalOpen(false);
        setSelectedTemplateType(null);
        // Refresh the templates list
    };

    return (
        <div className="flex flex-col space-y-6 p-6">
            <div className="py-2">
                <h1 className="text-3xl font-bold tracking-tight">Workflow Management</h1>
                <p className="text-muted-foreground">Configure approval workflows for different business processes</p>
            </div>

            <WorkflowTemplatesList />

            {/* Create Template Modal */}
            <CreateTemplateModal
                isOpen={isCreateModalOpen}
                templateType={selectedTemplateType}
                onClose={() => setIsCreateModalOpen(false)}
                onTemplateCreated={handleTemplateCreated}
            />
        </div>
    );
}
