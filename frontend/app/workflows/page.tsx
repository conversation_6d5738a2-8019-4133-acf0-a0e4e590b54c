"use client";

import { useQuery } from "@tanstack/react-query";
import { FileText, XCircle } from "lucide-react";
import { useMemo, useState } from "react";

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { AdvancedWorkflowFilters } from "@/components/workflow/AdvancedWorkflowFilters";
import { WorkflowGridItem, WorkflowGridItemSkeleton } from "@/components/workflow/WorkflowGridItem";
import { WorkflowListItem, WorkflowListItemSkeleton } from "@/components/workflow/WorkflowListItem";
import { WorkflowStatsCards, WorkflowStatsHeader } from "@/components/workflow/WorkflowStatsCards";
import { WorkflowViewToggle } from "@/components/workflow/WorkflowViewToggle";
import { fetchWorkflowDashboard, fetchWorkflowStats } from "@/services/WorkflowService";
import { useWorkflowStore } from "@/stores/useWorkflowStore";
import { WorkflowDashboardFilter, WorkflowListDto } from "@/types";

export default function WorkflowsDashboard() {
    if (typeof document !== "undefined") {
        document.title = "Workflows - myNGO";
    }
    
    const [viewMode, setViewMode] = useState<"list" | "grid">("list");

    const { filters, setFilters, setWorkflows } = useWorkflowStore();

    // Convert store filters to API format
    const apiFilters = useMemo((): WorkflowDashboardFilter => {
        const filter: WorkflowDashboardFilter = {};

        if (filters.search) filter.search = filters.search;
        if (filters.status && filters.status !== "all") filter.status = filters.status.toUpperCase();
        if (filters.priority && filters.priority !== "all") filter.priority = filters.priority.toUpperCase();
        if (filters.assigned_to_me) filter.assigned_to_me = filters.assigned_to_me;
        if (filters.application_type && filters.application_type !== "all")
            filter.application_type = filters.application_type.toUpperCase();

        return filter;
    }, [filters]);

    const {
        data: stats,
        isLoading: statsLoading,
        error: statsError,
    } = useQuery({
        queryKey: ["workflow-stats"],
        queryFn: async () => {
            const response = await fetchWorkflowStats();

            if (!response.success) {
                console.log(response.errors);
                throw new Error("Failed to fetch workflow stats");
            }

            return response.data;
        },
        staleTime: 5 * 60 * 1000,
        gcTime: 10 * 60 * 1000,
    });

    const {
        data: workflowsResponse,
        isLoading: workflowsLoading,
        error: workflowsError,
        refetch: refetchWorkflows,
    } = useQuery({
        queryKey: ["workflow-dashboard", apiFilters],
        queryFn: async () => {
            const response = await fetchWorkflowDashboard(apiFilters);

            if (!response.success) {
                console.log(response.errors);
                throw new Error("Failed to fetch applications");
            }

            return response;
        },
        staleTime: 2 * 60 * 1000,
        gcTime: 5 * 60 * 1000,
    });

    const workflows = workflowsResponse?.data || [];
    const totalWorkflows = workflowsResponse?.total || 0;

    // Update store workflows when data is loaded
    useMemo(() => {
        if (workflows.length > 0) {
            setWorkflows(workflows);
        }
    }, [workflows, setWorkflows]);

    // Error handling
    if (workflowsError?.message) {
        return (
            <div className="max-w-7xl mx-auto p-6 space-y-6">
                <Card>
                    <CardContent className="text-center py-12">
                        <XCircle className="h-12 w-12 mx-auto text-red-400 mb-4" />
                        <h3 className="text-lg font-semibold mb-2">Error Loading Dashboard</h3>
                        <p className="text-gray-500 mb-4">
                            {statsError?.message || workflowsError?.message || "Something went wrong"}
                        </p>
                        <button
                            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                            onClick={() => refetchWorkflows()}
                        >
                            Retry
                        </button>
                    </CardContent>
                </Card>
            </div>
        );
    }

    return (
        <div className="w-full p-6 space-y-6 max-w-7xl mx-auto">
            {/* Header */}
            <div className="flex items-center justify-between">
                <div>
                    <h1 className="text-3xl font-bold tracking-tight text-foreground">Applications Dashboard</h1>
                    <p className="text-muted-foreground mt-1">Monitor and manage all workflow applications</p>
                </div>
                <div className="flex items-center gap-4">
                    <WorkflowStatsHeader isLoading={statsLoading} stats={stats} />
                </div>
            </div>

            {/* Stats Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-7 gap-4">
                <WorkflowStatsCards isLoading={statsLoading} stats={stats} />
            </div>

            {/* Advanced Filters */}
            <AdvancedWorkflowFilters onFiltersChange={(newFilters) => setFilters(newFilters)} />

            {/* View Mode Toggle & Applications */}
            <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-semibold text-foreground">
                    Applications ({workflowsLoading ? "..." : totalWorkflows})
                </h2>
                <WorkflowViewToggle viewMode={viewMode} onViewModeChange={setViewMode} />
            </div>

            {/* List View */}
            {viewMode === "list" && (
                <Card className="border-border bg-background/50 backdrop-blur">
                    <CardHeader>
                        <CardTitle className="text-foreground">Applications</CardTitle>
                        <CardDescription className="text-muted-foreground">
                            Click on any workflow to view detailed information
                        </CardDescription>
                    </CardHeader>
                    <CardContent className="p-0">
                        <div className="divide-y">
                            {workflowsLoading
                                ? Array.from({ length: 5 }).map((_, i) => <WorkflowListItemSkeleton key={i} />)
                                : workflows.map((workflow: WorkflowListDto) => (
                                      <WorkflowListItem key={workflow.id} workflow={workflow} />
                                  ))}
                        </div>
                    </CardContent>
                </Card>
            )}

            {/* Grid View */}
            {viewMode === "grid" && (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {workflowsLoading
                        ? Array.from({ length: 6 }).map((_, i) => <WorkflowGridItemSkeleton key={i} />)
                        : workflows.map((workflow: WorkflowListDto) => (
                              <WorkflowGridItem key={workflow.id} workflow={workflow} />
                          ))}
                </div>
            )}

            {/* Empty State */}
            {!workflowsLoading && workflows.length === 0 && (
                <Card className="border-border bg-background/50 backdrop-blur">
                    <CardContent className="text-center py-12">
                        <FileText className="h-12 w-12 mx-auto text-muted-foreground/50 mb-4" />
                        <h3 className="text-lg font-semibold text-foreground mb-2">No applications found</h3>
                        <p className="text-muted-foreground">
                            Try adjusting your search terms or filters to find applications.
                        </p>
                    </CardContent>
                </Card>
            )}
        </div>
    );
}
