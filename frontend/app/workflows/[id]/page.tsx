"use client";

import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { <PERSON>ertCircle, ArrowLeft, Building2, Calendar, User } from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { use, useCallback, useState } from "react";
import { toast } from "sonner";

import { TiptapEditor } from "@/components/inputs/editor";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { ApplicationDocuments } from "@/components/workflow/ApplicationDocuments";
import { WorkflowProgress } from "@/components/workflow/WorkflowProgress";
import { useAuth } from "@/composables/useStore";
import { approveWorkflowStage, getWorkflowById, rejectWorkflowStage } from "@/services/WorkflowService";
import { WorkflowActionRequest, WorkflowStageDetailDto } from "@/types";

type StageStatus = "IN_REVIEW" | "APPROVED" | "COMPLETED" | "REJECTED";

interface ActionDialogState {
    open: boolean;
    action: "APPROVE" | "REJECT" | null;
    stageId?: string;
    stageName?: string;
}

export default function WorkflowUI({ params }: { params: Promise<{ id: string }> }) {
    if (typeof document !== "undefined") {
        document.title = "Workflow Details - myNGO";
    }
    
    const { session } = useAuth();
    const router = useRouter();
    const { id: workflowId } = use(params);
    const queryClient = useQueryClient();

    const [actionDialog, setActionDialog] = useState<ActionDialogState>({
        open: false,
        action: null,
    });
    const [comments, setComments] = useState<string>("");
    const [rejectionReason, setRejectionReason] = useState<string>("");

    const {
        data: workflowData,
        isLoading,
        error,
        refetch,
    } = useQuery({
        queryKey: ["workflow", workflowId],
        queryFn: () => getWorkflowById(workflowId),
        enabled: !!workflowId,
        select: (response) => response.data,
    });

    const approveMutation = useMutation({
        mutationFn: ({
            workflowId,
            stageId,
            payload,
        }: {
            workflowId: string;
            stageId: string;
            payload: WorkflowActionRequest;
        }) => approveWorkflowStage(workflowId, stageId, payload),
        onSuccess: (response) => {
            if (response.errors.length) {
                for (const error of response.errors) {
                    toast.error(error.message || "An error occurred while approving the stage");
                }
            }

            if (response.success) {
                toast.success("Stage approved successfully");
            }

            setActionDialog({ open: false, action: null });
            setComments("");

            queryClient.setQueryData(["workflow", workflowId], response);
            queryClient.invalidateQueries({ queryKey: ["workflow", workflowId] });
            queryClient.invalidateQueries({ queryKey: ["workflow-dashboard"] });
        },
        onError: (error: any) => {
            toast.error(error?.response?.data?.message || "Failed to approve stage");
        },
    });

    const rejectMutation = useMutation({
        mutationFn: ({
            workflowId,
            stageId,
            payload,
        }: {
            workflowId: string;
            stageId: string;
            payload: WorkflowActionRequest;
        }) => rejectWorkflowStage(workflowId, stageId, payload),
        onSuccess: (response) => {
            toast.success("Stage rejected successfully");
            setActionDialog({ open: false, action: null });
            setComments("");
            setRejectionReason("");

            // queryClient.setQueryData(["workflow", workflowId], response);
            queryClient.invalidateQueries({ queryKey: ["workflow", workflowId] });
            // queryClient.invalidateQueries({ queryKey: ["workflow-dashboard"] });
        },
        onError: (error: any) => {
            toast.error(error?.response?.data?.message || "Failed to reject stage");
        },
    });

    const canActOnStage = useCallback(
        (stage: WorkflowStageDetailDto) => {
            if (!stage.created_at) {
                return false;
            }

            if (!session?.role_id) return false;

            const stageRoles = stage.template_stage.roles.map((role) => role.role_id);

            return stageRoles.includes(session.role_id);
        },
        [session],
    );

    const formatDate = (dateString: string | null | undefined): string => {
        if (!dateString) return "Not started";

        return new Date(dateString).toLocaleDateString("en-US", {
            month: "short",
            day: "numeric",
            year: "numeric",
            hour: "2-digit",
            minute: "2-digit",
        });
    };

    const handleStageAction = (action: "APPROVE" | "REJECT", stageId: string, stageName: string) => {
        setActionDialog({
            open: true,
            action,
            stageId,
            stageName,
        });
    };

    const submitAction = () => {
        if (!actionDialog.stageId || !actionDialog.action) return;

        const payload: WorkflowActionRequest = {};

        if (actionDialog.action === "APPROVE") {
            if (comments) {
                payload.comment = comments;
            }
            approveMutation.mutate({
                workflowId,
                stageId: actionDialog.stageId,
                payload,
            });
        } else {
            if (rejectionReason) {
                payload.reason = rejectionReason;
            }
            rejectMutation.mutate({
                workflowId,
                stageId: actionDialog.stageId,
                payload,
            });
        }
    };

    const closeDialog = () => {
        setActionDialog({ open: false, action: null });
        setComments("");
        setRejectionReason("");
    };

    if (isLoading) {
        return (
            <div className="max-w-7xl mx-auto p-6 space-y-6 min-h-screen">
                <div className="flex items-center justify-center py-8">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary" />
                    <span className="ml-2 text-foreground">Loading workflow...</span>
                </div>
            </div>
        );
    }

    if (error || !workflowData) {
        return (
            <div className="max-w-7xl mx-auto p-6 space-y-6 min-h-screen">
                <Button variant="outline" onClick={() => router.push("/workflows")}>
                    <ArrowLeft />
                    Back to Approvals
                </Button>
                <div className="flex items-center justify-center py-8">
                    <div className="text-center">
                        <AlertCircle className="h-12 w-12 text-destructive mx-auto mb-2" />
                        <h3 className="text-lg font-semibold text-foreground">Error loading workflow</h3>
                        <p className="text-muted-foreground mt-1">{error?.message || "Failed to load workflow data"}</p>
                        <Button className="mt-4" onClick={() => refetch()}>
                            Try Again
                        </Button>
                    </div>
                </div>
            </div>
        );
    }

    return (
        <div className="max-w-6xl mx-auto space-y-6 min-h-screen py-6">
            <Button variant="outline" onClick={() => router.push("/workflows")}>
                <ArrowLeft />
                Back to Approvals
            </Button>

            {/* Header */}
            <div className="flex items-center justify-between">
                <div>
                    <h1 className="text-3xl font-bold tracking-tight text-foreground">Approval History</h1>
                    <p className="text-muted-foreground mt-1">Manage and track application approval workflows</p>
                </div>
                <Badge className="px-3 py-1" variant="outline">
                    {workflowData.application.type.replace("_", " ")}
                </Badge>
            </div>

            {/* Application Overview Card */}
            <Card className="border-border/50 bg-card/50 backdrop-blur-sm">
                <CardHeader>
                    <div className="flex items-center justify-between">
                        <div>
                            <CardTitle className="flex items-center gap-2 text-foreground hover:underline">
                                <Building2 className="h-5 w-5" />
                                <Link href={`/organizations/${workflowData.application.organization.id}`}>
                                    {workflowData.application.organization.name}
                                </Link>
                            </CardTitle>
                            <CardDescription className="mt-1">
                                Application Code:{" "}
                                <span className="font-mono font-medium">{workflowData.application.code}</span>
                            </CardDescription>
                        </div>
                        <div className="flex items-center gap-4">
                            <ApplicationDocuments documents={workflowData.application.documents} />
                            <div className="text-right text-sm text-muted-foreground">
                                <div className="flex items-center gap-1">
                                    <User className="h-4 w-4" />
                                    Submitted by {workflowData.application?.creator?.handle || "Unknown User"}
                                </div>
                                <div className="flex items-center gap-1 mt-1">
                                    <Calendar className="h-4 w-4" />
                                    {formatDate(workflowData.application.created_at)}
                                </div>
                            </div>
                        </div>
                    </div>
                </CardHeader>
            </Card>

            {/* Enhanced Workflow Progress */}
            <WorkflowProgress
                canActOnStage={canActOnStage}
                isApproving={approveMutation.isPending}
                isRejecting={rejectMutation.isPending}
                stages={workflowData.stages}
                templateStages={workflowData.template.stages}
                workflowCreatedAt={workflowData.application.created_at}
                onApprove={(stageId, stageName) => handleStageAction("APPROVE", stageId, stageName)}
                onReject={(stageId, stageName) => handleStageAction("REJECT", stageId, stageName)}
            />

            {/* Next Stage Info */}
            {workflowData.next_stage && (
                <Card className="border-blue-200/50 bg-gradient-to-r from-blue-50/80 to-cyan-50/80 dark:from-blue-950/50 dark:to-cyan-950/50 backdrop-blur-sm">
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2 text-blue-900 dark:text-blue-100">
                            <AlertCircle className="h-5 w-5" />
                            Next Stage
                        </CardTitle>
                        <CardDescription className="text-blue-700 dark:text-blue-300">
                            {workflowData.next_stage.name} - {workflowData.next_stage.description}
                        </CardDescription>
                    </CardHeader>
                </Card>
            )}

            {/* Action Dialog */}
            <Dialog open={actionDialog.open} onOpenChange={closeDialog}>
                <DialogContent className="sm:max-w-md bg-background/95 backdrop-blur-sm border-border/50">
                    <DialogHeader>
                        <DialogTitle className="text-foreground">
                            {actionDialog.action === "APPROVE" ? "Approve Stage" : "Reject Stage"}
                        </DialogTitle>
                        <DialogDescription className="text-muted-foreground">
                            {actionDialog.action === "APPROVE"
                                ? `Are you sure you want to approve "${actionDialog.stageName}"? This action will move the workflow to the next step.`
                                : `Please provide a reason for rejecting "${actionDialog.stageName}". This will notify the applicant.`}
                        </DialogDescription>
                    </DialogHeader>

                    <div className="space-y-4">
                        {/* Rejection Reason - Only show for REJECT */}
                        {actionDialog.action === "REJECT" && (
                            <div>
                                <Label
                                    className="text-sm font-medium text-destructive mb-2 block"
                                    htmlFor="rejection-reason"
                                >
                                    Rejection Reason *
                                </Label>
                                <TiptapEditor
                                    className="min-h-[120px] border-destructive/20 focus-within:border-destructive/50"
                                    content={rejectionReason}
                                    limitUnit="char"
                                    maxLimit={500}
                                    placeholder="Please explain in detail why this stage is being rejected..."
                                    onChange={setRejectionReason}
                                />
                            </div>
                        )}

                        {/* Comments - Only show for APPROVE */}
                        {actionDialog.action === "APPROVE" && (
                            <div>
                                <Label className="text-sm font-medium mb-2 block text-foreground" htmlFor="comments">
                                    Comments (Optional)
                                </Label>
                                <TiptapEditor
                                    className="min-h-[100px]"
                                    content={comments}
                                    limitUnit="char"
                                    maxLimit={300}
                                    placeholder="Add any additional comments or instructions..."
                                    onChange={setComments}
                                />
                            </div>
                        )}

                        <div className="flex justify-end gap-2">
                            <Button variant="outline" onClick={closeDialog}>
                                Cancel
                            </Button>
                            <Button
                                className={
                                    actionDialog.action === "APPROVE"
                                        ? "bg-emerald-600 hover:bg-emerald-700 text-white shadow-lg"
                                        : "bg-red-600 hover:bg-red-700 text-white shadow-lg"
                                }
                                disabled={
                                    approveMutation.isPending ||
                                    rejectMutation.isPending ||
                                    (actionDialog.action === "REJECT" && !rejectionReason.trim())
                                }
                                onClick={submitAction}
                            >
                                {(approveMutation.isPending || rejectMutation.isPending) && (
                                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                                )}
                                {actionDialog.action === "APPROVE" ? "Approve Stage" : "Reject Stage"}
                            </Button>
                        </div>
                    </div>
                </DialogContent>
            </Dialog>
        </div>
    );
}
