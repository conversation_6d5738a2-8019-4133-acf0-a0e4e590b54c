"use client";

import { useState } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import {
    ArrowLeft,
    Clock,
    Calendar,
    User,
    Building,
    FileText,
    Phone,
    Mail,
    AlertCircle,
    CheckCircle,
    TrendingUp,
    History,
    MessageSquare,
    Save,
} from "lucide-react";
import { format, formatDistanceToNow } from "date-fns";

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import {
    ComplaintStatusUpdateRequest,
    ComplaintStatusType,
    STATUS_LABELS,
    STATUS_COLORS,
    PRIORITY_LABELS,
    PRIORITY_COLORS,
} from "@/types";
import { fetchComplaintById, updateComplaintStatus } from "@/services/ComplaintService";

export default function ComplaintDetailPage() {
    if (typeof document !== "undefined") {
        document.title = "Complaint Details - myNGO";
    }
    
    const params = useParams();
    const router = useRouter();
    const queryClient = useQueryClient();
    const complaintId = params.id as string;
    const [showStatusUpdate, setShowStatusUpdate] = useState(false);
    const [statusUpdate, setStatusUpdate] = useState<ComplaintStatusUpdateRequest>({
        status: ComplaintStatusType.OPEN,
        resolution: "",
        internal_notes: "",
        public_response: "",
    });

    const {
        data: complaintResponse,
        isLoading,
        error,
        refetch,
    } = useQuery({
        queryKey: ["complaint", complaintId],
        queryFn: () => fetchComplaintById(complaintId),
        enabled: !!complaintId,
    });

    const complaint = complaintResponse?.data;

    const statusUpdateMutation = useMutation({
        mutationFn: (data: ComplaintStatusUpdateRequest) => updateComplaintStatus(complaintId, data),
        onSuccess: () => {
            toast.success("Complaint status updated successfully");
            setShowStatusUpdate(false);
            queryClient.invalidateQueries({ queryKey: ["complaint", complaintId] });
            queryClient.invalidateQueries({ queryKey: ["complaint-statistics"] });
            queryClient.invalidateQueries({ queryKey: ["complaint-dashboard"] });
        },
        onError: () => {
            toast.error("Failed to update complaint status");
        },
    });

    const handleStatusUpdate = () => {
        statusUpdateMutation.mutate(statusUpdate);
    };

    const getStatusIcon = (status: string) => {
        switch (status) {
            case "OPEN":
                return <AlertCircle className="w-5 h-5 text-yellow-600" />;
            case "IN_PROGRESS":
                return <Clock className="w-5 h-5 text-blue-600" />;
            case "RESOLVED":
                return <CheckCircle className="w-5 h-5 text-green-600" />;
            case "ESCALATED":
                return <TrendingUp className="w-5 h-5 text-red-600" />;
            case "CLOSED":
                return <CheckCircle className="w-5 h-5 text-gray-600" />;
            default:
                return <FileText className="w-5 h-5 text-gray-600" />;
        }
    };

    if (isLoading) {
        return (
            <div className="flex items-center justify-center min-h-[400px]">
                <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4" />
                    <p className="text-gray-600">Loading complaint details...</p>
                </div>
            </div>
        );
    }

    if (error) {
        return (
            <div className="max-w-2xl mx-auto">
                <Alert>
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>Error loading complaint details. Please try again.</AlertDescription>
                </Alert>
            </div>
        );
    }

    if (!complaint) {
        return (
            <div className="max-w-2xl mx-auto">
                <Alert>
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>Complaint not found or you don't have permission to view it.</AlertDescription>
                </Alert>
            </div>
        );
    }

    return (
        <div className="max-w-6xl mx-auto space-y-6">
            <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                    <Button size="sm" variant="outline" onClick={() => router.back()}>
                        <ArrowLeft className="w-4 h-4 mr-2" />
                        Back
                    </Button>
                    <div>
                        <h1 className="text-2xl font-bold text-gray-900">Complaint Details</h1>
                        <p className="text-sm text-gray-500">
                            Tracking Code: <span className="font-mono font-medium">{complaint.tracking_code}</span>
                        </p>
                    </div>
                </div>
                <div className="flex items-center space-x-2">
                    <Badge className={STATUS_COLORS[complaint.status as keyof typeof STATUS_COLORS]}>
                        {STATUS_LABELS[complaint.status as keyof typeof STATUS_LABELS]}
                    </Badge>
                    <Button
                        variant={showStatusUpdate ? "secondary" : "default"}
                        onClick={() => setShowStatusUpdate(!showStatusUpdate)}
                    >
                        {showStatusUpdate ? "Cancel" : "Update Status"}
                    </Button>
                </div>
            </div>

            <div className="grid gap-6 lg:grid-cols-3">
                <div className="lg:col-span-2 space-y-6">
                    <Card>
                        <CardHeader>
                            <div className="flex items-start justify-between">
                                <div className="flex-1">
                                    <CardTitle className="text-xl mb-2">{complaint.title}</CardTitle>
                                    <CardDescription className="text-base leading-relaxed">
                                        {complaint.summary}
                                    </CardDescription>
                                </div>
                                <div className="ml-4">{getStatusIcon(complaint.status)}</div>
                            </div>
                        </CardHeader>
                    </Card>

                    {showStatusUpdate && (
                        <Card className="border-blue-200 bg-blue-50">
                            <CardHeader>
                                <CardTitle className="text-blue-900 flex items-center">
                                    <MessageSquare className="w-5 h-5 mr-2" />
                                    Update Complaint Status
                                </CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="grid gap-2">
                                    <Label htmlFor="status">New Status</Label>
                                    <Select
                                        value={statusUpdate.status}
                                        onValueChange={(value) =>
                                            setStatusUpdate({ ...statusUpdate, status: value as ComplaintStatusType })
                                        }
                                    >
                                        <SelectTrigger>
                                            <SelectValue />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {Object.values(ComplaintStatusType).map((status) => (
                                                <SelectItem key={status} value={status}>
                                                    <div className="flex items-center">
                                                        {getStatusIcon(status)}
                                                        <span className="ml-2">{STATUS_LABELS[status]}</span>
                                                    </div>
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                </div>

                                <div className="grid gap-2">
                                    <Label htmlFor="public_response">Public Response (visible to complainant)</Label>
                                    <Textarea
                                        id="public_response"
                                        placeholder="Response that will be shared with the complainant..."
                                        rows={3}
                                        value={statusUpdate.public_response}
                                        onChange={(e) =>
                                            setStatusUpdate({ ...statusUpdate, public_response: e.target.value })
                                        }
                                    />
                                </div>

                                <div className="grid gap-2">
                                    <Label htmlFor="resolution">Resolution Details</Label>
                                    <Textarea
                                        id="resolution"
                                        placeholder="Detailed resolution information..."
                                        rows={3}
                                        value={statusUpdate.resolution}
                                        onChange={(e) =>
                                            setStatusUpdate({ ...statusUpdate, resolution: e.target.value })
                                        }
                                    />
                                </div>

                                <div className="grid gap-2">
                                    <Label htmlFor="internal_notes">Internal Notes (not visible to complainant)</Label>
                                    <Textarea
                                        id="internal_notes"
                                        placeholder="Internal notes for staff reference..."
                                        rows={2}
                                        value={statusUpdate.internal_notes}
                                        onChange={(e) =>
                                            setStatusUpdate({ ...statusUpdate, internal_notes: e.target.value })
                                        }
                                    />
                                </div>

                                <div className="flex gap-2 pt-2">
                                    <Button
                                        className="flex-1"
                                        disabled={statusUpdateMutation.isPending}
                                        onClick={handleStatusUpdate}
                                    >
                                        <Save className="w-4 h-4 mr-2" />
                                        {statusUpdateMutation.isPending ? "Updating..." : "Update Status"}
                                    </Button>
                                    <Button
                                        className="flex-1"
                                        variant="outline"
                                        onClick={() => setShowStatusUpdate(false)}
                                    >
                                        Cancel
                                    </Button>
                                </div>
                            </CardContent>
                        </Card>
                    )}

                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center">
                                <History className="w-5 h-5 mr-2" />
                                Timeline
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-4">
                                <div className="flex items-start">
                                    <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-4 mt-1">
                                        <FileText className="w-4 h-4 text-blue-600" />
                                    </div>
                                    <div className="flex-1">
                                        <div className="font-medium">Complaint Submitted</div>
                                        <div className="text-sm text-gray-500">
                                            {format(new Date(complaint.created_at), "MMM d, yyyy 'at' h:mm a")}
                                        </div>
                                        <div className="text-sm text-gray-600 mt-1">
                                            Complaint received and assigned tracking code {complaint.tracking_code}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                </div>

                <div className="space-y-6">
                    <Card>
                        <CardHeader>
                            <CardTitle>Complaint Information</CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="space-y-3">
                                <div className="flex items-center justify-between text-sm">
                                    <span className="text-gray-600">Priority</span>
                                    <Badge
                                        className={PRIORITY_COLORS[complaint.priority as keyof typeof PRIORITY_COLORS]}
                                    >
                                        {PRIORITY_LABELS[complaint.priority as keyof typeof PRIORITY_LABELS]}
                                    </Badge>
                                </div>

                                <div className="flex items-center justify-between text-sm">
                                    <span className="text-gray-600">Status</span>
                                    <Badge className={STATUS_COLORS[complaint.status as keyof typeof STATUS_COLORS]}>
                                        {STATUS_LABELS[complaint.status as keyof typeof STATUS_LABELS]}
                                    </Badge>
                                </div>

                                <Separator />

                                <div className="flex items-center text-sm">
                                    <Calendar className="w-4 h-4 mr-2 text-gray-500" />
                                    <div>
                                        <div className="text-gray-600">Submitted</div>
                                        <div className="font-medium">
                                            {format(new Date(complaint.created_at), "MMM d, yyyy")}
                                        </div>
                                    </div>
                                </div>

                                <div className="flex items-center text-sm">
                                    <Clock className="w-4 h-4 mr-2 text-gray-500" />
                                    <div>
                                        <div className="text-gray-600">Time Elapsed</div>
                                        <div className="font-medium">
                                            {formatDistanceToNow(new Date(complaint.created_at), { addSuffix: true })}
                                        </div>
                                    </div>
                                </div>

                                {complaint.organization_id && (
                                    <div className="flex items-center text-sm">
                                        <Building className="w-4 h-4 mr-2 text-gray-500" />
                                        <div>
                                            <div className="text-gray-600">Organization</div>
                                            <div className="font-medium">Related NGO</div>
                                        </div>
                                    </div>
                                )}
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader>
                            <CardTitle>Complainant Information</CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-3">
                            {complaint.is_anonymous ? (
                                <div className="text-center py-4">
                                    <div className="w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-2">
                                        <User className="w-6 h-6 text-gray-400" />
                                    </div>
                                    <div className="text-sm text-gray-600">Anonymous Complaint</div>
                                    <div className="text-xs text-gray-500 mt-1">No contact information available</div>
                                </div>
                            ) : (
                                <div className="space-y-3">
                                    {complaint.complainant_name && (
                                        <div className="flex items-center text-sm">
                                            <User className="w-4 h-4 mr-2 text-gray-500" />
                                            <div>
                                                <div className="text-gray-600">Name</div>
                                                <div className="font-medium">{complaint.complainant_name}</div>
                                            </div>
                                        </div>
                                    )}

                                    {complaint.complainant_email && (
                                        <div className="flex items-center text-sm">
                                            <Mail className="w-4 h-4 mr-2 text-gray-500" />
                                            <div>
                                                <div className="text-gray-600">Email</div>
                                                <div className="font-medium">{complaint.complainant_email}</div>
                                            </div>
                                        </div>
                                    )}

                                    {complaint.complainant_phone && (
                                        <div className="flex items-center text-sm">
                                            <Phone className="w-4 h-4 mr-2 text-gray-500" />
                                            <div>
                                                <div className="text-gray-600">Phone</div>
                                                <div className="font-medium">{complaint.complainant_phone}</div>
                                            </div>
                                        </div>
                                    )}
                                </div>
                            )}
                        </CardContent>
                    </Card>
                </div>
            </div>
        </div>
    );
}
