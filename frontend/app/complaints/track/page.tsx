"use client";

import { format, formatDistanceToNow } from "date-fns";
import {
    AlertCircle,
    Building,
    Calendar,
    CheckCircle,
    Clock,
    FileText,
    Info,
    Search,
    Tag,
    TrendingUp,
    User,
} from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";

import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { trackComplaint } from "@/services/ComplaintService";
import { ComplaintDTO, PRIORITY_COLORS, PRIORITY_LABELS, STATUS_COLORS, STATUS_LABELS } from "@/types";

export default function TrackComplaintPage() {
    if (typeof document !== "undefined") {
        document.title = "Track Complaint - myNGO";
    }
    const [trackingCode, setTrackingCode] = useState("");
    const [complaint, setComplaint] = useState<ComplaintDTO | null>(null);
    const [loading, setLoading] = useState(false);
    const [searched, setSearched] = useState(false);

    const handleTrack = async () => {
        if (!trackingCode.trim()) {
            toast.error("Please enter a tracking code");

            return;
        }

        setLoading(true);
        setSearched(true);
        try {
            const response = await trackComplaint(trackingCode.trim());

            if (response.success && response.data) {
                setComplaint(response.data);
                toast.success("Complaint found!");
            } else {
                setComplaint(null);
                toast.error("Complaint not found. Please check your tracking code.");
            }
        } catch (error) {
            setComplaint(null);
            toast.error("Failed to track complaint. Please try again.");
        } finally {
            setLoading(false);
        }
    };

    const getStatusIcon = (status: string) => {
        switch (status) {
            case "OPEN":
                return <AlertCircle className="w-5 h-5 text-yellow-600" />;
            case "IN_PROGRESS":
                return <Clock className="w-5 h-5 text-blue-600" />;
            case "RESOLVED":
                return <CheckCircle className="w-5 h-5 text-green-600" />;
            case "ESCALATED":
                return <TrendingUp className="w-5 h-5 text-red-600" />;
            case "CLOSED":
                return <CheckCircle className="w-5 h-5 text-gray-600" />;
            default:
                return <FileText className="w-5 h-5 text-gray-600" />;
        }
    };

    const getStatusDescription = (status: string) => {
        switch (status) {
            case "OPEN":
                return "Your complaint has been received and is awaiting initial review.";
            case "IN_PROGRESS":
                return "Your complaint is being actively investigated by NGORA staff.";
            case "RESOLVED":
                return "Your complaint has been resolved. A response has been prepared.";
            case "ESCALATED":
                return "Your complaint requires higher-level attention and has been escalated.";
            case "CLOSED":
                return "Your complaint has been fully processed and closed.";
            default:
                return "Status information is not available.";
        }
    };

    return (
        <div className="max-w-4xl mx-auto">
            <div className="text-center mb-8">
                <h1 className="text-4xl font-bold mb-4">Track Your Complaint</h1>
                <p className="text-xl text-gray-600 max-w-2xl mx-auto">
                    Enter your tracking code to check the status of your complaint with NGORA
                </p>
            </div>

            <div className="grid gap-8 grid-cols-1">
                <div className="space-y-6">
                    <Card className="shadow-lg">
                        <CardHeader>
                            <CardTitle className="flex items-center">
                                <Search className="w-5 h-5 mr-2" />
                                Enter Tracking Code
                            </CardTitle>
                            <CardDescription>
                                Enter the tracking code you received when you submitted your complaint
                            </CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="grid gap-2">
                                <Label htmlFor="tracking">Tracking Code</Label>
                                <div className="flex gap-2">
                                    <Input
                                        className="font-mono text-base"
                                        id="tracking"
                                        placeholder="e.g., CPL202507R0KM"
                                        value={trackingCode}
                                        onChange={(e) => setTrackingCode(e.target.value.toUpperCase())}
                                        onKeyPress={(e) => e.key === "Enter" && handleTrack()}
                                    />
                                    <Button disabled={loading} onClick={handleTrack}>
                                        {loading ? "Searching..." : "Track"}
                                    </Button>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    {searched && !complaint && (
                        <Alert>
                            <AlertCircle className="h-4 w-4" />
                            <AlertDescription>
                                <strong>Complaint not found.</strong> Please check your tracking code and try again.
                                Make sure you&apos;ve entered the code exactly as provided.
                            </AlertDescription>
                        </Alert>
                    )}

                    {complaint && (
                        <Card className="shadow-lg">
                            <CardHeader>
                                <div className="flex items-center justify-between">
                                    <div>
                                        <CardTitle className="flex items-center text-xl">
                                            {getStatusIcon("OPEN")}
                                            <span className="ml-2">Complaint Details</span>
                                        </CardTitle>
                                        <CardDescription className="mt-2">
                                            Tracking Code:{" "}
                                            <span className="font-mono font-medium">{complaint.tracking_code}</span>
                                        </CardDescription>
                                    </div>
                                    <Badge className={STATUS_COLORS["OPEN" as keyof typeof STATUS_COLORS]}>
                                        {STATUS_LABELS["OPEN" as keyof typeof STATUS_LABELS]}
                                    </Badge>
                                </div>
                            </CardHeader>
                            <CardContent className="space-y-6">
                                <div>
                                    <h3 className="font-semibold text-lg mb-2">{complaint.title}</h3>
                                    <p className="text-gray-700 leading-relaxed">{complaint.summary}</p>
                                </div>

                                <Separator />

                                <div className="grid gap-4 md:grid-cols-2">
                                    <div className="space-y-3">
                                        <div className="flex items-center text-sm">
                                            <Calendar className="w-4 h-4 mr-2 text-gray-500" />
                                            <span className="text-gray-600">Submitted:</span>
                                            <span className="ml-2 font-medium">
                                                {format(new Date(complaint.created_at), "MMM d, yyyy")}
                                            </span>
                                        </div>
                                        <div className="flex items-center text-sm">
                                            <Clock className="w-4 h-4 mr-2 text-gray-500" />
                                            <span className="text-gray-600">Time elapsed:</span>
                                            <span className="ml-2 font-medium">
                                                {formatDistanceToNow(new Date(complaint.created_at), {
                                                    addSuffix: true,
                                                })}
                                            </span>
                                        </div>

                                        <div className="flex items-center text-sm">
                                            <Tag className="w-4 h-4 mr-2 text-gray-500" />
                                            <span className="text-gray-600">Priority:</span>
                                            <Badge
                                                className={`ml-2 ${PRIORITY_COLORS[complaint.priority as keyof typeof PRIORITY_COLORS]}`}
                                            >
                                                {PRIORITY_LABELS[complaint.priority as keyof typeof PRIORITY_LABELS]}
                                            </Badge>
                                        </div>
                                    </div>

                                    <div className="space-y-3">
                                        {complaint.organization_id && (
                                            <div className="flex items-center text-sm">
                                                <Building className="w-4 h-4 mr-2 text-gray-500" />
                                                <span className="text-gray-600">Organization:</span>
                                                <span className="ml-2 font-medium">Related NGO</span>
                                            </div>
                                        )}

                                        {!complaint.is_anonymous && complaint.complainant_name && (
                                            <div className="flex items-center text-sm">
                                                <User className="w-4 h-4 mr-2 text-gray-500" />
                                                <span className="text-gray-600">Submitted by:</span>
                                                <span className="ml-2 font-medium">{complaint.complainant_name}</span>
                                            </div>
                                        )}

                                        {complaint.is_anonymous && (
                                            <div className="flex items-center text-sm">
                                                <User className="w-4 h-4 mr-2 text-gray-500" />
                                                <span className="text-gray-600">Type:</span>
                                                <span className="ml-2 font-medium text-blue-600">Anonymous</span>
                                            </div>
                                        )}
                                    </div>
                                </div>

                                <Separator />

                                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                                    <div className="flex items-start">
                                        <div className="mr-3 mt-0.5">{getStatusIcon("OPEN")}</div>
                                        <div>
                                            <h4 className="font-semibold text-blue-900 mb-1">
                                                Current Status: {STATUS_LABELS["OPEN" as keyof typeof STATUS_LABELS]}
                                            </h4>
                                            <p className="text-blue-800 text-sm">{getStatusDescription("OPEN")}</p>
                                        </div>
                                    </div>
                                </div>

                                <Alert>
                                    <Info className="h-4 w-4" />
                                    <AlertDescription>
                                        You will receive updates on your complaint&apos;s progress. Keep this tracking
                                        code safe for future reference.
                                    </AlertDescription>
                                </Alert>
                            </CardContent>
                        </Card>
                    )}
                </div>

                <div className="space-y-6">
                    <Card className="bg-green-50 border-green-200">
                        <CardHeader>
                            <CardTitle className="text-green-900 flex items-center">
                                <Info className="w-5 h-5 mr-2" />
                                How to Track
                            </CardTitle>
                        </CardHeader>
                        <CardContent className="text-sm text-green-800 space-y-2">
                            <p>1. Enter your tracking code in the search box</p>
                            <p>2. Click &quot;Track&quot; to view your complaint status</p>
                            <p>3. Check back regularly for updates</p>
                            <p>4. Contact us if you have questions</p>
                        </CardContent>
                    </Card>

                    <Card className="border-blue-200">
                        <CardHeader>
                            <CardTitle className="text-blue-900">Status Meanings</CardTitle>
                        </CardHeader>
                        <CardContent className="text-sm text-blue-800 space-y-3">
                            <div className="flex items-start">
                                <Badge className="bg-yellow-100 text-yellow-800 mr-2 mt-0.5">Open</Badge>
                                <span>Received and awaiting review</span>
                            </div>
                            <div className="flex items-start">
                                <Badge className="bg-blue-100 text-blue-800 mr-2 mt-0.5">In Progress</Badge>
                                <span>Being actively investigated</span>
                            </div>
                            <div className="flex items-start">
                                <Badge className="bg-green-100 text-green-800 mr-2 mt-0.5">Resolved</Badge>
                                <span>Investigation complete</span>
                            </div>
                            <div className="flex items-start">
                                <Badge className="bg-red-100 text-red-800 mr-2 mt-0.5">Escalated</Badge>
                                <span>Requires higher-level review</span>
                            </div>
                            <div className="flex items-start">
                                <Badge className="bg-gray-100 text-gray-800 mr-2 mt-0.5">Closed</Badge>
                                <span>Fully processed and completed</span>
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader>
                            <CardTitle>Need Help?</CardTitle>
                        </CardHeader>
                        <CardContent className="text-sm space-y-2">
                            <div>
                                📞 <strong>Phone:</strong> +265 1 123 456
                            </div>
                            <div>
                                📧 <strong>Email:</strong> <EMAIL>
                            </div>
                            <div>
                                📍 <strong>Office:</strong> Lilongwe, Malawi
                            </div>
                            <div className="text-xs text-gray-500 mt-2">
                                Office hours: Monday - Friday, 8:00 AM - 5:00 PM
                            </div>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </div>
    );
}
