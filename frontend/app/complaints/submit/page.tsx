"use client";

import { useMutation } from "@tanstack/react-query";
import { AlertCircle, CheckCircle, FileText, Send, User } from "lucide-react";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { toast } from "sonner";

import LoadableItemInput from "@/components/inputs/loadable-item";
import { ManagerInput } from "@/components/inputs/manager";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { createComplaint } from "@/services/ComplaintService";
import { ComplaintRequest } from "@/types";

export default function SubmitComplaintPage() {
    if (typeof document !== "undefined") {
        document.title = "Submit Complaint - myNGO";
    }
    
    const router = useRouter();
    const [formData, setFormData] = useState<ComplaintRequest>({
        title: "",
        summary: "",
        priority: 2,
        category_id: "",
        is_anonymous: false,
        organization_id: undefined,
        complainant_id: undefined,
        complainant_name: "",
        complainant_email: "",
        complainant_phone: "",
    });

    const createComplaintMutation = useMutation({
        mutationFn: createComplaint,
        onSuccess: (response) => {
            if (response.success && response.data) {
                toast.success("Complaint submitted successfully");
                router.push(`/complaints/${response.data.id}`);
            } else {
                toast.error("Failed to submit complaint");
            }
        },
        onError: () => {
            toast.error("Failed to submit complaint");
        },
    });

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();

        if (!formData.title.trim()) {
            toast.error("Please provide a complaint title");

            return;
        }

        if (!formData.summary.trim()) {
            toast.error("Please provide a complaint description");

            return;
        }

        if (!formData.category_id) {
            toast.error("Please select a complaint category");

            return;
        }

        createComplaintMutation.mutate(formData);
    };

    const handleInputChange = (field: keyof ComplaintRequest, value: any) => {
        setFormData((prev) => ({
            ...prev,
            [field]: value,
        }));
    };

    return (
        <div className="max-w-4xl mx-auto space-y-6">
            <div className="text-center mt-6">
                <h1 className="text-3xl font-bold">Submit a Complaint</h1>
                <p className="text-muted-foreground mt-2">Report issues or concerns about NGO operations</p>
            </div>

            <div className="grid gap-6">
                <div className="space-y-6">
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center">
                                <User className="h-5 w-5 mr-2" />
                                Submission Guidelines
                            </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div>
                                <h4 className="font-medium text-sm mb-2">Required Information</h4>
                                <ul className="text-sm text-muted-foreground space-y-1">
                                    <li>• Clear and descriptive title</li>
                                    <li>• Detailed description of the issue</li>
                                    <li>• Appropriate category selection</li>
                                    <li>• Priority level assessment</li>
                                </ul>
                            </div>

                            <div>
                                <h4 className="font-medium text-sm mb-2">Anonymous Submissions</h4>
                                <p className="text-sm text-muted-foreground">
                                    You can submit complaints anonymously. However, this may limit our ability to follow
                                    up with you for additional information.
                                </p>
                            </div>

                            <div>
                                <h4 className="font-medium text-sm mb-2">Processing Time</h4>
                                <p className="text-sm text-muted-foreground">
                                    Complaints are typically reviewed within 3-5 business days. High priority complaints
                                    receive expedited handling.
                                </p>
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center">
                                <AlertCircle className="h-5 w-5 mr-2" />
                                Important Notice
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <Alert>
                                <CheckCircle className="h-4 w-4" />
                                <AlertDescription className="text-sm">
                                    All complaints are treated confidentially and investigated thoroughly. You will
                                    receive a tracking code upon submission to monitor the status of your complaint.
                                </AlertDescription>
                            </Alert>
                        </CardContent>
                    </Card>
                </div>

                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center">
                            <FileText className="h-5 w-5 mr-2" />
                            Complaint Details
                        </CardTitle>
                        <CardDescription>Provide detailed information about your complaint</CardDescription>
                    </CardHeader>
                    <CardContent>
                        <form className="space-y-6" onSubmit={handleSubmit}>
                            <div className="space-y-2">
                                <Label className="required" htmlFor="title">
                                    Complaint Title
                                </Label>
                                <Input
                                    required
                                    id="title"
                                    placeholder="Brief description of the issue"
                                    value={formData.title}
                                    onChange={(e) => handleInputChange("title", e.target.value)}
                                />
                            </div>

                            <div className="space-y-2">
                                <Label className="required" htmlFor="summary">
                                    Detailed Description
                                </Label>
                                <Textarea
                                    required
                                    id="summary"
                                    placeholder="Provide a detailed description of the complaint, including dates, locations, and any relevant information..."
                                    rows={6}
                                    value={formData.summary}
                                    onChange={(e) => handleInputChange("summary", e.target.value)}
                                />
                            </div>

                            <div className="grid gap-4 md:grid-cols-2">
                                <Select
                                    label="Priority Level<"
                                    options={[
                                        { value: "1", label: "High" },
                                        { value: "2", label: "Medium" },
                                        { value: "3", label: "Low" },
                                    ]}
                                    value={formData.priority.toString()}
                                    onValueChange={(value) => handleInputChange("priority", parseInt(value))}
                                />
                                <LoadableItemInput
                                    initialFetchSize={30}
                                    label="Category"
                                    type="COMPLAINT_CATEGORY"
                                    value={formData.category_id}
                                    onItemSelect={(e) => handleInputChange("category_id", e?.id)}
                                />
                            </div>

                            <div className="space-y-2">
                                <ManagerInput
                                    label="Related Organization"
                                    placeholder="Select an organization"
                                    type="organization"
                                    value={formData.organization_id}
                                    onItemSelect={(item) =>
                                        handleInputChange("organization_id", item ? item.id : undefined)
                                    }
                                />
                            </div>

                            {!formData.is_anonymous && (
                                <div className="grid gap-4 md:grid-cols-2">
                                    <Input
                                        id="name"
                                        label="Full name"
                                        placeholder="Full name"
                                        value={formData.complainant_name}
                                        onChange={(e) => handleInputChange("complainant_name", e.target.value)}
                                    />
                                    <Input
                                        id="email"
                                        label="Email Address"
                                        placeholder="<EMAIL>"
                                        type="email"
                                        value={formData.complainant_email}
                                        onChange={(e) => handleInputChange("complainant_email", e.target.value)}
                                    />
                                    <Input
                                        id="phone"
                                        label="Phone Number"
                                        placeholder="+265 XXX XXX XXX"
                                        value={formData.complainant_phone}
                                        onChange={(e) => handleInputChange("complainant_phone", e.target.value)}
                                    />
                                </div>
                            )}

                            <div className="flex items-center space-x-2">
                                <Checkbox
                                    checked={formData.is_anonymous}
                                    id="anonymous"
                                    onCheckedChange={(checked) => {
                                        handleInputChange("is_anonymous", checked);
                                        if (checked) {
                                            handleInputChange("organization_id", undefined);
                                        }
                                    }}
                                />
                                <Label className="text-sm cursor-pointer" htmlFor="anonymous">
                                    Submit this complaint anonymously
                                </Label>
                            </div>

                            <div className="flex justify-end gap-4 pt-4">
                                <Button type="button" variant="outline" onClick={() => router.back()}>
                                    Cancel
                                </Button>
                                <Button className="min-w-32" disabled={createComplaintMutation.isPending} type="submit">
                                    <Send className="h-4 w-4 mr-2" />
                                    {createComplaintMutation.isPending ? "Submitting..." : "Submit Complaint"}
                                </Button>
                            </div>
                        </form>
                    </CardContent>
                </Card>
            </div>
        </div>
    );
}
