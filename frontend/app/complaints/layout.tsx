"use client";

import { Bar<PERSON>hart3, FileText, Plus, Search, Settings, TrendingUp, Users } from "lucide-react";

import { useAuth } from "@/composables/useStore";
import DefaultLayout from "@/layouts/DefaultLayout";
import PublicLayout from "@/layouts/PublicLayout";
import { LayoutProps } from "@/types";

const navigationItems = [
    {
        title: "Overview",
        href: "/complaints",
        icon: BarChart3,
        description: "Complaints dashboard and key metrics",
    },
    {
        title: "All Complaints",
        href: "/complaints/all",
        icon: FileText,
        description: "Comprehensive view of all complaints",
    },
    {
        title: "Submit Complaint",
        href: "/complaints/submit",
        icon: Plus,
        description: "Create new complaint submission",
        badge: "New",
    },
    {
        title: "Track Complaint",
        href: "/complaints/track",
        icon: Search,
        description: "Track complaint status by code",
    },
    {
        title: "Analytics",
        href: "/complaints/analytics",
        icon: TrendingUp,
        description: "Complaint trends and performance insights",
    },
    {
        title: "Reports",
        href: "/complaints/reports",
        icon: FileText,
        description: "Comprehensive complaint reporting",
    },
    {
        title: "Organizations",
        href: "/complaints/organizations",
        icon: Users,
        description: "Complaints by organization",
    },
    {
        title: "Categories",
        href: "/complaints/categories",
        icon: Settings,
        description: "Complaint category management",
    },
];

export default function ComplaintsLayout({ children }: LayoutProps) {
    const { isAuthenticated } = useAuth();

    return isAuthenticated ? <DefaultLayout>{children}</DefaultLayout> : <PublicLayout>{children}</PublicLayout>;
}
