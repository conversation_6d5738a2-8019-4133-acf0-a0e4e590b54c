"use client";

import { useState, useEffect } from "react";
import { toast } from "sonner";
import { FileText, TrendingUp, Download, Bar<PERSON>hart3, <PERSON><PERSON><PERSON>, Clock, CheckCircle, Users } from "lucide-react";
import { ColumnDef } from "@tanstack/react-table";
import { format } from "date-fns";

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { DataTable } from "@/components/ui/data-table";
import {
    ComplaintStatsDto,
    ComplaintTrendDto,
    ComplaintSummaryDto,
    ComplaintDashboardFilter,
    STATUS_LABELS,
    STATUS_COLORS,
    PRIORITY_LABELS,
    PRIORITY_COLORS,
} from "@/types";
import { fetchComplaintStatistics, fetchComplaintTrends, fetchComplaintDashboard } from "@/services/ComplaintService";

export default function ComplaintReportsPage() {
    if (typeof document !== "undefined") {
        document.title = "Complaint Reports - myNGO";
    }
    
    const [stats, setStats] = useState<ComplaintStatsDto | null>(null);
    const [trends, setTrends] = useState<ComplaintTrendDto[]>([]);
    const [complaints, setComplaints] = useState<ComplaintSummaryDto[]>([]);
    const [loading, setLoading] = useState(true);
    const [reportFilter, setReportFilter] = useState<ComplaintDashboardFilter>({
        period: "monthly",
    });

    useEffect(() => {
        loadReportData();
    }, [reportFilter]);

    const loadReportData = async () => {
        try {
            setLoading(true);
            const [statsResponse, trendsResponse, dashboardResponse] = await Promise.all([
                fetchComplaintStatistics(),
                fetchComplaintTrends(reportFilter.period || "monthly"),
                fetchComplaintDashboard(reportFilter),
            ]);

            if (statsResponse.success && statsResponse.data) {
                setStats(statsResponse.data);
            }

            if (trendsResponse.success && trendsResponse.data) {
                setTrends(trendsResponse.data);
            }

            if (dashboardResponse.success && dashboardResponse.data) {
                setComplaints(dashboardResponse.data);
            }
        } catch (error) {
            toast.error("Failed to load report data");
        } finally {
            setLoading(false);
        }
    };

    const exportToCSV = () => {
        if (!complaints.length) {
            toast.error("No data to export");

            return;
        }

        const csvData = complaints.map((complaint) => ({
            tracking_code: complaint.tracking_code,
            title: complaint.title,
            status: STATUS_LABELS[complaint.status as keyof typeof STATUS_LABELS],
            priority: PRIORITY_LABELS[complaint.priority as keyof typeof PRIORITY_LABELS],
            category: complaint.category_name,
            organization: complaint.organization_name || "N/A",
            days_open: complaint.days_open,
            created_at: format(new Date(complaint.created_at), "yyyy-MM-dd"),
        }));

        const csvContent = [
            Object.keys(csvData[0]).join(","),
            ...csvData.map((row) => Object.values(row).join(",")),
        ].join("\n");

        const blob = new Blob([csvContent], { type: "text/csv" });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement("a");

        a.href = url;
        a.download = `complaints-report-${format(new Date(), "yyyy-MM-dd")}.csv`;
        a.click();
        window.URL.revokeObjectURL(url);
        toast.success("Report exported successfully");
    };

    const reportColumns: ColumnDef<ComplaintSummaryDto>[] = [
        {
            accessorKey: "tracking_code",
            header: "Tracking Code",
            cell: ({ row }) => <span className="font-mono text-sm font-medium">{row.getValue("tracking_code")}</span>,
        },
        {
            accessorKey: "title",
            header: "Title",
            cell: ({ row }) => <div className="max-w-[250px] truncate">{row.getValue("title")}</div>,
        },
        {
            accessorKey: "priority",
            header: "Priority",
            cell: ({ row }) => {
                const priority = row.getValue("priority") as number;

                return (
                    <Badge className={PRIORITY_COLORS[priority as keyof typeof PRIORITY_COLORS]}>
                        {PRIORITY_LABELS[priority as keyof typeof PRIORITY_LABELS]}
                    </Badge>
                );
            },
        },
        {
            accessorKey: "status",
            header: "Status",
            cell: ({ row }) => {
                const status = row.getValue("status") as string;

                return (
                    <Badge className={STATUS_COLORS[status as keyof typeof STATUS_COLORS]}>
                        {STATUS_LABELS[status as keyof typeof STATUS_LABELS]}
                    </Badge>
                );
            },
        },
        {
            accessorKey: "category_name",
            header: "Category",
            cell: ({ row }) => <span className="text-sm">{row.getValue("category_name")}</span>,
        },
        {
            accessorKey: "organization_name",
            header: "Organization",
            cell: ({ row }) => <span className="text-sm">{row.getValue("organization_name") || "N/A"}</span>,
        },
        {
            accessorKey: "days_open",
            header: "Days Open",
            cell: ({ row }) => <span className="text-sm">{row.getValue("days_open")} days</span>,
        },
        {
            accessorKey: "created_at",
            header: "Submitted",
            cell: ({ row }) => (
                <span className="text-sm">{format(new Date(row.getValue("created_at")), "MMM d, yyyy")}</span>
            ),
        },
    ];

    const StatCard = ({
        title,
        value,
        icon: Icon,
        description,
        color = "blue",
    }: {
        title: string;
        value: number | string;
        icon: any;
        description?: string;
        color?: string;
    }) => (
        <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">{title}</CardTitle>
                <Icon className={`h-4 w-4 text-${color}-600`} />
            </CardHeader>
            <CardContent>
                <div className="text-2xl font-bold">{value}</div>
                {description && <p className="text-xs text-muted-foreground">{description}</p>}
            </CardContent>
        </Card>
    );

    if (loading) {
        return (
            <div className="flex items-center justify-center min-h-[400px]">
                <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4" />
                    <p className="text-gray-600">Loading report data...</p>
                </div>
            </div>
        );
    }

    return (
        <div className="space-y-6">
            <div className="flex items-center justify-between">
                <div>
                    <h1 className="text-3xl font-bold text-gray-900">Complaints Reports</h1>
                    <p className="text-gray-600">Comprehensive analytics and reporting for complaint management</p>
                </div>
                <Button className="flex items-center gap-2" onClick={exportToCSV}>
                    <Download className="w-4 h-4" />
                    Export CSV
                </Button>
            </div>

            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                {stats && (
                    <>
                        <StatCard
                            description="All time"
                            icon={FileText}
                            title="Total Complaints"
                            value={stats.total_complaints}
                        />
                        <StatCard
                            color="yellow"
                            description="Requiring attention"
                            icon={Clock}
                            title="Open & In Progress"
                            value={stats.open_complaints + stats.in_progress_complaints}
                        />
                        <StatCard
                            color="green"
                            description="Successfully handled"
                            icon={CheckCircle}
                            title="Resolved This Month"
                            value={stats.resolved_complaints}
                        />
                        <StatCard
                            description="Average processing time"
                            icon={TrendingUp}
                            title="Avg Resolution Time"
                            value={stats.avg_resolution_days ? `${stats.avg_resolution_days} days` : "N/A"}
                        />
                    </>
                )}
            </div>

            <div className="grid gap-6 lg:grid-cols-3">
                <div className="lg:col-span-2 space-y-6">
                    <Card>
                        <CardHeader>
                            <div className="flex items-center justify-between">
                                <div>
                                    <CardTitle>Complaints Report</CardTitle>
                                    <CardDescription>
                                        Detailed view of all complaints with filtering options
                                    </CardDescription>
                                </div>
                                <div className="flex items-center gap-2">
                                    <Select
                                        value={reportFilter.period || "monthly"}
                                        onValueChange={(value) => setReportFilter({ ...reportFilter, period: value })}
                                    >
                                        <SelectTrigger className="w-[140px]">
                                            <SelectValue />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="weekly">Weekly</SelectItem>
                                            <SelectItem value="monthly">Monthly</SelectItem>
                                            <SelectItem value="quarterly">Quarterly</SelectItem>
                                            <SelectItem value="yearly">Yearly</SelectItem>
                                        </SelectContent>
                                    </Select>
                                </div>
                            </div>
                        </CardHeader>
                        <CardContent>
                            <DataTable columns={reportColumns} data={complaints} searchKey="tracking_code" />
                        </CardContent>
                    </Card>
                </div>

                <div className="space-y-6">
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center">
                                <PieChart className="w-5 h-5 mr-2" />
                                Status Distribution
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            {stats && (
                                <div className="space-y-3">
                                    <div className="flex items-center justify-between">
                                        <div className="flex items-center">
                                            <div className="w-3 h-3 bg-yellow-500 rounded-full mr-2" />
                                            <span className="text-sm">Open</span>
                                        </div>
                                        <span className="font-medium">{stats.open_complaints}</span>
                                    </div>
                                    <div className="flex items-center justify-between">
                                        <div className="flex items-center">
                                            <div className="w-3 h-3 bg-blue-500 rounded-full mr-2" />
                                            <span className="text-sm">In Progress</span>
                                        </div>
                                        <span className="font-medium">{stats.in_progress_complaints}</span>
                                    </div>
                                    <div className="flex items-center justify-between">
                                        <div className="flex items-center">
                                            <div className="w-3 h-3 bg-green-500 rounded-full mr-2" />
                                            <span className="text-sm">Resolved</span>
                                        </div>
                                        <span className="font-medium">{stats.resolved_complaints}</span>
                                    </div>
                                    <div className="flex items-center justify-between">
                                        <div className="flex items-center">
                                            <div className="w-3 h-3 bg-red-500 rounded-full mr-2" />
                                            <span className="text-sm">Escalated</span>
                                        </div>
                                        <span className="font-medium">{stats.escalated_complaints}</span>
                                    </div>
                                    <div className="flex items-center justify-between">
                                        <div className="flex items-center">
                                            <div className="w-3 h-3 bg-gray-500 rounded-full mr-2" />
                                            <span className="text-sm">Closed</span>
                                        </div>
                                        <span className="font-medium">{stats.closed_complaints}</span>
                                    </div>
                                </div>
                            )}
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center">
                                <BarChart3 className="w-5 h-5 mr-2" />
                                Priority Breakdown
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            {stats && (
                                <div className="space-y-3">
                                    {Object.entries(stats.complaints_by_priority).map(([priority, count]) => (
                                        <div key={priority} className="flex items-center justify-between">
                                            <div className="flex items-center">
                                                <Badge
                                                    className={`mr-2 ${
                                                        priority.includes("1")
                                                            ? "bg-red-100 text-red-800"
                                                            : priority.includes("2")
                                                                ? "bg-yellow-100 text-yellow-800"
                                                                : "bg-green-100 text-green-800"
                                                    }`}
                                                >
                                                    {priority.replace("priority_", "")}
                                                </Badge>
                                            </div>
                                            <span className="font-medium">{count}</span>
                                        </div>
                                    ))}
                                </div>
                            )}
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center">
                                <Users className="w-5 h-5 mr-2" />
                                Top Categories
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            {stats && (
                                <div className="space-y-2">
                                    {Object.entries(stats.complaints_by_category)
                                        .sort(([, a], [, b]) => b - a)
                                        .slice(0, 5)
                                        .map(([category, count]) => (
                                            <div key={category} className="flex items-center justify-between">
                                                <span className="text-sm truncate max-w-[140px]">{category}</span>
                                                <span className="font-medium">{count}</span>
                                            </div>
                                        ))}
                                </div>
                            )}
                        </CardContent>
                    </Card>
                </div>
            </div>
        </div>
    );
}
