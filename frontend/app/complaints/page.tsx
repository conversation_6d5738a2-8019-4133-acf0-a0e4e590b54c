"use client";

import { useQuery } from "@tanstack/react-query";
import {
    AlertTriangle,
    BarChart3,
    CheckCircle,
    Clock,
    Eye,
    FileText,
    MessageSquare,
    Plus,
    TrendingUp,
} from "lucide-react";
import Link from "next/link";

import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { cn } from "@/lib/utils";
import { fetchComplaintDashboard, fetchComplaintStatistics } from "@/services/ComplaintService";
import { ComplaintSummaryDto, PRIORITY_LABELS } from "@/types";

export default function ComplaintsPage() {
    if (typeof document !== "undefined") {
        document.title = "Complaints - myNGO";
    }
    
    const {
        data: complaintsStats,
        isLoading: statsLoading,
        error: statsError,
    } = useQuery({
        queryKey: ["complaint-statistics"],
        queryFn: () => fetchComplaintStatistics(),
    });

    const { data: recentComplaints, isLoading: complaintsLoading } = useQuery({
        queryKey: ["complaint-dashboard"],
        queryFn: () => fetchComplaintDashboard({ size: 10 }),
    });

    const stats = complaintsStats?.data;
    const complaints = recentComplaints?.data || [];

    return (
        <div className="max-w-7xl mx-auto space-y-6">
            {/* Header */}
            <div className="flex items-center justify-between">
                <div>
                    <h1 className="text-3xl font-bold tracking-tight">Complaints Management</h1>
                    <p className="text-muted-foreground">Monitor and manage complaints from NGOs and the public</p>
                </div>
            </div>

            {/* Quick Statistics */}
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Pending Complaints</CardTitle>
                        <Clock className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold text-yellow-600">
                            {statsLoading ? <Skeleton className="h-8 w-12" /> : stats?.open_complaints || 0}
                        </div>
                        <p className="text-xs text-muted-foreground">Awaiting review</p>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Total Complaints</CardTitle>
                        <FileText className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold text-blue-600">
                            {statsLoading ? <Skeleton className="h-8 w-20" /> : stats?.total_complaints || 0}
                        </div>
                        <p className="text-xs text-muted-foreground">All time submissions</p>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Resolved</CardTitle>
                        <CheckCircle className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold text-green-600">
                            {statsLoading ? <Skeleton className="h-8 w-8" /> : stats?.resolved_complaints || 0}
                        </div>
                        <p className="text-xs text-muted-foreground">Successfully resolved</p>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Escalated</CardTitle>
                        <AlertTriangle className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold text-red-600">
                            {statsLoading ? <Skeleton className="h-8 w-8" /> : stats?.escalated_complaints || 0}
                        </div>
                        <p className="text-xs text-muted-foreground">Require attention</p>
                    </CardContent>
                </Card>
            </div>

            {/* Quick Actions */}
            <div className="flex flex-wrap gap-4">
                <Button asChild>
                    <Link className="flex items-center" href="/complaints/all">
                        <FileText className="h-4 w-4 mr-2" />
                        View All Complaints
                    </Link>
                </Button>
                <Button asChild variant="outline">
                    <Link className="flex items-center" href="/complaints/submit">
                        <Plus className="h-4 w-4 mr-2" />
                        Submit Complaint
                    </Link>
                </Button>
                <Button asChild variant="outline">
                    <Link className="flex items-center" href="/complaints/analytics">
                        <TrendingUp className="h-4 w-4 mr-2" />
                        View Analytics
                    </Link>
                </Button>
                <Button asChild variant="outline">
                    <Link className="flex items-center" href="/complaints/reports">
                        <BarChart3 className="h-4 w-4 mr-2" />
                        Generate Reports
                    </Link>
                </Button>
            </div>

            {/* Recent Complaints Section */}
            <div className="space-y-6">
                <div className="flex items-center justify-between">
                    <div>
                        <span className="text-lg font-bold">Recent Complaints</span>
                        <p className="text-sm text-muted-foreground">
                            Latest complaint submissions requiring attention
                        </p>
                    </div>
                    {complaints.length > 0 && <Badge variant="secondary">{complaints.length} complaints</Badge>}
                </div>
                <div className="space-y-4">
                    {complaintsLoading ? (
                        <div className="space-y-4">
                            {Array.from({ length: 3 }).map((_, i) => (
                                <div key={i} className="flex items-start justify-between p-4 border rounded-lg">
                                    <div className="flex items-start gap-3">
                                        <Skeleton className="h-4 w-4 rounded" />
                                        <div className="space-y-2">
                                            <Skeleton className="h-4 w-48" />
                                            <Skeleton className="h-3 w-32" />
                                            <Skeleton className="h-3 w-64" />
                                        </div>
                                    </div>
                                    <div className="flex gap-2">
                                        <Skeleton className="h-8 w-16" />
                                        <Skeleton className="h-8 w-20" />
                                    </div>
                                </div>
                            ))}
                        </div>
                    ) : statsError ? (
                        <div className="text-center py-8 text-red-600">
                            <AlertTriangle className="h-12 w-12 mx-auto mb-4" />
                            <p>Error loading complaints</p>
                            <p className="text-sm">{statsError.message}</p>
                        </div>
                    ) : complaints.length === 0 ? (
                        <div className="text-center py-12 text-muted-foreground">
                            <CheckCircle className="h-12 w-12 mx-auto mb-4 opacity-50" />
                            <p className="text-lg font-medium">No recent complaints</p>
                            <p className="text-sm">All complaints are up to date</p>
                        </div>
                    ) : (
                        complaints.map((complaint) => <ComplaintCard key={complaint.id} complaint={complaint} />)
                    )}
                </div>
            </div>
        </div>
    );
}

interface ComplaintCardProps {
    complaint: ComplaintSummaryDto;
}

function ComplaintCard({ complaint }: ComplaintCardProps) {
    const getStatusColor = (status: string) => {
        switch (status) {
            case "OPEN":
                return "text-yellow-600 bg-yellow-100";
            case "IN_PROGRESS":
                return "text-blue-600 bg-blue-100";
            case "RESOLVED":
                return "text-green-600 bg-green-100";
            case "ESCALATED":
                return "text-red-600 bg-red-100";
            case "CLOSED":
                return "text-gray-600 bg-gray-100";
            default:
                return "text-gray-600 bg-gray-100";
        }
    };

    const getStatusIcon = (status: string) => {
        switch (status) {
            case "OPEN":
                return <AlertTriangle className="h-4 w-4" />;
            case "IN_PROGRESS":
                return <Clock className="h-4 w-4" />;
            case "RESOLVED":
                return <CheckCircle className="h-4 w-4" />;
            case "ESCALATED":
                return <TrendingUp className="h-4 w-4" />;
            case "CLOSED":
                return <CheckCircle className="h-4 w-4" />;
            default:
                return <FileText className="h-4 w-4" />;
        }
    };

    const getPriorityColor = (priority: number) => {
        switch (priority) {
            case 1:
                return "text-red-600 bg-red-100";
            case 2:
                return "text-yellow-600 bg-yellow-100";
            case 3:
                return "text-green-600 bg-green-100";
            default:
                return "text-gray-600 bg-gray-100";
        }
    };

    return (
        <div className="flex items-start justify-between p-4 border rounded-lg hover:bg-muted/50 transition-colors">
            <div className="flex items-start gap-3 flex-1">
                {getStatusIcon(complaint.status)}
                <div className="space-y-2 flex-1">
                    <div className="flex items-center gap-2">
                        <h4 className="font-medium text-md">{complaint.title}</h4>
                        <Badge className={cn("text-xs", getStatusColor(complaint.status))}>{complaint.status}</Badge>
                        <Badge className={cn("text-xs", getPriorityColor(complaint.priority))}>
                            {PRIORITY_LABELS[complaint.priority as keyof typeof PRIORITY_LABELS]}
                        </Badge>
                    </div>

                    <div className="text-xs text-muted-foreground space-y-1">
                        <p>
                            <strong>Tracking Code:</strong> {complaint.tracking_code} • {complaint.category_name}
                        </p>
                        <p>
                            <strong>Organization:</strong> {complaint.organization_name || "Anonymous"}
                        </p>
                        <p>
                            <strong>Days Open:</strong> {complaint.days_open} days
                        </p>
                    </div>
                </div>
            </div>

            <div className="flex items-center gap-2 ml-4">
                <Button asChild size="sm" variant="outline">
                    <Link className="flex items-center" href={`/complaints/${complaint.id}`}>
                        <Eye className="h-3 w-3 mr-1" />
                        View Details
                    </Link>
                </Button>

                {complaint.status === "OPEN" || complaint.status === "IN_PROGRESS" ? (
                    <Button asChild size="sm">
                        <Link className="flex items-center" href={`/complaints/${complaint.id}`}>
                            <MessageSquare className="h-3 w-3 mr-1" />
                            Review
                        </Link>
                    </Button>
                ) : null}
            </div>
        </div>
    );
}
