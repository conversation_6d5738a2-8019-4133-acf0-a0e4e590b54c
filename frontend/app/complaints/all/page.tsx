"use client";

import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { useRouter } from "next/navigation";
import { AlertTriangle, Eye, FileText, MessageSquare, Search } from "lucide-react";
import { ColumnDef } from "@tanstack/react-table";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Skeleton } from "@/components/ui/skeleton";
import { DataTable } from "@/components/ui/data-table";
import { cn } from "@/lib/utils";
import { fetchComplaintsWithPagination, fetchComplaintCategories } from "@/services/ComplaintService";
import {
    ComplaintFilter,
    ComplaintSummaryDto,
    ComplaintStatusType,
    STATUS_LABELS,
    STATUS_COLORS,
    PRIORITY_LABELS,
    PRIORITY_COLORS,
} from "@/types";

export default function AllComplaintsPage() {
    if (typeof document !== "undefined") {
        document.title = "All Complaints - myNGO";
    }
    
    const router = useRouter();
    const [filter, setFilter] = useState<ComplaintFilter>({
        page: 1,
        size: 20,
    });

    const {
        data: complaintsResponse,
        isLoading: complaintsLoading,
        error: complaintsError,
    } = useQuery({
        queryKey: ["complaints", filter],
        queryFn: () => fetchComplaintsWithPagination(filter),
    });

    const { data: categoriesResponse } = useQuery({
        queryKey: ["complaint-categories"],
        queryFn: fetchComplaintCategories,
    });

    const complaints = complaintsResponse?.data?.data || [];
    const totalComplaints = complaintsResponse?.data?.total || 0;
    const categories = categoriesResponse?.data || [];

    const complaintsColumns: ColumnDef<ComplaintSummaryDto>[] = [
        {
            accessorKey: "tracking_code",
            header: "Tracking Code",
            cell: ({ row }) => <span className="font-mono text-sm font-medium">{row.getValue("tracking_code")}</span>,
        },
        {
            accessorKey: "title",
            header: "Title",
            cell: ({ row }) => (
                <div className="max-w-[200px] truncate" title={row.getValue("title") as string}>
                    {row.getValue("title")}
                </div>
            ),
        },
        {
            accessorKey: "priority",
            header: "Priority",
            cell: ({ row }) => {
                const priority = row.getValue("priority") as number;

                return (
                    <Badge className={PRIORITY_COLORS[priority as keyof typeof PRIORITY_COLORS]}>
                        {PRIORITY_LABELS[priority as keyof typeof PRIORITY_LABELS]}
                    </Badge>
                );
            },
        },
        {
            accessorKey: "status",
            header: "Status",
            cell: ({ row }) => {
                const status = row.getValue("status") as string;

                return (
                    <Badge className={STATUS_COLORS[status as keyof typeof STATUS_COLORS]}>
                        {STATUS_LABELS[status as keyof typeof STATUS_LABELS]}
                    </Badge>
                );
            },
        },
        {
            accessorKey: "category_name",
            header: "Category",
            cell: ({ row }) => <span className="text-sm">{row.getValue("category_name")}</span>,
        },
        {
            accessorKey: "organization_name",
            header: "Organization",
            cell: ({ row }) => <span className="text-sm">{row.getValue("organization_name") || "Anonymous"}</span>,
        },
        {
            accessorKey: "days_open",
            header: "Days Open",
            cell: ({ row }) => {
                const days = row.getValue("days_open") as number;
                const color = days > 30 ? "text-red-600" : days > 14 ? "text-yellow-600" : "text-green-600";

                return <span className={cn("text-sm font-medium", color)}>{days} days</span>;
            },
        },
        {
            id: "actions",
            header: "Actions",
            cell: ({ row }) => {
                const complaint = row.original;

                return (
                    <div className="flex items-center gap-2">
                        <Button
                            size="sm"
                            title="View Details"
                            variant="ghost"
                            onClick={() => router.push(`/complaints/${complaint.id}`)}
                        >
                            <Eye className="h-4 w-4" />
                        </Button>
                        {(complaint.status === "OPEN" || complaint.status === "IN_PROGRESS") && (
                            <Button
                                size="sm"
                                title="Review Complaint"
                                variant="ghost"
                                onClick={() => router.push(`/complaints/${complaint.id}`)}
                            >
                                <MessageSquare className="h-4 w-4" />
                            </Button>
                        )}
                    </div>
                );
            },
        },
    ];

    const handleFilterChange = (key: keyof ComplaintFilter, value: any) => {
        setFilter((prev) => ({
            ...prev,
            [key]: value,
            page: 1, // Reset to first page when filtering
        }));
    };

    return (
        <div className="space-y-6">
            <div className="flex items-center justify-between">
                <div>
                    <h1 className="text-3xl font-bold tracking-tight">All Complaints</h1>
                    <p className="text-muted-foreground">Comprehensive view of all complaints in the system</p>
                </div>
                <div className="text-sm text-muted-foreground">{totalComplaints} total complaints</div>
            </div>

            {/* Filters */}
            <Card>
                <CardHeader>
                    <CardTitle className="text-lg">Filters</CardTitle>
                    <CardDescription>Filter complaints by status, category, or search terms</CardDescription>
                </CardHeader>
                <CardContent>
                    <div className="grid gap-4 md:grid-cols-4">
                        <div className="space-y-2">
                            <Label>Status</Label>
                            <Select
                                value={filter.status || ""}
                                onValueChange={(value) => handleFilterChange("status", value || undefined)}
                            >
                                <SelectTrigger>
                                    <SelectValue placeholder="All statuses" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="">All Statuses</SelectItem>
                                    {Object.values(ComplaintStatusType).map((status) => (
                                        <SelectItem key={status} value={status}>
                                            {STATUS_LABELS[status]}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                        </div>

                        <div className="space-y-2">
                            <Label>Priority</Label>
                            <Select
                                value={filter.priority?.toString() || ""}
                                onValueChange={(value) =>
                                    handleFilterChange("priority", value ? parseInt(value) : undefined)
                                }
                            >
                                <SelectTrigger>
                                    <SelectValue placeholder="All priorities" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="">All Priorities</SelectItem>
                                    <SelectItem value="1">High</SelectItem>
                                    <SelectItem value="2">Medium</SelectItem>
                                    <SelectItem value="3">Low</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>

                        <div className="space-y-2">
                            <Label>Category</Label>
                            <Select
                                value={filter.category_id || ""}
                                onValueChange={(value) => handleFilterChange("category_id", value || undefined)}
                            >
                                <SelectTrigger>
                                    <SelectValue placeholder="All categories" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="">All Categories</SelectItem>
                                    {categories.map((category) => (
                                        <SelectItem key={category.id} value={category.id}>
                                            {category.display_value}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                        </div>

                        <div className="space-y-2">
                            <Label>Search</Label>
                            <div className="relative">
                                <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                                <Input
                                    className="pl-10"
                                    placeholder="Tracking code or title..."
                                    value={filter.tracking_code || ""}
                                    onChange={(e) => handleFilterChange("tracking_code", e.target.value || undefined)}
                                />
                            </div>
                        </div>
                    </div>
                </CardContent>
            </Card>

            {/* Complaints Table */}
            <Card>
                <CardHeader>
                    <div className="flex items-center justify-between">
                        <div>
                            <CardTitle>Complaints</CardTitle>
                            <CardDescription>
                                {complaintsLoading ? "Loading..." : `${totalComplaints} complaints found`}
                            </CardDescription>
                        </div>
                    </div>
                </CardHeader>
                <CardContent>
                    {complaintsLoading ? (
                        <div className="space-y-4">
                            {Array.from({ length: 5 }).map((_, i) => (
                                <div key={i} className="flex items-center space-x-4 p-4">
                                    <Skeleton className="h-4 w-24" />
                                    <Skeleton className="h-4 w-48" />
                                    <Skeleton className="h-4 w-16" />
                                    <Skeleton className="h-4 w-20" />
                                    <Skeleton className="h-4 w-32" />
                                </div>
                            ))}
                        </div>
                    ) : complaintsError ? (
                        <div className="text-center py-8 text-red-600">
                            <AlertTriangle className="h-12 w-12 mx-auto mb-4" />
                            <p>Error loading complaints</p>
                        </div>
                    ) : complaints.length === 0 ? (
                        <div className="text-center py-12 text-muted-foreground">
                            <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
                            <p className="text-lg font-medium">No complaints found</p>
                            <p className="text-sm">Try adjusting your filters</p>
                        </div>
                    ) : (
                        <DataTable
                            columns={complaintsColumns}
                            data={complaints}
                            pagination={{
                                page: filter.page || 1,
                                size: filter.size || 20,
                                total: totalComplaints,
                                onPageChange: (page) => handleFilterChange("page", page),
                                onSizeChange: (size) => handleFilterChange("size", size),
                            }}
                            searchKey="tracking_code"
                        />
                    )}
                </CardContent>
            </Card>
        </div>
    );
}
