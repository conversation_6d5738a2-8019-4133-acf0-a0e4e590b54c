"use client";

import Link from "next/link";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { toast } from "sonner";

import { AuthFormFooter, AuthFormHeader } from "@/components/auth/auth-form-sections";
import { Logo } from "@/components/icons";
import { LoginMotion } from "@/components/motions/login-motion";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
import { useAuth } from "@/composables/useStore";
import * as AuthService from "@/services/AuthService";

const LoginPage = () => {
    if (typeof document !== "undefined") {
        document.title = "Login - myNGO";
    }

    const [identifier, setIdentifier] = useState("");
    const [password, setPassword] = useState("");
    const [rememberMe, setRememberMe] = useState(false);
    const [isLoading, setIsLoading] = useState(false);
    const [formErrors, setFormErrors] = useState({
        identifier: "",
        password: "",
    });

    const router = useRouter();
    const auth = useAuth();

    const validateForm = () => {
        const errors = { identifier: "", password: "" };
        let isValid = true;

        if (!identifier) {
            errors.identifier = "Username or Email is required";
            isValid = false;
        }

        if (!password) {
            errors.password = "Password is required";
            isValid = false;
        } else if (password.length < 8) {
            errors.password = "Password must be at least 8 characters";
            isValid = false;
        }

        setFormErrors(errors);

        return isValid;
    };

    const handleSubmit = async (e: { preventDefault: () => void }) => {
        e.preventDefault();
        if (!validateForm()) {
            return;
        }
        setIsLoading(true);

        const response = await AuthService.login({
            identifier,
            password,
            rememberMe,
        });

        if (!response.success) {
            for (const error of response.errors) {
                toast.error(error.message);
            }
        }

        if (response.success && response.data) {
            auth.setUser(response.data);

            if (["VERIFICATION", "LOGIN_2FA"].includes(response.data.auth_type)) {
                return router.push("/auth/2fa");
            }

            router.push("/dashboard");
        }

        setIsLoading(false);
    };

    return (
        <div className="flex min-h-screen">
            {/* Left side - Enhanced Slideshow */}
            <div className="hidden lg:block lg:w-1/2 relative bg-purple-900 overflow-hidden">
                <LoginMotion />
                {/* NGORA branding in corner */}
                <div className="absolute top-8 left-8 z-30 flex items-center bg-white/10 backdrop-blur-sm p-3 rounded-lg">
                    <Logo />
                </div>
            </div>

            {/* Right side - Enhanced Login form */}
            <div className="w-full lg:w-1/2 flex items-center justify-center p-6 sm:p-12">
                <form className="w-full max-w-md" onSubmit={handleSubmit}>
                    <AuthFormHeader page="login" />

                    <div className="space-y-6">
                        <Input
                            required
                            autoComplete="identifier"
                            className="p-6"
                            error={formErrors.identifier}
                            id="identifier"
                            label="Username or Email"
                            name="identifier"
                            placeholder="Username or Email"
                            type="text"
                            value={identifier}
                            onChange={(e) => setIdentifier(e.target.value)}
                        />

                        <Input
                            required
                            autoComplete="current-password"
                            className="p-6"
                            error={formErrors.password}
                            id="password"
                            label="Password"
                            labelRight={
                                <Link className="text-primary hover:underline" href="/auth/password-reset">
                                    Forgot password?
                                </Link>
                            }
                            name="password"
                            placeholder="Enter password"
                            type="password"
                            value={password}
                            onChange={(e) => setPassword(e.target.value)}
                        />

                        <Checkbox
                            checked={rememberMe}
                            containerClassName="justify-start space-x-3"
                            label="Remember me"
                            labelClassName="text-base font-medium"
                            onCheckedChange={(checked) => setRememberMe(!!checked)}
                        />

                        <Button className="w-full" loading={isLoading} loadingText="Signing in..." type="submit">
                            Sign in to your account
                        </Button>
                    </div>

                    <AuthFormFooter page="login" />
                </form>
            </div>
        </div>
    );
};

export default LoginPage;
