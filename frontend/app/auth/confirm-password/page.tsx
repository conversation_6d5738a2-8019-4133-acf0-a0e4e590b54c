"use client";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, ArrowLeftIcon, Check, CheckCircle, Lock, X } from "lucide-react";
import { useRouter, useSearchParams } from "next/navigation";
import { useMemo, useState } from "react";
import { toast } from "sonner";

import { AuthFormFooter } from "@/components/auth/auth-form-sections";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import * as AuthService from "@/services/AuthService";

type PageState = "loading" | "error" | "form" | "success";

interface PasswordValidation {
    minLength: boolean;
    symbol: boolean;
    lowercase: boolean;
    number: boolean;
    match: boolean;
}

const ConfirmPasswordPage = () => {
    if (typeof document !== "undefined") {
        document.title = "Confirm Password - myNGO";
    }
    
    const [pageState, setPageState] = useState<PageState>("loading");
    const [password, setPassword] = useState("");
    const [confirmPassword, setConfirmPassword] = useState("");
    const [isLoading, setIsLoading] = useState(false);
    const [errorMessage, setErrorMessage] = useState("");

    const router = useRouter();
    const searchParams = useSearchParams();
    const token = searchParams.get("token");

    const passwordValidation = useMemo((): PasswordValidation => {
        return {
            minLength: password.length >= 8,
            symbol: /(?=.*[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?])/.test(password),
            lowercase: /(?=.*[a-z])/.test(password),
            number: /(?=.*\d)/.test(password),
            match: password === confirmPassword && password.length > 0 && confirmPassword.length > 0,
        };
    }, [password, confirmPassword]);

    const isPasswordValid = useMemo(() => {
        return Object.values(passwordValidation).every(Boolean);
    }, [passwordValidation]);

    useMemo(() => {
        if (pageState !== "loading") return;

        const verifyToken = async () => {
            if (!token) {
                setErrorMessage("No reset token found in the URL. The link may be invalid.");
                setPageState("error");

                return;
            }

            setPageState("form");
        };

        verifyToken();
    }, [token, pageState]);

    const validatePassword = (password: string): string | null => {
        if (password.length < 8) {
            return "Password must be at least 8 characters long";
        }
        if (!/(?=.*[a-z])/.test(password)) {
            return "Password must contain at least one lowercase letter";
        }
        if (!/(?=.*[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?])/.test(password)) {
            return "Password must contain at least one special character";
        }
        if (!/(?=.*\d)/.test(password)) {
            return "Password must contain at least one number";
        }

        return null;
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();

        if (!password || !confirmPassword) {
            toast.error("Please fill in all fields");

            return;
        }

        const passwordError = validatePassword(password);

        if (passwordError) {
            toast.error(passwordError);

            return;
        }

        if (password !== confirmPassword) {
            toast.error("Passwords do not match");

            return;
        }

        if (!token) {
            toast.error("Reset token is missing");

            return;
        }

        setIsLoading(true);

        const response = await AuthService.resetPassword(token, password);

        if (response.success) {
            setPageState("success");
            toast.success("Password reset successfully!");
        } else {
        }

        for (const error of response.errors) {
            toast.error(error.message);
        }

        setIsLoading(false);
    };

    const handleCancel = () => {
        router.push("/auth/login");
    };

    const ValidationItem = ({
        isValid,
        children,
        showValidation,
    }: {
        isValid: boolean;
        children: React.ReactNode;
        showValidation: boolean;
    }) => (
        <li className="flex items-center space-x-2">
            {showValidation ? (
                isValid ? (
                    <Check className="w-4 h-4 text-green-600 flex-shrink-0" />
                ) : (
                    <X className="w-4 h-4 text-red-600 flex-shrink-0" />
                )
            ) : (
                <div className="w-4 h-4 flex-shrink-0" />
            )}
            <span
                className={`text-xs ${
                    showValidation ? (isValid ? "text-green-600" : "text-red-600") : "text-muted-foreground"
                }`}
            >
                {children}
            </span>
        </li>
    );

    if (pageState === "loading") {
        return (
            <div className="flex min-h-screen">
                <div className="w-full flex items-center justify-center p-6 sm:p-12">
                    <div className="w-full max-w-md text-center">
                        <div className="mx-auto w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mb-4">
                            <Lock className="w-8 h-8 text-primary animate-pulse" />
                        </div>
                        <h2 className="text-2xl font-semibold mb-2">Verifying Request</h2>
                        <p className="text-muted-foreground">
                            Please wait while we verify your password reset request...
                        </p>
                    </div>
                </div>
            </div>
        );
    }

    if (pageState === "error") {
        return (
            <div className="flex min-h-screen">
                <div className="w-full flex items-center justify-center p-6 sm:p-12">
                    <div className="w-full max-w-md">
                        <div className="text-center mb-8">
                            <div className="mx-auto w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mb-4">
                                <AlertTriangle className="w-8 h-8 text-red-600" />
                            </div>
                            <h2 className="text-2xl font-semibold mb-2">Invalid Request</h2>
                        </div>

                        <Alert className="mb-6 border-red-200 bg-red-100">
                            <AlertTriangle className="h-4 w-4 text-red-600" />
                            <AlertDescription className="text-red-800">
                                {errorMessage ||
                                    "We couldn't verify your password reset request. The link may be invalid, expired, or already used."}
                            </AlertDescription>
                        </Alert>

                        <Button className="w-full" onClick={handleCancel}>
                            <ArrowLeftIcon className="w-4 h-4 mr-2" />
                            Back to Sign In
                        </Button>

                        <AuthFormFooter page="password-reset" />
                    </div>
                </div>
            </div>
        );
    }

    if (pageState === "success") {
        return (
            <div className="flex min-h-screen">
                <div className="w-full flex items-center justify-center p-6 sm:p-12">
                    <div className="w-full max-w-md">
                        <div className="text-center mb-8">
                            <div className="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-4">
                                <CheckCircle className="w-8 h-8 text-green-600" />
                            </div>
                            <h2 className="text-2xl font-semibold mb-2">Password Reset Successful</h2>
                            <p className="text-muted-foreground mb-6">
                                Your password has been successfully updated. You can now sign in with your new password.
                            </p>
                        </div>

                        <Button className="w-full" onClick={() => router.push("/auth/login")}>
                            Continue to Sign In
                        </Button>

                        <AuthFormFooter page="password-reset" />
                    </div>
                </div>
            </div>
        );
    }

    const showValidation = password.length > 0;

    return (
        <div className="flex min-h-screen">
            <div className="w-full flex items-center justify-center p-6 sm:p-12">
                <div className="w-full max-w-md">
                    <div className="text-center mb-8">
                        <div className="mx-auto w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mb-4">
                            <Lock className="w-8 h-8 text-primary" />
                        </div>
                        <h2 className="text-2xl font-semibold mb-2">Set New Password</h2>
                        <p className="text-muted-foreground">Create a strong password for your account</p>
                    </div>

                    <form className="space-y-6" onSubmit={handleSubmit}>
                        <Input
                            required
                            className="mt-1"
                            id="password"
                            label="New Password"
                            placeholder="Enter your new password"
                            type="password"
                            value={password}
                            onChange={(e) => setPassword(e.target.value)}
                        />

                        <Input
                            required
                            className="mt-1"
                            id="confirmPassword"
                            label="Confirm New Password"
                            placeholder="Confirm your new password"
                            type="password"
                            value={confirmPassword}
                            onChange={(e) => setConfirmPassword(e.target.value)}
                        />

                        <div className="space-y-2">
                            <p className="text-xs text-muted-foreground">Password must contain:</p>
                            <ul className="space-y-2">
                                <ValidationItem isValid={passwordValidation.minLength} showValidation={showValidation}>
                                    At least 8 characters
                                </ValidationItem>
                                <ValidationItem isValid={passwordValidation.symbol} showValidation={showValidation}>
                                    One special character
                                </ValidationItem>
                                <ValidationItem isValid={passwordValidation.lowercase} showValidation={showValidation}>
                                    One lowercase letter
                                </ValidationItem>
                                <ValidationItem isValid={passwordValidation.number} showValidation={showValidation}>
                                    One number
                                </ValidationItem>
                                {confirmPassword.length > 0 && (
                                    <ValidationItem isValid={passwordValidation.match} showValidation={true}>
                                        Passwords match
                                    </ValidationItem>
                                )}
                            </ul>
                        </div>

                        <div className="space-y-3">
                            <Button
                                className="w-full"
                                disabled={!isPasswordValid || isLoading}
                                loading={isLoading}
                                loadingText="Updating Password..."
                                type="submit"
                            >
                                Update Password
                            </Button>

                            <Button
                                className="w-full"
                                disabled={isLoading}
                                type="button"
                                variant="outline"
                                onClick={handleCancel}
                            >
                                Cancel
                            </Button>
                        </div>
                    </form>

                    <AuthFormFooter page="password-reset" />
                </div>
            </div>
        </div>
    );
};

export default ConfirmPasswordPage;
