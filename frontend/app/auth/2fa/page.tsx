"use client";

import { ArrowLeft, Mail } from "lucide-react";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { toast } from "sonner";

import { AuthFormFooter } from "@/components/auth/auth-form-sections";
import { OneTimePassword } from "@/components/inputs/one-time-password";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { useAuth } from "@/composables/useStore";
import * as AuthService from "@/services/AuthService";

const OTPVerificationPage = () => {
    if (typeof document !== "undefined") {
        document.title = "Two-Factor Authentication - myNGO";
    }
    
    const { session, destroySession, setUser } = useAuth();
    const [otp, setOtp] = useState("");
    const [isLoading, setIsLoading] = useState(false);
    const [resendLoading, setResendLoading] = useState(false);
    const [timeLeft, setTimeLeft] = useState(60);
    const [canResend, setCanResend] = useState(false);
    const [token, setToken] = useState("");
    const [isAutoVerifying, setIsAutoVerifying] = useState(false);

    const router = useRouter();

    const OTP_LENGTH = 6;

    useEffect(() => {
        const urlParams = new URLSearchParams(window.location.search);
        const tokenFromUrl = urlParams.get("token");

        if (tokenFromUrl) {
            setToken(tokenFromUrl);
            setIsAutoVerifying(true);
            handleTokenVerification(tokenFromUrl);
        }
    }, []);

    useEffect(() => {
        if (timeLeft > 0) {
            const timer = setTimeout(() => setTimeLeft(timeLeft - 1), 1000);

            return () => clearTimeout(timer);
        } else {
            setCanResend(true);
        }
    }, [timeLeft]);

    const handleTokenVerification = async (tokenFromUrl: string) => {
        setIsLoading(true);

        try {
            const response = await AuthService.verifyTwoFA({
                token: tokenFromUrl,
            });

            if (!response.success) {
                setIsAutoVerifying(false);
                for (const error of response.errors) {
                    toast.error(error.message);
                }

                return;
            }

            if (response.success && response.data) {
                toast.success("Email verified successfully!");
                setUser(response.data);
                router.push("/dashboard");
            }
        } catch (error) {
            setIsAutoVerifying(false);
            toast.error("Verification failed. Please try again.");
        } finally {
            setIsLoading(false);
        }
    };

    const handleOtpChange = (value: string) => {
        setOtp(value);
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();

        if (otp.length !== OTP_LENGTH) {
            toast.error(`Please enter the complete ${OTP_LENGTH}-digit code`);

            return;
        }

        setIsLoading(true);

        try {
            const response = await AuthService.verifyTwoFA({
                email: session?.email!,
                code: otp.toUpperCase(),
                token,
            });

            if (!response.success) {
                for (const error of response.errors) {
                    toast.error(error.message);
                }
                setOtp("");

                return;
            }

            if (response.success && response.data) {
                toast.success("Email verified successfully!");
                setUser(response.data);
                router.push("/dashboard");
            }
        } catch (error) {
            toast.error("Verification failed. Please try again.");
            setOtp("");
        } finally {
            setIsLoading(false);
        }
    };

    const handleResendCode = async () => {
        setResendLoading(true);

        try {
            const response = await AuthService.resendVerificationCode(session?.email!);

            if (response.success) {
                toast.success("Verification code sent to your email!");
                setTimeLeft(60);
                setCanResend(false);
                setOtp("");
            } else {
                for (const error of response.errors) {
                    toast.error(error.message);
                }
            }
        } catch (error) {
            toast.error("Failed to resend code. Please try again.");
        } finally {
            setResendLoading(false);
        }
    };

    const logout = () => {
        destroySession();
        router.push("/auth/login");
    };

    if (isAutoVerifying) {
        return (
            <div className="flex min-h-screen">
                <div className="w-full flex items-center justify-center p-6 sm:p-12">
                    <div className="w-full max-w-md text-center">
                        <div className="mx-auto w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mb-4">
                            <Mail className="w-8 h-8 text-primary animate-pulse" />
                        </div>
                        <h2 className="text-2xl font-semibold mb-2">Verifying...</h2>
                        <p className="text-muted-foreground">Please wait while we verify your email address.</p>
                        <div className="mt-6">
                            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto" />
                        </div>
                    </div>
                </div>
            </div>
        );
    }

    return (
        <div className="flex min-h-screen">
            <div className="w-full flex items-center justify-center p-6 sm:p-12">
                <div className="w-full max-w-md">
                    <div className="text-center mb-8">
                        <div className="mx-auto w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mb-4">
                            <Mail className="w-8 h-8 text-primary" />
                        </div>

                        <h2 className="text-2xl font-semibold mb-2">Check Your Email</h2>

                        <p className="text-muted-foreground mb-2">
                            We&apos;ve sent a {OTP_LENGTH}-digit verification code to
                        </p>

                        <p className="font-medium text-primary mb-4">{session?.email!}</p>

                        <p className="text-sm text-muted-foreground">
                            Enter the code below to verify your email address
                        </p>
                    </div>

                    <form className="space-y-6" onSubmit={handleSubmit}>
                        <div className="flex flex-col items-center space-y-4">
                            <Label className="text-sm font-medium">Verification Code</Label>

                            <OneTimePassword groupSize={3} length={OTP_LENGTH} value={otp} onChange={handleOtpChange} />

                            <p className="text-xs text-muted-foreground text-center">
                                {otp.length === 0 && `Please enter your ${OTP_LENGTH}-digit code`}
                                {otp.length > 0 &&
                                    otp.length < OTP_LENGTH &&
                                    `${OTP_LENGTH - otp.length} digits remaining`}
                                {otp.length === OTP_LENGTH && "Code complete"}
                            </p>
                        </div>

                        <Button
                            className="w-full"
                            disabled={otp.length !== OTP_LENGTH}
                            loading={isLoading}
                            loadingText="Verifying..."
                            type="submit"
                        >
                            Verify Email
                        </Button>
                    </form>

                    <div className="mt-6 space-y-4">
                        <div className="text-center">
                            <p className="text-sm text-muted-foreground mb-3">Didn&apos;t receive the code?</p>

                            {canResend ? (
                                <Button
                                    className="w-full"
                                    loading={resendLoading}
                                    loadingText="Sending..."
                                    type="button"
                                    variant="outline"
                                    onClick={handleResendCode}
                                >
                                    Resend Verification Code
                                </Button>
                            ) : (
                                <div className="text-sm text-muted-foreground">
                                    <p>You can request a new code in</p>
                                    <p className="font-medium text-primary">
                                        {Math.floor(timeLeft / 60)}:{(timeLeft % 60).toString().padStart(2, "0")}
                                    </p>
                                </div>
                            )}
                        </div>

                        <div className="flex items-center justify-center pt-4 border-t">
                            <Button
                                className="text-muted-foreground hover:text-foreground"
                                type="button"
                                variant="ghost"
                                onClick={logout}
                            >
                                <ArrowLeft className="w-4 h-4 mr-2" />
                                Back to Login
                            </Button>
                        </div>
                    </div>

                    <AuthFormFooter page="2fa" />
                </div>
            </div>
        </div>
    );
};

export default OTPVerificationPage;
