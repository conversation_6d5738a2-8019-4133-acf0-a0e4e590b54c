"use client";

import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { format, formatDistanceToNow } from "date-fns";
import {
    AlertCircle,
    Archive,
    Bell,
    Calendar,
    Check,
    CheckCheck,
    CheckCircle,
    Filter,
    Info,
    RefreshCw,
    Settings,
    Triangle,
    User,
} from "lucide-react";
import { useState } from "react";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Select } from "@/components/ui/select";
import { Skeleton } from "@/components/ui/skeleton";
import { Switch } from "@/components/ui/switch";
import {
    fetchNotifications,
    getUnreadCount,
    markAllAsRead,
    updateNotificationStatus,
} from "@/services/NotificationService";
import {
    NotificationFilter,
    NotificationPriority,
    NotificationType,
    NotificationWithRecipientDto,
    UpdateNotificationStatusRequest,
} from "@/types";

const ITEMS_PER_PAGE = 15;

// Helper functions for styling
const getTypeIcon = (type: NotificationType) => {
    switch (type) {
        case NotificationType.SUCCESS:
            return <CheckCircle className="h-4 w-4 text-green-500" />;
        case NotificationType.WARN:
            return <Triangle className="h-4 w-4 text-yellow-500" />;
        case NotificationType.ALERT:
            return <AlertCircle className="h-4 w-4 text-red-500" />;
        case NotificationType.ACTIVITY:
            return <Bell className="h-4 w-4 text-blue-500" />;
        case NotificationType.GENERAL:
            return <User className="h-4 w-4 text-gray-500" />;
        default:
            return <Info className="h-4 w-4 text-gray-500" />;
    }
};

const getPriorityConfig = (priority: NotificationPriority) => {
    switch (priority) {
        case NotificationPriority.URGENT:
            return { variant: "destructive" as const, color: "text-red-700", bg: "bg-red-50" };
        case NotificationPriority.HIGH:
            return { variant: "secondary" as const, color: "text-orange-700", bg: "bg-orange-50" };
        case NotificationPriority.MEDIUM:
            return { variant: "outline" as const, color: "text-blue-700", bg: "bg-blue-50" };
        default:
            return { variant: "outline" as const, color: "text-gray-700", bg: "bg-gray-50" };
    }
};

const getTypeConfig = (type: NotificationType) => {
    switch (type) {
        case NotificationType.SUCCESS:
            return { label: "Success", color: "text-green-700" };
        case NotificationType.WARN:
            return { label: "Warning", color: "text-yellow-700" };
        case NotificationType.ALERT:
            return { label: "Alert", color: "text-red-700" };
        case NotificationType.ACTIVITY:
            return { label: "Activity", color: "text-blue-700" };
        case NotificationType.GENERAL:
            return { label: "General", color: "text-gray-700" };
        default:
            return { label: "Info", color: "text-gray-700" };
    }
};

export default function NotificationsPage() {
    if (typeof document !== "undefined") {
        document.title = "Notifications - myNGO";
    }
    
    const queryClient = useQueryClient();
    const [currentPage, setCurrentPage] = useState(1);
    const [showFilters, setShowFilters] = useState(false);
    const [filters, setFilters] = useState<Partial<NotificationFilter>>({
        page: 1,
        size: ITEMS_PER_PAGE,
    });

    // Fetch notifications with filters
    const {
        data: notificationsResponse,
        isLoading,
        refetch,
        isFetching,
    } = useQuery({
        queryKey: ["notifications", filters],
        queryFn: () => fetchNotifications(filters),
        refetchOnWindowFocus: false,
    });

    // Fetch unread count
    const { data: unreadCountResponse } = useQuery({
        queryKey: ["notifications", "unread-count"],
        queryFn: getUnreadCount,
        refetchInterval: 30000, // Refetch every 30 seconds
    });

    // Update notification status mutation
    const updateStatusMutation = useMutation({
        mutationFn: ({ notificationId, data }: { notificationId: string; data: UpdateNotificationStatusRequest }) =>
            updateNotificationStatus(notificationId, data),
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ["notifications"] });
        },
    });

    // Mark all as read mutation
    const markAllAsReadMutation = useMutation({
        mutationFn: markAllAsRead,
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ["notifications"] });
        },
    });

    const notifications = notificationsResponse?.data || [];
    const totalCount = notificationsResponse?.total || 0;
    const unreadCount = unreadCountResponse?.data || 0;

    const handleMarkAsRead = (notificationId: string, isRead: boolean) => {
        updateStatusMutation.mutate({
            notificationId,
            data: { is_read: !isRead },
        });
    };

    const handleArchive = (notificationId: string, isArchived: boolean) => {
        updateStatusMutation.mutate({
            notificationId,
            data: { is_archived: !isArchived },
        });
    };

    const handleMarkAllAsRead = () => {
        markAllAsReadMutation.mutate();
    };

    const handleFilterChange = (key: keyof NotificationFilter, value: any) => {
        const newFilters = { ...filters, [key]: value, page: 1 };

        setFilters(newFilters);
        setCurrentPage(1);
    };

    const handlePageChange = (page: number) => {
        const newFilters = { ...filters, page };

        setFilters(newFilters);
        setCurrentPage(page);
    };

    const totalPages = Math.ceil(totalCount / ITEMS_PER_PAGE);

    if (isLoading) {
        return (
            <div className="flex-1 space-y-4 p-4 md:p-8 pt-6">
                <div className="flex items-center justify-between space-y-2">
                    <h2 className="text-3xl font-bold tracking-tight">Notifications</h2>
                    <div className="flex items-center space-x-2">
                        <Skeleton className="h-9 w-32" />
                    </div>
                </div>
                <div className="space-y-4">
                    {Array.from({ length: 5 }).map((_, i) => (
                        <Card key={i}>
                            <CardContent className="p-6">
                                <div className="flex items-start space-x-4">
                                    <Skeleton className="h-4 w-4 mt-1" />
                                    <div className="space-y-2 flex-1">
                                        <Skeleton className="h-4 w-1/3" />
                                        <Skeleton className="h-4 w-full" />
                                        <Skeleton className="h-3 w-1/4" />
                                    </div>
                                    <div className="flex space-x-2">
                                        <Skeleton className="h-8 w-8" />
                                        <Skeleton className="h-8 w-8" />
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    ))}
                </div>
            </div>
        );
    }

    return (
        <div className="flex-1 space-y-4 p-4 md:p-8 pt-6 mx-auto max-w-6xl">
            {/* Header */}
            <div className="flex items-center justify-between space-y-2">
                <div>
                    <h2 className="text-3xl font-bold tracking-tight flex items-center gap-2">
                        <Bell className="h-8 w-8" />
                        Notifications
                        {unreadCount > 0 && (
                            <Badge className="rounded-full" variant="destructive">
                                {unreadCount}
                            </Badge>
                        )}
                    </h2>
                    <p className="text-muted-foreground">Stay updated with your latest notifications and updates</p>
                </div>
                <div className="flex items-center space-x-2">
                    <Button disabled={isFetching} size="sm" variant="outline" onClick={() => refetch()}>
                        <RefreshCw className={`h-4 w-4 mr-2 ${isFetching ? "animate-spin" : ""}`} />
                        Refresh
                    </Button>
                    <Button size="sm" variant="outline" onClick={() => setShowFilters(!showFilters)}>
                        <Settings className="h-4 w-4 mr-2" />
                        Filters
                    </Button>
                    <Button
                        disabled={markAllAsReadMutation.isPending || unreadCount === 0}
                        size="sm"
                        onClick={handleMarkAllAsRead}
                    >
                        <CheckCheck className="h-4 w-4 mr-2" />
                        Mark All Read
                    </Button>
                </div>
            </div>

            {/* Filters */}
            {showFilters && (
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <Filter className="h-4 w-4" />
                            Filter Notifications
                        </CardTitle>
                        <CardDescription>Filter notifications by type, priority, and status</CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                            <div className="space-y-2">
                                <Label>Type</Label>
                                <Select
                                    options={[
                                        { label: "General", value: NotificationType.GENERAL },
                                        { label: "Info", value: NotificationType.INFO },
                                        { label: "Normal", value: NotificationType.SUCCESS },
                                        { label: "Warning", value: NotificationType.WARN },
                                        { label: "Alert", value: NotificationType.ALERT },
                                        { label: "Activity", value: NotificationType.ACTIVITY },
                                    ]}
                                    value={filters.type || ""}
                                    onValueChange={(value) => handleFilterChange("type", value || undefined)}
                                />
                            </div>

                            <div className="space-y-2">
                                <Label>Priority</Label>
                                <Select
                                    options={[
                                        { label: "Low", value: NotificationPriority.LOW },
                                        { label: "Medium", value: NotificationPriority.MEDIUM },
                                        { label: "High", value: NotificationPriority.HIGH },
                                        { label: "Urgent", value: NotificationPriority.URGENT },
                                    ]}
                                    value={filters.priority || ""}
                                    onValueChange={(value) => handleFilterChange("priority", value || undefined)}
                                />
                            </div>

                            <div className="space-y-3">
                                <Label>Status Filters</Label>
                                <div className="flex items-center space-x-2">
                                    <Switch
                                        checked={filters.is_read === false}
                                        id="show-unread"
                                        onCheckedChange={(checked) =>
                                            handleFilterChange("is_read", checked ? false : undefined)
                                        }
                                    />
                                    <Label htmlFor="show-unread">Show unread only</Label>
                                </div>
                            </div>

                            <div className="space-y-3">
                                <Label>Archive Options</Label>
                                <div className="flex items-center space-x-2">
                                    <Switch
                                        checked={filters.is_archived !== false}
                                        id="include-archived"
                                        onCheckedChange={(checked) =>
                                            handleFilterChange("is_archived", checked ? undefined : false)
                                        }
                                    />
                                    <Label htmlFor="include-archived">Include archived</Label>
                                </div>
                            </div>
                        </div>
                    </CardContent>
                </Card>
            )}

            {/* Notifications List */}
            <div className="space-y-4">
                {notifications.length === 0 ? (
                    <Card>
                        <CardContent className="flex flex-col items-center justify-center py-12">
                            <Bell className="h-16 w-16 text-muted-foreground/50 mb-4" />
                            <h3 className="text-lg font-semibold mb-2">No notifications found</h3>
                            <p className="text-sm text-muted-foreground text-center max-w-sm">
                                {Object.keys(filters).some(
                                    (key) =>
                                        key !== "page" && key !== "size" && filters[key as keyof NotificationFilter],
                                )
                                    ? "Try adjusting your filters to see more notifications."
                                    : "You're all caught up! No notifications to display."}
                            </p>
                        </CardContent>
                    </Card>
                ) : (
                    notifications.map((notification: NotificationWithRecipientDto) => {
                        const priorityConfig = getPriorityConfig(notification.priority);
                        const typeConfig = getTypeConfig(notification.type);

                        return (
                            <Card
                                key={notification.recipient_id}
                                className={`transition-all duration-200 hover:shadow-md ${
                                    !notification.is_read ? "border-l-4 border-l-blue-500 bg-blue-50/30" : ""
                                } ${notification.is_archived ? "opacity-60" : ""}`}
                            >
                                <CardContent className="p-6">
                                    <div className="flex items-start justify-between gap-4">
                                        <div className="flex items-start gap-4 flex-1 min-w-0">
                                            <div className="mt-1 flex-shrink-0">{getTypeIcon(notification.type)}</div>
                                            <div className="flex-1 min-w-0 space-y-3">
                                                <div className="flex items-start justify-between gap-4">
                                                    <div className="min-w-0 flex-1">
                                                        <h3
                                                            className={`text-sm font-medium leading-5 ${
                                                                !notification.is_read ? "font-semibold" : ""
                                                            }`}
                                                        >
                                                            {notification.title}
                                                        </h3>
                                                        <p className="text-sm text-muted-foreground mt-1 leading-5">
                                                            {notification.message}
                                                        </p>
                                                    </div>
                                                    <div className="flex items-start gap-2 flex-shrink-0">
                                                        <Badge className="text-xs" variant={priorityConfig.variant}>
                                                            {notification.priority}
                                                        </Badge>
                                                        <Badge className="text-xs" variant="outline">
                                                            {typeConfig.label}
                                                        </Badge>
                                                        {notification.is_archived && (
                                                            <Badge className="text-xs" variant="secondary">
                                                                Archived
                                                            </Badge>
                                                        )}
                                                    </div>
                                                </div>

                                                <div className="flex items-center justify-between text-xs text-muted-foreground">
                                                    <div className="flex items-center gap-4">
                                                        <span className="flex items-center gap-1">
                                                            <Calendar className="h-3 w-3" />
                                                            {format(
                                                                new Date(notification.notification_created_at),
                                                                "MMM d, yyyy 'at' h:mm a",
                                                            )}
                                                        </span>
                                                        <span>
                                                            {formatDistanceToNow(
                                                                new Date(notification.notification_created_at),
                                                                { addSuffix: true },
                                                            )}
                                                        </span>
                                                    </div>
                                                    {notification.read_at && (
                                                        <span className="text-green-600">
                                                            Read{" "}
                                                            {formatDistanceToNow(new Date(notification.read_at), {
                                                                addSuffix: true,
                                                            })}
                                                        </span>
                                                    )}
                                                </div>
                                            </div>
                                        </div>

                                        <div className="flex items-center gap-1 flex-shrink-0">
                                            <Button
                                                className="h-8 w-8 p-0"
                                                disabled={updateStatusMutation.isPending}
                                                size="sm"
                                                title={notification.is_read ? "Mark as unread" : "Mark as read"}
                                                variant="ghost"
                                                onClick={() =>
                                                    handleMarkAsRead(notification.notification_id, notification.is_read)
                                                }
                                            >
                                                <Check
                                                    className={`h-4 w-4 ${notification.is_read ? "text-green-600" : "text-muted-foreground"}`}
                                                />
                                            </Button>
                                            <Button
                                                className="h-8 w-8 p-0"
                                                disabled={updateStatusMutation.isPending}
                                                size="sm"
                                                title={notification.is_archived ? "Unarchive" : "Archive"}
                                                variant="ghost"
                                                onClick={() =>
                                                    handleArchive(
                                                        notification.notification_id,
                                                        notification.is_archived,
                                                    )
                                                }
                                            >
                                                <Archive
                                                    className={`h-4 w-4 ${notification.is_archived ? "text-orange-600" : "text-muted-foreground"}`}
                                                />
                                            </Button>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>
                        );
                    })
                )}
            </div>

            {/* Pagination */}
            {totalPages > 1 && (
                <Card>
                    <CardContent className="p-4">
                        <div className="flex items-center justify-between">
                            <div className="text-sm text-muted-foreground">
                                Showing {(currentPage - 1) * ITEMS_PER_PAGE + 1} to{" "}
                                {Math.min(currentPage * ITEMS_PER_PAGE, totalCount)} of {totalCount} notifications
                            </div>
                            <div className="flex items-center gap-2">
                                <Button
                                    disabled={currentPage === 1}
                                    size="sm"
                                    variant="outline"
                                    onClick={() => handlePageChange(currentPage - 1)}
                                >
                                    Previous
                                </Button>
                                {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                                    const page = Math.max(1, currentPage - 2) + i;

                                    if (page > totalPages) return null;

                                    return (
                                        <Button
                                            key={page}
                                            size="sm"
                                            variant={currentPage === page ? "default" : "outline"}
                                            onClick={() => handlePageChange(page)}
                                        >
                                            {page}
                                        </Button>
                                    );
                                })}
                                <Button
                                    disabled={currentPage === totalPages}
                                    size="sm"
                                    variant="outline"
                                    onClick={() => handlePageChange(currentPage + 1)}
                                >
                                    Next
                                </Button>
                            </div>
                        </div>
                    </CardContent>
                </Card>
            )}
        </div>
    );
}
