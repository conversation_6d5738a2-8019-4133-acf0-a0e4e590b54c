"use client";

import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { ReactQueryDevtools } from "@tanstack/react-query-devtools";
import { ThemeProvider as NextThemesProvider } from "next-themes";
import * as React from "react";
import { Toaster } from "sonner";

import { LoadingProvider } from "@/context/LoadingContext";
import { ErrorProvider } from "@/context/ErrorContext";
import { DeleteConfirmationProvider } from "@/context/DeleteItemContext";
import NetworkProvider from "@/context/NetworkContext";
import { RouterProvider } from "@/components/providers/RouterProvider";

function ThemeProvider({ children, ...props }: React.ComponentProps<typeof NextThemesProvider>) {
    return (
        <NextThemesProvider
            attribute="class"
            defaultTheme="system"
            disableTransitionOnChange={false}
            enableSystem={true}
            {...props}
        >
            {children}
        </NextThemesProvider>
    );
}

export default function Providers({ children }: { children: React.ReactNode }) {
    const [queryClient] = React.useState(
        () =>
            new QueryClient({
                defaultOptions: {
                    queries: {
                        staleTime: 60 * 1000,
                        gcTime: 5 * 60 * 1000,
                        retry: 2,
                        refetchOnWindowFocus: false,
                    },
                },
            }),
    );

    return (
        <QueryClientProvider client={queryClient}>
            <ThemeProvider>
                <RouterProvider>
                    <ErrorProvider>
                        <DeleteConfirmationProvider>
                            <LoadingProvider>
                                <NetworkProvider>{children}</NetworkProvider>
                                {/* Only show devtools in development */}
                                {process.env.NODE_ENV === "development" && <ReactQueryDevtools initialIsOpen={false} />}
                                <Toaster richColors position="bottom-right" />
                            </LoadingProvider>
                        </DeleteConfirmationProvider>
                    </ErrorProvider>
                </RouterProvider>
            </ThemeProvider>
        </QueryClientProvider>
    );
}
