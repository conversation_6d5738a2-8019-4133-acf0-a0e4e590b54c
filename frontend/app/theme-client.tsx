"use client";

import { useTheme } from "next-themes";
import { useEffect } from "react";

export function ThemeClient() {
    const { setTheme, theme, systemTheme } = useTheme();

    useEffect(() => {
        // Force next-themes to handle system theme changes automatically
        // This ensures that when system theme changes, it updates immediately
        const handleSystemChange = () => {
            if (theme === "system") {
                // Force a re-evaluation of the system theme
                setTheme("system");
            }
        };

        const mediaQuery = window.matchMedia("(prefers-color-scheme: dark)");

        mediaQuery.addEventListener("change", handleSystemChange);

        return () => mediaQuery.removeEventListener("change", handleSystemChange);
    }, [theme, setTheme]);

    return null;
}
