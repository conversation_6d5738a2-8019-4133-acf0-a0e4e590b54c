"use client";

import React, { useEffect, useState } from "react";
import { toast } from "sonner";
import { BadgeDollarSignIcon } from "lucide-react";

import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { useSystemConfiguration } from "@/hooks/use-system-configuration";
import { useError } from "@/context/ErrorContext";
import { useLoading } from "@/context/LoadingContext";

const defaultValues = {
    payment_modes: ["Bank Transfer", "Cash", "Cheque"],
    invoice_number_prefix: "NGO-",
    receipt_number_prefix: "NGO-R-",
};

export default function PaymentModesPage() {
    if (typeof document !== "undefined") {
        document.title = "Payment Modes - myNGO";
    }
    
    const { loading, updating, error, data, loadConfiguration, updateConfiguration } = useSystemConfiguration();
    const { showError, hideError } = useError();
    const { showLoading, hideLoading } = useLoading();
    const [formData, setFormData] = useState<Record<string, any>>(defaultValues);
    const [errors, setErrors] = useState<Record<string, string>>({});
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [modeInput, setModeInput] = useState("");
    const [selectedModes, setSelectedModes] = useState<string[]>([]);

    useEffect(() => {
        loadConfiguration("payment_modes");
    }, [loadConfiguration]);

    useEffect(() => {
        if (loading) showLoading("Loading payment modes...");
        if (updating) showLoading("Updating payment modes...");
        !loading && !updating ? hideLoading() : null;
    }, [loading, updating]);

    useEffect(() => {
        error ? showError("Failed to load payment modes", () => loadConfiguration("payment_modes")) : hideError();
    }, [error]);

    useEffect(() => {
        if (data && Object.keys(data).length > 0) {
            const props = { ...defaultValues, ...data };
            let paymentModes: string[] = [];

            if (typeof props.payment_modes === "string") {
                paymentModes = (props.payment_modes as string)
                    .split(",")
                    .map((m: string) => m.trim())
                    .filter(Boolean);
            } else if (Array.isArray(props.payment_modes)) {
                paymentModes = props.payment_modes;
            } else {
                paymentModes = defaultValues.payment_modes;
            }

            setFormData({
                ...props,
                payment_modes: paymentModes,
            });
        }
    }, [data]);

    const handleInputChange = (field: string, value: any) => {
        setFormData((prev) => ({ ...prev, [field]: value }));
        if (errors[field]) setErrors((prev) => ({ ...prev, [field]: "" }));
    };

    const handleAddMode = () => {
        const trimmed = modeInput.trim();

        if (!trimmed) return;
        if (formData.payment_modes.includes(trimmed)) return;
        setFormData((prev) => ({ ...prev, payment_modes: [...prev.payment_modes, trimmed] }));
        setModeInput("");
    };

    const handleRemoveModes = () => {
        setFormData((prev) => ({
            ...prev,
            payment_modes: prev.payment_modes.filter((mode: string) => !selectedModes.includes(mode)),
        }));
        setSelectedModes([]);
    };

    const handleModeSelect = (mode: string) => {
        setSelectedModes((prev) => (prev.includes(mode) ? prev.filter((m) => m !== mode) : [...prev, mode]));
    };

    const validateForm = (): boolean => {
        const newErrors: Record<string, string> = {};

        if (!formData.invoice_number_prefix) newErrors.invoice_number_prefix = "Required";
        if (!formData.receipt_number_prefix) newErrors.receipt_number_prefix = "Required";
        setErrors(newErrors);

        return Object.keys(newErrors).length === 0;
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        if (!validateForm()) return;
        setIsSubmitting(true);
        // Convert payment_modes array to comma-separated string for saving
        const submitData = {
            ...formData,
            payment_modes: Array.isArray(formData.payment_modes)
                ? formData.payment_modes.join(",")
                : formData.payment_modes,
        };

        await updateConfiguration({ group: "payment_modes", properties: submitData });
        setIsSubmitting(false);
        if (!error) toast.success("Payment modes updated successfully!");
    };

    return (
        <div className="max-w-full mx-auto space-y-8">
            <div className="flex items-center gap-3 mt-4">
                <div className="p-2 bg-primary/10 rounded-lg">
                    <BadgeDollarSignIcon className="h-6 w-6 text-primary" />
                </div>
                <div>
                    <h1 className="text-2xl font-bold">Payment Modes</h1>
                    <p className="text-muted-foreground">Configure your system payment modes</p>
                </div>
            </div>
            <div className="max-w-full">
                <Card>
                    <CardContent>
                        <form className="space-y-8" onSubmit={handleSubmit}>
                            <div className="space-y-8">
                                <label className="font-bold text-lg block mb-4">Available Modes of Payment</label>
                                <div className="flex flex-wrap gap-2">
                                    {formData.payment_modes.map((mode: string) => (
                                        <Button
                                            key={mode}
                                            className={selectedModes.includes(mode) ? "bg-muted" : ""}
                                            type="button"
                                            variant={selectedModes.includes(mode) ? "secondary" : "outline"}
                                            onClick={() => handleModeSelect(mode)}
                                        >
                                            {mode}
                                        </Button>
                                    ))}
                                </div>
                                <div className="flex gap-2 mt-2">
                                    <Input
                                        className="flex-1"
                                        placeholder="Add new mode"
                                        value={modeInput}
                                        onChange={(e) => setModeInput(e.target.value)}
                                    />
                                    <Button type="button" variant="outline" onClick={handleAddMode}>
                                        Add
                                    </Button>
                                    <Button
                                        disabled={selectedModes.length === 0}
                                        type="button"
                                        variant="destructive"
                                        onClick={handleRemoveModes}
                                    >
                                        Remove
                                    </Button>
                                </div>
                            </div>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 items-center">
                                <label className="font-semibold" htmlFor="invoice_number_prefix">
                                    Invoice Number Prefix
                                </label>
                                <Input
                                    className="w-full"
                                    disabled={loading || isSubmitting}
                                    error={errors.invoice_number_prefix}
                                    id="invoice_number_prefix"
                                    value={formData.invoice_number_prefix}
                                    onChange={(e) => handleInputChange("invoice_number_prefix", e.target.value)}
                                />
                                <label className="font-semibold" htmlFor="receipt_number_prefix">
                                    Receipt Number Prefix
                                </label>
                                <Input
                                    className="w-full"
                                    disabled={loading || isSubmitting}
                                    error={errors.receipt_number_prefix}
                                    id="receipt_number_prefix"
                                    value={formData.receipt_number_prefix}
                                    onChange={(e) => handleInputChange("receipt_number_prefix", e.target.value)}
                                />
                            </div>
                            <div className="flex justify-end pt-4">
                                <Button className="min-w-32" disabled={loading} loading={isSubmitting} type="submit">
                                    Save
                                </Button>
                            </div>
                        </form>
                    </CardContent>
                </Card>
            </div>
        </div>
    );
}
