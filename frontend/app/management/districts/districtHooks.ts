// React Query hooks for districts CRUD
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";

import { fetchAll, create, update, deleteDistrict, getById, DistrictRequest } from "@/services/DistrictService";
import { DistrictFilter } from "@/types";

export function useDistricts(filter: Partial<DistrictFilter> = {}) {
    return useQuery({
        queryKey: ["districts", filter],
        queryFn: () => fetchAll(filter),
        select: (res) => res.data || [],
        retry: 2,
    });
}

export function useDistrict(id: string) {
    return useQuery({
        queryKey: ["districts", id],
        queryFn: () => getById(id),
        select: (res) => res.data,
        enabled: !!id,
    });
}

export function useCreateDistrict() {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: (district: DistrictRequest) => create(district),
        onSuccess: () => queryClient.invalidateQueries({ queryKey: ["districts"] }),
    });
}

export function useUpdateDistrict() {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: ({ id, data }: { id: string; data: DistrictRequest }) => update(id, data),
        onSuccess: () => queryClient.invalidateQueries({ queryKey: ["districts"] }),
    });
}

export function useDeleteDistrict() {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: (districtId: string) => deleteDistrict(districtId),
        onSuccess: () => queryClient.invalidateQueries({ queryKey: ["districts"] }),
    });
}
