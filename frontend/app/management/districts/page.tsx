"use client";

import { useQuery } from "@tanstack/react-query";
import { useEffect, useState } from "react";
import { toast } from "sonner";

import { useCreateDistrict, useDeleteDistrict, useDistricts, useUpdateDistrict } from "./districtHooks";

import { DistrictsTable } from "@/components/district/districts-table";
import { Button } from "@/components/ui/button";
import { Dialog, DialogContent, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Select } from "@/components/ui/select";
import { useDeleteConfirmation } from "@/context/DeleteItemContext";
import { useError } from "@/context/ErrorContext";
import { DistrictRequest } from "@/services/DistrictService";
import { fetchRegions } from "@/services/SettingsService";
import { DistrictDto, RegionDto } from "@/types";

interface DistrictFormModalProps {
    open: boolean;
    onClose: () => void;
    onSubmit: (district: DistrictRequest) => void;
    initialDistrict?: Partial<DistrictDto>;
    loading?: boolean;
    regions: RegionDto[];
}

export default function DistrictsManagementPage() {
    if (typeof document !== "undefined") {
        document.title = "District Management - myNGO";
    }
    
    const [modalOpen, setModalOpen] = useState(false);
    const [editingDistrict, setEditingDistrict] = useState<Partial<DistrictDto> | undefined>(undefined);

    const { showError, hideError } = useError();
    const { openDeleteDialog } = useDeleteConfirmation();

    const { data: districtsData, isLoading, isError } = useDistricts({ page: 1, size: 100 });
    const { data: regionsData } = useQuery({
        queryKey: ["regions"],
        queryFn: () => fetchRegions(),
        select: (res) => res.data || [],
        retry: 2,
    });

    const createMutation = useCreateDistrict();
    const updateMutation = useUpdateDistrict();
    const deleteMutation = useDeleteDistrict();

    useEffect(() => {
        if (isError) {
            showError("Failed to load districts. Please try again later.");

            return;
        }
        hideError();
    }, [isError]);

    // Handlers for UI actions
    const handleCreateDistrict = () => {
        setEditingDistrict(undefined);
        setModalOpen(true);
    };

    const handleEditDistrict = (district: DistrictDto) => {
        setEditingDistrict(district);
        setModalOpen(true);
    };

    const handleDeleteDistrict = (district: DistrictDto) => {
        openDeleteDialog({
            title: "Delete District",
            description: `Are you sure you want to delete the district '${district.name}'?`,
            onConfirm: () => {
                deleteMutation.mutate(district.id);
                toast.warning("District deleted successfully");
            },
            itemName: district.name,
        });
    };

    const handleModalClose = () => {
        setModalOpen(false);
        setEditingDistrict(undefined);
    };

    const handleModalSubmit = (district: DistrictRequest) => {
        if (editingDistrict && editingDistrict.id) {
            updateMutation.mutate({ id: editingDistrict.id, data: district }, { onSuccess: handleModalClose });
            toast.success("District updated successfully");
        } else {
            createMutation.mutate(district, { onSuccess: handleModalClose });
            toast.success("District created successfully");
        }
    };

    return (
        <>
            <DistrictsTable
                districts={districtsData || []}
                loading={isLoading}
                onCreate={handleCreateDistrict}
                onDelete={handleDeleteDistrict}
                onEdit={handleEditDistrict}
            />

            <DistrictFormModal
                initialDistrict={editingDistrict}
                loading={createMutation.isPending || updateMutation.isPending}
                open={modalOpen}
                regions={regionsData || []}
                onClose={handleModalClose}
                onSubmit={handleModalSubmit}
            />
        </>
    );
}

function DistrictFormModal({
    open,
    onClose,
    onSubmit,
    initialDistrict = {},
    loading,
    regions,
}: DistrictFormModalProps) {
    const [name, setName] = useState(initialDistrict.name || "");
    const [code, setCode] = useState(initialDistrict.code || "");
    const [regionId, setRegionId] = useState(initialDistrict.region_id || "");

    useEffect(() => {
        if (!initialDistrict || !initialDistrict.id) return;
        setName(initialDistrict.name || "");
        setCode(initialDistrict.code || "");
        setRegionId(initialDistrict.region_id || "");
    }, [initialDistrict]);

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        if (!name || !code || !regionId) {
            toast.error("Please fill in all required fields");

            return;
        }
        onSubmit({ name, code, region_id: regionId });
    };

    return (
        <Dialog open={open} onOpenChange={onClose}>
            <DialogContent>
                <DialogHeader>
                    <DialogTitle>{initialDistrict.id ? "Edit District" : "Create District"}</DialogTitle>
                </DialogHeader>
                <form className="space-y-4" onSubmit={handleSubmit}>
                    <Input
                        required
                        label="Name"
                        placeholder="Enter district name"
                        value={name}
                        onChange={(e) => setName(e.target.value)}
                    />
                    <Input
                        required
                        label="Code"
                        maxLength={5}
                        placeholder="Enter district code"
                        value={code}
                        onChange={(e) => setCode(e.target.value)}
                    />
                    <div className="space-y-2">
                        <Select
                            label="Region *"
                            options={regions.map((region) => ({ value: region.id, label: region.name }))}
                            value={regionId}
                            onValueChange={setRegionId}
                        />
                    </div>
                    <DialogFooter>
                        <Button disabled={loading} type="button" variant="secondary" onClick={onClose}>
                            Cancel
                        </Button>
                        <Button loading={loading} type="submit" variant="default">
                            {initialDistrict.id ? "Update" : "Create"}
                        </Button>
                    </DialogFooter>
                </form>
            </DialogContent>
        </Dialog>
    );
}
