// React Query hooks for zones CRUD
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";

import { fetchAll, create, update, deleteZone, getById, ZoneRequest } from "@/services/ZoneService";
import { LoadableItemFilter } from "@/types";

export function useZones(filter: Partial<LoadableItemFilter> = {}) {
    return useQuery({
        queryKey: ["zones", filter],
        queryFn: () => fetchAll(filter),
        select: (res) => res.data || [],
        retry: 2,
    });
}

export function useZone(id: string) {
    return useQuery({
        queryKey: ["zones", id],
        queryFn: () => getById(id),
        select: (res) => res.data,
        enabled: !!id,
    });
}

export function useCreateZone() {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: (zone: ZoneRequest) => create(zone),
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ["zones"] });
            queryClient.invalidateQueries({ queryKey: ["loadable_items"] });
        },
    });
}

export function useUpdateZone() {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: ({ id, data }: { id: string; data: ZoneRequest }) => update(id, data),
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ["zones"] });
            queryClient.invalidateQueries({ queryKey: ["loadable_items"] });
        },
    });
}

export function useDeleteZone() {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: (zoneId: string) => deleteZone(zoneId),
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ["zones"] });
            queryClient.invalidateQueries({ queryKey: ["loadable_items"] });
        },
    });
}
