"use client";

import { useEffect, useState } from "react";
import { toast } from "sonner";

import { useZones, useCreateZone, useUpdateZone, useDeleteZone } from "./zoneHooks";

import { ZoneRequest } from "@/services/ZoneService";
import { LoadableItemDto } from "@/types";
import { <PERSON><PERSON>, DialogContent, <PERSON>alogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { ZonesTable } from "@/components/zone/zones-table";
import { useError } from "@/context/ErrorContext";
import { useDeleteConfirmation } from "@/context/DeleteItemContext";

interface ZoneFormModalProps {
    open: boolean;
    onClose: () => void;
    onSubmit: (zone: ZoneRequest) => void;
    initialZone?: Partial<LoadableItemDto>;
    loading?: boolean;
}

export default function ZonesManagementPage() {
    if (typeof document !== "undefined") {
        document.title = "Zone Management - myNGO";
    }
    
    const [modalOpen, setModalOpen] = useState(false);
    const [editingZone, setEditingZone] = useState<Partial<LoadableItemDto> | undefined>(undefined);

    const { showError, hideError } = useError();
    const { openDeleteDialog } = useDeleteConfirmation();

    const { data: zonesData, isLoading, isError } = useZones();
    const createMutation = useCreateZone();
    const updateMutation = useUpdateZone();
    const deleteMutation = useDeleteZone();

    useEffect(() => {
        if (isError) {
            showError("Failed to load zones. Please try again later.");

            return;
        }
        hideError();
    }, [isError]);

    // Handlers for UI actions
    const handleCreateZone = () => {
        setEditingZone(undefined);
        setModalOpen(true);
    };

    const handleEditZone = (zone: LoadableItemDto) => {
        setEditingZone(zone);
        setModalOpen(true);
    };

    const handleDeleteZone = (zone: LoadableItemDto) => {
        openDeleteDialog({
            title: "Delete Zone",
            description: `Are you sure you want to delete the zone '${zone.display_value}'?`,
            onConfirm: () => {
                deleteMutation.mutate(zone.id);
                toast.warning("Zone deleted successfully");
            },
            itemName: zone.display_value,
        });
    };

    const handleModalClose = () => {
        setModalOpen(false);
        setEditingZone(undefined);
    };

    const handleModalSubmit = (zone: ZoneRequest) => {
        if (editingZone && editingZone.id) {
            updateMutation.mutate({ id: editingZone.id, data: zone }, { onSuccess: handleModalClose });
            toast.success("Zone updated successfully");
        } else {
            createMutation.mutate(zone, { onSuccess: handleModalClose });
            toast.success("Zone created successfully");
        }
    };

    return (
        <>
            <ZonesTable
                loading={isLoading}
                zones={zonesData || []}
                onCreate={handleCreateZone}
                onDelete={handleDeleteZone}
                onEdit={handleEditZone}
            />

            <ZoneFormModal
                initialZone={editingZone}
                loading={createMutation.isPending || updateMutation.isPending}
                open={modalOpen}
                onClose={handleModalClose}
                onSubmit={handleModalSubmit}
            />
        </>
    );
}

function ZoneFormModal({ open, onClose, onSubmit, initialZone = {}, loading }: ZoneFormModalProps) {
    const [displayValue, setDisplayValue] = useState(initialZone.display_value || "");
    const [code, setCode] = useState(initialZone.code || "");
    const [description, setDescription] = useState(initialZone.description || "");

    useEffect(() => {
        if (!initialZone || !initialZone.id) return;
        setDisplayValue(initialZone.display_value || "");
        setCode(initialZone.code || "");
        setDescription(initialZone.description || "");
    }, [initialZone]);

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        if (!displayValue || !code) {
            toast.error("Please fill in all required fields");

            return;
        }
        onSubmit({
            display_value: displayValue,
            code,
            description: description || undefined,
        });
    };

    return (
        <Dialog open={open} onOpenChange={onClose}>
            <DialogContent>
                <DialogHeader>
                    <DialogTitle>{initialZone.id ? "Edit Zone" : "Create Zone"}</DialogTitle>
                </DialogHeader>
                <form className="space-y-4" onSubmit={handleSubmit}>
                    <Input
                        required
                        label="Name"
                        placeholder="Enter zone name"
                        value={displayValue}
                        onChange={(e) => setDisplayValue(e.target.value)}
                    />
                    <Input
                        required
                        label="Code"
                        placeholder="Enter zone code"
                        value={code}
                        onChange={(e) => setCode(e.target.value)}
                    />
                    <div className="space-y-2">
                        <label className="text-sm font-medium">Description</label>
                        <Textarea
                            placeholder="Enter zone description (optional)"
                            value={description}
                            onChange={(e) => setDescription(e.target.value)}
                        />
                    </div>
                    <DialogFooter>
                        <Button disabled={loading} type="button" variant="secondary" onClick={onClose}>
                            Cancel
                        </Button>
                        <Button loading={loading} type="submit" variant="default">
                            {initialZone.id ? "Update" : "Create"}
                        </Button>
                    </DialogFooter>
                </form>
            </DialogContent>
        </Dialog>
    );
}
