"use client";

import { Building2, Settings } from "lucide-react";
import React, { useEffect, useState } from "react";
import { toast } from "sonner";

import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { useError } from "@/context/ErrorContext";
import { useLoading } from "@/context/LoadingContext";
import { SystemConfigurationData, useSystemConfiguration } from "@/hooks/use-system-configuration";

interface SecurityFormProps {
    initialData: SystemConfigurationData;
    loading?: boolean;
    onSubmit?: (data: any) => Promise<void>;
}

const defaultValues = {
    account_lockout_duration: 20,
    account_auto_unlock_duration: 5,
    account_lockout_threshold: 3,
};

const SecurityForm: React.FC<SecurityFormProps> = ({ initialData, loading = false, onSubmit }) => {
    const [formData, setFormData] = useState<Record<string, any>>(defaultValues);
    const [errors, setErrors] = useState<Record<string, string>>({});
    const [isSubmitting, setIsSubmitting] = useState(false);

    useEffect(() => {
        if (initialData && Object.keys(initialData).length > 0) {
            setFormData({ ...initialData });
        }
    }, [initialData]);

    const handleInputChange = (field: string, value: any) => {
        setFormData((prev) => ({ ...prev, [field]: value || 0 }));
        if (errors[field]) setErrors((prev) => ({ ...prev, [field]: "" }));
    };

    const validateForm = (): boolean => {
        const newErrors: Record<string, string> = {};

        if (!formData.account_lockout_duration) newErrors.account_lockout_duration = "Required";
        if (formData.account_auto_unlock_duration === undefined || formData.account_auto_unlock_duration === null)
            newErrors.account_auto_unlock_duration = "Required";
        if (!formData.account_lockout_threshold) newErrors.account_lockout_threshold = "Required";
        setErrors(newErrors);

        return Object.keys(newErrors).length === 0;
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        if (!validateForm()) return;
        setIsSubmitting(true);
        await onSubmit?.({ group: "security", properties: formData });
        setIsSubmitting(false);
    };

    return (
        <div className="mx-auto py-6">
            <div className="max-w-full mx-auto space-y-6">
                <div className="flex items-center gap-3 mb-6">
                    <div className="p-2 bg-primary/10 rounded-lg">
                        <Settings className="h-6 w-6 text-primary" />
                    </div>
                    <div>
                        <h1 className="text-2xl font-bold">System Security</h1>
                        <p className="text-muted-foreground">Configure your system security settings and preferences</p>
                    </div>
                </div>
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <Building2 className="h-5 w-5" />
                            Account Security Settings
                        </CardTitle>
                        <CardDescription>Configure system account settings</CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-4">
                        <form className="space-y-6" onSubmit={handleSubmit}>
                            <div>
                                <Input
                                    disabled={loading || isSubmitting}
                                    error={errors.account_lockout_duration}
                                    id="account_lockout_duration"
                                    label="Lockout Duration"
                                    min={1}
                                    type="number"
                                    value={parseInt(formData.account_lockout_duration)}
                                    onChange={(e) => handleInputChange("account_lockout_duration", e.target.value)}
                                />
                                <code>minutes of no activity</code>
                            </div>

                            <div>
                                <Input
                                    disabled={loading || isSubmitting}
                                    error={errors.account_auto_unlock_duration}
                                    id="account_auto_unlock_duration"
                                    label="Auto Unlock Duration"
                                    min={0}
                                    type="number"
                                    value={parseInt(formData.account_auto_unlock_duration)}
                                    onChange={(e) => handleInputChange("account_auto_unlock_duration", e.target.value)}
                                />
                                <span>
                                    minutes <em className="text-muted-foreground">(0 = no auto-unlock)</em>
                                </span>
                            </div>

                            <div>
                                <Input
                                    disabled={loading || isSubmitting}
                                    error={errors.account_lockout_threshold}
                                    id="account_lockout_threshold"
                                    label="Lockout Threshold"
                                    min={1}
                                    type="number"
                                    value={parseInt(formData.account_lockout_threshold)}
                                    onChange={(e) => handleInputChange("account_lockout_threshold", e.target.value)}
                                />
                                <span>invalid logon attempts</span>
                            </div>
                            <div className="flex justify-end pt-4">
                                <Button className="min-w-32" disabled={loading} loading={isSubmitting} type="submit">
                                    Save
                                </Button>
                            </div>
                        </form>
                    </CardContent>
                </Card>
            </div>
        </div>
    );
};

export default function SecurityPage() {
    if (typeof document !== "undefined") {
        document.title = "Security Settings - myNGO";
    }
    
    const { loading, updating, error, data, loadConfiguration, updateConfiguration } = useSystemConfiguration();
    const { showError, hideError } = useError();
    const { showLoading, hideLoading } = useLoading();

    useEffect(() => {
        loadConfiguration("security");
    }, [loadConfiguration]);

    useEffect(() => {
        if (loading) showLoading("Loading security settings...");
        if (updating) showLoading("Updating security settings...");
        !loading && !updating ? hideLoading() : null;
    }, [loading, updating]);

    useEffect(() => {
        error ? showError("Failed to load security settings", () => loadConfiguration("security")) : hideError();
    }, [error]);

    const handleSubmit = async (formData: any) => {
        await updateConfiguration(formData);
        if (!error) toast.success("Security settings updated successfully!");
    };

    return <SecurityForm initialData={data} loading={loading || updating} onSubmit={handleSubmit} />;
}
