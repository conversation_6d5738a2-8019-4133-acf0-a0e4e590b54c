"use client";

import { useEffect, useState } from "react";
import { toast } from "sonner";

import { useRoles, useCreateRole, useUpdateRole, useDeleteRole } from "./roleHooks";

import { RolePermissionDto } from "@/types";
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, <PERSON><PERSON>Footer, Di<PERSON>Header, DialogTitle } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { RolesTable } from "@/components/role/roles-table";
import { useError } from "@/context/ErrorContext";
import { useDeleteConfirmation } from "@/context/DeleteItemContext";
import PermissionsForm from "@/components/role/permissions-form";
import { Separator } from "@/components/ui/separator";

interface RoleFormModalProps {
    open: boolean;
    onClose: () => void;
    onSubmit: (role: Partial<RolePermissionDto>) => void;
    initialRole?: Partial<RolePermissionDto>;
    loading?: boolean;
}

export default function RolesManagementPage() {
    if (typeof document !== "undefined") {
        document.title = "Role Management - myNGO";
    }
    
    const [modalOpen, setModalOpen] = useState(false);
    const [permissionModalOpen, setPermissionModalOpen] = useState(false);
    const [editingRole, setEditingRole] = useState<Partial<RolePermissionDto> | undefined>(undefined);

    const { showError, hideError } = useError();
    const { openDeleteDialog } = useDeleteConfirmation();

    const { data: rolesData, isLoading, isError } = useRoles();
    const createMutation = useCreateRole();
    const updateMutation = useUpdateRole();
    const deleteMutation = useDeleteRole();

    useEffect(() => {
        if (isError) {
            showError("Failed to load roles. Please try again later.");

            return;
        }
        hideError();
    }, [isError]);

    // Handlers for UI actions
    const handleCreateRole = () => {
        setEditingRole(undefined);
        setModalOpen(true);
    };

    const handleEditRole = (role: RolePermissionDto) => {
        setEditingRole(role);
        setModalOpen(true);
    };

    const handleEditPermissions = (role: RolePermissionDto) => {
        setEditingRole(role);
        setPermissionModalOpen(true);
    };

    const handleDeleteRole = (role: RolePermissionDto) => {
        openDeleteDialog({
            title: "Delete Role",
            description: `Are you sure you want to delete the role '${role.name}'?`,
            onConfirm: () => {
                deleteMutation.mutate(role.id);
                toast.warning("Role deleted successfully");
            },
            itemName: role.name,
        });
    };

    const handleModalClose = () => {
        setModalOpen(false);
        setEditingRole(undefined);
    };

    const handleModalSubmit = (role: Partial<RolePermissionDto>) => {
        if (editingRole && editingRole.id) {
            updateMutation.mutate(role, { onSuccess: handleModalClose });
            toast.success("Role updated successfully");
        } else {
            createMutation.mutate(role, { onSuccess: handleModalClose });
            toast.success("Role created successfully");
        }
    };

    const handlePermissionsSave = (role: Partial<RolePermissionDto>) => {
        handleModalSubmit(role);
        setPermissionModalOpen(false);
        toast.success(`Permissions for ${role.name} updated successfully`);
    };

    return (
        <>
            <RolesTable
                loading={isLoading}
                roles={rolesData || []}
                onCreate={handleCreateRole}
                onDelete={handleDeleteRole}
                onEdit={handleEditRole}
                onEditPermissions={handleEditPermissions}
            />

            <RoleFormModal
                initialRole={editingRole}
                loading={createMutation.isPending || updateMutation.isPending}
                open={modalOpen}
                onClose={handleModalClose}
                onSubmit={handleModalSubmit}
            />

            <Dialog open={permissionModalOpen} onOpenChange={() => setPermissionModalOpen(false)}>
                <DialogContent className="md:max-w-6xl max-h-[90vh] overflow-y-auto">
                    <DialogHeader>
                        <DialogTitle>Manage {editingRole?.name} Permissions</DialogTitle>
                    </DialogHeader>
                    <Separator className="mt-4" />
                    <PermissionsForm
                        loading={false}
                        role={editingRole!}
                        onClose={() => setPermissionModalOpen(false)}
                        onSave={(role: Partial<RolePermissionDto>) => handlePermissionsSave(role)}
                    />
                </DialogContent>
            </Dialog>
        </>
    );
}

function RoleFormModal({ open, onClose, onSubmit, initialRole = {}, loading }: RoleFormModalProps) {
    const [name, setName] = useState(initialRole.name || "");
    const [code, setCode] = useState(initialRole.code || "");
    const [description, setDescription] = useState(initialRole.description || "");

    useEffect(() => {
        if (!initialRole || !initialRole.id) return;
        setName(initialRole.name || "");
        setCode(initialRole.code || "");
        setDescription(initialRole.description || "");
    }, [initialRole]);

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        onSubmit({ ...initialRole, name, description, code });
    };

    return (
        <Dialog open={open} onOpenChange={onClose}>
            <DialogContent>
                <DialogHeader>
                    <DialogTitle>{initialRole.id ? "Edit Role" : "Create Role"}</DialogTitle>
                </DialogHeader>
                <Input required label="Name" value={name} onChange={(e) => setName(e.target.value)} />
                <Input required label="Code" value={code} onChange={(e) => setCode(e.target.value)} />
                <Input label="Description" value={description} onChange={(e) => setDescription(e.target.value)} />
                <DialogFooter>
                    <Button disabled={loading} type="button" variant="secondary" onClick={onClose}>
                        Cancel
                    </Button>
                    <Button loading={loading} variant="default" onClick={handleSubmit}>
                        {initialRole.id ? "Update" : "Create"}
                    </Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    );
}
