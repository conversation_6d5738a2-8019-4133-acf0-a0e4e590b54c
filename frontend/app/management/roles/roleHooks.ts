// React Query hooks for roles CRUD
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";

import { fetchAll, createRole, updateRole, deleteRole } from "@/services/RoleService";
import { LoadableItemType, RoleFilter, RolePermissionDto } from "@/types";
import { fetchLoadableItems } from "@/services/SettingsService";

export function useRoles(filter: Partial<RoleFilter> = {}) {
    return useQuery({
        queryKey: ["roles", filter],
        queryFn: () => fetchAll(filter),
        select: (res) => res.data || [],
        retry: 2,
    });
}

export function usePermissions(roleId: string) {
    return useQuery({
        queryKey: ["Permissions", roleId],
        queryFn: () => fetchLoadableItems({ type: "PERMISSION" as LoadableItemType, page: 1, size: 100 }),
        select: (res) => res.data || [],
    });
}

export function useCreateRole() {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: (role: Partial<RolePermissionDto>) => createRole(role),
        onSuccess: () => queryClient.invalidateQueries({ queryKey: ["roles"] }),
    });
}

export function useUpdateRole() {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: (role: Partial<RolePermissionDto>) => updateRole(role),
        onSuccess: () => queryClient.invalidateQueries({ queryKey: ["roles"] }),
    });
}

export function useDeleteRole() {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: (roleId: string) => deleteRole(roleId),
        onSuccess: () => queryClient.invalidateQueries({ queryKey: ["roles"] }),
    });
}
