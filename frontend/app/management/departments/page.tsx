"use client";

import { ColumnDef } from "@tanstack/react-table";
import { MoreHorizontal, Pencil, Plus, Trash2 } from "lucide-react";
import { useEffect, useRef, useState } from "react";
import { toast } from "sonner";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";

import { DepartmentForm } from "@/components/department/department-form";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { DataTable } from "@/components/ui/data-table";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useAuth } from "@/composables/useStore";
import * as DepartmentService from "@/services/DepartmentService";
import { DepartmentDto, HttpResponse } from "@/types";
import { useError } from "@/context/ErrorContext";
import { useDeleteConfirmation } from "@/context/DeleteItemContext";
import { Input } from "@/components/ui/input";

export default function DepartmentsPage() {
    if (typeof document !== "undefined") {
        document.title = "Department Management - myNGO";
    }
    
    const { isAuthenticated } = useAuth();
    const [searchQuery, setSearchQuery] = useState<string>("");
    const [page, setPage] = useState<number>(1);
    const [size, setPageSize] = useState<number>(10);

    const [formDialogOpen, setFormDialogOpen] = useState(false);

    const [formMode, setFormMode] = useState<"create" | "edit">("create");
    const [selectedDepartment, setSelectedDepartment] = useState<DepartmentDto | null>(null);

    const fetchedRef = useRef(false);
    const debounceTimeout = useRef<NodeJS.Timeout | null>(null);

    const queryClient = useQueryClient();
    const { showError } = useError();

    const { openDeleteDialog } = useDeleteConfirmation();

    const queryKey = ["departments", { page, size, q: searchQuery }];

    const {
        data: departmentsReponse = { data: [], total: 0 },
        isLoading: depsLoading,
        refetch,
        error: depsError,
    } = useQuery<HttpResponse<DepartmentDto[]>>({
        queryKey,
        queryFn: async () => {
            return await Promise.try(() => DepartmentService.fetchAll({ page, size, name: searchQuery }));
        },
        enabled: isAuthenticated,
        retry: 3,
    });

    useEffect(() => {
        if (depsError) showError((depsError as Error)?.message || "Failed to retrieve departments", () => refetch());
    }, [depsError]);

    // Debounced search
    useEffect(() => {
        if (debounceTimeout.current) {
            clearTimeout(debounceTimeout.current);
        }

        debounceTimeout.current = setTimeout(() => {
            if (fetchedRef.current) {
                refetch();
            }
        }, 300);

        return () => {
            if (debounceTimeout.current) {
                clearTimeout(debounceTimeout.current);
            }
        };
    }, [searchQuery]);

    const handleAdd = () => {
        setFormMode("create");
        setSelectedDepartment(null);
        setFormDialogOpen(true);
    };

    const handleEdit = (department: DepartmentDto) => {
        setFormMode("edit");
        setSelectedDepartment(department);
        setFormDialogOpen(true);
    };

    const handleDelete = (department: DepartmentDto) => {
        setSelectedDepartment(department);
        openDeleteDialog({
            itemName: department.name,
            onConfirm: (voidReason: string) => voidDept.mutate({ id: department.id, voidReason }),
            title: `Delete ${department.name}`,
            description: `Are you sure you want to delete ${department.name}? This action cannot be undone.`,
        });
    };

    // Mutations
    const createDept = useMutation({
        mutationFn: (data: any) => DepartmentService.create(data),
        onSuccess: (res) => {
            if (res.success && res.data) {
                queryClient.invalidateQueries({ queryKey: ["departments"] });
                setFormDialogOpen(false);
                toast.success("Department created successfully");
            } else {
                toast.error("Failed to create department");
            }
        },
        onError: (err: any) => showError(err?.message || "Failed to create department"),
    });

    const updateDept = useMutation({
        mutationFn: ({ id, data }: { id: string; data: any }) => DepartmentService.update(id, data),
        onSuccess: (res) => {
            if (res.success && res.data) {
                queryClient.invalidateQueries({ queryKey: ["departments"] });
                setFormDialogOpen(false);
                toast.success("Department updated successfully");
            } else {
                toast.error("Failed to update department");
            }
        },
        onError: (err: any) => showError(err?.message || "Failed to update department"),
    });

    const voidDept = useMutation({
        mutationFn: ({ id, voidReason }: { id: string; voidReason: string }) =>
            DepartmentService.voidDepartment(id, voidReason),
        onSuccess: (res) => {
            if (res) {
                queryClient.invalidateQueries({ queryKey: ["departments"] });
                toast.success("Department deleted successfully");
            } else {
                toast.error("Failed to delete department");
            }
        },
        onError: (err: any) =>
            showError(err?.message || "Failed to delete department", () => handleDelete(selectedDepartment!)),
    });

    const columns: ColumnDef<DepartmentDto>[] = [
        {
            accessorKey: "code",
            header: "Code",
            cell: ({ row }) => <div className="font-medium">{row.getValue("code")}</div>,
        },
        {
            accessorKey: "name",
            header: "Name",
            cell: ({ row }) => <div className="font-medium">{row.getValue("name")}</div>,
        },
        {
            accessorKey: "description",
            header: "Description",
            cell: ({ row }) => {
                const description = row.getValue("description") as string;

                return (
                    <div className="max-w-[300px] truncate" title={description}>
                        {description || "—"}
                    </div>
                );
            },
        },
        {
            accessorKey: "created_at",
            header: "Created",
            cell: ({ row }) => {
                const date = row.getValue("created_at") as string;

                return <div className="text-sm text-muted-foreground">{new Date(date).toLocaleDateString()}</div>;
            },
        },
        {
            id: "actions",
            header: "Actions",
            cell: ({ row }) => {
                const department = row.original;

                return (
                    <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                            <Button className="h-8 w-8 p-0" variant="ghost">
                                <span className="sr-only">Open menu</span>
                                <MoreHorizontal className="h-4 w-4" />
                            </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                            <DropdownMenuItem onClick={() => handleEdit(department)}>
                                <Pencil className="mr-2 h-4 w-4" />
                                Edit
                            </DropdownMenuItem>
                            <DropdownMenuItem className="text-red-600" onClick={() => handleDelete(department)}>
                                <Trash2 className="mr-2 h-4 w-4" />
                                Delete
                            </DropdownMenuItem>
                        </DropdownMenuContent>
                    </DropdownMenu>
                );
            },
        },
    ];

    return (
        <div>
            <div className="flex items-center justify-between py-6">
                <div>
                    <CardTitle className="text-2xl">Departments</CardTitle>
                    <p className="text-muted-foreground">Manage your organization&apos;s departments</p>
                </div>
                <Button onClick={handleAdd}>
                    <Plus className="mr-2 h-4 w-4" />
                    Add Department
                </Button>
            </div>
            <Card>
                <CardHeader>
                    <Input
                        className="w-full"
                        placeholder="Search departments..."
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                    />
                </CardHeader>
                <CardContent>
                    <DataTable
                        columns={columns}
                        data={departmentsReponse?.data || []}
                        enableFiltering={false}
                        enableSorting={true}
                        loading={depsLoading}
                        serverPagination={{
                            pageSize: size,
                            pageIndex: page - 1,
                            total: departmentsReponse?.total || 0,
                            onPageChange: (newIndex: number) => setPage(newIndex + 1),
                            onPageSizeChange: (newSize: number) => {
                                setPageSize(newSize);
                                setPage(1);
                            },
                        }}
                    />
                </CardContent>
            </Card>

            {/* Form Dialog */}
            <Dialog open={formDialogOpen} onOpenChange={setFormDialogOpen}>
                <DialogContent className="sm:max-w-2xl">
                    <DialogHeader>
                        <DialogTitle>{formMode === "create" ? "Create Department" : "Edit Department"}</DialogTitle>
                        <DialogDescription>
                            {formMode === "create"
                                ? "Add a new department to your organization."
                                : "Update the department information."}
                        </DialogDescription>
                    </DialogHeader>
                    <DepartmentForm
                        formMode={formMode}
                        isSubmitting={createDept.status === "pending" || updateDept.status === "pending"}
                        user={selectedDepartment}
                        onAdd={(data) => createDept.mutate(data)}
                        onClose={() => setFormDialogOpen(false)}
                        onUpdate={(data) => updateDept.mutate({ id: selectedDepartment!.id, data })}
                    />
                </DialogContent>
            </Dialog>
        </div>
    );
}
