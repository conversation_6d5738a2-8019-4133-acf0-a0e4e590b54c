"use client";

import React, { useEffect } from "react";
import { useRouter } from "next/navigation";
import { toast } from "sonner";

import { SystemConfigurationForm } from "@/components/forms/SystemConfigurationForm";
import { SystemConfigurationData, useSystemConfiguration } from "@/hooks/use-system-configuration";
import { useError } from "@/context/ErrorContext";
import { useLoading } from "@/context/LoadingContext";

export default function SystemConfigurationPage() {
    if (typeof document !== "undefined") {
        document.title = "System Settings - myNGO";
    }
    
    const router = useRouter();
    const { loading, updating, error, data, loadConfiguration, updateConfiguration } = useSystemConfiguration();
    const { showError, hideError } = useError();
    const { showLoading, hideLoading } = useLoading();

    // Load existing configuration on component mount
    useEffect(() => {
        loadConfiguration("system");
    }, [loadConfiguration]);

    useEffect(() => {
        if (loading) showLoading("Loading system configuration...");
        if (updating) showLoading("Updating system configuration...");

        !loading && !updating ? hideLoading() : null;
    }, [loading, updating]);

    useEffect(() => {
        error ? showError("Failed to load system configuration", () => loadConfiguration("system")) : hideError();
    }, [error]);

    const handleSubmit = async (formData: SystemConfigurationData) => {
        await updateConfiguration(formData);
        if (!error) toast.success("System configuration updated successfully!");
    };

    const handleCancel = () => {
        router.push("/settings");
    };

    return (
        <SystemConfigurationForm
            initialData={data!}
            loading={loading || updating}
            onCancel={handleCancel}
            onSubmit={handleSubmit}
        />
    );
}
