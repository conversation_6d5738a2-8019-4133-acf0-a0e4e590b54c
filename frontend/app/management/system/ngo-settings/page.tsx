"use client";

import React, { useEffect } from "react";
import { useRouter } from "next/navigation";
import { toast } from "sonner";

import { NGOSettingsForm } from "@/components/forms/NGOSettingsForm";
import { useError } from "@/context/ErrorContext";
import { useLoading } from "@/context/LoadingContext";

export default function NGOSettingsPage() {
    if (typeof document !== "undefined") {
        document.title = "System NGO Settings - myNGO";
    }
    
    const router = useRouter();
    const { loading, updating, error, data, loadSettings, updateSettings } = useNGOSettings();
    const { showError, hideError } = useError();
    const { showLoading, hideLoading } = useLoading();

    useEffect(() => {
        loadSettings();
    }, [loadSettings]);

    useEffect(() => {
        if (loading) showLoading("Loading NGO settings...");
        if (updating) showLoading("Updating NGO settings...");
        !loading && !updating ? hideLoading() : null;
    }, [loading, updating]);

    useEffect(() => {
        error ? showError("Failed to load NGO settings", loadSettings) : hideError();
    }, [error]);

    const handleSubmit = async (formData: any) => {
        await updateSettings(formData);
        if (!error) toast.success("NGO settings updated successfully!");
    };

    const handleCancel = () => {
        router.push("/settings");
    };

    return (
        <div className="container mx-auto py-6">
            <NGOSettingsForm
                initialData={data?.properties || {}}
                loading={loading || updating}
                onCancel={handleCancel}
                onSubmit={handleSubmit}
            />
        </div>
    );
}
