"use client";

import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { useState } from "react";
import { toast } from "sonner";

import UserFormDialog from "@/components/users/user-form-dialog";
import UsersView from "@/components/users/users-view";
import VoidUserDialog from "@/components/users/void-user-dialog";
import * as userService from "@/services/UserService";
import { UserDto } from "@/types";

const UsersIndexPage = () => {
    if (typeof document !== "undefined") {
        document.title = "User Management - myNGO";
    }
    
    const queryClient = useQueryClient();
    const [selectedUser, setSelectedUser] = useState<UserDto | null>(null);
    const [isFormDialogOpen, setIsFormDialogOpen] = useState(false);
    const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
    const [formMode, setFormMode] = useState<"create" | "edit">("create");

    const {
        data: users = [],
        isLoading,
        error,
    } = useQuery({
        queryKey: ["users", { page: 1, size: 20, is_external: false }],
        queryFn: async () => {
            const response = await userService.fetchAll({ page: 1, size: 20, is_external: false });

            if (response.errors && response.errors.length > 0) {
                for (const error of response.errors) {
                    toast.error(error.message);
                }
            }

            return response.data || [];
        },
        staleTime: 5 * 60 * 1000,
        gcTime: 10 * 60 * 1000,
        retry: 2,
        refetchOnWindowFocus: false,
    });

    // Create user mutation
    const createUserMutation = useMutation({
        mutationFn: userService.createUser,
        onSuccess: (response) => {
            if (response.success && response.data) {
                queryClient.invalidateQueries({ queryKey: ["users"] });
                toast.success("User created successfully");
                handleCloseFormDialog();
            } else {
                toast.error(response.errors?.[0]?.message || "Failed to create user");
            }
        },
        onError: () => {
            toast.error("An error occurred while creating user");
        },
    });

    // Update user mutation
    const updateUserMutation = useMutation({
        mutationFn: userService.updateUser,
        onSuccess: (response) => {
            if (response.success && response.data) {
                queryClient.invalidateQueries({ queryKey: ["users"] });
                toast.success("User updated successfully");
                handleCloseFormDialog();
            } else {
                toast.error(response.errors?.[0]?.message || "Failed to update user");
            }
        },
        onError: () => {
            toast.error("An error occurred while updating user");
        },
    });

    // Delete user mutation
    const deleteUserMutation = useMutation({
        mutationFn: (userId: string) => userService.deleteUser(userId),
        onSuccess: (response) => {
            if (response.success) {
                queryClient.invalidateQueries({ queryKey: ["users"] });
                toast.success("User deleted successfully");
                handleCloseDeleteDialog();
            } else {
                toast.error(response.errors?.[0]?.message || "Failed to delete user");
            }
        },
        onError: () => {
            toast.error("An error occurred while deleting user");
        },
    });

    const handleAddUser = () => {
        setFormMode("create");
        setSelectedUser(null);
        setIsFormDialogOpen(true);
    };

    const handleEditUser = (user: UserDto) => {
        setFormMode("edit");
        setSelectedUser(user);
        setIsFormDialogOpen(true);
    };

    const handleDeleteUser = (user: UserDto) => {
        setSelectedUser(user);
        setIsDeleteDialogOpen(true);
    };

    const handleCloseFormDialog = () => {
        setIsFormDialogOpen(false);
        setSelectedUser(null);
    };

    const handleCloseDeleteDialog = () => {
        setIsDeleteDialogOpen(false);
        setSelectedUser(null);
    };

    const handleFormSubmit = (formData: any) => {
        if (formMode === "create") {
            createUserMutation.mutate(formData);
        } else {
            updateUserMutation.mutate({ ...formData, id: selectedUser?.id });
        }
    };

    const handleConfirmDelete = () => {
        if (selectedUser?.id) {
            deleteUserMutation.mutate(selectedUser.id);
        }
    };

    if (error) {
        toast.error("Failed to fetch users");
    }

    return (
        <div>
            <UsersView
                isLoading={isLoading}
                users={users}
                onAdd={handleAddUser}
                onDelete={handleDeleteUser}
                onEdit={handleEditUser}
            />

            <UserFormDialog
                formMode={formMode}
                isLoading={createUserMutation.isPending || updateUserMutation.isPending}
                open={isFormDialogOpen}
                user={selectedUser}
                onClose={handleCloseFormDialog}
                onSubmit={handleFormSubmit}
            />

            <VoidUserDialog
                isLoading={deleteUserMutation.isPending}
                open={isDeleteDialogOpen}
                user={selectedUser}
                onClose={handleCloseDeleteDialog}
                onConfirm={handleConfirmDelete}
            />
        </div>
    );
};

export default UsersIndexPage;
