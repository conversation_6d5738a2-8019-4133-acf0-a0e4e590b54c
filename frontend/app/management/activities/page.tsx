"use client";

import { useEffect, useMemo, useState } from "react";
import { Calendar, Edit2Icon, Mail, MailQuestion, MailsIcon, MapPin, MapPlusIcon, MoreHorizontal, Pencil, Plus, Send, Trash2, Users } from "lucide-react";
import { useQuery } from "@tanstack/react-query";

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { DataTable } from "@/components/ui/data-table";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { DatePicker } from "@/components/ui/date-picker";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from "@/components/ui/alert-dialog";
import { useDebouncedValue } from "@/hooks/use-debounce";
import { toast } from "sonner";

import * as activityService from "@/services/ActivitiyService";
import * as activityInvitationService from "@/services/ActivityInvitationService";
import * as settingsService from "@/services/SettingsService";
import * as userService from "@/services/UserService";
import * as organizationService from "@/services/OrganizationService";
import { ActivityDto, LoadableItemDto, DistrictDto, ActivityRequest, LoadableItemType } from "@/types";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Checkbox } from '@/components/ui/checkbox';
import { useDeleteConfirmation } from '@/context/DeleteItemContext';
import LoadableItemInput from '@/components/inputs/loadable-item';
import { ManagerInput } from '@/components/inputs/manager';

export default function ActivitiesManagementPage() {
  const [open, setOpen] = useState(false);
  const [inviteOpen, setInviteOpen] = useState(false);
  const [viewInvitationsOpen, setViewInvitationsOpen] = useState(false);
  const [editMode, setEditMode] = useState(false);
  const [selected, setSelected] = useState<ActivityDto | null>(null);
  const [filter, setFilter] = useState({ title: "", page: 0, pageSize: 10 });

  const { openDeleteDialog, closeDeleteDialog } = useDeleteConfirmation();
  const defaultForm = {
    title: "",
    category_id: "" as any,
    visibility: "ALL",
    venue: "",
    district_id: "" as any,
    longitude: "",
    latitude: "",
    map_pin: "",
    start_date: new Date(),
    end_date: new Date(),
    summary: "",
  } as ActivityRequest;

  const [form, setForm] = useState<ActivityRequest>(defaultForm);

  const { data: districtsResp } = useQuery({
    queryKey: ["districts"],
    queryFn: async () => settingsService.fetchDistricts({}),
  });
  const districts = districtsResp?.data || [];


  const { data: categoriesResp } = useQuery({
    queryKey: ["activity_categories"],
    queryFn: async () => settingsService.fetchLoadableItems({ type: "ACTIVITY_CATEGORY" }),
  });
  const categories = categoriesResp?.data || [];


  const { data: activitiesResp, refetch, isLoading } = useQuery({
    queryKey: ["activities", filter],
    queryFn: async () => activityService.fetchActivities({ ...filter, page: (filter.page + 1) }),
  });

  const activities = activitiesResp?.data || [];

  const columns = useMemo(
    () => [
      {
        accessorKey: "title",
        header: "Title",
        cell: ({ row }: any) => (
          <div className="font-medium flex items-center gap-2">
            <Calendar className="w-4 h-4 text-primary" /> {row.original.title}
          </div>
        ),
      },
      {
        accessorKey: "venue",
        header: "Venue",
        cell: ({ row }: any) => {
          const district = districts && districts.find((d: any) => d.id === row.original.district_id)?.name
          return <div className="flex items-center gap-2">
            <MapPin className="w-4 h-4 text-muted-foreground" />
            {row.original.venue}
            {
              district && <span className="text-sm text-muted-foreground">({district})</span>
            }
          </div>

        }
      },
      {
        accessorKey: "start_date",
        header: "Period",
        cell: ({ row }: any) => (
          <div className="text-sm">
            {new Date(row.original.start_date + 'Z').toLocaleString()} -
            <br />
            {new Date(row.original.end_date + 'Z').toLocaleString()}
          </div>
        ),
      },
      {
        accessorKey: "visibility",
        header: "Visibility",
        cell: ({ row }: any) => (
          <Badge variant={row.original.visibility === "ALL" ? "default" : "secondary"}>
            {row.original.visibility}
          </Badge>
        ),
      },
      {
        id: "actions",
        header: "Actions",
        cell: ({ row }: any) => (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button className="h-8 w-8 p-0" variant="ghost">
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => openInvite(row.original)}>
                <Users className="mr-2 h-4 w-4" />
                Invite Users / Organizations
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => onEditInvitation(row.original)}>
                <Edit2Icon className="mr-2 h-4 w-4" />
                Edit Activity
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => onViewInvitations(row.original)}>
                <Users className="mr-2 h-4 w-4" />
                View Invitations
              </DropdownMenuItem>
              <DropdownMenuItem className="text-red-600" onClick={() => onDeleteActivity(row.original)}>
                <Trash2 className="mr-2 h-4 w-4" />
                Delete Activity
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu >
        ),
      },
    ],
    [districts]
  );

  const onEditInvitation = (activity: ActivityDto) => {
    setSelected(activity);
    setOpen(true);
    setEditMode(true)

    setForm({
      title: activity.title,
      category_id: activity.category_id,
      visibility: activity.visibility,
      venue: activity.venue,
      district_id: activity.district_id,
      longitude: activity.longitude,
      latitude: activity.latitude,
      map_pin: activity.map_pin,
      start_date: new Date(activity.start_date + 'Z'),
      end_date: new Date(activity.end_date + 'Z'),
      summary: activity.summary,
    });
  };

  function onViewInvitations(activity: ActivityDto) {
    setSelected(activity);
    setViewInvitationsOpen(true);
  }

  function onDeleteActivity(activity: ActivityDto) {
    setSelected(activity);
    openDeleteDialog({
      title: "Delete Activity",
      description: `Are you sure you want to delete the activity '${activity.title}'?`,
      onConfirm: () => {
        activityService.deleteActivity(activity.id);
        toast.warning("Activity deleted successfully");
        closeDeleteDialog();
        setTimeout(() => {
          refetch();
        }, 500);

      },
      itemName: activity.title,
    });
  }

  function openInvite(activity: ActivityDto) {
    setSelected(activity);
    setInviteOpen(true);
  }

  async function saveActivity() {
    // Coordinate validation
    const lat = (form.latitude || "").trim();
    const lng = (form.longitude || "").trim();
    if (lat && (isNaN(Number(lat)) || Number(lat) < -90 || Number(lat) > 90)) {
      toast.error("Latitude must be between -90 and 90");
      return;
    }
    if (lng && (isNaN(Number(lng)) || Number(lng) < -180 || Number(lng) > 180)) {
      toast.error("Longitude must be between -180 and 180");
      return;
    }
    let resp = null;

    if (selected || editMode) {
      resp = await activityService.updateActivity(selected?.id!, form);
    } else {
      resp = await activityService.createActivity(form);
    }
    setOpen(false);
    setSelected(null);
    if (resp.errors.length === 0) toast.success(`Activity ${selected ? 'updated' : 'created'} successfully`);
    if (resp.errors.length > 0) toast.error("Failed to save activity" + resp.errors.map((e: any) => e.message).join(", "));
    await refetch();
  }

  return (
    <div className="space-y-6 py-8">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-semibold">Activities</h1>
          <p className="text-sm text-muted-foreground">Create and manage activities, schedule and invite participants</p>
        </div>
        <div className="flex gap-2">
          <Button onClick={() => {
            setSelected(null);
            setForm(defaultForm);
            setEditMode(false);
            setOpen(true);
          }}>
            <Plus className="w-4 h-4 mr-1" /> New Activity
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Planned Activities</CardTitle>
          <CardDescription>Recent and upcoming activities</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between pb-8">
            <Input placeholder="Search activities..." value={filter.title || ""} onChange={(e) => setFilter({ ...filter, title: e.target.value })} />
          </div>
          <DataTable
            columns={columns as any}
            data={activities}
            loading={isLoading}
            serverPagination={
              {
                pageSize: filter.pageSize,
                pageIndex: filter.page,
                total: activitiesResp?.total || 0,
                onPageChange: (page) => setFilter({ ...filter, page: page }),
                onPageSizeChange: (size) => {
                  setFilter({ ...filter, page: 0, pageSize: size });
                },
              }
            }
          />
        </CardContent>
      </Card>

      <Dialog open={open} onOpenChange={setOpen}>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle>{selected ? "Edit Activity" : "Add Activity"}</DialogTitle>
          </DialogHeader>
          <Tabs defaultValue="details">
            <TabsList>
              <TabsTrigger value="details">Details</TabsTrigger>
              <TabsTrigger value="invitations">Invitations</TabsTrigger>
            </TabsList>

            <TabsContent value="details">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 py-4">
                <div>
                  <label className="text-sm">Title</label>
                  <Input value={form.title} onChange={(e) => setForm({ ...form, title: e.target.value })} />
                </div>
                <div>
                  <label className="text-sm">Category</label>
                  <Select
                    options={categories.map((c: LoadableItemDto) => ({ label: c.display_value, value: c.id }))}
                    value={form.category_id as any}
                    onValueChange={(v) => setForm({ ...form, category_id: v as any })}
                  />
                </div>

                <div>
                  <label className="text-sm">Start Date</label>
                  <DatePicker

                    disabled={{ before: new Date() }}
                    value={form?.start_date?.toISOString()}
                    onChange={(v) => setForm({ ...form, start_date: new Date(v!) })}
                  />
                </div>
                <div>
                  <label className="text-sm">End Date</label>
                  <DatePicker disabled={{ before: form?.start_date }} value={form?.end_date?.toISOString()} onChange={(v) => setForm({ ...form, end_date: new Date(v!) })} />
                </div>
                <div>
                  <label className="text-sm">Start Time</label>
                  <Input
                    type='time'
                    step={60}
                    defaultValue={
                      form.start_date
                        ? form.start_date.getHours().toString().padStart(2, "0") +
                        ":" +
                        form.start_date.getMinutes().toString().padStart(2, "0")
                        : ""
                    }
                    onChange={(v) => {
                      const [hours, minutes] = v.target.value.split(":").map(Number);
                      const updated = new Date(form.start_date || new Date());
                      updated.setHours(hours, minutes, 0, 0);
                      setForm({ ...form, start_date: updated });
                    }
                    }
                  />
                </div>
                <div>
                  <label className="text-sm">End Time</label>
                  <Input type="time"
                    step={60}
                    defaultValue={
                      form.end_date
                        ? form.end_date.getHours().toString().padStart(2, "0") +
                        ":" +
                        form.end_date.getMinutes().toString().padStart(2, "0")
                        : ""
                    }
                    onChange={(e) => {
                      const [hours, minutes] = e.target.value.split(":").map(Number);
                      const updated = new Date(form.end_date || new Date());
                      updated.setHours(hours, minutes, 0, 0);
                      setForm({ ...form, end_date: updated });
                    }} />
                </div>
                <div>
                  <label className="text-sm">Venue</label>
                  <Input value={form.venue} onChange={(e) => setForm({ ...form, venue: e.target.value })} />
                </div>

                <div>
                  <ManagerInput
                    type="district"
                    value={form.district_id}
                    onItemSelect={(item) => setForm({ ...form, district_id: item?.id! })}
                  />
                </div>
                <div className="md:col-span-2">
                  <label className="text-sm">Details</label>
                  <Textarea value={form.summary} onChange={(e) => setForm({ ...form, summary: e.target.value })} />
                </div>
                <div className="flex justify-end gap-2 md:col-span-2">
                  <Button variant="outline" onClick={() => setOpen(false)}>Cancel</Button>
                  <Button onClick={saveActivity}><Send className="w-4 h-4 mr-1" />
                    {
                      editMode ? "Update Activity" : "Create Activity"
                    }
                  </Button>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="invitations">
              <Card>
                <CardHeader>
                  <CardTitle className="text-base">Invitations</CardTitle>
                  <CardDescription>
                    Save the activity first, then manage invitations. You can open the invitation manager from the Activities table.
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Button variant="secondary" onClick={() => {
                    setOpen(false);
                    setViewInvitationsOpen(true);
                  }}>
                    <Users className="w-4 h-4 mr-2" /> Close and manage invitations
                  </Button>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </DialogContent>
      </Dialog>

      {inviteOpen && selected && (
        <InviteDialog activity={selected} onClose={() => setInviteOpen(false)} setViewInvitationsOpen={setViewInvitationsOpen} />
      )}

      {viewInvitationsOpen && selected && (
        <ViewInvitationsDialog activity={selected} onClose={() => setViewInvitationsOpen(false)} inviteMore={() => setInviteOpen(true)} />
      )}
    </div>
  );
}

function InviteDialog({ activity, onClose, setViewInvitationsOpen }: { activity: ActivityDto; onClose: () => void, setViewInvitationsOpen: (v: boolean) => void }) {
  const [tab, setTab] = useState("internal");

  // Existing invitations filters
  const [invStatus, setInvStatus] = useState<activityInvitationService.InvitationStatus | "ALL">("ALL");
  const [invSearch, setInvSearch] = useState("");
  const debouncedInvSearch = useDebouncedValue(invSearch, 300);

  const { data: invitationsResp, isLoading: invitationsLoading, refetch: refetchInvitations } = useQuery({
    queryKey: ["activity-invitations", activity.id, invStatus, debouncedInvSearch],
    queryFn: async () =>
      activityInvitationService.listInvitations(activity.id, {
        status: invStatus === "ALL" ? undefined : invStatus,
        search: debouncedInvSearch || undefined,
      }),
  });

  // Internal invitee selection
  const [search, setSearch] = useState("");
  const debouncedSearch = useDebouncedValue(search, 300);
  const [orgDistrict, setOrgDistrict] = useState<string | undefined>(undefined);
  const [selectedAccounts, setSelectedAccounts] = useState<string[]>([]);
  const [external, setExternal] = useState<{ email: string; name?: string; account_type?: "ORG" | "USER" }>({ email: "" });

  const { data: usersResp } = useQuery({
    queryKey: ["users", debouncedSearch],
    queryFn: async () => userService.fetchAll({ search: debouncedSearch }),
  });
  const users = usersResp?.data || [];

  const { data: orgsResp } = useQuery({
    queryKey: ["orgs", debouncedSearch, orgDistrict],
    queryFn: async () => organizationService.fetchOrganizations({ name: debouncedSearch, district_id: orgDistrict }),
  });
  const orgs = orgsResp?.data || [];

  const [sending, setSending] = useState(false);
  const [toDelete, setToDelete] = useState<string | null>(null);

  async function sendInvites() {
    setSending(true);
    try {
      const body: activityInvitationService.ActivityInvitationRequest[] = [];

      for (const user of users.filter((u: any) => selectedAccounts.includes(u.account_id))) {
        body.push({ type: "INTERNAL", user_id: user.id });
      }

      for (const org of orgs.filter((o: any) => selectedAccounts.includes(o.account_id))) {
        body.push({ type: "INTERNAL", organization_id: org.id });
      }

      if (external.email) {
        body.push({ ...external, type: "EXTERNAL" });
      }

      if (body.length === 0) return;

      await activityInvitationService.createInvitations(activity.id, body);
      setSelectedAccounts([]);
      setExternal({ email: "" });
      await refetchInvitations();
    } finally {
      setSending(false);
      toast.success("Invitations sent successfully");
      onClose()
      setViewInvitationsOpen(true);
    }
  }

  const toggleSelected = (accountId: string) => {
    setSelectedAccounts((prev) => (prev.includes(accountId) ? prev.filter((id) => id !== accountId) : [...prev, accountId]));
  };

  async function confirmDelete() {
    if (!toDelete) return;
    const resp = await activityInvitationService.deleteInvitation(activity.id, toDelete);
    if (resp.success) {
      await refetchInvitations();
    }
    setToDelete(null);
  }

  return (
    <Dialog open onOpenChange={(v) => (!v ? onClose() : null)}>
      <DialogContent className="max-w-5xl">
        <DialogHeader>
          <DialogTitle>Invite participants for {activity.title}</DialogTitle>
        </DialogHeader>

        <Tabs value={tab} onValueChange={setTab}>
          <TabsList className='grid grid-cols-2 w-full'>
            <TabsTrigger value="internal"><Users className="w-4 h-4 mr-1" />Internal (Users & Organizations)</TabsTrigger>
            <TabsTrigger value="external"><Mail className="w-4 h-4 mr-1" />External</TabsTrigger>
          </TabsList>

          <TabsContent value="internal" className="space-y-4 py-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-2">
              <div className="md:col-span-2 my-6">
                <Input className='py-2' placeholder="Search users/organizations" value={search} onChange={(e) => setSearch(e.target.value)} />
              </div>
              <div>
                <ManagerInput
                  type="district"
                  value={orgDistrict}
                  onItemSelect={(item) => setOrgDistrict(item?.id)}
                />
              </div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 max-h-80 overflow-y-auto">
              <div>
                <h3 className="text-sm font-medium mb-4">Users</h3>
                <Checkbox
                  label="Select All"
                  checked={selectedAccounts.length > 0 && selectedAccounts.every((id) => users.map((u: any) => u.account_id).includes(id))}
                  onCheckedChange={() => {
                    if (selectedAccounts.length === users.length) {
                      setSelectedAccounts([]);
                    } else {
                      setSelectedAccounts(users.map((u: any) => u.account_id));
                    }
                  }}
                />
                <div className="space-y-2 py-4">
                  {users.map((u: any) => (
                    <span key={u.id} className="flex items-center gap-2 p-4 rounded border">
                      <Checkbox
                        className='px-3'
                        key={u.id}
                        checked={selectedAccounts.includes(u.account_id)}
                        label={`${u.first_name} ${u.last_name} — ${u.email}`}
                        onCheckedChange={() => toggleSelected(u.account_id)}
                      />
                    </span>
                  ))}
                </div>
              </div>
              <div>
                <h3 className="text-sm font-medium mb-4">Organizations</h3>
                {/* Select All */}
                <Checkbox
                  label="Select All"
                  checked={selectedAccounts.length > 0 && selectedAccounts.every((id) => orgs.map((o: any) => o.account_id).includes(id))}
                  onCheckedChange={() => {
                    if (selectedAccounts.length === orgs.length) {
                      setSelectedAccounts([]);
                    } else {
                      setSelectedAccounts(orgs.map((o: any) => o.account_id));
                    }
                  }}
                />
                <div className="space-y-2 py-4">
                  {orgs.map((o: any) => (
                    <span key={o.id} className='flex items-center gap-2 p-4 rounded border'>
                      <Checkbox
                        className='px-3'
                        key={o.id}
                        checked={selectedAccounts.includes(o.account_id)}
                        label={o.name}
                        onCheckedChange={() => toggleSelected(o.account_id)}
                      />
                    </span>
                  ))}
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="external" className="space-y-4 py-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="text-sm">Email</label>
                <Input type="email" value={external.email} onChange={(e) => setExternal({ ...external, email: e.target.value })} />
              </div>
              <div>
                <label className="text-sm">Name (optional)</label>
                <Input value={external.name || ""} onChange={(e) => setExternal({ ...external, name: e.target.value })} />
              </div>
              <div>
                <label className="text-sm">Type</label>
                <Select
                  options={[
                    { label: "Individual", value: "USER" },
                    { label: "Organization", value: "ORG" },
                  ]}
                  value={external.account_type as any}
                  onValueChange={(v) => setExternal({ ...external, account_type: v as any })}
                />
              </div>
            </div>
          </TabsContent>
        </Tabs>

        <div className="flex justify-end gap-2">
          <Button variant="outline" onClick={() => {
            onClose();
            setViewInvitationsOpen(true);
          }}>View Existing Invitations</Button>
          <Button variant="outline" onClick={onClose}>Close</Button>
          <Button disabled={sending} onClick={sendInvites}>
            <Send className="w-4 h-4 mr-1" /> Send Invitations
          </Button>
        </div>

        {/* Delete confirmation */}
        <AlertDialog open={!!toDelete} onOpenChange={(open) => !open && setToDelete(null)}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Delete Invitation</AlertDialogTitle>
            </AlertDialogHeader>
            <p className="text-sm text-muted-foreground">This action cannot be undone. Are you sure you want to remove this invitation?</p>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <AlertDialogAction onClick={confirmDelete}>Delete</AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </DialogContent>
    </Dialog>
  );
}



function ViewInvitationsDialog({ activity, onClose, inviteMore }: { activity: ActivityDto; onClose: () => void, inviteMore: () => void }) {
  // Existing invitations filters
  const [invStatus, setInvStatus] = useState<activityInvitationService.InvitationStatus | "ALL">("ALL");
  const [invSearch, setInvSearch] = useState("");
  const debouncedInvSearch = useDebouncedValue(invSearch, 300);

  const { data: invitationsResp, isLoading: invitationsLoading, refetch: refetchInvitations } = useQuery({
    queryKey: ["activity-invitations", activity.id, invStatus, debouncedInvSearch],
    queryFn: async () =>
      activityInvitationService.listInvitations(activity.id, {
        status: invStatus === "ALL" ? undefined : invStatus,
        search: debouncedInvSearch || undefined,
      }),
  });


  const invitations = invitationsResp?.data || [];
  const [toDelete, setToDelete] = useState<string | null>(null);
  const [toResend, setToResend] = useState<string | null>(null);

  async function confirmDelete(inv: activityInvitationService.ActivityInvitationDto) {
    setToDelete(inv.id);
  }

  const onDeleteConfirmed = async (inv: typeof toDelete) => {
    await activityInvitationService.deleteInvitation(activity.id, inv!);
    await refetchInvitations();
  };

  const onResendInvitation = async (invitationId: string) => {
    setToResend(invitationId);
  };

  const onResendConfirmed = async (inv: typeof toResend) => {
    await activityInvitationService.resendInvitationEmail(activity.id, inv!);
    await refetchInvitations();
  };

  const DeleteAlert = () => (
    <AlertDialog open={!!toDelete} onOpenChange={(open) => !open && setToDelete(null)}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Delete Invitation</AlertDialogTitle>
        </AlertDialogHeader>
        <p className="text-sm text-muted-foreground">This action cannot be undone. Are you sure you want to remove this invitation?</p>
        <AlertDialogFooter>
          <AlertDialogCancel>Cancel</AlertDialogCancel>
          <AlertDialogAction onClick={() => onDeleteConfirmed(toDelete)}>Delete</AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );

  const ResendEmailAlert = () => (
    <AlertDialog open={!!toResend} onOpenChange={(open) => !open && setToResend(null)}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Resend Invitation</AlertDialogTitle>
        </AlertDialogHeader>
        <p className="text-sm text-muted-foreground">Are you sure you want to resend this invitation?</p>
        <AlertDialogFooter>
          <AlertDialogCancel>Cancel</AlertDialogCancel>
          <AlertDialogAction onClick={() => onResendConfirmed(toResend)}>
            <Mail className="w-4 h-4 mr-2" /> Resend
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );

  return (
    <Dialog open onOpenChange={(v) => (!v ? onClose() : null)}>
      <DialogContent className="max-w-5xl">
        <DialogHeader>
          <DialogTitle>Manage Participants for {activity.title}</DialogTitle>
        </DialogHeader>
        <DeleteAlert />
        <ResendEmailAlert />
        {/* Existing Invitations */}
        <Card className="mb-4">
          <CardHeader className="flex flex-col gap-2">
            <CardTitle className="text-base">Existing Invitations</CardTitle>
            <div className="flex flex-col md:flex-row gap-2 md:items-center">
              <Input
                placeholder="Search invitations (name/email)"
                value={invSearch}
                onChange={(e) => setInvSearch(e.target.value)}
                aria-label="Search invitations"
              />
              <Select
                options={[
                  { label: "All", value: "ALL" },
                  { label: "Pending", value: "PENDING" },
                  { label: "Accepted", value: "ACCEPTED" },
                  { label: "Rejected", value: "REJECTED" },
                  { label: "Cancelled", value: "CANCELLED" },
                ]}
                value={invStatus as any}
                onValueChange={(v) => setInvStatus(v as any)}
              />
            </div>
          </CardHeader>
          <CardContent>
            {invitationsLoading ? (
              <div className="space-y-2">
                <div className="h-4 w-full bg-muted rounded" />
                <div className="h-4 w-3/4 bg-muted rounded" />
              </div>
            ) : invitations.length === 0 ? (
              <p className="text-sm text-muted-foreground">No invitations found.</p>
            ) : (
              <div className="space-y-2 max-h-56 overflow-y-auto">
                {invitations.map((inv) => (
                  <div key={inv.id} className="flex items-center justify-between rounded border p-2">
                    <div className="flex items-center gap-2 text-sm">
                      <Badge variant={inv.type === "EXTERNAL" ? "secondary" : "default"}>{inv.type}</Badge>
                      <Badge variant={inv.account_type === "ORG" ? "secondary" : "default"}>{inv.account_type}</Badge>
                      <Badge variant="outline">{inv.status}</Badge>
                      <span>
                        ({inv.name}) {inv.email}
                      </span>
                    </div>
                    <div>
                      <Button size="icon" variant="ghost" aria-haspopup aria-label="Resend invitation" onClick={() => onResendInvitation(inv.id)}>
                        <MailsIcon className="w-4 h-4" />
                      </Button>
                      <Button size="icon" variant="ghost" aria-label="Delete invitation" onClick={() => confirmDelete(inv)}>
                        <Trash2 className="w-4 h-4 text-destructive" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            )}
            <div className="flex justify-end gap-2 mt-8">
              <Button variant="outline" onClick={inviteMore}><MailQuestion /> &nbsp;Invite More</Button>
            </div>
          </CardContent>
        </Card>
      </DialogContent>
    </Dialog>
  );

}
