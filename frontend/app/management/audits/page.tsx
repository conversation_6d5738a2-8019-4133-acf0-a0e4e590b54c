"use client";

import { useEffect, useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { ColumnDef } from "@tanstack/react-table";
import { MoreHorizontal, Pencil } from "lucide-react";

import { useAudits, useAuditActions } from "./auditHooks";

import { Input } from "@/components/ui/input";
import { Select } from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { DataTable } from "@/components/ui/data-table";
import { fetchAll } from "@/services/UserService";
import { useError } from "@/context/ErrorContext";
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { AuditDto } from "@/types";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import AuditDiffDialog from "@/components/management/AuditDiffDialog";

export default function AuditsPage() {
    if (typeof document !== "undefined") {
        document.title = "Audit Logs - myNGO";
    }
    
    const [filters, setFilters] = useState<any>({ page: 1, size: 50 });
    const [dialogOpen, setDialogOpen] = useState(false);
    const [dialogPayload, setDialogPayload] = useState<AuditDto | null>(null);

    const { data: auditsData, isLoading, isError, refetch } = useAudits(filters);
    const { data: usersData } = useQuery({
        queryKey: ["users"],
        queryFn: () => fetchAll({ page: 1, size: 100 }),
        select: (res) => res.data || [],
    });
    const { data: actionsData } = useAuditActions();
    const { showError, hideError } = useError();

    useEffect(() => {
        if (isError) {
            showError("Failed to load audits", refetch);

            return;
        }
        hideError();
    }, [isError]);

    const handleFilterChange = (key: string, value: any) => {
        setFilters((s: any) => ({ ...s, [key]: value }));
    };

    const applyFilters = () => {
        refetch();
    };

    const onViewDetails = (audit: AuditDto) => {
        setDialogPayload(audit);
        setDialogOpen(true);
    };

    const columns: ColumnDef<AuditDto>[] = [
        {
            accessorKey: "time",
            header: "Time",
            cell: ({ row }) => (
                <div>
                    <div className="font-medium text-foreground">
                        {new Date(row.original.created_at).toLocaleString()}
                    </div>
                </div>
            ),
        },
        {
            accessorKey: "action",
            header: "Action",
            cell: ({ row }) => (
                <div className="max-w-[300px] truncate" title={row.original.action}>
                    {row.original.action || "—"}
                </div>
            ),
        },
        {
            accessorKey: "table",
            header: "Table",
            cell: ({ row }) => <div className="text-sm text-muted-foreground">{row.original.table_name}</div>,
        },
        {
            accessorKey: "user",
            header: "User",
            cell: ({ row }) => (
                <div className="text-sm text-muted-foreground">
                    {`${row.original.user.first_name} ${row.original.user.last_name}` || "—"}
                </div>
            ),
        },
        {
            id: "actions",
            header: "Actions",
            cell: ({ row }) => (
                <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                        <Button className="h-8 w-8 p-0" size="icon-sm" variant="ghost">
                            <span className="sr-only">Open menu</span>
                            <MoreHorizontal className="h-4 w-4" />
                        </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => onViewDetails(row.original)}>
                            <Pencil className="mr-2 h-4 w-4" /> View Details
                        </DropdownMenuItem>
                    </DropdownMenuContent>
                </DropdownMenu>
            ),
        },
    ];

    return (
        <>
            <div className="flex items-center justify-between py-6">
                <div>
                    <CardTitle className="text-2xl">Audit Trail</CardTitle>
                    <p className="text-muted-foreground">View all system activity and changes</p>
                </div>
            </div>
            <Card className="pb-6">
                <CardHeader>
                    <CardTitle className="text-2xl">Filters</CardTitle>
                </CardHeader>
                <CardContent>
                    <div className="grid grid-cols-3 gap-4">
                        {usersData && actionsData && (
                            <>
                                <div>
                                    <Select
                                        label="User"
                                        options={(usersData || []).map((u: any) => ({
                                            value: u.id,
                                            label: `${u.first_name} ${u.last_name}`,
                                        }))}
                                        value={filters.user_id}
                                        onValueChange={(v: any) => handleFilterChange("user_id", v)}
                                    />
                                </div>
                                <div>
                                    <Select
                                        label="Action"
                                        options={(actionsData || []).map((a: any) => ({ value: a, label: a }))}
                                        value={filters.action}
                                        onValueChange={(v: any) => handleFilterChange("action", v)}
                                    />
                                </div>
                            </>
                        )}
                        <div>
                            <Input
                                label="Query"
                                placeholder="Search old/new values"
                                value={filters.query || ""}
                                onChange={(e) => handleFilterChange("query", e.target.value)}
                            />
                        </div>
                    </div>

                    <div className="grid grid-cols-2 gap-4 py-6">
                        <Input
                            label="Start date"
                            type="date"
                            value={filters.start_date || ""}
                            onChange={(e) => handleFilterChange("start_date", e.target.value)}
                        />
                        <Input
                            label="End date"
                            type="date"
                            value={filters.end_date || ""}
                            onChange={(e) => handleFilterChange("end_date", e.target.value)}
                        />
                    </div>
                </CardContent>
                <CardFooter className="flex justify-end">
                    <Button variant="secondary" onClick={() => setFilters({ page: 1, size: 50 })}>
                        Reset
                    </Button>
                </CardFooter>
            </Card>

            <Card className="mt-6">
                <CardContent>
                    <DataTable
                        columns={columns}
                        data={auditsData || []}
                        enableFiltering={false}
                        enablePagination={true}
                        enableSorting={true}
                        pageSize={10}
                    />
                </CardContent>
            </Card>
            <AuditDiffDialog open={dialogOpen} payload={dialogPayload} onOpenChange={(v) => setDialogOpen(v)} />
        </>
    );
}
