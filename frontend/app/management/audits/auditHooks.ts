import { useQuery } from "@tanstack/react-query";

import { fetchAudits, fetchActions } from "@/services/AuditService";
import { AuditFilter } from "@/types";

export function useAudits(filter: Partial<AuditFilter> = {}) {
    return useQuery({
        queryKey: ["audits", filter],
        queryFn: () => fetchAudits(filter),
        select: (res) => res.data || [],
    });
}

export function useAuditActions() {
    return useQuery({
        queryKey: ["auditActions"],
        queryFn: () => fetchActions(),
        select: (res) => res.data || [],
    });
}
