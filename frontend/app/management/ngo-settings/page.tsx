"use client";

import React, { useEffect } from "react";
import { useRouter } from "next/navigation";
import { toast } from "sonner";

import { NGOSettingsForm } from "@/components/forms/NGOSettingsForm";
import { useError } from "@/context/ErrorContext";
import { useLoading } from "@/context/LoadingContext";
import { SystemConfigurationData, useSystemConfiguration } from "@/hooks/use-system-configuration";

const GROUP = "ngo";

export default function NGOSettingsPage() {
    if (typeof document !== "undefined") {
        document.title = "NGO Settings - myNGO";
    }
    
    const router = useRouter();
    const { loading, updating, error, data, loadConfiguration, updateConfiguration } = useSystemConfiguration();
    const { showError, hideError } = useError();
    const { showLoading, hideLoading } = useLoading();

    useEffect(() => {
        loadConfiguration(GROUP);
    }, [loadConfiguration]);

    useEffect(() => {
        if (loading) showLoading("Loading NGO settings...");
        if (updating) showLoading("Updating NGO settings...");
        !loading && !updating ? hideLoading() : null;
    }, [loading, updating]);

    useEffect(() => {
        error ? showError("Failed to load NGO settings", () => loadConfiguration(GROUP)) : hideError();
    }, [error]);

    const handleSubmit = async (formData: SystemConfigurationData) => {
        await updateConfiguration(formData);
        if (!error) toast.success("NGO settings updated successfully!");
    };

    const handleCancel = () => {
        router.push("/settings");
    };

    return (
        <NGOSettingsForm
            initialData={data}
            loading={loading || updating}
            onCancel={handleCancel}
            onSubmit={handleSubmit}
        />
    );
}
