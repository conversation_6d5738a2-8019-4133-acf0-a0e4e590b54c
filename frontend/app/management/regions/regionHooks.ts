// React Query hooks for regions CRUD
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";

import { fetchAll, create, update, deleteRegion, getById, RegionRequest } from "@/services/RegionService";
import { RegionFilter } from "@/types";

export function useRegions(filter: Partial<RegionFilter> = {}) {
    return useQuery({
        queryKey: ["regions", filter],
        queryFn: () => fetchAll(filter),
        select: (res) => res.data || [],
        retry: 2,
    });
}

export function useRegion(id: string) {
    return useQuery({
        queryKey: ["regions", id],
        queryFn: () => getById(id),
        select: (res) => res.data,
        enabled: !!id,
    });
}

export function useCreateRegion() {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: (region: RegionRequest) => create(region),
        onSuccess: () => queryClient.invalidateQueries({ queryKey: ["regions"] }),
    });
}

export function useUpdateRegion() {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: ({ id, data }: { id: string; data: RegionRequest }) => update(id, data),
        onSuccess: () => queryClient.invalidateQueries({ queryKey: ["regions"] }),
    });
}

export function useDeleteRegion() {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: (regionId: string) => deleteRegion(regionId),
        onSuccess: () => queryClient.invalidateQueries({ queryKey: ["regions"] }),
    });
}
