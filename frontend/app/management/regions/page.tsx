"use client";

import { useEffect, useState } from "react";
import { toast } from "sonner";

import { useRegions, useCreateRegion, useUpdateRegion, useDeleteRegion } from "./regionHooks";

import { RegionRequest } from "@/services/RegionService";
import { RegionDto } from "@/types";
import { <PERSON>alog, DialogContent, <PERSON><PERSON>Footer, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { RegionsTable } from "@/components/region/regions-table";
import { useError } from "@/context/ErrorContext";
import { useDeleteConfirmation } from "@/context/DeleteItemContext";

interface RegionFormModalProps {
    open: boolean;
    onClose: () => void;
    onSubmit: (region: RegionRequest) => void;
    initialRegion?: Partial<RegionDto>;
    loading?: boolean;
}

export default function RegionsManagementPage() {
    if (typeof document !== "undefined") {
        document.title = "Region Management - myNGO";
    }
    
    const [modalOpen, setModalOpen] = useState(false);
    const [editingRegion, setEditingRegion] = useState<Partial<RegionDto> | undefined>(undefined);

    const { showError, hideError } = useError();
    const { openDeleteDialog } = useDeleteConfirmation();

    const { data: regionsData, isLoading, isError } = useRegions();
    const createMutation = useCreateRegion();
    const updateMutation = useUpdateRegion();
    const deleteMutation = useDeleteRegion();

    useEffect(() => {
        if (isError) {
            showError("Failed to load regions. Please try again later.");

            return;
        }
        hideError();
    }, [isError]);

    // Handlers for UI actions
    const handleCreateRegion = () => {
        setEditingRegion(undefined);
        setModalOpen(true);
    };

    const handleEditRegion = (region: RegionDto) => {
        setEditingRegion(region);
        setModalOpen(true);
    };

    const handleDeleteRegion = (region: RegionDto) => {
        openDeleteDialog({
            title: "Delete Region",
            description: `Are you sure you want to delete the region '${region.name}'? This will also affect all districts in this region.`,
            onConfirm: () => {
                deleteMutation.mutate(region.id);
                toast.warning("Region deleted successfully");
            },
            itemName: region.name,
        });
    };

    const handleModalClose = () => {
        setModalOpen(false);
        setEditingRegion(undefined);
    };

    const handleModalSubmit = (region: RegionRequest) => {
        if (editingRegion && editingRegion.id) {
            updateMutation.mutate({ id: editingRegion.id, data: region }, { onSuccess: handleModalClose });
            toast.success("Region updated successfully");
        } else {
            createMutation.mutate(region, { onSuccess: handleModalClose });
            toast.success("Region created successfully");
        }
    };

    return (
        <>
            <RegionsTable
                loading={isLoading}
                regions={regionsData || []}
                onCreate={handleCreateRegion}
                onDelete={handleDeleteRegion}
                onEdit={handleEditRegion}
            />

            <RegionFormModal
                initialRegion={editingRegion}
                loading={createMutation.isPending || updateMutation.isPending}
                open={modalOpen}
                onClose={handleModalClose}
                onSubmit={handleModalSubmit}
            />
        </>
    );
}

function RegionFormModal({ open, onClose, onSubmit, initialRegion = {}, loading }: RegionFormModalProps) {
    const [name, setName] = useState(initialRegion.name || "");
    const [code, setCode] = useState(initialRegion.code || "");
    const [description, setDescription] = useState(initialRegion.description || "");

    useEffect(() => {
        if (!initialRegion || !initialRegion.id) return;
        setName(initialRegion.name || "");
        setCode(initialRegion.code || "");
        setDescription(initialRegion.description || "");
    }, [initialRegion]);

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        if (!name || !code || !description) {
            toast.error("Please fill in all required fields");

            return;
        }
        onSubmit({ name, code, description });
    };

    return (
        <Dialog open={open} onOpenChange={onClose}>
            <DialogContent>
                <DialogHeader>
                    <DialogTitle>{initialRegion.id ? "Edit Region" : "Create Region"}</DialogTitle>
                </DialogHeader>
                <form className="space-y-4" onSubmit={handleSubmit}>
                    <Input
                        required
                        label="Name"
                        maxLength={20}
                        placeholder="Enter region name"
                        value={name}
                        onChange={(e) => setName(e.target.value)}
                    />
                    <Input
                        required
                        label="Code"
                        maxLength={10}
                        placeholder="Enter region code"
                        value={code}
                        onChange={(e) => setCode(e.target.value)}
                    />
                    <div className="space-y-2">
                        <label className="text-sm font-medium">Description *</label>
                        <Textarea
                            required
                            maxLength={255}
                            placeholder="Enter region description"
                            value={description}
                            onChange={(e) => setDescription(e.target.value)}
                        />
                    </div>
                    <DialogFooter>
                        <Button disabled={loading} type="button" variant="secondary" onClick={onClose}>
                            Cancel
                        </Button>
                        <Button loading={loading} type="submit" variant="default">
                            {initialRegion.id ? "Update" : "Create"}
                        </Button>
                    </DialogFooter>
                </form>
            </DialogContent>
        </Dialog>
    );
}
