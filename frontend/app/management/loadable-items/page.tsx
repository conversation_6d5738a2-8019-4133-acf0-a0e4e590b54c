"use client";

import React, { useState, useEffect } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { Plus, Filter, RefreshCw, Package, AlertCircle } from "lucide-react";
import { toast } from "sonner";

import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { EnhancedSelect as Select } from "@/components/ui/enhanced-select";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { LoadableItemsTable } from "@/components/settings/LoadableItemsTable";
import { LoadableItemForm } from "@/components/settings/LoadableItemForm";
import { HttpResponse, LoadableItemDto, LoadableItemType } from "@/types";
import * as settingsService from "@/services/SettingsService";
import { useError } from "@/context/ErrorContext";
import { useDeleteConfirmation } from "@/context/DeleteItemContext";

export default function LoadableItemsPage() {
    if (typeof document !== "undefined") {
        document.title = "Loadable Items - myNGO";
    }
    
    const [selectedType, setSelectedType] = useState<LoadableItemType | undefined>(undefined);
    const [showForm, setShowForm] = useState(false);
    const [editingItem, setEditingItem] = useState<LoadableItemDto | null>(null);
    const [formMode, setFormMode] = useState<"create" | "edit">("create");
    const [voidReason, setVoidReason] = useState("");

    const queryClient = useQueryClient();
    const { showError, hideError } = useError();

    const { openDeleteDialog } = useDeleteConfirmation();

    // Fetch loadable item types
    const {
        data: typesResponse,
        isLoading: typesLoading,
        error: typesError,
        refetch: refetchTypes,
    } = useQuery({
        queryKey: ["loadable-item-types"],
        queryFn: () => settingsService.fetchLoadableItemTypes({ size: 100 }),
        retry: 3,
    });

    // Fetch loadable items based on selected type
    const {
        data: itemsResponse,
        isLoading: itemsLoading,
        error: itemsError,
        refetch: refetchItems,
    } = useQuery({
        queryKey: ["loadable-items", selectedType],
        queryFn: () => settingsService.fetchLoadableItems({ type: selectedType as LoadableItemType, size: 100 }),
        enabled: true,
    });

    useEffect(() => {
        if (itemsError || typesError) {
            showError("Failed to load loadable items", () => {
                refetchItems();
                refetchTypes();
            });

            return;
        }
        hideError();
    }, [itemsError, typesError]);

    // Create mutation
    const createMutation = useMutation({
        mutationFn: settingsService.createLoadableItem,
        onSuccess: () => {
            toast.success("Loadable item created successfully");
            queryClient.invalidateQueries({ queryKey: ["loadable-items"] });
            queryClient.invalidateQueries({ queryKey: ["loadable-item-types"] });
            setShowForm(false);
        },
        onError: (error: any) => {
            toast.error(error?.message || "Failed to create loadable item");
        },
    });

    // Update mutation
    const updateMutation = useMutation({
        mutationFn: ({ id, data }: { id: string; data: any }) => settingsService.updateLoadableItem(id, data),
        onSuccess: () => {
            toast.success("Loadable item updated successfully");
            queryClient.invalidateQueries({ queryKey: ["loadable-items"] });
            queryClient.invalidateQueries({ queryKey: ["loadable-item-types"] });
            setShowForm(false);
            setEditingItem(null);
        },
        onError: (error: any) => {
            toast.error(error?.message || "Failed to update loadable item");
        },
    });

    // Delete mutation
    const deleteMutation = useMutation({
        mutationFn: () => settingsService.deleteLoadableItem(editingItem!.id, voidReason),
        onSuccess: (data: HttpResponse<boolean>) => {
            if (data.success) {
                hideError();
                toast.success("Loadable item deleted successfully");
                queryClient.invalidateQueries({ queryKey: ["loadable-items"] });
                setEditingItem(null);

                return;
            }
            showError("Failed to Delete Item", () => handleDeleteItem(editingItem!));
        },
        onError: (error: any) => {
            toast.error(error?.message || "Failed to delete loadable item");
        },
    });

    const handleCreateItem = () => {
        setFormMode("create");
        setEditingItem(null);
        setShowForm(true);
    };

    const handleEditItem = (item: LoadableItemDto) => {
        setFormMode("edit");
        setEditingItem(item);
        setShowForm(true);
    };

    const handleDeleteItem = (item: LoadableItemDto) => {
        setEditingItem(item);
        openDeleteDialog({
            onConfirm: (voidReason: string) => {
                setVoidReason(voidReason);
                deleteMutation.mutate();
            },
            itemName: item.display_value,
            title: "Delete Loadable Item",
            description: `This will permanently delete the loadable item "${item.display_value}" and all associated data. This action cannot be undone.`,
        });
    };

    const handleFormSubmit = async (data: any) => {
        if (formMode === "create") {
            await createMutation.mutateAsync(data);
        } else if (editingItem) {
            await updateMutation.mutateAsync({ id: editingItem.id, data });
        }
    };

    const availableTypes = typesResponse?.data || [];
    const loadableItems = itemsResponse?.data || [];

    const typeOptions = [
        { value: "All", label: "All Types" },
        ...availableTypes.map((type) => ({
            value: type,
            label: type.charAt(0) + type.slice(1).toLowerCase(),
        })),
    ];

    return (
        <div className="flex flex-col py-6 space-y-6">
            {/* Header */}
            <div className="flex items-center justify-between">
                <div>
                    <h1 className="text-3xl font-bold tracking-tight">Loadable Items</h1>
                    <p className="text-muted-foreground">Manage system loadable items and their configurations</p>
                </div>
                <Button className="flex items-center gap-2" onClick={handleCreateItem}>
                    <Plus className="h-4 w-4" />
                    Add Item
                </Button>
            </div>

            {/* Filters */}
            <Card className="py-6">
                <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                        <Filter className="h-5 w-5" />
                        Filters
                    </CardTitle>
                    <CardDescription>Filter loadable items by type to view specific categories</CardDescription>
                </CardHeader>
                <CardContent>
                    <div className="flex items-center gap-4">
                        <div className="flex-1 max-w-xs">
                            {typeOptions && (
                                <Select
                                    defaultValue={typeOptions[0].value}
                                    disabled={typesLoading}
                                    options={typeOptions}
                                    placeholder="Select type to filter"
                                    value={selectedType}
                                    onValueChange={(v) => setSelectedType(v as LoadableItemType | undefined)}
                                />
                            )}
                        </div>
                        <Button
                            className="flex items-center gap-2"
                            disabled={itemsLoading}
                            variant="outline"
                            onClick={() => refetchItems()}
                        >
                            <RefreshCw className={`h-4 w-4 ${itemsLoading ? "animate-spin" : ""}`} />
                            Refresh
                        </Button>
                    </div>
                </CardContent>
            </Card>

            {/* Error Alert */}
            {itemsError && (
                <Alert variant="destructive">
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>Failed to load loadable items. Please try again.</AlertDescription>
                    <AlertDescription>Failed to load loadable items. Please try again.</AlertDescription>
                </Alert>
            )}

            {/* Data Table */}
            <Card className="py-6">
                <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                        <Package className="h-5 w-5" />
                        Items
                        {selectedType && (
                            <span className="text-sm font-normal text-muted-foreground">({selectedType})</span>
                        )}
                    </CardTitle>
                    <CardDescription>
                        {loadableItems.length} item{loadableItems.length !== 1 ? "s" : ""} found
                        {loadableItems.length} item{loadableItems.length !== 1 ? "s" : ""} found
                    </CardDescription>
                </CardHeader>
                <CardContent>
                    <LoadableItemsTable
                        data={loadableItems}
                        loading={itemsLoading}
                        onDelete={handleDeleteItem}
                        onEdit={handleEditItem}
                    />
                </CardContent>
            </Card>

            {/* Form Modal */}
            <LoadableItemForm
                availableTypes={availableTypes}
                initialData={editingItem}
                loading={createMutation.isPending || updateMutation.isPending}
                mode={formMode}
                open={showForm}
                onClose={() => {
                    setShowForm(false);
                    setEditingItem(null);
                }}
                onSubmit={handleFormSubmit}
            />
        </div>
    );
}
