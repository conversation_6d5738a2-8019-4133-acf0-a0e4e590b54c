"use client";

import { useAuth } from "@/composables/useStore";
import DefaultLayout from "@/layouts/DefaultLayout";
import PublicLayout from "@/layouts/PublicLayout";
import { LayoutProps } from "@/types";

export default function AuthLayout({ children }: LayoutProps) {
    const { isAuthenticated } = useAuth();

    return <>{isAuthenticated ? <DefaultLayout>{children}</DefaultLayout> : <PublicLayout>{children}</PublicLayout>}</>;
}
