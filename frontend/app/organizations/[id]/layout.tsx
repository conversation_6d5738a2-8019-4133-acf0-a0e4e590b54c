"use client";

import { Eye, FileText, Settings, Users } from "lucide-react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { use } from "react";

import { useAuth } from "@/composables/useStore";
import { cn } from "@/lib/utils";

const menuItems = [
    {
        id: "overview",
        label: "Overview",
        icon: Eye,
        href: "",
    },
    {
        id: "members",
        label: "Members",
        icon: Users,
        href: "/members",
    },
    {
        id: "applications",
        label: "Applications",
        icon: FileText,
        href: "/applications",
    },
    {
        id: "settings",
        label: "Settings",
        icon: Settings,
        href: "/settings",
    },
];

export default function OrganizationLayout({
    params,
    children,
}: {
    params: Promise<{ id: string }>;
    children: React.ReactNode;
}) {
    const { id } = use(params);
    const { isAuthenticated } = useAuth();
    const pathname = usePathname();

    const currentPath = pathname.split("/").pop() || "";
    const activeMenuItem =
        menuItems.find((item) => {
            if (item.href === "" && (currentPath === id || pathname.endsWith(`/${id}`))) return true;
            if (item.href !== "" && pathname.endsWith(item.href)) return true;

            return false;
        })?.id || "overview";

    return (
        <div className="min-h-screen">
            <div className="max-w-7xl mx-auto space-y-6">
                {/* Menu Navigation */}
                {isAuthenticated && (
                    <nav className="flex border-b border-border/50 mt-3">
                        {menuItems.map((item) => {
                            const Icon = item.icon;
                            const isActive = activeMenuItem === item.id;
                            const href = `/organizations/${id}${item.href}`;

                            return (
                                <Link
                                    key={item.id}
                                    className={cn(
                                        "flex items-center gap-2 px-6 py-2 rounded-xl rounded-b-none text-sm font-medium border-b-2 border-transparent transition-colors hover:text-primary hover:bg-muted/50",
                                        isActive && "border-primary text-primary bg-primary/5",
                                    )}
                                    href={href}
                                >
                                    <Icon className="w-4 h-4" />
                                    {item.label}
                                </Link>
                            );
                        })}
                    </nav>
                )}

                {/* Page Content */}
                <div className="min-h-[400px]">{children}</div>
            </div>
        </div>
    );
}
