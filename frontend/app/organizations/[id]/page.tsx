"use client";

import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import {
    AlertCircle,
    Briefcase,
    Building2,
    CheckCircle,
    Clock,
    DollarSign,
    Edit,
    Eye,
    FileText,
    Globe,
    MapPin,
    Plus,
    Target,
    Users,
} from "lucide-react";
import { use, useState } from "react";
import { toast } from "sonner";

import BankDetailsManagement from "@/components/organization/bank-details-management";
import DirectorsManagement from "@/components/organization/directors-management";
import OrganizationProfileBanner from "@/components/organization/organization-profile-banner";
import SectorsManagement from "@/components/organization/sectors-management";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useAuth } from "@/composables/useStore";
import * as applicationService from "@/services/ApplicationService";
import * as organizationService from "@/services/OrganizationService";
import { OrganizationDto } from "@/types/organization.dto";

const getStatusConfig = (status: string) => {
    switch (status) {
        case "DRAFT":
        case "RENEWAL_DRAFT":
            return {
                color: "bg-yellow-100 text-yellow-800",
                borderColor: "border-yellow-200",
                bgColor: "bg-yellow-500/20",
                iconColor: "text-yellow-600 dark:text-yellow-400",
                icon: <Edit className="w-4 h-4" />,
                label: "Draft",
                description: "Organization profile is being prepared",
            };
        case "PENDING":
            return {
                color: "bg-blue-100 text-blue-800",
                borderColor: "border-blue-200",
                bgColor: "bg-blue-500/20",
                iconColor: "text-blue-600 dark:text-blue-400",
                icon: <Clock className="w-4 h-4" />,
                label: "Pending Review",
                description: "Application submitted and awaiting review",
            };
        case "REVIEW":
            return {
                color: "bg-purple-100 text-purple-800",
                borderColor: "border-purple-200",
                bgColor: "bg-purple-500/20",
                iconColor: "text-purple-600 dark:text-purple-400",
                icon: <Eye className="w-4 h-4" />,
                label: "Under Review",
                description: "Application is being reviewed by authorities",
            };
        case "REGISTERED":
        case "ACTIVE":
            return {
                color: "bg-green-100 text-green-800",
                borderColor: "border-green-200",
                bgColor: "bg-green-500/20",
                iconColor: "text-green-600 dark:text-green-400",
                icon: <CheckCircle className="w-4 h-4" />,
                label: "Active",
                description: "Organization is approved and operational",
            };
        case "SUSPENDED":
            return {
                color: "bg-red-100 text-red-800",
                borderColor: "border-red-200",
                bgColor: "bg-red-500/20",
                iconColor: "text-red-600 dark:text-red-400",
                icon: <AlertCircle className="w-4 h-4" />,
                label: "Suspended",
                description: "Organization activities are temporarily suspended",
            };
        case "INACTIVE":
            return {
                color: "bg-gray-100 text-gray-800",
                borderColor: "border-gray-200",
                bgColor: "bg-gray-500/20",
                iconColor: "text-gray-600 dark:text-gray-400",
                icon: <AlertCircle className="w-4 h-4" />,
                label: "Inactive",
                description: "Organization is no longer active",
            };
        default:
            return {
                color: "bg-gray-100 text-gray-800",
                borderColor: "border-gray-200",
                bgColor: "bg-gray-500/20",
                iconColor: "text-gray-600 dark:text-gray-400",
                icon: <AlertCircle className="w-4 h-4" />,
                label: status,
                description: "Unknown status",
            };
    }
};

export default function OrganizationProfile({ params }: { params: Promise<{ id: string }> }) {
    const { id } = use(params);

    if (typeof document !== "undefined") {
        document.title = "Organization Details - myNGO";
    }
    const { isAuthenticated } = useAuth();
    const queryClient = useQueryClient();
    const [activeTab, setActiveTab] = useState("overview");

    const {
        data: organizationData,
        isLoading,
        error,
        refetch,
    } = useQuery({
        queryKey: ["organization", id],
        queryFn: () => organizationService.getOrganization(id),
        enabled: !!id,
        select: (response) => response.data,
    });

    const { data: applicationHistoryData } = useQuery({
        queryKey: ["application-history", id],
        queryFn: () => applicationService.getApplicationHistory?.(id) || Promise.resolve({ data: [] }),
        enabled: !!id && !!organizationData && isAuthenticated,
        select: (response) => response.data || [],
    });

    const submitMutation = useMutation({
        mutationFn: () => applicationService.submitForReview(id),
        onSuccess: (response) => {
            if (response.success) {
                toast.success("Application submitted for review successfully!");
            }

            for (const error of response.errors) {
                toast.error(error.message);
            }

            queryClient.invalidateQueries({ queryKey: ["organization", id] });
        },
        onError: (error: any) => {
            toast.error(error?.message || "Failed to submit application for review");
        },
    });

    const organization = organizationData as OrganizationDto;
    const directors = organizationData?.directors || [];
    const sectors = organizationData?.sectors || [];
    const locationActivities = organizationData?.location_activities || [];
    const targetGroups = organizationData?.target_groups || [];
    const projects = organizationData?.projects || [];
    const applicationHistory = applicationHistoryData || [];
    const bankDetails = organizationData?.bank_details || [];
    const auditors = organizationData?.auditors || [];
    const donors = organizationData?.donors || [];
    const fundingSources = organizationData?.funding_sources || [];

    const statusConfig = organization ? getStatusConfig(organization.status) : getStatusConfig("DRAFT");
    const canEdit =
        organization?.status === "DRAFT" ||
        organization?.status === "REJECTED" ||
        organization?.status === "RENEWAL_DRAFT";
    const canSubmit = ["DRAFT", "RENEWAL_DRAFT"].includes(organization?.status);

    if (isLoading) {
        return (
            <div className="max-w-7xl mx-auto p-6 space-y-6 min-h-screen">
                <div className="flex items-center justify-center py-16">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary" />
                    <span className="ml-4 text-lg text-foreground">Loading organization...</span>
                </div>
            </div>
        );
    }

    if (error || !organization) {
        return (
            <div className="max-w-7xl mx-auto p-6 space-y-6 min-h-screen">
                <div className="flex items-center justify-center py-16">
                    <div className="text-center">
                        <AlertCircle className="h-16 w-16 text-destructive mx-auto mb-4" />
                        <h3 className="text-xl font-semibold text-foreground mb-2">Error loading organization</h3>
                        <p className="text-muted-foreground mb-4">
                            {error?.message || "Failed to load organization data"}
                        </p>
                        <Button onClick={() => refetch()}>Try Again</Button>
                    </div>
                </div>
            </div>
        );
    }

    return (
        <div className="space-y-6">
            {/* Organization Banner */}
            <OrganizationProfileBanner
                canEdit={canEdit}
                canSubmit={canSubmit}
                isSubmitting={submitMutation.isPending}
                organization={organization}
                onSubmitForReview={() => submitMutation.mutate()}
            />

            {/* Quick Stats */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <Card className="border-border/50 bg-card/50 backdrop-blur-sm transition-all duration-300">
                    <CardContent className="p-6">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm font-medium text-muted-foreground">Directors</p>
                                <p className="text-2xl font-bold text-foreground">{directors.length}</p>
                            </div>
                            <div className="p-3 rounded-full bg-blue-500/20 text-blue-600 dark:text-blue-400">
                                <Users className="w-6 h-6" />
                            </div>
                        </div>
                    </CardContent>
                </Card>

                <Card className="border-border/50 bg-card/50 backdrop-blur-sm transition-all duration-300">
                    <CardContent className="p-6">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm font-medium text-muted-foreground">Sectors</p>
                                <p className="text-2xl font-bold text-foreground">{sectors.length}</p>
                            </div>
                            <div className="p-3 rounded-full bg-green-500/20 text-green-600 dark:text-green-400">
                                <Target className="w-6 h-6" />
                            </div>
                        </div>
                    </CardContent>
                </Card>

                <Card className="border-border/50 bg-card/50 backdrop-blur-sm transition-all duration-300">
                    <CardContent className="p-6">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm font-medium text-muted-foreground">Annual Income</p>
                                <p className="text-lg font-bold text-foreground">
                                    ${organization.annual_income?.toLocaleString() || "0"}
                                </p>
                            </div>
                            <div className="p-3 rounded-full bg-purple-500/20 text-purple-600 dark:text-purple-400">
                                <DollarSign className="w-6 h-6" />
                            </div>
                        </div>
                    </CardContent>
                </Card>

                <Card className="border-border/50 bg-card/50 backdrop-blur-sm transition-all duration-300">
                    <CardContent className="p-6">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm font-medium text-muted-foreground">Status</p>
                                <p className="text-sm text-muted-foreground">{statusConfig.description}</p>
                            </div>
                            <div className={`p-3 rounded-full ${statusConfig.bgColor} ${statusConfig.iconColor}`}>
                                {statusConfig.icon}
                            </div>
                        </div>
                    </CardContent>
                </Card>
            </div>

            {/* Main Content Tabs */}
            <Card className="border-border/50 bg-card/50 backdrop-blur-sm">
                <Tabs className="" value={activeTab} onValueChange={setActiveTab}>
                    <TabsList className="grid w-full grid-cols-2 md:grid-cols-8 dark:bg-background">
                        <TabsTrigger
                            className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground"
                            value="overview"
                        >
                            Overview
                        </TabsTrigger>
                        <TabsTrigger
                            className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground"
                            value="directors"
                        >
                            Directors
                        </TabsTrigger>
                        <TabsTrigger
                            className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground"
                            value="operations"
                        >
                            Operations
                        </TabsTrigger>
                        <TabsTrigger
                            className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground"
                            value="financial"
                        >
                            Financial
                        </TabsTrigger>
                        <TabsTrigger
                            className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground"
                            value="projects"
                        >
                            Projects
                        </TabsTrigger>
                        <TabsTrigger
                            className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground"
                            value="documents"
                        >
                            Documents
                        </TabsTrigger>
                        <TabsTrigger
                            className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground"
                            value="compliance"
                        >
                            Compliance
                        </TabsTrigger>
                        <TabsTrigger
                            className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground"
                            value="history"
                        >
                            History
                        </TabsTrigger>
                    </TabsList>

                    {/* Overview Tab */}
                    <TabsContent className="space-y-6 p-6" value="overview">
                        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                            {/* Vision & Mission */}
                            <div className="lg:col-span-2 space-y-6">
                                <Card className="border-border/50 bg-card/30 backdrop-blur-sm">
                                    <CardHeader>
                                        <CardTitle className="flex items-center gap-2 text-foreground">
                                            <Globe className="w-5 h-5 text-primary" />
                                            Vision & Mission
                                        </CardTitle>
                                    </CardHeader>
                                    <CardContent className="space-y-4">
                                        {organization.vision && (
                                            <div>
                                                <h4 className="font-semibold text-foreground mb-2">Vision</h4>
                                                <p className="text-muted-foreground leading-relaxed">
                                                    {organization.vision}
                                                </p>
                                            </div>
                                        )}
                                        {organization.motto && (
                                            <div>
                                                <h4 className="font-semibold text-foreground mb-2">Motto</h4>
                                                <p className="text-primary font-medium italic">
                                                    &quot;{organization.motto}&quot;
                                                </p>
                                            </div>
                                        )}
                                        {organization.objectives && organization.objectives.length > 0 && (
                                            <div>
                                                <h4 className="font-semibold text-foreground mb-2">Objectives</h4>
                                                <ul className="space-y-2">
                                                    {organization.objectives.map((objective, index) => (
                                                        <li key={index} className="flex items-start gap-2">
                                                            <CheckCircle className="w-4 h-4 text-emerald-500 mt-1 flex-shrink-0" />
                                                            <span className="text-muted-foreground">{objective}</span>
                                                        </li>
                                                    ))}
                                                </ul>
                                            </div>
                                        )}
                                        {organization.biography && (
                                            <div>
                                                <h4 className="font-semibold text-foreground mb-2">About</h4>
                                                <p className="text-muted-foreground leading-relaxed">
                                                    {organization.biography}
                                                </p>
                                            </div>
                                        )}
                                    </CardContent>
                                </Card>
                            </div>

                            {/* Quick Info */}
                            <Card className="border-border/50 border-none bg-card/30 backdrop-blur-sm">
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2 text-foreground">
                                        <Building2 className="w-5 h-5 text-primary" />
                                        Organization Details
                                    </CardTitle>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    <div className="space-y-3">
                                        <div>
                                            <p className="text-sm font-medium text-muted-foreground">
                                                Registration Number
                                            </p>
                                            <p className="font-medium text-foreground">
                                                {organization.registration_number}
                                            </p>
                                        </div>
                                        {organization.charity_number && (
                                            <div>
                                                <p className="text-sm font-medium text-muted-foreground">
                                                    Charity Number
                                                </p>
                                                <p className="font-medium text-foreground">
                                                    {organization.charity_number}
                                                </p>
                                            </div>
                                        )}
                                        <div>
                                            <p className="text-sm font-medium text-muted-foreground">Financial Year</p>
                                            <p className="font-medium text-foreground">
                                                {organization.financial_start_month} -{" "}
                                                {organization.financial_end_month}
                                            </p>
                                        </div>
                                        <div>
                                            <p className="text-sm font-medium text-muted-foreground">Annual Income</p>
                                            <p className="font-medium text-foreground">
                                                ${organization.annual_income?.toLocaleString() || "0"}
                                            </p>
                                        </div>
                                        <div>
                                            <p className="text-sm font-medium text-muted-foreground">Status</p>
                                            <p className="text-sm text-muted-foreground">{statusConfig.description}</p>
                                        </div>
                                        <div>
                                            <p className="text-sm font-medium text-muted-foreground">Created</p>
                                            <p className="font-medium text-foreground">
                                                {new Date(organization.created_at).toLocaleDateString()}
                                            </p>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>
                        </div>

                        {/* Sectors */}
                        <SectorsManagement canEdit={canEdit} organizationId={id} sectors={sectors} />
                    </TabsContent>

                    {/* Directors Tab */}
                    <TabsContent className="space-y-6 p-6" value="directors">
                        <DirectorsManagement canEdit={canEdit} directors={directors} organizationId={id} />
                    </TabsContent>

                    {/* Operations Tab */}
                    <TabsContent className="space-y-6 p-6" value="operations">
                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <Card className="border-border/50 bg-card/30 backdrop-blur-sm shadow-lg">
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2 text-foreground">
                                        <MapPin className="w-5 h-5 text-primary" />
                                        Location Activities
                                    </CardTitle>
                                </CardHeader>
                                <CardContent className="text-center py-8">
                                    <MapPin className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                                    {locationActivities.map((location) => {
                                        return <div key={location.id}>{location?.district}</div>;
                                    })}
                                    {locationActivities.length ? (
                                        <p className="text-muted-foreground mb-4">No location activities added yet.</p>
                                    ) : (
                                        ""
                                    )}
                                    {canEdit && <Button className="w-full">Add Location Activity</Button>}
                                </CardContent>
                            </Card>

                            <Card className="border-border/50 bg-card/30 backdrop-blur-sm shadow-lg">
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2 text-foreground">
                                        <Target className="w-5 h-5 text-primary" />
                                        Target Groups
                                    </CardTitle>
                                </CardHeader>
                                <CardContent className="text-center py-8">
                                    <Target className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                                    <p className="text-muted-foreground mb-4">No target groups defined yet.</p>
                                    {canEdit && <Button className="w-full">Add Target Group</Button>}
                                </CardContent>
                            </Card>

                            <Card className="border-border/50 bg-card/30 backdrop-blur-sm shadow-lg lg:col-span-2">
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2 text-foreground">
                                        <Users className="w-5 h-5 text-primary" />
                                        Staff Information
                                    </CardTitle>
                                </CardHeader>
                                <CardContent className="text-center py-8">
                                    <Users className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                                    <p className="text-muted-foreground mb-4">No staff information added yet.</p>
                                    {canEdit && <Button className="w-full">Add Staff Details</Button>}
                                </CardContent>
                            </Card>
                        </div>
                    </TabsContent>

                    {/* Financial Tab */}
                    <TabsContent className="space-y-6 p-6" value="financial">
                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <BankDetailsManagement bankDetails={bankDetails} canEdit={canEdit} organizationId={id} />

                            <Card className="border-border/50 bg-card/30 backdrop-blur-sm shadow-lg">
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2 text-foreground">
                                        <DollarSign className="w-5 h-5 text-primary" />
                                        Funding Sources ({fundingSources.length})
                                    </CardTitle>
                                    {canEdit && (
                                        <Button size="sm">
                                            <Plus className="w-4 h-4 mr-2" />
                                            Add Funding Source
                                        </Button>
                                    )}
                                </CardHeader>
                                <CardContent>
                                    {fundingSources.length > 0 ? (
                                        <div className="space-y-2">
                                            {fundingSources.map((source) => (
                                                <div key={source.id} className="p-3 bg-card/20 rounded-lg">
                                                    <p className="font-medium">{source.donor?.display_value}</p>
                                                    <p className="text-sm text-muted-foreground">{source.amount}</p>
                                                </div>
                                            ))}
                                        </div>
                                    ) : (
                                        <div className="text-center py-8">
                                            <DollarSign className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                                            <p className="text-muted-foreground mb-4">No funding sources added yet.</p>
                                            {canEdit && <Button className="w-full">Add Funding Source</Button>}
                                        </div>
                                    )}
                                </CardContent>
                            </Card>

                            <Card className="border-border/50 bg-card/30 backdrop-blur-sm shadow-lg lg:col-span-2">
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2 text-foreground">
                                        <Users className="w-5 h-5 text-primary" />
                                        Donors ({donors.length})
                                    </CardTitle>
                                    {canEdit && (
                                        <Button size="sm">
                                            <Plus className="w-4 h-4 mr-2" />
                                            Add Donor
                                        </Button>
                                    )}
                                </CardHeader>
                                <CardContent>
                                    {donors.length > 0 ? (
                                        <div className="space-y-2">
                                            {donors.map((donor) => (
                                                <div key={donor.id} className="p-3 bg-card/20 rounded-lg">
                                                    <p className="font-medium">{donor.donor_name}</p>
                                                    <p className="text-sm text-muted-foreground">{donor.amount}</p>
                                                </div>
                                            ))}
                                        </div>
                                    ) : (
                                        <div className="text-center py-8">
                                            <Users className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                                            <p className="text-muted-foreground mb-4">No donors added yet.</p>
                                            {canEdit && <Button className="w-full">Add Donor</Button>}
                                        </div>
                                    )}
                                </CardContent>
                            </Card>
                        </div>
                    </TabsContent>

                    {/* Projects Tab */}
                    <TabsContent className="space-y-6 p-6" value="projects">
                        <Card className="border-border/50 bg-card/30 backdrop-blur-sm shadow-lg">
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2 text-foreground">
                                    <Briefcase className="w-5 h-5 text-primary" />
                                    Projects & Programs
                                </CardTitle>
                            </CardHeader>
                            <CardContent className="text-center py-8">
                                <Briefcase className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                                <p className="text-muted-foreground mb-4">No projects added yet.</p>
                                {canEdit && <Button className="w-full">Add Project</Button>}
                            </CardContent>
                        </Card>
                    </TabsContent>

                    {/* Documents Tab */}
                    <TabsContent className="space-y-6 p-6" value="documents">
                        <Card className="border-border/50 bg-card/30 backdrop-blur-sm shadow-lg">
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2 text-foreground">
                                    <FileText className="w-5 h-5 text-primary" />
                                    Supporting Documents
                                </CardTitle>
                            </CardHeader>
                            <CardContent className="text-center py-8">
                                <FileText className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                                <p className="text-muted-foreground mb-4">No documents uploaded yet.</p>
                                {canEdit && <Button className="w-full">Upload Documents</Button>}
                            </CardContent>
                        </Card>
                    </TabsContent>

                    {/* Compliance Tab */}
                    <TabsContent className="space-y-6 p-6" value="compliance">
                        <Card className="border-border/50 bg-card/30 backdrop-blur-sm shadow-lg">
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2 text-foreground">
                                    <CheckCircle className="w-5 h-5 text-primary" />
                                    Auditors & Compliance
                                </CardTitle>
                            </CardHeader>
                            <CardContent className="text-center py-8">
                                <CheckCircle className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                                <p className="text-muted-foreground mb-4">No auditors added yet.</p>
                                {canEdit && <Button className="w-full">Add Auditor</Button>}
                            </CardContent>
                        </Card>
                    </TabsContent>

                    {/* Application History Tab */}
                    <TabsContent className="space-y-6 p-6" value="history">
                        <Card className="border-border/50 bg-card/30 backdrop-blur-sm shadow-lg">
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2 text-foreground">
                                    <Clock className="w-5 h-5 text-primary" />
                                    Application History
                                </CardTitle>
                            </CardHeader>
                            <CardContent>
                                {applicationHistory.length > 0 ? (
                                    <div className="space-y-4">
                                        {applicationHistory.map((application) => (
                                            <Card
                                                key={application.id}
                                                className="border-border/50 bg-card/20 backdrop-blur-sm"
                                            >
                                                <CardContent className="p-4">
                                                    <div className="flex justify-between items-start">
                                                        <div>
                                                            <h3 className="font-semibold text-foreground">
                                                                {application.type.replace(/_/g, " ")}
                                                            </h3>
                                                            <p className="text-sm text-muted-foreground">
                                                                Code: {application.code}
                                                            </p>
                                                            <p className="text-sm text-muted-foreground">
                                                                Created:{" "}
                                                                {new Date(application.created_at).toLocaleDateString()}
                                                            </p>
                                                        </div>
                                                        <Badge
                                                            className={`${getStatusConfig(application.status).color} border ${getStatusConfig(application.status).borderColor}`}
                                                        >
                                                            {application.status}
                                                        </Badge>
                                                    </div>
                                                </CardContent>
                                            </Card>
                                        ))}
                                    </div>
                                ) : (
                                    <div className="text-center py-8">
                                        <Clock className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                                        <p className="text-muted-foreground">No application history available.</p>
                                    </div>
                                )}
                            </CardContent>
                        </Card>
                    </TabsContent>
                </Tabs>
            </Card>
        </div>
    );
}
