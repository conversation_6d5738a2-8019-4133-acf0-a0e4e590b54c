"use client";

import type { <PERSON><PERSON><PERSON><PERSON>, MemberInvitation<PERSON><PERSON>er, MemberInvitationRequest } from "@/types/membership.dto";

import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { ArrowLeft, Mail, UserCheck, UserPlus, Users } from "lucide-react";
import { use, useState } from "react";
import { toast } from "sonner";

import InvitationsTab from "@/components/organization/members/InvitationsTab";
import MembersTab from "@/components/organization/members/MembersTab";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select } from "@/components/ui/select";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { useAuth } from "@/composables/useStore";
import * as membershipService from "@/services/MembershipService";
import Link from "next/link";

const roleOptions = [{ value: "MEMBER", label: "Member" }];

export default function OrganizationMembers({ params }: { params: Promise<{ id: string }> }) {
    const { id } = use(params);

    if (typeof document !== "undefined") {
        document.title = "Organization Members - myNGO";
    }

    const { isAuthenticated } = useAuth();

    const queryClient = useQueryClient();

    const [showInviteDialog, setShowInviteDialog] = useState(false);
    const [inviteEmail, setInviteEmail] = useState("");
    const [inviteRole, setInviteRole] = useState<"MEMBER">("MEMBER");

    const [invitationStatusFilter, setInvitationStatusFilter] = useState<string>("all");

    const [membersPagination, setMembersPagination] = useState({ page: 1, size: 10 });
    const [invitationsPagination, setInvitationsPagination] = useState({ page: 1, size: 10 });
    const [membersSearch, setMembersSearch] = useState("");
    const [invitationsSearch, setInvitationsSearch] = useState("");

    const {
        data: membersResponse,
        isLoading,
        error,
    } = useQuery({
        queryKey: ["organization-members", id, membersPagination, membersSearch],
        queryFn: () =>
            membershipService.getOrganizationMembers(id, {
                page: membersPagination.page,
                size: membersPagination.size,
                search: membersSearch || undefined,
            } as Partial<MemberFilter>),
        enabled: !!id && isAuthenticated,
        select: (response) => response,
    });

    const { data: invitationsResponse } = useQuery({
        queryKey: ["member-invitations", id, invitationsPagination, invitationsSearch, invitationStatusFilter],
        queryFn: () =>
            membershipService.getMemberInvitations(id, {
                page: invitationsPagination.page,
                size: invitationsPagination.size,
                search: invitationsSearch || undefined,
                status: invitationStatusFilter !== "all" ? (invitationStatusFilter as any) : undefined,
            } as Partial<MemberInvitationFilter>),
        enabled: !!id && isAuthenticated,
        select: (response) => response,
    });

    const { data: statsResponse } = useQuery({
        queryKey: ["membership-stats", id],
        queryFn: () => membershipService.getOrganizationMembershipStats(id),
        enabled: !!id && isAuthenticated,
        select: (response) => response,
    });

    const members = membersResponse?.data || [];
    const invitations = invitationsResponse?.data || [];
    const membersTotal = membersResponse?.total || 0;
    const invitationsTotal = invitationsResponse?.total || 0;
    const stats = statsResponse?.data;

    const ownerCount = members.filter((m) => m.role === "OWNER").length;

    const inviteMutation = useMutation({
        mutationFn: (request: MemberInvitationRequest) => membershipService.inviteMember(id, request),
        onSuccess: (response) => {
            if (response.success) {
                toast.success("Invitation sent successfully!");
                setShowInviteDialog(false);
                setInviteEmail("");
                setInviteRole("MEMBER");
                handleDataChange();
            } else {
                toast.error(response.errors?.[0]?.message || "Failed to send invitation");
            }
        },
        onError: (error: any) => {
            toast.error(error?.message || "Failed to send invitation");
        },
    });

    const handleDataChange = () => {
        queryClient.invalidateQueries({ queryKey: ["organization-members", id] });
        queryClient.invalidateQueries({ queryKey: ["member-invitations", id] });
        queryClient.invalidateQueries({ queryKey: ["membership-stats", id] });
    };

    const handleInvite = () => {
        if (!inviteEmail.trim()) {
            toast.error("Please enter an email address");

            return;
        }

        inviteMutation.mutate({ email: inviteEmail, role: inviteRole });
    };

    const handleMembersPageChange = (newPage: number) => {
        setMembersPagination((prev) => ({ ...prev, page: newPage }));
    };

    const handleInvitationsPageChange = (newPage: number) => {
        setInvitationsPagination((prev) => ({ ...prev, page: newPage }));
    };

    // Fallback to client-side calculation if stats aren't loaded yet
    const activeMembers = stats?.active_members ?? members.length;
    const pendingInvites = stats?.pending_invites ?? invitations.filter((i) => i.status === "PENDING").length;
    const totalMembers = stats?.total_members ?? activeMembers + pendingInvites;

    if (!isAuthenticated) {
        return (
            <div className="flex justify-center items-center h-screen">
                <div className="grid gap-3">
                    <Button>
                        <ArrowLeft />
                        <Link href={`/organizations/${id}`}>Back to organization</Link>
                    </Button>
                </div>
            </div>
        );
    }

    if (isLoading) {
        return (
            <div className="space-y-6">
                <div className="flex items-center justify-center py-16">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary" />
                    <span className="ml-4 text-lg text-foreground">Loading members...</span>
                </div>
            </div>
        );
    }

    if (error) {
        return (
            <div className="space-y-6">
                <div className="text-center py-16">
                    <Users className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
                    <h3 className="text-xl font-semibold text-foreground mb-2">Error loading members</h3>
                    <p className="text-muted-foreground">Unable to load organization members</p>
                </div>
            </div>
        );
    }

    return (
        <div className="space-y-6">
            <div className="flex items-center justify-between">
                <div>
                    <h1 className="text-2xl font-bold text-foreground">Organization Members</h1>
                    <p className="text-muted-foreground">Manage team members and their access levels</p>
                </div>
                <Button onClick={() => setShowInviteDialog(true)}>
                    <UserPlus className="w-4 h-4 mr-2" />
                    Invite Member
                </Button>
            </div>

            {/* Stats */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Card className="border-border/50 bg-card/50 backdrop-blur-sm">
                    <CardContent className="p-6">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm font-medium text-muted-foreground">Active Members</p>
                                <p className="text-2xl font-bold text-foreground">{activeMembers}</p>
                            </div>
                            <div className="p-3 rounded-full bg-green-500/20 text-green-600 dark:text-green-400">
                                <UserCheck className="w-6 h-6" />
                            </div>
                        </div>
                    </CardContent>
                </Card>

                <Card className="border-border/50 bg-card/50 backdrop-blur-sm">
                    <CardContent className="p-6">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm font-medium text-muted-foreground">Pending Invites</p>
                                <p className="text-2xl font-bold text-foreground">{pendingInvites}</p>
                            </div>
                            <div className="p-3 rounded-full bg-yellow-500/20 text-yellow-600 dark:text-yellow-400">
                                <Mail className="w-6 h-6" />
                            </div>
                        </div>
                    </CardContent>
                </Card>

                <Card className="border-border/50 bg-card/50 backdrop-blur-sm">
                    <CardContent className="p-6">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm font-medium text-muted-foreground">Total Members</p>
                                <p className="text-2xl font-bold text-foreground">{totalMembers}</p>
                            </div>
                            <div className="p-3 rounded-full bg-blue-500/20 text-blue-600 dark:text-blue-400">
                                <Users className="w-6 h-6" />
                            </div>
                        </div>
                    </CardContent>
                </Card>
            </div>

            <Tabs className="w-full" defaultValue="members">
                <TabsList className="grid w-full grid-cols-2">
                    <TabsTrigger value="members">Members ({membersTotal})</TabsTrigger>
                    <TabsTrigger value="invitations">Invitations ({invitationsTotal})</TabsTrigger>
                </TabsList>

                <TabsContent value="members">
                    <MembersTab
                        handleMembersPageChange={handleMembersPageChange}
                        members={members}
                        membersPagination={membersPagination}
                        membersSearch={membersSearch}
                        membersTotal={membersTotal}
                        organizationId={id}
                        ownerCount={ownerCount}
                        setMembersSearch={setMembersSearch}
                        setShowInviteDialog={setShowInviteDialog}
                        onDataChange={handleDataChange}
                    />
                </TabsContent>

                <TabsContent value="invitations">
                    <InvitationsTab
                        handleInvitationsPageChange={handleInvitationsPageChange}
                        invitationStatusFilter={invitationStatusFilter}
                        invitations={invitations}
                        invitationsPagination={invitationsPagination}
                        invitationsSearch={invitationsSearch}
                        invitationsTotal={invitationsTotal}
                        organizationId={id}
                        setInvitationStatusFilter={setInvitationStatusFilter}
                        setInvitationsSearch={setInvitationsSearch}
                        setShowInviteDialog={setShowInviteDialog}
                        onDataChange={handleDataChange}
                    />
                </TabsContent>
            </Tabs>

            {/* Invite Member Dialog */}
            <Dialog open={showInviteDialog} onOpenChange={setShowInviteDialog}>
                <DialogContent>
                    <DialogHeader>
                        <DialogTitle>Invite Team Member</DialogTitle>
                        <DialogDescription>
                            Invite a new member to join your organization. They will receive an email invitation.
                        </DialogDescription>
                    </DialogHeader>
                    <div className="space-y-4">
                        <div>
                            <Label htmlFor="email">Email Address</Label>
                            <Input
                                id="email"
                                placeholder="Enter email address"
                                type="email"
                                value={inviteEmail}
                                onChange={(e) => setInviteEmail(e.target.value)}
                            />
                        </div>
                        <div>
                            <Label htmlFor="role">Role</Label>
                            <Select
                                options={roleOptions}
                                placeholder="Select role"
                                value={inviteRole}
                                onValueChange={(value: string) => setInviteRole(value as "MEMBER")}
                            />
                        </div>
                    </div>
                    <DialogFooter>
                        <Button variant="outline" onClick={() => setShowInviteDialog(false)}>
                            Cancel
                        </Button>
                        <Button disabled={inviteMutation.isPending} onClick={handleInvite}>
                            {inviteMutation.isPending ? (
                                <>
                                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2" />
                                    Sending...
                                </>
                            ) : (
                                <>
                                    <Mail className="w-4 h-4 mr-2" />
                                    Send Invitation
                                </>
                            )}
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        </div>
    );
}
