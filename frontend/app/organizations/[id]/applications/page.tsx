"use client";

import { useQuery } from "@tanstack/react-query";
import { AlertCircle, ArrowLeft, Calendar, CheckCircle, Clock, Eye, FileText, User, UserCheck } from "lucide-react";
import { use } from "react";
import { toast } from "sonner";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useAuth } from "@/composables/useStore";
import { getApplicationHistory, getApplicationStats } from "@/services/ApplicationService";
import { ApplicationDto, ApplicationStatsDto } from "@/types/application.dto";
import Link from "next/link";

const getStatusConfig = (status: string) => {
    switch (status) {
        case "DRAFT":
            return {
                color: "bg-yellow-100 text-yellow-800 border-yellow-200",
                icon: <FileText className="w-4 h-4" />,
                label: "Draft",
                description: "Application is being prepared",
            };
        case "IN_REVIEW":
            return {
                color: "bg-blue-100 text-blue-800 border-blue-200",
                icon: <Clock className="w-4 h-4" />,
                label: "In Review",
                description: "Application is being reviewed by authorities",
            };
        case "APPROVED":
            return {
                color: "bg-green-100 text-green-800 border-green-200",
                icon: <CheckCircle className="w-4 h-4" />,
                label: "Approved",
                description: "Application has been approved",
            };
        case "REJECTED":
            return {
                color: "bg-red-100 text-red-800 border-red-200",
                icon: <AlertCircle className="w-4 h-4" />,
                label: "Rejected",
                description: "Application has been rejected",
            };
        default:
            return {
                color: "bg-gray-100 text-gray-800 border-gray-200",
                icon: <FileText className="w-4 h-4" />,
                label: status,
                description: "Unknown status",
            };
    }
};

const getTypeConfig = (type: string) => {
    switch (type) {
        case "ORGANIZATION_REGISTRATION":
            return {
                label: "Organization Registration",
                description: "Initial organization registration",
            };
        case "LICENCE_RENEWAL":
            return {
                label: "License Renewal",
                description: "License renewal application",
            };
        case "PERMIT_APPLICATION":
            return {
                label: "Permit Application",
                description: "Permit application",
            };
        default:
            return {
                label: type.replace(/_/g, " "),
                description: "Application type",
            };
    }
};

export default function OrganizationApplications({ params }: { params: Promise<{ id: string }> }) {
    const { id } = use(params);

    if (typeof document !== "undefined") {
        document.title = "Organization Applications - myNGO";
    }

    const { isAuthenticated } = useAuth();

    const { data: applicationsResponse, isLoading: applicationsLoading } = useQuery({
        queryKey: ["organization-applications", id],
        queryFn: async () => {
            const response = await getApplicationHistory(id);

            if (response.success && response.data) {
                return response.data;
            }

            for (const error in response.errors) {
                toast.error(error);
            }
        },
        enabled: !!id && isAuthenticated,
    });

    const { data: statsResponse, isLoading: statsLoading } = useQuery({
        queryKey: ["organization-application-stats", id],
        queryFn: async () => {
            const response = await getApplicationStats(id);

            if (response.success && response.data) {
                return response.data;
            }

            for (const error in response.errors) {
                toast.error(error);
            }
        },
        enabled: !!id && isAuthenticated,
    });

    const applications: ApplicationDto[] = applicationsResponse || [];
    const stats: ApplicationStatsDto = statsResponse || {
        total: 0,
        draft: 0,
        in_review: 0,
        approved: 0,
        rejected: 0,
        suspended: 0,
    };

    const isLoading = applicationsLoading || statsLoading;

    if (!isAuthenticated) {
        return (
            <div className="flex justify-center items-center h-screen">
                <div className="grid gap-3">
                    <Button>
                        <ArrowLeft />
                        <Link href={`/organizations/${id}`}>Back to organization</Link>
                    </Button>
                </div>
            </div>
        );
    }

    if (isLoading) {
        return (
            <div className="space-y-6">
                <div className="flex items-center justify-center py-16">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary" />
                    <span className="ml-4 text-lg text-foreground">Loading applications...</span>
                </div>
            </div>
        );
    }

    return (
        <div className="space-y-6">
            {/* Header */}
            <div className="flex items-center justify-between">
                <div>
                    <h1 className="text-2xl font-bold text-foreground">Applications</h1>
                    <p className="text-muted-foreground">View and manage all organization applications</p>
                </div>
                <div className="flex gap-2">
                    <Link href="/permits/create">
                        <Button className="bg-blue-600 hover:bg-blue-700 text-white">
                            <UserCheck className="w-4 h-4 mr-2" />
                            Apply for TEP
                        </Button>
                    </Link>
                </div>
            </div>

            {/* Stats */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <Card className="border-border/50 bg-card/50 backdrop-blur-sm">
                    <CardContent className="p-6">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm font-medium text-muted-foreground">Total Applications</p>
                                <p className="text-2xl font-bold text-foreground">{stats.total}</p>
                            </div>
                            <div className="p-3 rounded-full bg-blue-500/20 text-blue-600 dark:text-blue-400">
                                <FileText className="w-6 h-6" />
                            </div>
                        </div>
                    </CardContent>
                </Card>

                <Card className="border-border/50 bg-card/50 backdrop-blur-sm">
                    <CardContent className="p-6">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm font-medium text-muted-foreground">Pending Review</p>
                                <p className="text-2xl font-bold text-foreground">{stats.in_review}</p>
                            </div>
                            <div className="p-3 rounded-full bg-yellow-500/20 text-yellow-600 dark:text-yellow-400">
                                <Clock className="w-6 h-6" />
                            </div>
                        </div>
                    </CardContent>
                </Card>

                <Card className="border-border/50 bg-card/50 backdrop-blur-sm">
                    <CardContent className="p-6">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm font-medium text-muted-foreground">Approved</p>
                                <p className="text-2xl font-bold text-foreground">{stats.approved}</p>
                            </div>
                            <div className="p-3 rounded-full bg-green-500/20 text-green-600 dark:text-green-400">
                                <CheckCircle className="w-6 h-6" />
                            </div>
                        </div>
                    </CardContent>
                </Card>

                <Card className="border-border/50 bg-card/50 backdrop-blur-sm">
                    <CardContent className="p-6">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm font-medium text-muted-foreground">Drafts</p>
                                <p className="text-2xl font-bold text-foreground">{stats.draft}</p>
                            </div>
                            <div className="p-3 rounded-full bg-gray-500/20 text-gray-600 dark:text-gray-400">
                                <FileText className="w-6 h-6" />
                            </div>
                        </div>
                    </CardContent>
                </Card>
            </div>

            {/* Applications List */}
            <Card className="border-border/50 bg-card/30 backdrop-blur-sm">
                <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                        <FileText className="w-5 h-5 text-primary" />
                        Application History
                    </CardTitle>
                </CardHeader>
                <CardContent>
                    {applications.length === 0 ? (
                        <div className="text-center py-8">
                            <FileText className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                            <p className="text-muted-foreground mb-4">No applications found</p>
                            <p className="text-sm text-muted-foreground">
                                Applications will appear here once you start the registration process
                            </p>
                        </div>
                    ) : (
                        <div className="space-y-4">
                            {applications.map((application) => {
                                const statusConfig = getStatusConfig(application.status);
                                const typeConfig = getTypeConfig(application.type);

                                return (
                                    <Card
                                        key={application.id}
                                        className="border-border/50 bg-card/20 backdrop-blur-sm hover:bg-card/30 transition-colors"
                                    >
                                        <CardContent className="p-6">
                                            <div className="flex items-start justify-between">
                                                <div className="flex-1">
                                                    <div className="flex items-center gap-3 mb-2">
                                                        <h3 className="font-semibold text-foreground text-lg">
                                                            {typeConfig.label} Application
                                                        </h3>
                                                        <Badge className={`${statusConfig.color} border`}>
                                                            {statusConfig.icon}
                                                            <span className="ml-1">{statusConfig.label}</span>
                                                        </Badge>
                                                    </div>

                                                    <div className="space-y-2 mb-4">
                                                        <p className="text-sm text-muted-foreground">
                                                            <span className="font-medium">Code:</span>{" "}
                                                            {application.code}
                                                        </p>
                                                        <p className="text-sm text-muted-foreground">
                                                            {typeConfig.description}
                                                        </p>
                                                    </div>

                                                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-muted-foreground">
                                                        <div className="flex items-center gap-2">
                                                            <Calendar className="w-4 h-4" />
                                                            <span>
                                                                Created:{" "}
                                                                {new Date(application.created_at).toLocaleDateString()}
                                                            </span>
                                                        </div>

                                                        <div className="flex items-center gap-2">
                                                            <Clock className="w-4 h-4" />
                                                            <span>
                                                                Updated:{" "}
                                                                {new Date(application.updated_at).toLocaleDateString()}
                                                            </span>
                                                        </div>

                                                        {application.creator && (
                                                            <div className="flex items-center gap-2">
                                                                <User className="w-4 h-4" />
                                                                <span>
                                                                    By: {application.creator.first_name}{" "}
                                                                    {application.creator.last_name}
                                                                </span>
                                                            </div>
                                                        )}
                                                    </div>
                                                </div>

                                                <div className="flex flex-col gap-2 ml-4">
                                                    <Button size="sm" variant="outline">
                                                        <Eye className="w-4 h-4 mr-2" />
                                                        View Details
                                                    </Button>

                                                    {application.status === "DRAFT" && (
                                                        <Button size="sm">Continue</Button>
                                                    )}
                                                </div>
                                            </div>
                                        </CardContent>
                                    </Card>
                                );
                            })}
                        </div>
                    )}
                </CardContent>
            </Card>
        </div>
    );
}
