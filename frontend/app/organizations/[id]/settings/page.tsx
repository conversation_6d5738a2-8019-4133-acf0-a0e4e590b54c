"use client";

import { useQuery } from "@tanstack/react-query";
import { AlertTriangle, Archive, ArrowLeft, Building2, Shield, Trash2 } from "lucide-react";
import { use, useState } from "react";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { useAuth } from "@/composables/useStore";
import * as organizationService from "@/services/OrganizationService";
import { OrganizationDto } from "@/types/organization.dto";
import Link from "next/link";

export default function OrganizationSettings({ params }: { params: Promise<{ id: string }> }) {
    const { isAuthenticated } = useAuth();

    const { id } = use(params);

    if (typeof document !== "undefined") {
        document.title = "Organization Settings - myNGO";
    }

    const [showDeleteDialog, setShowDeleteDialog] = useState(false);
    const [showArchiveDialog, setShowArchiveDialog] = useState(false);
    const [confirmationText, setConfirmationText] = useState("");

    const { data: organizationData, isLoading } = useQuery({
        queryKey: ["organization", id],
        queryFn: () => organizationService.getOrganization(id),
        enabled: !!id,
        select: (response) => response.data,
    });

    const organization = organizationData as OrganizationDto;

    const handleDeleteOrganization = () => {
        //!TODO: Implement delete organization logic
        setShowDeleteDialog(false);
        setConfirmationText("");
    };

    const handleArchiveOrganization = () => {
        //!TODO: Implement archive organization logic

        setShowArchiveDialog(false);
        setConfirmationText("");
    };

    if (!isAuthenticated) {
        return (
            <div className="flex justify-center items-center h-screen">
                <div className="grid gap-3">
                    <Button>
                        <ArrowLeft />
                        <Link href={`/organizations/${id}`}>Back to organization</Link>
                    </Button>
                </div>
            </div>
        );
    }

    if (isLoading) {
        return (
            <div className="space-y-6">
                <div className="flex items-center justify-center py-16">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary" />
                    <span className="ml-4 text-lg text-foreground">Loading settings...</span>
                </div>
            </div>
        );
    }

    if (!organization) {
        return (
            <div className="space-y-6">
                <div className="text-center py-16">
                    <Building2 className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
                    <h3 className="text-xl font-semibold text-foreground mb-2">Organization not found</h3>
                    <p className="text-muted-foreground">Unable to load organization settings</p>
                </div>
            </div>
        );
    }

    const isOwner = true; //!TODO Replace with actual ownership check
    const canDelete = organization.status === "DRAFT" || organization.status === "INACTIVE";
    const canArchive = organization.status === "REGISTERED";

    return (
        <div className="space-y-6">
            {/* Header */}
            <div>
                <h1 className="text-2xl font-bold text-foreground">Organization Settings</h1>
                <p className="text-muted-foreground">Manage your organization settings and preferences</p>
            </div>

            {/* General Settings */}
            <Card className="border-border/50 bg-card/30 backdrop-blur-sm">
                <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                        <Building2 className="w-5 h-5 text-primary" />
                        General Information
                    </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <Label>Organization Name</Label>
                            <p className="text-sm text-muted-foreground mt-1">{organization.name}</p>
                        </div>

                        <div>
                            <Label>Registration Number</Label>
                            <p className="text-sm text-muted-foreground mt-1">{organization.registration_number}</p>
                        </div>

                        <div>
                            <Label>Status</Label>
                            <p className="text-sm text-muted-foreground mt-1">{organization.status}</p>
                        </div>

                        <div>
                            <Label>Created Date</Label>
                            <p className="text-sm text-muted-foreground mt-1">
                                {new Date(organization.created_at).toLocaleDateString()}
                            </p>
                        </div>
                    </div>
                </CardContent>
            </Card>

            {/* Permissions & Access */}
            <Card className="border-border/50 bg-card/30 backdrop-blur-sm">
                <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                        <Shield className="w-5 h-5 text-primary" />
                        Permissions & Access
                    </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                    <div>
                        <h4 className="font-medium text-foreground mb-2">Your Role</h4>
                        <p className="text-sm text-muted-foreground">
                            {isOwner ? "Owner - Full access to all organization settings" : "Member - Limited access"}
                        </p>
                    </div>

                    <Separator />

                    <div>
                        <h4 className="font-medium text-foreground mb-2">Access Permissions</h4>
                        <div className="space-y-2 text-sm text-muted-foreground">
                            <div className="flex items-center justify-between">
                                <span>View organization details</span>
                                <span className="text-green-600">✓ Allowed</span>
                            </div>
                            <div className="flex items-center justify-between">
                                <span>Edit organization information</span>
                                <span className={isOwner ? "text-green-600" : "text-red-600"}>
                                    {isOwner ? "✓ Allowed" : "✗ Restricted"}
                                </span>
                            </div>
                            <div className="flex items-center justify-between">
                                <span>Invite members</span>
                                <span className={isOwner ? "text-green-600" : "text-red-600"}>
                                    {isOwner ? "✓ Allowed" : "✗ Restricted"}
                                </span>
                            </div>
                            <div className="flex items-center justify-between">
                                <span>Manage applications</span>
                                <span className={isOwner ? "text-green-600" : "text-red-600"}>
                                    {isOwner ? "✓ Allowed" : "✗ Restricted"}
                                </span>
                            </div>
                        </div>
                    </div>
                </CardContent>
            </Card>

            {/* Danger Zone */}
            {isOwner && (
                <Card className="border-red-200 bg-red-50/50 dark:border-red-800 dark:bg-red-900/10">
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2 text-red-700 dark:text-red-400">
                            <AlertTriangle className="w-5 h-5" />
                            Danger Zone
                        </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                        <div className="space-y-4">
                            {canArchive && (
                                <div className="flex items-center justify-between p-4 border border-yellow-200 rounded-lg bg-yellow-50/50 dark:border-yellow-800 dark:bg-yellow-900/10">
                                    <div>
                                        <h4 className="font-medium text-foreground mb-1">Archive Organization</h4>
                                        <p className="text-sm text-muted-foreground">
                                            Archive this organization. It will be hidden but can be restored later.
                                        </p>
                                    </div>
                                    <Button
                                        className="border-yellow-300 text-yellow-700 hover:bg-yellow-50 dark:border-yellow-700 dark:text-yellow-400"
                                        variant="outline"
                                        onClick={() => setShowArchiveDialog(true)}
                                    >
                                        <Archive className="w-4 h-4 mr-2" />
                                        Archive
                                    </Button>
                                </div>
                            )}

                            {canDelete && (
                                <div className="flex items-center justify-between p-4 border border-red-200 rounded-lg bg-red-50/50 dark:border-red-800 dark:bg-red-900/10">
                                    <div>
                                        <h4 className="font-medium text-foreground mb-1">Delete Organization</h4>
                                        <p className="text-sm text-muted-foreground">
                                            Permanently delete this organization. This action cannot be undone.
                                        </p>
                                    </div>
                                    <Button variant="destructive" onClick={() => setShowDeleteDialog(true)}>
                                        <Trash2 className="w-4 h-4 mr-2" />
                                        Delete
                                    </Button>
                                </div>
                            )}

                            {!canDelete && !canArchive && (
                                <div className="p-4 border border-gray-200 rounded-lg bg-gray-50/50 dark:border-gray-700 dark:bg-gray-800/10">
                                    <h4 className="font-medium text-foreground mb-1">Organization Management</h4>
                                    <p className="text-sm text-muted-foreground">
                                        Active organizations cannot be deleted or archived. Contact support for
                                        assistance.
                                    </p>
                                </div>
                            )}
                        </div>
                    </CardContent>
                </Card>
            )}

            {/* Archive Confirmation Dialog */}
            <Dialog open={showArchiveDialog} onOpenChange={setShowArchiveDialog}>
                <DialogContent>
                    <DialogHeader>
                        <DialogTitle className="flex items-center gap-2 text-yellow-700 dark:text-yellow-400">
                            <Archive className="w-5 h-5" />
                            Archive Organization
                        </DialogTitle>
                        <DialogDescription>
                            Are you sure you want to archive this organization? This will hide it from public view but
                            it can be restored later.
                        </DialogDescription>
                    </DialogHeader>
                    <div>
                        <Label htmlFor="archive-confirm">
                            Type <strong>{organization.name}</strong> to confirm:
                        </Label>
                        <Input
                            className="mt-2"
                            id="archive-confirm"
                            placeholder={organization.name}
                            value={confirmationText}
                            onChange={(e) => setConfirmationText(e.target.value)}
                        />
                    </div>
                    <DialogFooter>
                        <Button
                            variant="outline"
                            onClick={() => {
                                setShowArchiveDialog(false);
                                setConfirmationText("");
                            }}
                        >
                            Cancel
                        </Button>
                        <Button
                            className="border-yellow-300 text-yellow-700 hover:bg-yellow-50 dark:border-yellow-700 dark:text-yellow-400"
                            disabled={confirmationText !== organization.name}
                            variant="outline"
                            onClick={handleArchiveOrganization}
                        >
                            <Archive className="w-4 h-4 mr-2" />
                            Archive Organization
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>

            {/* Delete Confirmation Dialog */}
            <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
                <DialogContent>
                    <DialogHeader>
                        <DialogTitle className="flex items-center gap-2 text-red-600">
                            <Trash2 className="w-5 h-5" />
                            Delete Organization
                        </DialogTitle>
                        <DialogDescription>
                            This action cannot be undone. This will permanently delete the organization and all
                            associated data.
                        </DialogDescription>
                    </DialogHeader>
                    <div>
                        <Label htmlFor="delete-confirm">
                            Type <strong>{organization.name}</strong> to confirm:
                        </Label>
                        <Input
                            className="mt-2"
                            id="delete-confirm"
                            placeholder={organization.name}
                            value={confirmationText}
                            onChange={(e) => setConfirmationText(e.target.value)}
                        />
                    </div>
                    <DialogFooter>
                        <Button
                            variant="outline"
                            onClick={() => {
                                setShowDeleteDialog(false);
                                setConfirmationText("");
                            }}
                        >
                            Cancel
                        </Button>
                        <Button
                            disabled={confirmationText !== organization.name}
                            variant="destructive"
                            onClick={handleDeleteOrganization}
                        >
                            <Trash2 className="w-4 h-4 mr-2" />
                            Delete Organization
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        </div>
    );
}
