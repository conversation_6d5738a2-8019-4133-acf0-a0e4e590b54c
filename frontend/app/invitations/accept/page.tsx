"use client";
import { useRouter, useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";
import { toast } from "sonner";

import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { RadioGroup } from "@/components/ui/radio-group";
import { useAuth } from "@/composables/useStore";
import { useInvitations } from "@/hooks/useInvitations";
import { Gender, ValidateInvitationTokenResponse } from "@/types";

const AcceptInvitationPage = () => {
    if (typeof document !== "undefined") {
        document.title = "Accept Invitation - myNGO";
    }

    const router = useRouter();
    const searchParams = useSearchParams();
    const { session } = useAuth();
    const { isValidating, isAccepting, validateToken, acceptWithAccount } = useInvitations();
    const token = searchParams.get("token");

    const [invitationData, setInvitationData] = useState<ValidateInvitationTokenResponse | null>(null);

    const [firstName, setFirstName] = useState("");
    const [lastName, setLastName] = useState("");
    const [password, setPassword] = useState("");
    const [confirmPassword, setConfirmPassword] = useState("");
    const [gender, setGender] = useState<"MALE" | "FEMALE" | "OTHER">("MALE");
    const [formErrors, setFormErrors] = useState({
        firstName: "",
        lastName: "",
        password: "",
        confirmPassword: "",
    });

    useEffect(() => {
        if (!token) {
            toast.error("Invalid invitation link");
            router.push("/auth/login");

            return;
        }

        validateInvitationToken();
    }, [token, validateToken]);

    const validateInvitationToken = async () => {
        const result = await validateToken(token!);

        if (result) {
            setInvitationData(result);
        } else {
            router.push("/auth/login");
        }
    };

    const validateForm = () => {
        const errors = {
            firstName: "",
            lastName: "",
            password: "",
            confirmPassword: "",
        };
        let isValid = true;

        if (!firstName.trim()) {
            errors.firstName = "First name is required";
            isValid = false;
        }

        if (!lastName.trim()) {
            errors.lastName = "Last name is required";
            isValid = false;
        }

        if (!password) {
            errors.password = "Password is required";
            isValid = false;
        } else if (password.length < 8) {
            errors.password = "Password must be at least 8 characters";
            isValid = false;
        }

        if (password !== confirmPassword) {
            errors.confirmPassword = "Passwords do not match";
            isValid = false;
        }

        setFormErrors(errors);

        return isValid;
    };

    const handleAcceptWithAccount = async (e: React.FormEvent) => {
        e.preventDefault();

        if (!validateForm()) {
            return;
        }

        const result = await acceptWithAccount(token!, {
            first_name: firstName,
            last_name: lastName,
            gender,
            password,
        });

        if (result.success) {
            router.push(
                `/invitations/success?org=${encodeURIComponent(invitationData?.organization_name || "")}&role=${invitationData?.role || ""}`,
            );
        }
    };

    const handleLoginAndAccept = () => {
        // Redirect to login with return URL to come back and accept invitation
        router.push(`/auth/login?redirect=/invitations/accept?token=${token}`);
    };

    if (isValidating) {
        return (
            <div className="flex min-h-screen items-center justify-center">
                <Card className="w-full max-w-md p-6">
                    <div className="text-center">
                        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4" />
                        <p>Validating invitation...</p>
                    </div>
                </Card>
            </div>
        );
    }

    if (!invitationData?.valid) {
        return (
            <div className="flex min-h-screen items-center justify-center">
                <Card className="w-full max-w-md p-6">
                    <div className="text-center">
                        <h2 className="text-2xl font-bold text-red-600 mb-4">Invalid Invitation</h2>
                        <p className="text-muted-foreground mb-6">This invitation link is invalid or has expired.</p>
                        <Button onClick={() => router.push("/auth/login")}>Go to Login</Button>
                    </div>
                </Card>
            </div>
        );
    }

    return (
        <div className="flex min-h-screen items-center justify-center p-6">
            <div className="w-full max-w-md">
                <Card className="p-6">
                    <div className="text-center mb-6">
                        <h1 className="text-2xl font-bold text-foreground">Organization Invitation</h1>
                        <p className="text-muted-foreground mt-2">
                            You&apos;ve been invited to join <strong>{invitationData.organization_name}</strong>
                        </p>
                    </div>

                    {/* Invitation Details */}
                    <div className="rounded-lg p-4 mb-6 bg-muted-foreground/10">
                        <div className="space-y-2 text-sm">
                            <div className="flex justify-between">
                                <span className="text-muted-foreground">Organization:</span>
                                <span className="font-medium">{invitationData.organization_name}</span>
                            </div>
                            <div className="flex justify-between">
                                <span className="text-muted-foreground">Role:</span>
                                <span className="font-medium">{invitationData.role}</span>
                            </div>
                            <div className="flex justify-between">
                                <span className="text-muted-foreground">Invited by:</span>
                                <span className="font-medium">{invitationData.inviter_name}</span>
                            </div>
                            <div className="flex justify-between">
                                <span className="text-muted-foreground">Expires:</span>
                                <span className="font-medium">{invitationData.expires_at}</span>
                            </div>
                        </div>
                    </div>

                    {/* If user exists, show login option */}
                    {invitationData.user_exists && !session && (
                        <div className="text-center">
                            <p className="text-muted-foreground mb-4">
                                An account with this email already exists. Please log in to accept the invitation.
                            </p>
                            <Button className="w-full" onClick={handleLoginAndAccept}>
                                Login to Accept Invitation
                            </Button>
                        </div>
                    )}

                    {/* If user is logged in and exists */}
                    {session && invitationData.user_exists && (
                        <div className="text-center">
                            <p className="text-muted-foreground mb-4">
                                Click below to accept this invitation and join the organization.
                            </p>
                            <Button
                                className="w-full"
                                loading={isAccepting}
                                onClick={() => {
                                    // TODO: Implement direct acceptance for existing logged-in users
                                    toast.info("Feature coming soon - please contact support");
                                }}
                            >
                                Accept Invitation
                            </Button>
                        </div>
                    )}

                    {/* If user doesn't exist, show account creation form */}
                    {!invitationData.user_exists && (
                        <form className="space-y-4" onSubmit={handleAcceptWithAccount}>
                            <div className="text-center mb-4">
                                <p className="text-muted-foreground">Create your account to accept the invitation</p>
                            </div>

                            <div className="flex gap-3">
                                <Input
                                    required
                                    className="p-3"
                                    error={formErrors.firstName}
                                    id="firstName"
                                    label="First Name"
                                    name="firstName"
                                    placeholder="First Name"
                                    type="text"
                                    value={firstName}
                                    onChange={(e) => setFirstName(e.target.value)}
                                />

                                <Input
                                    required
                                    className="p-3"
                                    error={formErrors.lastName}
                                    id="lastName"
                                    label="Last Name"
                                    name="lastName"
                                    placeholder="Last Name"
                                    type="text"
                                    value={lastName}
                                    onChange={(e) => setLastName(e.target.value)}
                                />
                            </div>

                            <div>
                                <RadioGroup
                                    label="Gender"
                                    name="gender"
                                    options={[
                                        { value: "MALE", label: "Male" },
                                        { value: "FEMALE", label: "Female" },
                                    ]}
                                    value={gender}
                                    onValueChange={(value) => setGender(value as Gender)}
                                />
                            </div>

                            <Input
                                required
                                className="p-3"
                                error={formErrors.password}
                                id="password"
                                label="Password"
                                name="password"
                                placeholder="Create a password"
                                type="password"
                                value={password}
                                onChange={(e) => setPassword(e.target.value)}
                            />

                            <Input
                                required
                                className="p-3"
                                error={formErrors.confirmPassword}
                                id="confirmPassword"
                                label="Confirm Password"
                                name="confirmPassword"
                                placeholder="Confirm your password"
                                type="password"
                                value={confirmPassword}
                                onChange={(e) => setConfirmPassword(e.target.value)}
                            />

                            <Button
                                className="w-full"
                                loading={isAccepting}
                                loadingText="Creating account..."
                                type="submit"
                            >
                                Create Account & Accept Invitation
                            </Button>
                        </form>
                    )}
                </Card>

                <div className="text-center mt-6">
                    <Button variant="ghost" onClick={() => router.push("/auth/login")}>
                        Go to Login
                    </Button>
                </div>
            </div>
        </div>
    );
};

export default AcceptInvitationPage;
