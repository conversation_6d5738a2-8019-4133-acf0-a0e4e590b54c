"use client";
import { CheckCircle } from "lucide-react";
import { useRouter, useSearchParams } from "next/navigation";

import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";

const InvitationSuccessPage = () => {
    if (typeof document !== "undefined") {
        document.title = "Invitation Success - myNGO";
    }

    const router = useRouter();
    const searchParams = useSearchParams();

    const organizationName = searchParams.get("org") || "the organization";
    const role = searchParams.get("role") || "member";

    return (
        <div className="flex min-h-screen items-center justify-center p-6">
            <Card className="w-full max-w-md p-8 text-center">
                <div className="flex justify-center mb-6">
                    <CheckCircle className="h-16 w-16 text-green-500" />
                </div>

                <h1 className="text-2xl font-bold mb-4">Welcome to {organizationName}!</h1>

                <p className="text-foreground/50 mb-6">
                    Your invitation has been accepted successfully. You are now a {role.toLowerCase()} of{" "}
                    {organizationName}.
                </p>

                <div className="space-y-3">
                    <Button className="w-full" onClick={() => router.push("/auth/login")}>
                        Login to Your Account
                    </Button>

                    <Button
                        className="w-full !bg-foreground text-background hover:shadow-lg"
                        onClick={() => router.push("/")}
                    >
                        Go to Homepage
                    </Button>
                </div>

                <div className="mt-6 pt-6 border-t">
                    <p className="text-sm text-foreground/50">
                        You can now access your organization&apos;s dashboard and collaborate with your team members.
                    </p>
                </div>
            </Card>
        </div>
    );
};

export default InvitationSuccessPage;
