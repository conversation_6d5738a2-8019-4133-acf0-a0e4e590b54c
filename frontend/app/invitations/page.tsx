"use client";
import { Mail, Shield, Users } from "lucide-react";
import { useRouter } from "next/navigation";

import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";

const InvitationsPage = () => {
    if (typeof document !== "undefined") {
        document.title = "Invitations - myNGO";
    }
    
    const router = useRouter();

    return (
        <div className="flex min-h-screen items-center justify-center p-6">
            <div className="w-full max-w-2xl">
                <div className="text-center mb-8">
                    <h1 className="text-3xl font-bold text-gray-900 mb-2">Organization Invitations</h1>
                    <p className="text-gray-600">Join organizations and collaborate with NGOs in Malawi</p>
                </div>

                <div className="grid md:grid-cols-2 gap-6 mb-8">
                    <Card className="p-6">
                        <div className="flex items-center mb-4">
                            <Mail className="h-6 w-6 text-blue-500 mr-2" />
                            <h3 className="font-semibold">Email Invitations</h3>
                        </div>
                        <p className="text-gray-600 text-sm mb-4">
                            If you received an invitation via email, click the link in your email to accept it directly.
                        </p>
                    </Card>

                    <Card className="p-6">
                        <div className="flex items-center mb-4">
                            <Users className="h-6 w-6 text-green-500 mr-2" />
                            <h3 className="font-semibold">Join Organizations</h3>
                        </div>
                        <p className="text-gray-600 text-sm mb-4">
                            Become a member of NGOs and contribute to meaningful work in your community.
                        </p>
                    </Card>
                </div>

                <Card className="p-6 bg-blue-50 border-blue-200">
                    <div className="flex items-start">
                        <Shield className="h-6 w-6 text-blue-500 mr-3 mt-0.5 flex-shrink-0" />
                        <div>
                            <h3 className="font-semibold text-blue-900 mb-2">Secure Invitation Process</h3>
                            <p className="text-blue-800 text-sm">
                                All invitations are secured with encrypted tokens and have expiration dates. Your data
                                is protected throughout the invitation process.
                            </p>
                        </div>
                    </div>
                </Card>

                <div className="text-center mt-8 space-y-3">
                    <div>
                        <Button onClick={() => router.push("/auth/login")}>Login to Your Account</Button>
                    </div>
                    <div>
                        <Button variant="outline" onClick={() => router.push("/auth/register")}>
                            Create New Account
                        </Button>
                    </div>
                </div>

                <div className="text-center mt-6">
                    <p className="text-sm text-gray-500">Need help? Contact support or visit our help center.</p>
                </div>
            </div>
        </div>
    );
};

export default InvitationsPage;
