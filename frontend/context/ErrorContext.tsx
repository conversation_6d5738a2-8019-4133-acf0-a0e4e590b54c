"use client";

import { createContext, useContext, useState } from "react";

import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";

type ErrorContextType = {
    showError: (message: string, retryCallback?: () => void) => void;
    hideError: () => void;
    errorMessage: string | null;
    onRetry: () => void;
};

const ErrorContext = createContext<ErrorContextType | undefined>(undefined);

export function ErrorProvider({ children }: { children: React.ReactNode }) {
    const [errorMessage, setErrorMessage] = useState<string | null>(null);
    const [onRetry, setOnRetry] = useState<() => void>(() => () => window.location.reload());

    const showError = (message: string, retryCallback?: () => void) => {
        setErrorMessage(message);
        if (retryCallback) setOnRetry(() => retryCallback);
    };
    const hideError = () => setErrorMessage(null);

    return (
        <ErrorContext.Provider value={{ showError, hideError, errorMessage, onRetry }}>
            <ErrorDialog />
            {children}
        </ErrorContext.Provider>
    );
}

export function useError() {
    const context = useContext(ErrorContext);

    if (!context) throw new Error("useError must be used within ErrorProvider");

    return context;
}

export function ErrorDialog() {
    const { errorMessage, onRetry, hideError } = useError();

    if (!errorMessage) return null;

    return (
        <Dialog open={!!errorMessage}>
            <DialogContent className="md:max-w-2xl animate-in fade-in duration-500">
                <DialogHeader>
                    <div className="flex flex-col items-center">
                        <svg
                            className="w-10 h-10 text-red-300 mb-4"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                        >
                            <circle cx="12" cy="12" r="10" strokeWidth="2" />
                            <path d="M12 8v4m0 4h.01" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" />
                        </svg>
                        <DialogTitle>Something went wrong</DialogTitle>
                    </div>
                </DialogHeader>
                <DialogDescription className="mb-4 text-center">
                    {errorMessage || "An unexpected error occurred. Please try again."}
                </DialogDescription>
                <div className="mb-4 text-xs text-center text-muted-foreground">
                    If the issue persists, contact support at{" "}
                    <a className="underline" href="mailto:<EMAIL>">
                        <EMAIL>
                    </a>
                </div>
                <DialogFooter>
                    <div className="grid gap-4 grid-cols-2 w-full">
                        <Button className="w-full" variant="default" onClick={onRetry}>
                            Retry
                        </Button>
                        <Button className="w-full" variant="ghost" onClick={hideError}>
                            Close
                        </Button>
                    </div>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    );
}
