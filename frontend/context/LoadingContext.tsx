"use client";

import { LucideHourglass } from "lucide-react";
import React, { useState } from "react";
import { createContext, useContext } from "react";

import { Dialog, DialogContent, DialogDescription, DialogTitle } from "@/components/ui/dialog";

type LoadingContextType = {
    loadingComponent: React.ReactNode;
    loading: boolean;
    message: string;
    showLoading: (msg?: string, loadingComponent?: React.ReactNode) => void;
    hideLoading: () => void;
};

const LoadingContext = createContext<LoadingContextType | undefined>(undefined);

export function LoadingProvider({ children }: { children: React.ReactNode }) {
    const [loading, setLoading] = useState(false);
    const [message, setMessage] = useState("Loading...");
    const [loadingComponent, setLoadingComponent] = useState<React.ReactNode | null>(null);

    const showLoading = (msg?: string, loadingComponent?: React.ReactNode) => {
        if (msg) setMessage(msg);
        if (loadingComponent) setLoadingComponent(loadingComponent);
        setLoading(true);
    };

    const hideLoading = () => {
        setLoading(false);
    };

    return (
        <LoadingContext.Provider value={{ loadingComponent, loading, message, showLoading, hideLoading }}>
            <LoadingDialog />
            {children}
        </LoadingContext.Provider>
    );
}

export function useLoading() {
    const context = useContext(LoadingContext);

    if (!context) {
        throw new Error("useLoading must be used within a LoadingProvider");
    }

    return context;
}

export function LoadingDialog() {
    const { loading, message, loadingComponent } = useLoading();

    if (!loading) return null;

    return (
        // TODO: Improve the loading animation
        <Dialog open={loading}>
            <DialogContent>
                <DialogTitle>Please wait ...</DialogTitle>
                <DialogDescription className="mb-4 text-center flex items-center justify-center py-6">
                    {loadingComponent ? (
                        loadingComponent
                    ) : (
                        <>
                            <LucideHourglass className="h-6 w-6 animate-spin" />
                            <span className="ml-2 text-sm text-muted-foreground">{message}</span>
                        </>
                    )}
                </DialogDescription>
            </DialogContent>
        </Dialog>
    );
}
