import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Trash2 } from "lucide-react";
import { createContext, useContext, useEffect, useState } from "react";
import { Label } from "recharts";

import { Alert, AlertDescription } from "@/components/ui/alert";
import { But<PERSON> } from "@/components/ui/button";
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";

type DeleteConfimationProps = {
    onConfirm: (voidReason: string) => void;
    itemName: string;
    title: string;
    description: string;
};

type DeleteConfirmationContextType = {
    openDeleteDialog: (config: DeleteConfimationProps) => void;
    closeDeleteDialog: () => void;
    error: string | null;
};

type DeleteConfirmationDialogProps = DeleteConfimationProps & {
    onOpenChange: (open: boolean) => void;
    open: boolean;
    loading?: boolean;
    variant?: "default" | "destructive";
};

const DeleteConfirmationContext = createContext<DeleteConfirmationContextType | undefined>(undefined);

export function DeleteConfirmationProvider({ children }: { children: React.ReactNode }) {
    const [isOpen, setIsOpen] = useState(false);
    const [deleteConfig, setDeleteConfig] = useState<DeleteConfimationProps | null>(null);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);

    const openDeleteDialog = (config: DeleteConfimationProps) => {
        setDeleteConfig(config);
        setIsOpen(true);
    };

    const closeDeleteDialog = () => {
        setIsOpen(false);
        setDeleteConfig(null);
    };

    const handleConfirm = async (reasonValue: string) => {
        try {
            setLoading(true);
            await deleteConfig?.onConfirm(reasonValue);
            closeDeleteDialog();
        } catch (e: any) {
            setError(e?.message!);
        } finally {
            setLoading(false);
        }
    };

    const contextValue = {
        openDeleteDialog,
        closeDeleteDialog,
        error,
    };

    return (
        <DeleteConfirmationContext.Provider value={contextValue}>
            <DeleteConfirmationDialog
                description={deleteConfig?.description!}
                itemName={deleteConfig?.itemName!}
                loading={loading}
                open={isOpen}
                title={deleteConfig?.title!}
                onConfirm={handleConfirm}
                onOpenChange={setIsOpen}
            />
            {children}
        </DeleteConfirmationContext.Provider>
    );
}

export const useDeleteConfirmation = () => {
    const context = useContext(DeleteConfirmationContext);

    if (!context) {
        throw new Error("useDeleteConfirmation must be used within a DeleteConfirmationProvider");
    }

    return context;
};

export function DeleteConfirmationDialog({
    open,
    onOpenChange,
    onConfirm,
    title = "Confirm Deletion",
    description,
    itemName,
    loading = false,
    variant = "destructive",
}: DeleteConfirmationDialogProps) {
    const [inputValue, setInputValue] = useState("");
    const [isValid, setIsValid] = useState(false);

    const [reasonValue, setReasonValue] = useState("");

    // Reset input when dialog opens/closes
    useEffect(() => {
        if (!inputValue || !itemName) return;

        setIsValid(inputValue.trim() === itemName.trim() && reasonValue.trim().length > 10);
    }, [inputValue, itemName, reasonValue]);

    useEffect(() => {
        if (!open) {
            setInputValue("");
            setReasonValue("");
            setIsValid(false);
        }
    }, [open]);

    const handleConfirm = async () => {
        if (!isValid || loading) return;
        await onConfirm(reasonValue);
        onOpenChange(false);
    };

    const handleKeyDown = (e: React.KeyboardEvent) => {
        if (e.key === "Enter" && isValid && !loading) {
            handleConfirm();
        }
    };

    const defaultDescription = itemName
        ? `This action cannot be undone. This will permanently delete "${itemName}" and all associated data.`
        : "This action cannot be undone. This will permanently delete the selected item and all associated data.";

    return (
        <Dialog open={open} onOpenChange={onOpenChange}>
            <DialogContent className="sm:max-w-md">
                <DialogHeader>
                    <div className="flex items-center gap-3">
                        <div
                            className={`p-2 rounded-full ${
                                variant === "destructive" ? "bg-red-100 text-red-600" : "bg-orange-100 text-orange-600"
                            }`}
                        >
                            {variant === "destructive" ? (
                                <Trash2 className="h-5 w-5" />
                            ) : (
                                <AlertTriangle className="h-5 w-5" />
                            )}
                        </div>
                        <DialogTitle className="text-left">{title}</DialogTitle>
                    </div>
                    <DialogDescription className="text-left pt-2">
                        {description || defaultDescription}
                    </DialogDescription>
                </DialogHeader>

                <div className="space-y-4">
                    <Alert variant={variant === "destructive" ? "destructive" : "default"}>
                        <AlertTriangle className="h-4 w-4" />
                        <AlertDescription>
                            <strong>Warning:</strong> This action is irreversible. Please proceed with caution.
                        </AlertDescription>
                    </Alert>

                    <div className="space-y-2">
                        <Label>
                            To confirm, type{" "}
                            <code className="px-1.5 py-0.5 rounded text-sm font-mono" style={{ color: "darkred" }}>
                                {itemName}
                            </code>{" "}
                            below:
                        </Label>
                        <Textarea
                            required
                            autoComplete="off"
                            className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            disabled={loading}
                            id="reason-textarea"
                            placeholder={`Reason for deleting`}
                            value={reasonValue}
                            onChange={(e) => setReasonValue(e.target.value)}
                            onKeyDown={handleKeyDown}
                        />
                        {!isValid && reasonValue.trim().length > 0 && reasonValue.trim().length < 10 && (
                            <p className="text-sm text-red-600">At least 10 characters are required for the reason.</p>
                        )}
                        &nbsp;
                        <Input
                            autoComplete="off"
                            className={`${
                                inputValue && !isValid ? "border-red-300 focus:border-red-500 focus:ring-red-500" : ""
                            } ${isValid ? "border-green-300 focus:border-green-500 focus:ring-green-500" : ""}`}
                            disabled={loading}
                            id="confirmation-input"
                            placeholder={`Type "${itemName}" to confirm`}
                            spellCheck={false}
                            value={inputValue}
                            onChange={(e) => setInputValue(e.target.value)}
                            onKeyDown={handleKeyDown}
                        />
                        {inputValue && !isValid && inputValue.trim() !== itemName.trim() && (
                            <p className="text-sm text-red-600">
                                Text doesn&apos;t match. Please type exactly: {itemName}
                            </p>
                        )}
                    </div>
                </div>

                <DialogFooter className="flex-col sm:flex-row gap-2">
                    <Button
                        className="w-full sm:w-auto"
                        disabled={loading}
                        variant="outline"
                        onClick={() => onOpenChange(false)}
                    >
                        Cancel
                    </Button>
                    <Button
                        className="w-full sm:w-auto"
                        disabled={!isValid || loading}
                        variant={variant}
                        onClick={handleConfirm}
                    >
                        {loading ? (
                            <>
                                <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
                                Deleting...
                            </>
                        ) : (
                            <>
                                <Trash2 className="mr-2 h-4 w-4" />
                                Delete {itemName ? `"${itemName}"` : "Item"}
                            </>
                        )}
                    </Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    );
}
