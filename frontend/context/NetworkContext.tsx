"use client";

import React, { createContext, useContext, useEffect, useRef, useState } from "react";
import { toast } from "sonner";

type NetworkContextType = {
    online: boolean;
};

const NetworkContext = createContext<NetworkContextType | undefined>(undefined);

// Read env on client via NEXT_PUBLIC_*
const HEALTH_URL =
    (process.env.NEXT_PUBLIC_HEALTH_CHECK_URL as string) || `${process.env.NEXT_PUBLIC_API_BASE_URL}/health`;
const INTERVAL_MS = Number(process.env.NEXT_PUBLIC_HEALTH_CHECK_INTERVAL) || 15000;
const TIMEOUT_MS = Number(process.env.NEXT_PUBLIC_HEALTH_CHECK_TIMEOUT) || 5000;

export function NetworkProvider({ children }: { children: React.ReactNode }) {
    const [online, setOnline] = useState(true);

    const mounted = useRef(true);
    const prevOnline = useRef<boolean | null>(null);
    const checkingRef = useRef(false);

    useEffect(() => {
        mounted.current = true;

        return () => {
            mounted.current = false;
        };
    }, []);

    const networkErrorToast = () => {
        // check if toast is not already active
        if (toast.getToasts().some((t) => t.id === "network-error")) return;
        toast.warning("Network connection lost", {
            id: "network-error",
            position: "bottom-center",
            duration: Infinity,
            description: "Please check your internet connection and try again.",
            dismissible: false,
        });
    };

    const check = async () => {
        // avoid overlapping checks
        if (checkingRef.current) return;
        checkingRef.current = true;
        const controller = new AbortController();
        const id = setTimeout(() => controller.abort(), TIMEOUT_MS);
        let ok = false;

        try {
            const res = await fetch(HEALTH_URL, { method: "GET", cache: "no-store", signal: controller.signal });

            ok = !!(res && res.ok);
            if (ok) {
                // If we were previously offline, mark restored and keep a flag so the UI can offer a reload action
                if (prevOnline.current === false && mounted.current) {
                    toast.getToasts().forEach((t) => {
                        if (t.id === "network-error") toast.dismiss(t.id);
                    });
                    toast.success("Network restored", { position: "bottom-center" });
                }
                setOnline(true);
            } else {
                networkErrorToast();
                setOnline(false);
            }
        } catch (err) {
            ok = false;
            setOnline(false);
            networkErrorToast();
        } finally {
            // ensure timeout is cleared and checking flag reset
            clearTimeout(id);
            checkingRef.current = false;
            if (mounted.current) {
                // update prevOnline to the freshest value
                prevOnline.current = ok ?? false;
            }
        }
    };

    useEffect(() => {
        // initial check
        check();
        const handle = setInterval(check, INTERVAL_MS);

        return () => clearInterval(handle);
    }, []);

    return <NetworkContext.Provider value={{ online }}>{children}</NetworkContext.Provider>;
}

export function useNetwork() {
    const ctx = useContext(NetworkContext);

    if (!ctx) throw new Error("useNetwork must be used within NetworkProvider");

    return ctx;
}

export default NetworkProvider;
