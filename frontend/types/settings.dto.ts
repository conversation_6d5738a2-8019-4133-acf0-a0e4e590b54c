import { Pagination } from "@/types/common.type";

export interface CurrencyDto {
    id: string;
    name: string;
    code: string;
    exchange_rate: number;
    is_default: boolean;
    status: string;
    created_at: Date;
    updated_at: Date;
}

export type LoadableItemType =
    | "QUALIFICATION"
    | "ORGANIZATION_TYPE"
    | "COMPLAINT_CATEGORY"
    | "ACTIVITY_CATEGORY"
    | "OLD_PERMISSION"
    | "THEMATIC_AREA"
    | "TARGET_GROUP"
    | "PERMISSION"
    | "DOCUMENT_TYPE"
    | "PILLAR"
    | "STAFF_TYPE"
    | "SECTOR"
    | "DONOR"
    | "BANK"
    | "PAYMENT_MODE"
    | "FEE_CATEGORY"
    | "FOCUS_AREA"
    | "REGISTRATION_TYPE"
    | "ZONE";

export interface LoadableItemDto {
    id: string;
    type: LoadableItemType;
    code: string;
    display_value: string;
    description?: string;
    created_at: Date;
    updated_at: Date;
}

export interface LoadableItemFilter extends Pagination {
    type?: LoadableItemType;
    code?: string;
    display_value?: string;
}

export interface CountryFilter extends Pagination {
    name?: string;
}

export interface CountryDto {
    id: string;
    name: string;
    dial_code: string;
    short_code: string;
    flag: string;
}

export interface DistrictDto {
    id: string;
    name: string;
    code: string;
    region_id: string;
    created_at: string;
    updated_at: string;
    region_name: string;
}

export interface RegionDto {
    id: string;
    name: string;
    code: string;
    created_at: string;
    updated_at: string;
}

export interface DistrictFilter extends Pagination {
    name?: string;
    region_id?: string;
}

export interface RegionFilter extends Pagination {
    name?: string;
}
