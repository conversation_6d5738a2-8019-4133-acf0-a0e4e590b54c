import { ContactDto } from "./account.dto";
import { Pagination } from "./common.type";

import { CountryDto, DistrictDto, Gender, LoadableItemDto } from "@/types";

export type OrganizationStatus =
    | "DRAFT"
    | "PENDING"
    | "REVIEW"
    | "REJECTED"
    | "SUSPENDED"
    | "REGISTERED"
    | "INACTIVE"
    | "RENEWAL_IN_REVIEW"
    | "RENEWAL_DRAFT"
    | "RENEWAL_REJECTED";

export type BankDetailsDto = {
    id: string;
    organization_id: string;
    account_number: string;
    branch_name: string;
    bank_id: string;
    bank_name?: string;
    bank_code?: string;
};

export type OrganizationAuditorDto = {
    id: string;
    organization_id: string;
    name: string;
    email: string;
    phone: string;
    address: string;
    is_active: boolean;
};

export type OrganizationDonorDto = {
    id: string;
    organization_id: string;
    donor_id: string;
    is_active: boolean;
    amount: number;
    donor_name?: string;
};

export type FundingSourceDto = {
    id: string;
    organization_id: string;
    donor_id: string;
    currency_id: string;
    contact_person: string;
    amount: number;
    donor?: LoadableItemDto;
};

export type TargetGroupDto = {
    id: string;
    organization_id: string;
    type_id: string;
    is_active: boolean;
    created_at: string;
    updated_at: string;
    type?: string;
};

export type OrganizationStaffDto = {
    id: string;
    organization_id: string;
    staff_type_id: string;
    is_active: boolean;
    total: number;
    total_female: number;
    total_male: number;
    staff_type?: string;
};

export type LocationActivityDto = {
    id: string;
    vdc_id?: string;
    adc_id?: string;
    organization_id: string;
    district_id?: string;
    district?: string;
    created_at: string;
    updated_at: string;
};

export type OrganizationSectorDto = {
    id: string;
    organization_id: string;
    sector_id: string;
    name?: string;
    display_value?: string;
    code?: string;
    type?: string;
    created_at?: string;
    updated_at?: string;
};

export type OrganizationProjectDto = {
    id: string;
    organization_id: string;
    name: string;
    thematic_area_id: string;
    number_of_beneficiaries: number;
    is_active: boolean;
    thematic_area?: string;
};

export type DirectorDto = {
    id: string;
    fullname: string;
    email: string;
    phone: string;
    avatar?: string;
    national_identifier?: string;
    passport_number?: string;
    gender: Gender;
    position: string;
    country_id: string;
    occupation: string;
    timeframe: string;
    qualification_id: string;
    organization_id: string;
    country?: CountryDto;
    qualification?: string;
    created_at: string;
    updated_at: string;
};

export type OrganizationDto = {
    id: string;
    name: string;
    abbreviation: string;
    organization_type_id: string;
    registration_number: string;
    district_id: string;
    financial_start_month: string;
    financial_end_month: string;
    charity_number?: string;
    annual_income: number;
    registration_type_id: string;
    biography?: string;
    vision?: string;
    motto?: string;
    objectives?: string[];
    status: OrganizationStatus;
    type: string;
    banner?: string;
    logo?: string;
    account_id: string;
    created_at: string;
    updated_at: string;
    created_by?: string;
    updated_by?: string;
    staff?: OrganizationStaffDto[];
    sectors?: OrganizationSectorDto[];
    projects?: OrganizationProjectDto[];
    bank_details?: BankDetailsDto[];
    auditors?: OrganizationAuditorDto[];
    donors?: OrganizationDonorDto[];
    funding_sources?: FundingSourceDto[];
    target_groups?: TargetGroupDto[];
    location_activities?: LocationActivityDto[];
    directors?: DirectorDto[];
    organization_type?: LoadableItemDto;
    registration_type?: LoadableItemDto;
    district?: DistrictDto;
    contacts?: ContactDto[];
};

export interface OrganizationFilter extends Pagination {
    name?: string;
    abbreviation?: string;
    organization_type_id?: string;
    registration_number?: string;
    district_id?: string;
    registration_type_id?: string;
    status?: OrganizationStatus | string;
}

export type OrganizationStatusConfig = {
    color: string;
    icon: JSX.Element;
    label: string;
    description: string;
};

export type OrganizationVerificationDto = {
    registration_number: string;
    is_valid: boolean;
    organization_name?: string;
    abbreviation?: string;
    status?: string;
    message: string;
};

export interface OrganizationSummaryDto {
    id: string;
    name: string;
    logo: string;
    abbreviation: string;
    registration_number: string;
}
