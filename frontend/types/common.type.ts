export interface Pagination {
    segments?: string;
    start_date?: Date | string;
    end_date?: Date | string;
    page: number;
    size: number;
    search?: string;
}

export interface ValidationError {
    code: string;
    minimum?: number;
    type: string;
    inclusive: boolean;
    exact?: boolean;
    message: string;
    path: string[];
}

export interface HttpResponse<T> {
    success: boolean;
    errors: ValidationError[];
    data?: T;
    status: number;
    page: number;
    size: number;
    total: number;
}

export type AuthToken = {
    access_token: string;
    refresh_token: string;
    token_type: string;
};

export interface ISession {
    first_name: string;
    last_name: string;
    middle_name: string | null;
    username: string;
    phone: string | null;
    email: string;
    user_id: string;
    status: string;
    created_at: string;
    updated_at: string;
    avatar: string;
    name?: string;
}

export type LoginSchema = {
    identifier: string;
    password: string;
    rememberMe: boolean;
};

export interface LoginResponse extends ISession {
    auth: AuthToken;
}

export interface VoidRequest {
    void_reason: string;
}
