import { LoadableItemDto } from "./settings.dto";

export type FeeStatus = "ACTIVE" | "SCHEDULED" | "EXPIRED" | "DRAFT";

export interface FeeDto {
    id: string;
    amount: number;
    fee_category_id: string;
    currency_id: string;
    name: string;
    based_on_income: boolean;
    min_income: number;
    max_income: number;
    description?: string;
    version: number;
    effective_from: Date;
    effective_to?: Date;
    status: FeeStatus;
    replaces_fee_id?: string;
    created_at: Date;
    updated_at: Date;
    created_by?: string;
    updated_by?: string;

    // Related data
    category?: LoadableItemDto;
    currency_code?: string;
    currency_name?: string;
}

export interface FeeCreateRequest {
    amount: number;
    fee_category_id: string;
    currency_id: string;
    name: string;
    based_on_income?: boolean;
    min_income?: number;
    max_income?: number;
    description?: string;
    effective_from?: Date;
    status?: FeeStatus;
}

export interface FeeUpdateRequest {
    amount?: number;
    name?: string;
    based_on_income?: boolean;
    min_income?: number;
    max_income?: number;
    description?: string;
    effective_from?: Date;
    effective_to?: Date;
    status?: FeeStatus;
}

export interface FeeScheduleRequest {
    amount: number;
    name?: string;
    based_on_income?: boolean;
    min_income?: number;
    max_income?: number;
    description?: string;
    effective_from: Date;
}

export interface FeeFilter {
    page?: number;
    size?: number;
    name?: string;
    status?: FeeStatus;
    fee_category_id?: string;
    currency_id?: string;
    based_on_income?: boolean;
    effective_from_start?: Date;
    effective_from_end?: Date;
    active_only?: boolean;
    include_expired?: boolean;
    version?: number;
}

// UI-specific types
export interface FeeTableRow extends FeeDto {
    categoryName?: string;
    currencyDisplay?: string;
    statusBadgeColor?: string;
    incomeRangeDisplay?: string;
    effectivePeriodDisplay?: string;
}

export interface FeeFormData {
    amount: number | string;
    fee_category_id: string;
    currency_id: string;
    name: string;
    based_on_income: boolean;
    min_income: number | string;
    max_income: number | string;
    description: string;
    effective_from: Date | string;
    status: FeeStatus;
}
