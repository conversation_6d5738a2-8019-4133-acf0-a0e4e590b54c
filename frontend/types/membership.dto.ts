import { Pagination } from "./common.type";
import { OrganizationSummaryDto } from "./organization.dto";
import { Gender } from "./user.type";

export type MemberRole = "OWNER" | "MEMBER";

export interface MemberDto {
    id: string;
    organization_id: string;
    user_id: string;
    role: MemberRole;
    joined_at: string;
    is_active: boolean;
    created_at: string;
    updated_at: string;
    user_name?: string;
    user_email?: string;
    user_avatar?: string;
}

export interface MemberInvitationDto {
    id: string;
    organization_id: string;
    inviter_user_id?: string;
    invited_email: string;
    code: string;
    status: "PENDING" | "ACCEPTED" | "REJECTED" | "CANCELLED";
    role: MemberRole;
    expires_at: string;
    created_at: string;
    updated_at: string;
    organization_name?: string;
    inviter_name?: string;
}

export interface MemberInvitationRequest {
    email: string;
    role: "MEMBER";
}

export interface AcceptInvitationRequest {
    user_id?: string;
}

export interface RejectInvitationRequest {
    reason?: string;
}

export interface ValidateInvitationTokenRequest {
    token: string;
}

export interface ValidateInvitationTokenResponse {
    valid: boolean;
    email?: string;
    organization_name?: string;
    role?: string;
    inviter_name?: string;
    user_exists?: boolean;
    expires_at?: string;
}

export interface AcceptInvitationWithAccountRequest {
    first_name: string;
    last_name: string;
    gender: Gender;
    password: string;
}

export interface MemberFilter extends Pagination {
    role?: MemberRole;
}

export interface MemberInvitationFilter extends Pagination {
    status?: "PENDING" | "ACCEPTED" | "REJECTED" | "CANCELLED";
}

export interface OrganizationMembershipStats {
    active_members: number;
    pending_invites: number;
    total_members: number;
}

export interface MembershipSummaryDto {
    id: string;
    role: MemberRole;
    user_id: string;
    organization: OrganizationSummaryDto;
}
