import { UserDto } from "./user.type";

export interface AuditDto {
    id: string;
    table_name: string;
    record_id: string | null;
    action: string;
    old_values: string | null;
    new_values: string | null;
    user: UserDto;
    created_at: string;
}

export interface AuditFilter {
    page?: number;
    size?: number;
    user_id?: string;
    action?: string;
    table_name?: string;
    start_date?: string;
    end_date?: string;
    query?: string;
}
