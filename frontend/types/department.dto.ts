import { Pagination } from "./common.type";
import { CountryDto } from "./settings.dto";
import { Gender } from "./user.type";

export interface DepartmentDto {
    id: string;
    name: string;
    code: string;
    description?: string;
    created_at: Date;
    updated_at: Date;
}

export interface DepartmentFilter extends Pagination {
    name?: string;
    code?: string;
}

export interface DepartmentRequest {
    name: string;
    code: string;
    description?: string;
}

export type ContactType = "EMAIL" | "PHONE" | "ADDRESS" | "WEBSITE" | "SOCIAL_MEDIA";

export interface OrganizationContact {
    type: ContactType;
    details: Record<string, any>[];
}

export interface DirectorRequest {
    id?: string;
    country?: CountryDto;
    fullname: string;
    email: string;
    phone: string;
    avatar?: string;
    national_identifier?: string;
    passport_number?: string;
    gender: Gender;
    position: string;
    country_id: string;
    occupation: string;
    timeframe: string;
    qualification_id: string;
}

export interface FundingSourceRequest {
    id?: string;
    donor_id: string;
    currency_id: string;
    contact_person: string;
    amount: number;
}

export interface BankDetailsRequest {
    id?: string;
    account_number: string;
    branch_name: string;
    bank_id: string;
}

export interface OrganizationAuditorRequest {
    id?: string;
    name: string;
    email: string;
    phone: string;
    address: string;
    is_active: boolean;
}

export interface LocationActivityRequest {
    id?: string;
    vdc_id?: string;
    adc_id?: string;
    district_id: string;
}

export interface TargetGroupRequest {
    id?: string;
    type_id: string;
    is_active: boolean;
}

export interface OrganizationStaffRequest {
    id?: string;
    staff_type_id: string;
    is_active: boolean;
    total_female: number;
    total_male: number;
}

export interface OrganizationDonorRequest {
    id?: string;
    donor_id: string;
    is_active: boolean;
    donor_name?: string;
    amount: number;
    currency_id: string;
    currency_name?: string;
}

export interface OrganizationProjectRequest {
    id?: string;
    name: string;
    thematic_area_id: string;
    number_of_beneficiaries: number;
    is_active: boolean;
}
