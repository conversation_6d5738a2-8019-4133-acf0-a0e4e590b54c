import { ApplicationDocumentDto } from "./document.dto";
import { OrganizationDto } from "./organization.dto";
import { UserSummaryDto } from "./user.type";

export type ApplicationType = "ORGANIZATION_REGISTRATION" | "LICENCE_RENEWAL" | "PERMIT_APPLICATION";
export type ApplicationStatus = "DRAFT" | "REVIEW" | "REJECTED" | "SUSPENDED" | "REGISTERED";

export interface ApplicationDto {
    id: string;
    type: ApplicationType;
    status: ApplicationStatus;
    code: string;
    organization?: OrganizationDto | null;
    documents?: ApplicationDocumentDto[];
    created_at: Date;
    updated_at: Date;
    creator?: UserSummaryDto;
}

export interface ApplicationStatsDto {
    total: number;
    draft: number;
    in_review: number;
    approved: number;
    rejected: number;
    suspended: number;
}
