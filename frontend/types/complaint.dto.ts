export interface ComplaintDTO {
    id: string;
    tracking_code: string;
    title: string;
    summary: string;
    priority: number;
    organization_id: string | null;
    is_anonymous: boolean;
    category_id: string;
    complainant_id: string | null;
    complainant_name: string | null;
    complainant_email: string | null;
    complainant_phone: string | null;
    created_at: string;
    updated_at: string;
    // Additional properties from related models
    status?: ComplaintStatusType;
    category?: ComplaintCategory;
    organization?: {
        id: string;
        name: string;
        email?: string;
        phone_number?: string;
    };
    complainant?: {
        id: string;
        first_name: string;
        last_name: string;
        email: string;
    };
}

export interface ComplaintRequest {
    id?: string;
    title: string;
    summary: string;
    priority: number;
    organization_id?: string;
    is_anonymous?: boolean;
    category_id: string;
    complainant_id?: string;
    complainant_name?: string;
    complainant_phone?: string;
    complainant_email?: string;
}

export interface PublicComplaintRequest {
    title: string;
    summary: string;
    priority?: number;
    organization_id?: string;
    category_id: string;
    complainant_name?: string;
    complainant_email?: string;
    complainant_phone?: string;
}

export interface ComplaintStatusUpdateRequest {
    status: ComplaintStatusType;
    resolution?: string;
    internal_notes?: string;
    public_response?: string;
}

export enum ComplaintStatusType {
    OPEN = "OPEN",
    IN_PROGRESS = "IN_PROGRESS",
    CLOSED = "CLOSED",
    ESCALATED = "ESCALATED",
    RESOLVED = "RESOLVED",
}

export interface ComplaintFilter {
    priority?: number;
    category_id?: string;
    organization_id?: string;
    tracking_code?: string;
    status?: ComplaintStatusType;
    page?: number;
    size?: number;
}

export interface ComplaintStatsDto {
    total_complaints: number;
    open_complaints: number;
    in_progress_complaints: number;
    resolved_complaints: number;
    closed_complaints: number;
    escalated_complaints: number;
    avg_resolution_days: number | null;
    monthly_complaints: number;
    complaints_by_priority: Record<string, number>;
    complaints_by_category: Record<string, number>;
    recent_complaints: ComplaintDTO[];
}

export interface ComplaintDashboardFilter {
    period?: string;
    category_id?: string;
    organization_id?: string;
    page?: number;
    size?: number;
}

export interface ComplaintTrendDto {
    period: string;
    total_complaints: number;
    resolved_complaints: number;
    avg_resolution_time: number | null;
}

export interface ComplaintSummaryDto {
    id: string;
    tracking_code: string;
    title: string;
    priority: number;
    status: string;
    organization_name: string | null;
    category_name: string;
    created_at: string;
    days_open: number;
}

export interface ComplaintCategory {
    id: string;
    code: string;
    display_value: string;
    description: string;
}

export interface ComplaintSubmissionResponse {
    complaint_id: string;
    tracking_code: string;
    message: string;
}

export interface ComplaintAttachment {
    id: string;
    complaint_id: string;
    document_id: string;
    document_type: string;
    file_name: string;
    file_size: number;
    file_url: string;
    uploaded_at: string;
    uploaded_by: string;
}

export interface ComplaintStatus {
    id: string;
    complaint_id: string;
    status: ComplaintStatusType;
    resolution?: string;
    internal_notes?: string;
    public_response?: string;
    updated_by: string;
    created_at: string;
    updated_at: string;
}

export interface ComplaintDetailDTO extends ComplaintDTO {
    statuses: ComplaintStatus[];
    attachments: ComplaintAttachment[];
    current_status: ComplaintStatus;
}

export const PRIORITY_LABELS = {
    1: "High",
    2: "Medium",
    3: "Low",
} as const;

export const STATUS_LABELS = {
    [ComplaintStatusType.OPEN]: "Open",
    [ComplaintStatusType.IN_PROGRESS]: "In Progress",
    [ComplaintStatusType.RESOLVED]: "Resolved",
    [ComplaintStatusType.ESCALATED]: "Escalated",
    [ComplaintStatusType.CLOSED]: "Closed",
} as const;

export const STATUS_COLORS = {
    [ComplaintStatusType.OPEN]: "bg-yellow-100 text-yellow-800",
    [ComplaintStatusType.IN_PROGRESS]: "bg-blue-100 text-blue-800",
    [ComplaintStatusType.RESOLVED]: "bg-green-100 text-green-800",
    [ComplaintStatusType.ESCALATED]: "bg-red-100 text-red-800",
    [ComplaintStatusType.CLOSED]: "bg-gray-100 text-gray-800",
} as const;

export const PRIORITY_COLORS = {
    1: "bg-red-100 text-red-800",
    2: "bg-yellow-100 text-yellow-800",
    3: "bg-green-100 text-green-800",
} as const;
