import { Pagination } from "./common.type";
import { DepartmentDto } from "./department.dto";
import { RoleDto } from "./role.type";

export type Gender = "FEMALE" | "MALE";

export interface UserDto {
    id: string;
    first_name: string;
    last_name: string;
    middle_name: string;
    handle: string;
    email: string;
    account_status: string;
    is_external: boolean;
    phone: string;
    role_id?: string;
    department_id?: string;
    department?: DepartmentDto;
    role?: RoleDto;
    gender: Gender;
}

export interface UserRequest {
    first_name: string;
    last_name: string;
    email: string;
    password: string;
    gender: string;
    search?: string;
}

export interface TwoFactorVerificationRequest {
    email?: string;
    code?: string;
    token?: string;
}

export type UserFilter = {} & Pagination & UserDto;

export interface UserSummaryDto {
    id: string;
    first_name: string;
    last_name: string;
    handle: string;
    account_id: string;
    email: string;
}
