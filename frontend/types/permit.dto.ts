export interface PermitApplicantData {
    full_name: string;
    nationality_id: string;
    passport_number: string;
    position: string;
    department?: string;
    employment_start_date?: string;
    employment_end_date?: string;
}

export interface CreatePermitApplicationsRequest {
    organization_id: string;
    applicants: PermitApplicantData[];
    supporting_documents: Record<string, File>[];
}