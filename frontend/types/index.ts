import { ReactNode, SVGProps } from "react";

export type IconSvgProps = SVGProps<SVGSVGElement> & {
    size?: number;
};

export interface LayoutProps {
    children: ReactNode;
}

export * from "./activity.dto";
export * from "./application.dto";
export * from "./audit.dto";
export * from "./auth.dto";
export * from "./common.type";
export * from "./complaint.dto";
export * from "./dashboard";
export * from "./department.dto";
export * from "./document.dto";
export * from "./fee.dto";
export * from "./financial.dto";
export * from "./licence.dto";
export * from "./membership.dto";
export * from "./notification.dto";
export * from "./permit.dto";
export * from "./role.type";
export * from "./session.dto";
export * from "./settings.dto";
export * from "./user.type";
export * from "./workflow.dto";
