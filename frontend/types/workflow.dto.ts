import { Pagination } from "./common.type";
import { ApplicationDocumentDto } from "./document.dto";
import { OrganizationDto } from "./organization.dto";
import { UserSummaryDto } from "./user.type";

// Enums
export type ActionMode = "BEFORE" | "AFTER";
export type WorkflowTemplateCode = "ORGANIZATION_REGISTRATION" | "LICENCE_RENEWAL" | "PERMIT_APPLICATION";
export type WorkflowStatus = "IN_REVIEW" | "APPROVED" | "COMPLETED" | "REJECTED";
export type Priority = "HIGH" | "MEDIUM" | "LOW";

// Base DTOs
export interface UserBaseDto {
    user_id: string;
    first_name: string;
    last_name: string;
    email: string;
}

export interface RoleBaseDto {
    id: string;
    name: string;
    description: string;
}

export interface ApplicationSummaryDto {
    id: string;
    code: string;
    organization: OrganizationDto;
    type: string;
    status: string;
    documents?: ApplicationDocumentDto[];
    created_at: string;
    creator?: UserSummaryDto | null;
}

export interface TemplateStageRoleDto {
    id: string;
    template_stage_id: string;
    role_id: string;
    is_active: boolean;
    role?: RoleBaseDto | null;
}

export interface TemplateStageTriggerDto {
    id: string;
    template_stage_id: string;
    trigger_id: string;
    action_mode: ActionMode;
    is_active: boolean;
    trigger?: string | null;
}

export interface TemplateStageDto {
    id: string;
    template_id: string;
    name: string;
    description?: string | null;
    is_active: boolean;
    position: number;
    triggers: TemplateStageTriggerDto[];
    roles: TemplateStageRoleDto[];
}

export interface TemplateDto {
    id: string;
    name: string;
    code: string;
    description?: string | null;
    is_active: boolean;
    stages: TemplateStageDto[];
}

export interface WorkflowStageDto {
    id: string;
    approver?: UserBaseDto | null;
    created_at: string;
}

export interface WorkflowDto {
    id: string;
    template?: TemplateDto | null;
    next_stage?: TemplateStageDto | null;
    approvals: WorkflowStageDto[];
}

// Dashboard DTOs
export interface WorkflowStatsDto {
    total: number;
    pending: number;
    in_review: number;
    completed: number;
    rejected: number;
    average_time_days: number;
    this_month: number;
    trend_percentage: string;
}

export interface TemplateSummaryDto {
    name: string;
    description: string;
    stages?: TemplateStageDto[];
}

export interface CurrentStageDto {
    name: string;
    position: number;
    status: string;
}

export interface WorkflowListDto {
    id: string;
    application: ApplicationSummaryDto;
    template: TemplateSummaryDto;
    current_stage: CurrentStageDto;
    total_stages: number;
    assigned_to_me: boolean;
    priority: string;
    days_active: number;
}

export interface WorkflowStageDetailDto {
    id: string;
    template_stage_name: string;
    template_stage_description: string;
    position: number;
    status: string;
    approved_by_name?: string | null;
    created_at: string;
    updated_at?: string | null;
    template_stage: TemplateStageDto;
}

export interface WorkflowDashboardDto {
    id: string;
    application: ApplicationSummaryDto;
    template: TemplateSummaryDto & {
        stages: TemplateStageDto[];
    };
    stages: WorkflowStageDetailDto[];
    next_stage?: TemplateSummaryDto | null;
}

// Request DTOs
export interface TemplateStageTriggerRequest {
    trigger_id: string;
    action_mode: ActionMode;
}

export interface TemplateStageRequest {
    name: string;
    description?: string;
    position: number;
    is_active?: boolean;
    roles?: string[];
    triggers?: TemplateStageTriggerRequest[];
}

export interface TemplateRequest {
    name: string;
    code: string;
    description?: string;
    is_active?: boolean;
    stages?: TemplateStageRequest[];
}

export interface WorkflowActionRequest {
    comment?: string;
    reason?: string;
}

export interface TemplateStageRoleRequest {
    role_id: string;
    template_stage_id: string;
}

// Filter DTOs
export interface TemplateFilter extends Partial<Pagination> {
    name?: string;
    code?: string;
    is_active?: boolean;
}

export interface TemplateStageFilter extends Partial<Pagination> {
    name?: string;
    is_active?: boolean;
}

export interface TemplateStageRoleFilter extends Partial<Pagination> {
    role_id?: string;
    is_active?: boolean;
}

export interface TemplateStageTriggerFilter extends Partial<Pagination> {
    trigger_id?: string;
    action_mode?: ActionMode;
    is_active?: boolean;
}

export interface WorkflowDashboardFilter extends Partial<Pagination> {
    search?: string;
    status?: string;
    priority?: string;
    assigned_to_me?: boolean;
    application_type?: string;
}

// UI-specific types
export interface WorkflowTemplateOption {
    code: WorkflowTemplateCode;
    name: string;
    description: string;
    icon: string;
}

export interface StageFormData {
    name: string;
    description?: string;
    position: number;
    selectedRoles: string[];
    selectedTriggers: Array<{
        trigger_id: string;
        action_mode: ActionMode;
    }>;
}

// API Response types
export interface TemplatesResponse {
    data: TemplateDto[];
    total: number;
    page: number;
    size: number;
    pages: number;
}

export interface StagesResponse {
    data: TemplateStageDto[];
    total: number;
    page: number;
    size: number;
    pages: number;
}

export interface WorkflowNotification {
    id: string;
    workflowId: string;
    title: string;
    message: string;
    type: "info" | "success" | "warning" | "error";
    read: boolean;
    created_at: string;
    action?: {
        label: string;
        url: string;
    };
}

export type DateRange = { start?: Date; end?: Date };

export interface AdvancedWorkflowFilter extends WorkflowDashboardFilter {
    dateRange?: DateRange;
    currentStage?: string[];
    departments?: string[];
    stageDuration?: { min: number; max: number };
    savedFilterId?: string;
    slaStatus?: "on_track" | "at_risk" | "overdue";
}

export interface WorkflowStore {
    // State
    workflows: WorkflowListDto[];
    selectedWorkflows: string[];
    filters: AdvancedWorkflowFilter;
    notifications: WorkflowNotification[];
    realTimeConnections: Map<string, WebSocket>;
    bulkOperationInProgress: boolean;
    savedFilters: Array<{ id: string; name: string; filters: AdvancedWorkflowFilter }>;

    // Loading states
    isLoading: boolean;
    isBulkOperationLoading: boolean;

    // Actions
    setWorkflows: (workflows: WorkflowListDto[]) => void;
    setFilters: (filters: Partial<AdvancedWorkflowFilter>) => void;
    clearFilters: () => void;

    // Selection
    selectWorkflow: (id: string) => void;
    selectAllWorkflows: (workflows: WorkflowListDto[]) => void;
    clearSelection: () => void;

    // Bulk operations
    bulkApprove: (workflowIds: string[], comment?: string) => Promise<void>;
    bulkReject: (workflowIds: string[], reason: string, comment?: string) => Promise<void>;
    bulkAssign: (workflowIds: string[], userId: string) => Promise<void>;

    // Real-time
    subscribeToWorkflow: (workflowId: string) => void;
    unsubscribeFromWorkflow: (workflowId: string) => void;

    // Notifications
    addNotification: (notification: WorkflowNotification) => void;
    markNotificationAsRead: (notificationId: string) => void;
    clearNotifications: () => void;

    // Saved filters
    saveFilter: (name: string, filters: AdvancedWorkflowFilter) => void;
    loadFilter: (filterId: string) => void;
    deleteFilter: (filterId: string) => void;
}
