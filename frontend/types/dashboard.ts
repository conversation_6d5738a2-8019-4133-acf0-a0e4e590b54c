export interface DashboardStats {
    totalUsers: number;
    totalOrganizations: number;
    pendingApplications: number;
    approvedApplications: number;
    totalRevenue: number;
    activeWorkflows: number;
    monthlyGrowth: {
        users: number;
        organizations: number;
        applications: number;
        revenue: number;
    };
}

export interface RecentActivity {
    id: string;
    type: "application" | "organization" | "user" | "payment" | "workflow";
    title: string;
    description: string;
    timestamp: string;
    status?: "pending" | "approved" | "rejected" | "completed";
    user?: string;
    avatar?: string;
}

export interface ApplicationPipeline {
    stage: string;
    count: number;
    percentage: number;
    color: "blue" | "yellow" | "green" | "red";
}

export interface User {
    name: string;
    role: string;
    lastLogin?: string;
}

export interface OrganizationOverview {
    total_organizations: number;
    recent_organizations: number;
    total_applications: number;
    active_workflows: number;
}

export interface IncomeStatistics {
    total_organizations: number;
    average_income: number;
    total_income: number;
    minimum_income: number;
    maximum_income: number;
}

export interface MonthlyRegistration {
    month: string | null;
    count: number;
}

export interface ProcessingStatistics {
    average_processing_days: number;
    approved_count: number;
    rejected_count: number;
    approval_rate: number;
}

export interface RenewalStatistics {
    due_for_renewal: number;
    pending_renewals: number;
    completed_this_year: number;
}

export interface OrganizationStatistics {
    overview: OrganizationOverview;
    organization_status: Record<string, number>;
    application_status: Record<string, number>;
    organization_types: Record<string, number>;
    district_distribution: Record<string, number>;
    income_statistics: IncomeStatistics;
    monthly_registrations: MonthlyRegistration[];
    processing_statistics: ProcessingStatistics;
    renewal_statistics: RenewalStatistics;
}

export interface RecentOrganization {
    id: string;
    name: string;
    abbreviation: string;
    status: string;
    created_at: string | null;
    annual_income: number;
    registration_number: string;
}
