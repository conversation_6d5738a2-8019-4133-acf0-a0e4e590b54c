// Enums matching backend
export enum NotificationType {
    ACTIVITY = "ACTIVITY",
    ALERT = "ALERT",
    GENERAL = "GENERAL",
    INFO = "INFO",
    WARN = "WARN",
    SUCCESS = "SUCCESS",
}

export enum NotificationPriority {
    LOW = "LOW",
    MEDIUM = "MEDIUM",
    HIGH = "HIGH",
    URGENT = "URGENT",
}

// DTOs matching backend structure exactly
export interface NotificationDto {
    id: string;
    title: string;
    message: string;
    type: NotificationType;
    activity_id?: string;
    sender_id?: string;
    priority: NotificationPriority;
    // Audit fields from AuditMixin
    created_at: string;
    updated_at: string;
    created_by?: string;
    updated_by?: string;
    voided: boolean;
    voided_by?: string;
    void_reason?: string;
}

export interface NotificationRecipientDto {
    id: string;
    notification_id: string;
    account_id: string;
    is_read: boolean;
    is_archived: boolean;
    read_at?: string;
    // Audit fields from AuditMixin
    created_at: string;
    updated_at: string;
    created_by?: string;
    updated_by?: string;
    voided: boolean;
    voided_by?: string;
    void_reason?: string;
    // Related notification
    notification?: NotificationDto;
}

export interface NotificationWithRecipientDto {
    // Notification fields
    notification_id: string;
    title: string;
    message: string;
    type: NotificationType;
    activity_id?: string;
    sender_id?: string;
    priority: NotificationPriority;
    notification_created_at: string;
    notification_updated_at: string;
    // Recipient fields
    recipient_id: string;
    account_id: string;
    is_read: boolean;
    is_archived: boolean;
    read_at?: string;
    recipient_created_at: string;
    recipient_updated_at: string;
}

// Filter and Request schemas (from notification_schema.py)
export interface NotificationFilter {
    page?: number;
    size?: number;
    segments?: string;
    start_date?: string;
    end_date?: string;
    type?: NotificationType;
    priority?: NotificationPriority;
    is_read?: boolean;
    is_archived?: boolean;
    sender_id?: string;
}

export interface CreateNotificationRequest {
    title: string;
    message: string;
    type?: NotificationType;
    activity_id?: string;
    sender_id?: string;
    priority?: NotificationPriority;
    recipient_ids: string[];
}

export interface UpdateNotificationStatusRequest {
    is_read?: boolean;
    is_archived?: boolean;
}
