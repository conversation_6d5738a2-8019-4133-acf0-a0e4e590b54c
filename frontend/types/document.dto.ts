import { LoadableItemDto } from "./settings.dto";

export interface DocumentDto {
    id: string;
    filename: string;
    mimetype: string;
    location: string;
    original_name: string;
    size: string;
    created_at: string;
    updated_at?: string;
}

export interface ApplicationDocumentDto {
    id: string;
    application_id: string;
    document_id: string;
    document_type_id: string;
    document?: DocumentDto;
    document_type?: LoadableItemDto;
    created_at: string;
    updated_at?: string;
}
