import { ApplicationDto } from "./application.dto";
import { ApplicationDocumentDto } from "./document.dto";
import { OrganizationDto } from "./organization.dto";

export interface LicenceDto {
    id: string;
    licence_number: string;
    organization_id: string;
    invoice_item_id?: string;
    expires_at: string;
    type: "CERTIFICATE" | "LICENCE";
    created_at: string;
    updated_at: string;
}

export interface LicenceVerificationDto {
    licence_number: string;
    is_valid: boolean;
    licence?: LicenceDto;
    organization_name?: string;
    message: string;
}

export interface LicenceValidityCheckDto {
    licence_number: string;
    is_valid: boolean;
}

export type LicenceRenewalStatus = "DRAFT" | "SUBMITTED" | "PROCESSED";

export type OrganizationFormData = {
    document_types: string;
} & OrganizationDto;

export interface LicenceRenewalDto {
    id: string;
    organization_id: string;
    application_id: string;
    application: ApplicationDto;
    status: LicenceRenewalStatus;
    form_data: OrganizationFormData;
    application_documents?: ApplicationDocumentDto[];
    created_at: string;
    updated_at: string;
}
