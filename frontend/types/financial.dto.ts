import { Pagination } from "./common.type";
import { OrganizationDto } from "./organization.dto";

export type InvoiceStatus = "PENDING" | "PAID" | "PARTIALLY_PAID" | "OVERDUE";

export type PaymentStatus = "SUCCESS" | "FAILED";

export interface InvoiceDocumentDto {
    id: string;
    invoice_id: string;
    document_id: string;
    description: string;
    filename: string;
    location: string;
    original_name: string;
    size: string;
}

export interface WorkflowStageFilter extends Pagination {
    status?: string;
    stage_id?: string;
    role_code?: string;
    search?: string;
}

export interface FinancialDashboardData {
    total_revenue: number;
    revenue_by_month: Array<{
        year: number;
        month: number;
        revenue: number;
        payment_count: number;
        period: string;
    }>;
    revenue_by_application_type: Record<string, number>;
    collection_efficiency: number;
    top_paying_organizations: Array<{
        organization_id: string;
        organization_name: string;
        total_paid: number;
        payment_count: number;
    }>;
    revenue_growth: number;
    average_payment_amount: number;
    payment_count: number;
    payment_method_breakdown: Record<string, number>;
}

export interface PaymentAnalyticsData {
    payment_volume_trends: Array<{
        period: string;
        volume: number;
        amount: number;
    }>;
    payment_method_breakdown: Record<string, number>;
    payment_success_rate: number;
    payment_verification_metrics: {
        total_payments: number;
        verified_payments: number;
        pending_verification: number;
        verification_rate: number;
    };
    deposit_accuracy_analysis: {
        total_analyzed: number;
        exact_matches: number;
        overpayments: number;
        underpayments: number;
        accuracy_rate: number;
        average_difference: number;
    };
    seasonal_patterns: Array<{
        month: string;
        month_number: number;
        payment_count: number;
        total_amount: number;
    }>;
    average_verification_time: number;
}

export interface OrganizationFinancialProfile {
    organization_id: string;
    payment_history: Array<{
        payment_id: string;
        amount: number;
        transaction_number: string;
        status: string;
        payment_method: string;
        paid_by: string;
        date: string;
        invoice_id?: string;
    }>;
    financial_summary: {
        total_fees_paid: number;
        total_payments: number;
        average_payment_amount: number;
        outstanding_balance: number;
        latest_payment_date?: string;
    };
    payment_compliance: {
        total_invoices: number;
        paid_invoices: number;
        overdue_invoices: number;
        compliance_score: number;
        compliance_rating: string;
        average_payment_delay_days: number;
    };
    payment_behavior: {
        average_payment_interval_days: number;
        payment_frequency: string;
        total_payments_made: number;
        peak_payment_month: string;
        monthly_distribution: Record<string, number>;
    };
    outstanding_balances: {
        total_outstanding: number;
        invoice_count: number;
        aging_analysis: {
            current: number;
            "30_days": number;
            "60_days": number;
            "90_plus_days": number;
        };
        oldest_invoice_days: number;
    };
    payment_methods_used: Array<{
        payment_method: string;
        usage_count: number;
        total_amount: number;
        is_preferred: boolean;
    }>;
    recent_transactions: Array<{
        transaction_id: string;
        date: string;
        amount: number;
        status: string;
        transaction_number: string;
        payment_method: string;
        invoice_reference?: string;
    }>;
    risk_assessment: {
        risk_level: string;
        risk_score: number;
        risk_factors: string[];
        recommendation: string;
    };
}

export interface PaymentReconciliation {
    application_id: string;
    payment_amount: number;
    calculated_fees: number;
    difference: number;
    status: "exact" | "overpaid" | "underpaid";
    requires_action: boolean;
    payment_id: string;
    reconciliation_notes: string;
}

export interface PaymentDiscrepancy {
    payment_id: string;
    organization_id: string;
    application_id: string;
    invoice_id: string;
    discrepancy_amount: number;
    discrepancy_type: "overpaid" | "underpaid";
    payment_amount: number;
    calculated_fees: number;
    created_at: string;
    requires_follow_up: boolean;
}

export interface FeeEffectivenessAnalysis {
    income_bracket_utilization: Record<
        string,
        {
            fee_id: string;
            fee_name: string;
            min_income: number;
            max_income?: number;
            usage_count: number;
            fee_amount: number;
        }
    >;
    fee_accuracy_by_bracket: {
        total_income_based_applications: number;
        accurate_matches: number;
        accuracy_rate: number;
        mismatches_count: number;
        sample_mismatches: Array<{
            organization_income: number;
            fee_min: number;
            fee_max?: number;
            fee_amount: number;
        }>;
    };
    underutilized_brackets: Array<{
        bracket: string;
        usage_count: number;
        fee_amount: number;
        recommendation: string;
    }>;
    optimization_suggestions: Array<{
        type: string;
        priority: string;
        description: string;
        potential_impact: string;
    }>;
    revenue_impact_analysis: {
        current_total_revenue: number;
        scenarios: Record<
            string,
            {
                revenue_change: number;
                new_total_revenue: number;
                description: string;
            }
        >;
        recommendation: string;
    };
    fee_distribution_analysis: {
        fee_count_by_range: Record<string, number>;
        total_active_fees: number;
        income_based_fees: number;
        fixed_fees: number;
    };
    application_type_fee_analysis: Record<
        string,
        {
            application_count: number;
            total_revenue: number;
            average_fee: number;
        }
    >;
}

// Invoice Types
export interface InvoiceDto {
    id: string;
    reference_number: string;
    status: InvoiceStatus;
    total_amount: number;
    due_date: string;
    description: string;
    organization_id: string;
    created_at: string;
    updated_at: string;
    organization?: OrganizationDto;
}

export interface PaymentDto {
    id: string;
    amount: number;
    organization_id: string;
    invoice_id: string;
    transaction_number: string;
    payment_mode_id: string;
    status: PaymentStatus;
    created_at: string;
    updated_at: string;
    paid_by: string;
    organization?: {
        id: string;
        name: string;
    };
    invoice?: InvoiceDto;
    payment_mode: string;
}

export interface InvoiceFilter extends Pagination {
    status?: string;
    organization_id?: string;
    start_date?: string;
    end_date?: string;
}

export interface PaymentVerificationRequest {
    payment_id: string;
    status: "VERIFIED" | "FAILED";
    notes?: string;
}

export interface FinanceWorkflowStage {
    id: string;
    workflow_id: string;
    template_stage_id: string;
    status: "PENDING" | "IN_REVIEW" | "COMPLETED" | "APPROVED" | "REJECTED";
    template_stage: {
        id: string;
        name: string;
        description: string;
        position: number;
    };
    application: {
        id: string;
        code: string;
        type: string;
        organization_name: string;
    };
    invoice?: InvoiceDto;
    organization?: {
        id: string;
        name: string;
        code: string;
        email: string;
        phone_number: string;
        logo_url?: string;
    };
    proof_of_payment_document?: {
        id: string;
        filename: string;
        original_name: string;
        location: string;
        size: string;
        uploaded_at: string;
    };
    created_at: string;
    updated_at: string;
}
