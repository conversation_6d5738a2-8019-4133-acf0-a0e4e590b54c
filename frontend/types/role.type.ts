import { Pagination } from "./common.type";
import { LoadableItemDto } from "./settings.dto";

export interface RoleDto {
    id: string;
    name: string;
    code: string;
    description: string;
    created_at: Date;
    updated_at: Date;
}

export type RoleFilter = Partial<{} & Pagination & RoleDto>;

export type RolePermissionDto = {
    id: string;
    code: string;
    name: string;
    description?: string;
    role_permissions: LoadableItemDto[];
    created_at?: string;
    updated_at?: string;
};

export type RolePermissionFilterDto = RolePermissionDto & Pagination;
