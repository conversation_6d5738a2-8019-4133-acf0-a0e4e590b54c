import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
    return twMerge(clsx(inputs));
}

export function stringToDateTime(dateStr: string) {
    const [datePart, timePart] = dateStr.split("T");
    const [year, month, day] = datePart.split("-").map(Number);
    const [hour, minute, second] = timePart.replace("Z", "").split(":").map(Number);
    return new Date(year, month - 1, day, hour, minute, second || 0);
}
