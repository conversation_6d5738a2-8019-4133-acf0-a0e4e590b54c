@tailwind base;
@tailwind components;
@tailwind utilities;

/* Inter UI Font Family */
@font-face {
  font-family: 'Inter UI';
  src: url('./fonts/inter-ui/Inter-UI-Regular.otf') format('opentype');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Inter UI';
  src: url('./fonts/inter-ui/Inter-UI-Italic.otf') format('opentype');
  font-weight: 400;
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: 'Inter UI';
  src: url('./fonts/inter-ui/Inter-UI-Medium.otf') format('opentype');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Inter UI';
  src: url('./fonts/inter-ui/Inter-UI-MediumItalic.otf') format('opentype');
  font-weight: 500;
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: 'Inter UI';
  src: url('./fonts/inter-ui/Inter-UI-Bold.otf') format('opentype');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Inter UI';
  src: url('./fonts/inter-ui/Inter-UI-BoldItalic.otf') format('opentype');
  font-weight: 700;
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: 'Inter UI';
  src: url('./fonts/inter-ui/Inter-UI-Black.otf') format('opentype');
  font-weight: 900;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Inter UI';
  src: url('./fonts/inter-ui/Inter-UI-BlackItalic.otf') format('opentype');
  font-weight: 900;
  font-style: italic;
  font-display: swap;
}

html, body {
  font-family: 'Inter UI', sans-serif;
}

@layer base {
  :root {
        /* GitHub Light Theme */
        --background: 220 14% 96%; /* #f6f8fa */
        --foreground: 212 12% 9%;
        --card: 0 0% 100%;
        --card-foreground: 212 12% 9%;
        --popover: 0 0% 100%;
        --popover-foreground: 212 12% 9%;
        --primary: 22 100% 52%;
        --primary-foreground: 0 0% 100%;
        --secondary: 220 14% 96%;
        --secondary-foreground: 212 12% 9%;
        --muted: 220 14% 96%;
        --muted-foreground: 215 16% 47%;
        --accent: 220 14% 96%;
        --accent-foreground: 212 12% 9%;
        --destructive: 0 84% 60%;
        --destructive-foreground: 0 0% 100%;
        --border: 216 12% 84%;
        --input: 216 12% 84%;
        --ring: 22 100% 52%;
        --chart-1: 22 100% 52%;
        --chart-2: 262 84% 58%;
        --chart-3: 142 71% 45%;
        --chart-4: 346 84% 61%;
        --chart-5: 262 84% 58%;
        --radius: 0.5rem;
        --sidebar-background: 220 14% 100%;
        --sidebar-foreground: 212 12% 9%;
        --sidebar-primary: 22 100% 52%;
        --sidebar-primary-foreground: var(--background);
        --sidebar-accent: 220 14% 89%;
        --sidebar-accent-foreground: 212 12% 9%;
        --sidebar-border: 216 12% 84%;
        --sidebar-ring: 22 100% 52%;

        --input-background: 220 14% 80%; /* #f6f8fa */
    }
    .dark {
      --background: 220 18% 7%; /* #0d1117 */
      --foreground: 210 14% 82%; /* #c9d1d9 */

      --card: 215 21% 10%; /* #161b22 */
      --card-foreground: 210 14% 82%;

      --popover: 215 21% 10%;
      --popover-foreground: 210 14% 82%;

      --primary: 22 100% 52%;
      --primary-foreground: 0 0% 100%;

      --secondary: 215 28% 20%;
      --secondary-foreground: 210 14% 82%;

      --muted: 215 21% 15%;
      --muted-foreground: 210 9% 59%; /* #8b949e */

      --accent: 215 21% 15%;
      --accent-foreground: 210 14% 82%;

      --destructive: 0 63% 31%;
      --destructive-foreground: 210 14% 82%;

      --border: 210 10% 22%; /* #30363d */
      --input: 210 10% 22%;
      --ring: 22 100% 52%;

      --chart-1: 22 100% 52%;
      --chart-2: 262 84% 58%;
      --chart-3: 142 71% 45%;
      --chart-4: 346 84% 61%;
      --chart-5: 262 84% 58%;

      --sidebar-background: 215 21% 10%;
      --sidebar-foreground: 210 14% 82%;
      --sidebar-primary: 22 100% 52%;
      --sidebar-primary-foreground: 0 0% 100%;
      --sidebar-accent: 215 21% 20%;
      --sidebar-accent-foreground: 210 14% 82%;
      --sidebar-border: 210 10% 22%;
      --sidebar-ring: 22 100% 52%;

      /* --input: 215 14% 14%; #1d2229 */
      --input-focused: 215 14% 16%; /* slightly brighter for focus state */
      --border: 215 10% 25%; /* soft neutral border */
      --ring: 22 100% 52%; /* primary for focus ring */
    }

.dark .button-select,
.dark input,
.dark textarea,
.dark select {
    background-color: hsl(var(--muted)) !important;
    color: hsl(var(--foreground));
    border: 1px solid hsl(var(--border));
    /* border-radius: 6px; */
    padding: 0.5rem 0.75rem;
    font-size: 0.95rem;
    transition:
        border-color 0.2s ease,
        box-shadow 0.2s ease,
        background-color 0.2s ease;
}

.dark select::placeholder,
.dark .button-select::placeholder,
.dark input::placeholder,
.dark textarea::placeholder {
    color: hsl(var(--muted-foreground));
    opacity: 0.8;
}

.dark input:focus,
.dark textarea:focus,
.dark select:focus {
    outline: none;
    background-color: hsl(var(--input-focused));
    border-color: hsl(var(--ring));
    box-shadow: 0 0 0 3px hsl(var(--ring) / 0.3);
}

.dark .button-select:hover,
.dark input:hover,
.dark textarea:hover,
.dark select:hover {
    border-color: hsl(var(--ring));
    background-color: hsl(var(--ring) / 0.5);
}

.bg-card {
    /* border-radius: 5px !important; */
}


}

@layer base {
    * {
      @apply border-border;
      }
    body {
      @apply bg-background text-foreground;
      }
  }

@layer components {
  .glassmorphism {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(16px) saturate(180%);
    -webkit-backdrop-filter: blur(16px) saturate(180%);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .glassmorphism-header {
    background: rgba(255, 255, 255, 0.85);
    backdrop-filter: blur(20px) saturate(180%) brightness(1.05);
    -webkit-backdrop-filter: blur(20px) saturate(180%) brightness(1.05);
    border-bottom: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  }

  .dark .glassmorphism {
    background: rgba(0, 0, 0, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.05);
  }

  .dark .glassmorphism-header {
    background: rgba(0, 0, 0, 0.85);
    backdrop-filter: blur(20px) saturate(180%) brightness(0.95);
    -webkit-backdrop-filter: blur(20px) saturate(180%) brightness(0.95);
    border-bottom: 1px solid rgba(255, 255, 255, 0.15);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
  }

  /* 4K Resolution Optimizations */
  @media (min-width: 3840px) {
    .app-container {
      max-width: 3840px;
      margin: 0 auto;
    }

    .sidebar-container {
      position: fixed;
      left: calc((100vw - 3840px) / 2);
      height: 100vh;
      z-index: 40;
    }

    .main-content {
      padding-left: 0;
    }

    /* Adjust sidebar inset for 4K */
    [data-sidebar="inset"] {
      margin-left: 260px; /* Default sidebar width */
    }

    [data-sidebar="collapsed"] ~ [data-sidebar="inset"] {
      margin-left: 60px; /* Collapsed sidebar width */
    }
  }

  /* Ultra-wide screen adjustments */
  @media (min-width: 2560px) and (max-width: 3839px) {
    .main-content {
      max-width: calc(100vw - 280px);
    }

    .app-container {
      max-width: 2560px;
      margin: 0 auto;
    }
  }

  /* Regular desktop optimizations */
  @media (min-width: 1920px) and (max-width: 2559px) {
    .app-container {
      max-width: 1920px;
      margin: 0 auto;
    }
  }

  /* Prevent horizontal scroll on very wide screens */
  .main-content {
    overflow-x: hidden;
  }

  /* Ensure content doesn't stretch too wide on ultra-wide screens */
  .content-wrapper {
    max-width: 1200px;
    width: 100%;
  }

  @media (min-width: 1600px) {
    .content-wrapper {
      max-width: 1400px;
    }
  }

  @media (min-width: 2000px) {
    .content-wrapper {
      max-width: 1600px;
    }
  }
}

.viewer-container {
    background: transparent;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    height: 600px;
    overflow: hidden;
}

#buka-viewer {
    width: 100%;
    height: 100%;
}

.sample-links {
    margin: 20px 0;
}

.sample-links a {
    display: inline-block;
    margin: 5px 10px;
    padding: 8px 15px;
    background: transparent;
    color: #007bff;
    text-decoration: none;
    border-radius: 4px;
    border: 1px solid #dee2e6;
}

.sample-links a:hover {
    background: #e9ecef;
}

.error {
    background: #f8d7da;
    color: #721c24;
    padding: 10px;
    border-radius: 4px;
    margin: 10px 0;
    border: 1px solid #f5c6cb;
}

.loading {
    text-align: center;
    padding: 20px;
    color: #666;
}

/* Essential BukaJS styles */
.buka-viewer {
    display: flex;
    flex-direction: column;
    height: 100%;
    font-family: system-ui, sans-serif;
}

.buka-toolbar {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.5rem;
    border-bottom: 1px solid #007bff;
    background: rgba(255, 255, 255, 0.15);
    flex-shrink: 0;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px); /* Safari support */

    border-bottom: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 8px;
}

.buka-toolbar-section {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.buka-btn {
    padding: 0.25rem 0.5rem;
    border: 1px solid #ccc;
    background: white;
    cursor: pointer;
    border-radius: 3px;
    font-size: 14px;
}

.buka-btn:hover {
    background: #e9e9e9;
}

.buka-main {
    display: flex;
    flex: 1;
    overflow: hidden;
}

.buka-sidebar {
    width: 200px;
    border-right: 1px solid #ddd;
    background: transparent;
    overflow-y: auto;
    flex-shrink: 0;
}

.buka-content {
    flex: 1;
    position: relative;
    overflow: auto;
}

.buka-document-container {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: transparent;
}

.buka-page-info {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.buka-page-input {
    width: 60px;
    padding: 0.25rem;
    border: 1px solid #ccc;
    border-radius: 3px;
    text-align: center;
}

.buka-search-input {
    padding: 0.25rem;
    border: 1px solid #ccc;
    border-radius: 3px;
}

.buka-thumbnails {
    padding: 10px;
}

.buka-thumbnail {
    width: 120px;
    height: 160px;
    margin-bottom: 10px;
    background: transparent;
    border: 2px solid transparent;
    border-radius: 4px;
    cursor: pointer;
    overflow: hidden;
    transition: all 0.2s;
}

.buka-thumbnail:hover {
    border-color: #007bff;
}

.buka-thumbnail-active {
    border-color: #007bff !important;
    background-color: #f0f8ff;
}

.buka-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    padding: 20px;
    color: #666;
}

.buka-spinner {
    width: 20px;
    height: 20px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #007bff;
    border-radius: 50%;
    animation: buka-spin 1s linear infinite;
    margin-bottom: 10px;
}

@keyframes buka-spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

.buka-pdf-canvas {
    display: block;
    margin: 0 auto;
}
.buka-pdf-scroll-container {
    background-color: transparent!important;
}

/* Custom logo animations */
@keyframes fadeInScale {
    0% {
        opacity: 0;
        transform: scale(0.8);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes pulseGrow {
    0%, 100% {
        opacity: 0.4;
        transform: scale(0.8);
    }
    50% {
        opacity: 1;
        transform: scale(1.1);
    }
}

@keyframes typewriter {
    0% {
        width: 0;
    }
    100% {
        width: 100%;
    }
}

@keyframes blink {
    0%, 50% {
        opacity: 1;
    }
    51%, 100% {
        opacity: 0;
    }
}

@keyframes gradientShift {
    0% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}

.typewriter-text {
    overflow: hidden;
    white-space: nowrap;
    margin: 0 auto;
    border-right: 2px solid;
    animation: typewriter 2s steps(40) 1s forwards, blink 1s infinite 3s;
}

.gradient-text {
    background: linear-gradient(
        45deg,
        hsl(var(--background)),
        hsl(var(--primary)),
        hsl(var(--foreground)),
        hsl(var(--primary)),
        hsl(var(--background))
    );
    background-size: 300% 300%;
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    animation: gradientShift 3s ease-in-out infinite;
}

@keyframes typeText {
    0% { width: 0; }
    100% { width: 100%; }
}

@keyframes removeCursor {
    0% { opacity: 1; }
    100% { opacity: 0; }
}

@keyframes flyIn {
    0% {
    transform: translateX(-200px);
    opacity: 0;
    }
    50% {
    opacity: 1;
    }
    100% {
    transform: translateX(0);
    opacity: 1;
    }
}

.typing-text {
    overflow: hidden;
    white-space: nowrap;
    animation: typeText 2s steps(4) forwards;
}

.typing-cursor {
    animation: removeCursor 0.1s ease-in-out 2.5s forwards;
}

.flying-circle {
    animation: flyIn 1s ease-out 3s forwards;
}

@keyframes zoomInDown {
    from {
        opacity: 0;
        transform: scale3d(0.1, 0.1, 0.1) translate3d(0, -1000px, 0);
        animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
    }

    60% {
        opacity: 1;
        transform: scale3d(0.475, 0.475, 0.475) translate3d(0, 60px, 0);
        animation-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1);
    }

    to {
        opacity: 1;
        transform: scale3d(1, 1, 1) translate3d(0, 0, 0);
    }
}

.zoomInDown {
    animation-name: zoomInDown;
    animation-duration: 1s;
    animation-fill-mode: both;
    animation-timing-function: ease-out;
}

.zoomInDown--fast {
    animation-duration: 0.5s;
}

.zoomInDown--slow {
    animation-duration: 2s;
}

.zoomInDown--delay {
    animation-delay: 0.5s;
}
