import { toast } from "sonner";
import { create } from "zustand";
import { subscribeWithSelector } from "zustand/middleware";

import * as workflowService from "@/services/WorkflowService";
import { WorkflowStore } from "@/types";

export const useWorkflowStore = create<WorkflowStore>()(
    subscribeWithSelector((set, get) => ({
        // Initial state
        workflows: [],
        selectedWorkflows: [],
        filters: {},
        notifications: [],
        realTimeConnections: new Map(),
        bulkOperationInProgress: false,
        savedFilters: [],
        isLoading: false,
        isBulkOperationLoading: false,

        // Actions
        setWorkflows: (workflows) => set({ workflows }),

        setFilters: (newFilters) =>
            set((state) => ({
                filters: { ...state.filters, ...newFilters },
            })),

        clearFilters: () => set({ filters: {} }),

        // Selection actions
        selectWorkflow: (id) =>
            set((state) => {
                const selected = state.selectedWorkflows.includes(id)
                    ? state.selectedWorkflows.filter((wId) => wId !== id)
                    : [...state.selectedWorkflows, id];

                return { selectedWorkflows: selected };
            }),

        selectAllWorkflows: (workflows) =>
            set(() => ({
                selectedWorkflows: workflows.map((w) => w.id),
            })),

        clearSelection: () => set({ selectedWorkflows: [] }),

        // Bulk operations
        bulkApprove: async (workflowIds, comment) => {
            set({ isBulkOperationLoading: true });
            try {
                const promises = workflowIds.map(async (workflowId) => {
                    const workflow = get().workflows.find((w) => w.id === workflowId);

                    if (!workflow) return;

                    const detailedWorkflow = await workflowService.getWorkflowById(workflowId);

                    if (!detailedWorkflow.success) return;

                    const currentStage = detailedWorkflow.data!.stages.find((stage) => stage.status === "IN_REVIEW");

                    if (currentStage) {
                        return workflowService.approveWorkflowStage(workflowId, currentStage.id, {
                            comment: comment || undefined,
                        });
                    }
                });

                await Promise.all(promises);

                toast.success(
                    `Successfully approved ${workflowIds.length} workflow${workflowIds.length > 1 ? "s" : ""}`,
                );
                set({ selectedWorkflows: [] });

                // Add success notification
                get().addNotification({
                    id: Date.now().toString(),
                    workflowId: "bulk",
                    title: "Bulk Approval Complete",
                    message: `${workflowIds.length} workflows approved successfully`,
                    type: "success",
                    read: false,
                    created_at: new Date().toISOString(),
                });
            } catch (error: any) {
                toast.error(`Bulk approval failed: ${error.message}`);
                get().addNotification({
                    id: Date.now().toString(),
                    workflowId: "bulk",
                    title: "Bulk Approval Failed",
                    message: error.message,
                    type: "error",
                    read: false,
                    created_at: new Date().toISOString(),
                });
            } finally {
                set({ isBulkOperationLoading: false });
            }
        },

        bulkReject: async (workflowIds, reason, comment) => {
            set({ isBulkOperationLoading: true });
            try {
                const promises = workflowIds.map(async (workflowId) => {
                    const workflow = get().workflows.find((w) => w.id === workflowId);

                    if (!workflow) return;

                    const detailedWorkflow = await workflowService.getWorkflowById(workflowId);

                    if (!detailedWorkflow.success) return;

                    const currentStage = detailedWorkflow.data!.stages.find((stage) => stage.status === "IN_REVIEW");

                    if (currentStage) {
                        return workflowService.rejectWorkflowStage(workflowId, currentStage.id, {
                            reason,
                            comment: comment || undefined,
                        });
                    }
                });

                await Promise.all(promises);

                toast.success(
                    `Successfully rejected ${workflowIds.length} workflow${workflowIds.length > 1 ? "s" : ""}`,
                );
                set({ selectedWorkflows: [] });

                get().addNotification({
                    id: Date.now().toString(),
                    workflowId: "bulk",
                    title: "Bulk Rejection Complete",
                    message: `${workflowIds.length} workflows rejected`,
                    type: "success",
                    read: false,
                    created_at: new Date().toISOString(),
                });
            } catch (error: any) {
                toast.error(`Bulk rejection failed: ${error.message}`);
            } finally {
                set({ isBulkOperationLoading: false });
            }
        },

        bulkAssign: async (workflowIds, userId) => {
            // Implementation for bulk assignment
            // This would require a new backend endpoint
            toast.info("Bulk assignment feature coming soon");
        },

        // Real-time subscriptions
        subscribeToWorkflow: (workflowId) => {
            const connections = get().realTimeConnections;

            if (connections.has(workflowId)) return;

            try {
                const ws = new WebSocket(`${process.env.NEXT_PUBLIC_WS_URL}/workflows/${workflowId}/stream`);

                ws.onmessage = (event) => {
                    const update = JSON.parse(event.data);

                    // Update the specific workflow in the store
                    set((state) => ({
                        workflows: state.workflows.map((workflow) =>
                            workflow.id === workflowId ? { ...workflow, ...update } : workflow,
                        ),
                    }));

                    // Add notification for the update
                    get().addNotification({
                        id: Date.now().toString(),
                        workflowId,
                        title: "Workflow Updated",
                        message: update.message || "Workflow status changed",
                        type: "info",
                        read: false,
                        created_at: new Date().toISOString(),
                        action: {
                            label: "View Workflow",
                            url: `/workflows/${workflowId}`,
                        },
                    });
                };

                ws.onerror = () => {
                    console.error(`WebSocket error for workflow ${workflowId}`);
                };

                connections.set(workflowId, ws);
                set({ realTimeConnections: new Map(connections) });
            } catch (error) {
                console.error("Failed to subscribe to workflow updates:", error);
            }
        },

        unsubscribeFromWorkflow: (workflowId) => {
            const connections = get().realTimeConnections;
            const ws = connections.get(workflowId);

            if (ws) {
                ws.close();
                connections.delete(workflowId);
                set({ realTimeConnections: new Map(connections) });
            }
        },

        // Notifications
        addNotification: (notification) =>
            set((state) => ({
                notifications: [notification, ...state.notifications].slice(0, 50), // Keep only last 50
            })),

        markNotificationAsRead: (notificationId) =>
            set((state) => ({
                notifications: state.notifications.map((n) => (n.id === notificationId ? { ...n, read: true } : n)),
            })),

        clearNotifications: () => set({ notifications: [] }),

        // Saved filters
        saveFilter: (name, filters) =>
            set((state) => ({
                savedFilters: [...state.savedFilters, { id: Date.now().toString(), name, filters }],
            })),

        loadFilter: (filterId) => {
            const savedFilter = get().savedFilters.find((f) => f.id === filterId);

            if (savedFilter) {
                set({ filters: savedFilter.filters });
            }
        },

        deleteFilter: (filterId) =>
            set((state) => ({
                savedFilters: state.savedFilters.filter((f) => f.id !== filterId),
            })),
    })),
);

// Real-time connection cleanup
if (typeof window !== "undefined") {
    window.addEventListener("beforeunload", () => {
        const store = useWorkflowStore.getState();

        store.realTimeConnections.forEach((ws) => ws.close());
    });
}
