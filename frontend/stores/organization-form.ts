"use client";

import { create } from "zustand";
import { persist } from "zustand/middleware";

import {
    BankDetailsRequest,
    DirectorRequest,
    FundingSourceRequest,
    LocationActivityRequest,
    OrganizationAuditorRequest,
    OrganizationContact,
    OrganizationDonorRequest,
    OrganizationProjectRequest,
    OrganizationStaffRequest,
    TargetGroupRequest,
} from "@/types";

export interface OrganizationFormData {
    // Basic Info
    name: string;
    abbreviation: string;
    organization_type_id: string;
    district_id: string;
    financial_start_month: string;
    financial_end_month: string;
    registration_type_id: string;
    charity_number: string;
    annual_income: number;
    biography: string;
    vision: string;
    motto: string;
    objectives: string[];

    // Related Data
    directors: DirectorRequest[];
    fundingSources: FundingSourceRequest[];
    bankDetails: BankDetailsRequest[];
    auditors: OrganizationAuditorRequest[];
    locationActivities: LocationActivityRequest[];
    targetGroups: TargetGroupRequest[];
    staff: OrganizationStaffRequest[];
    donors: OrganizationDonorRequest[];
    projects: OrganizationProjectRequest[];
    contacts: OrganizationContact[];
    sectors: string[];
    documentTypes: string[];
    documents: File[];

    // Form State
    currentStep: number;
    completedSteps: number[];
    isSubmitting: boolean;
    mode: "registration" | "renewal";
}

interface OrganizationFormStore extends OrganizationFormData {
    // Actions
    updateFormData: (field: keyof OrganizationFormData, value: any) => void;
    updateArrayField: <T>(field: keyof OrganizationFormData, data: T[]) => void;
    setCurrentStep: (step: number) => void;
    addCompletedStep: (step: number) => void;
    setIsSubmitting: (submitting: boolean) => void;
    resetForm: () => void;
    initializeForm: (data: Partial<OrganizationFormData>) => void;

    // Computed
    validateCurrentStep: () => boolean;
}

const initialFormData: OrganizationFormData = {
    // Basic Info
    name: "",
    abbreviation: "",
    organization_type_id: "",
    district_id: "",
    financial_start_month: "JAN",
    financial_end_month: "DEC",
    registration_type_id: "",
    charity_number: "",
    annual_income: 0,
    biography: "",
    vision: "",
    motto: "",
    objectives: [""],

    // Related Data
    directors: [],
    fundingSources: [],
    bankDetails: [],
    auditors: [],
    locationActivities: [],
    targetGroups: [],
    staff: [],
    donors: [],
    projects: [],
    contacts: [],
    sectors: [],
    documentTypes: [],
    documents: [],

    // Form State
    currentStep: 0,
    completedSteps: [],
    isSubmitting: false,
    mode: "registration",
};

export const useOrganizationFormStore = create<OrganizationFormStore>()(
    persist(
        (set, get) => ({
            ...initialFormData,

            updateFormData: (field, value) => {
                set((state) => ({
                    ...state,
                    [field]: value,
                }));
            },

            updateArrayField: (field, data) => {
                set((state) => ({
                    ...state,
                    [field]: data,
                }));
            },

            setCurrentStep: (step) => {
                set({ currentStep: step });
            },

            addCompletedStep: (step) => {
                set((state) => {
                    const currentCompletedSteps = Array.isArray(state.completedSteps) ? state.completedSteps : [];

                    return {
                        completedSteps: currentCompletedSteps.includes(step)
                            ? currentCompletedSteps
                            : [...currentCompletedSteps, step],
                    };
                });
            },

            setIsSubmitting: (submitting) => {
                set({ isSubmitting: submitting });
            },

            resetForm: () => {
                set(initialFormData);
            },

            initializeForm: (data) => {
                set((state) => ({
                    ...state,
                    ...data,
                    documents: data.documents || state.documents,
                }));
            },

            validateCurrentStep: () => {
                const state = get();

                switch (state.currentStep) {
                    case 0:
                        const requiredFields = [
                            "name",
                            "abbreviation",
                            "organization_type_id",
                            "district_id",
                            "registration_type_id",
                        ];

                        return requiredFields.every(
                            (field) => state[field as keyof OrganizationFormData]?.toString().trim() !== "",
                        );
                    case 1:
                        return state.directors.length >= 1;
                    case 2:
                        return state.locationActivities.length > 0;
                    case 3:
                        return state.bankDetails.length > 0;
                    case 4:
                        return true;
                    case 5:
                        if (state.mode === "renewal") {
                            return true;
                        }

                        return state.documentTypes.length > 0;
                    default:
                        return true;
                }
            },
        }),
        {
            name: "organization-form-storage",
            partialize: (state) => ({
                ...state,
                documents: [],
            }),
        },
    ),
);
