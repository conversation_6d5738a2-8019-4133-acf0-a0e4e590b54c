"use client";

import { usePathname, useRouter } from "next/navigation";
import { ReactNode, useLayoutEffect, useMemo } from "react";

import { Logo } from "@/components/icons";
import { useAuth } from "@/composables/useStore";

interface Props {
    node: ReactNode;
}

const ProtectedRoute = ({ node }: Props): ReactNode => {
    const { isAuthenticated, session, isLoading } = useAuth();
    const router = useRouter();
    const currentPath = usePathname();

    const pathInfo = useMemo(
        () => ({
            isLogin: currentPath === "/auth/login",
            isRegister: currentPath === "/auth/register",
            is2FA: currentPath === "/auth/2fa",
            isPasswordReset: currentPath === "/auth/password-reset",
            isConfirmPassword: currentPath === "/auth/confirm-password",
            isAuth: currentPath.startsWith("/auth"),
        }),
        [currentPath],
    );

    const redirectUrl = useMemo(() => {
        if (isLoading) return null;

        const authType = session?.auth_type;

        if (isAuthenticated) {
            if (authType === "MAIN" && pathInfo.isAuth) {
                return "/dashboard";
            } else if ((authType === "LOGIN_2FA" || authType === "VERIFICATION") && !pathInfo.is2FA) {
                return "/auth/2fa";
            }
        } else {
            if (pathInfo.is2FA) {
                return "/auth/login";
            }

            if (!pathInfo.isLogin && !pathInfo.isRegister && !pathInfo.isPasswordReset && !pathInfo.isConfirmPassword) {
                return pathInfo.isAuth ? "/auth/login" : "/auth/login?redirect=unauthenticated";
            }
        }

        return null;
    }, [isLoading, isAuthenticated, session?.auth_type, pathInfo]);

    useLayoutEffect(() => {
        if (redirectUrl) {
            router.replace(redirectUrl);
        }
    }, [redirectUrl, router]);

    if (isLoading || redirectUrl) {
        return (
            <div className="flex items-center justify-center min-h-screen bg-background">
                <div className="flex flex-col items-center space-y-6 p-3 bg-white rounded-full shadow-xl zoomInDown">
                    <Logo size="lg" />
                </div>
            </div>
        );
    }

    return node;
};

export default ProtectedRoute;
