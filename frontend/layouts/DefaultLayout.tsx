"use client";

import { ReactNode, useEffect } from "react";

import { AppSidebar } from "@/components/app-sidebar";
import { AppHeader } from "@/components/layout/AppHeader";
import { SidebarInset, SidebarProvider } from "@/components/ui/sidebar";
import { useAuth } from "@/composables/useStore";
import ProtectedRoute from "@/guards/ProtectedRoute";

interface Props {
    children: ReactNode;
}

export default function DefaultLayout({ children }: Props) {
    const { initializeAuth } = useAuth();

    useEffect(() => {
        initializeAuth();
    }, [initializeAuth]);

    return (
        <ProtectedRoute
            node={
                <div className="min-h-dvh w-full flex justify-center bg-background">
                    <div className="w-full max-w-[3840px] min-h-dvh app-container">
                        <SidebarProvider>
                            <AppSidebar className="sidebar-container" />
                            <SidebarInset className="flex-1 min-w-0">
                                <AppHeader />
                                <main className="flex flex-1 flex-col gap-4 p-4 pt-0 main-content overflow-x-hidden">
                                    <div className="w-full content-wrapper mx-auto">{children}</div>
                                </main>
                            </SidebarInset>
                        </SidebarProvider>
                    </div>
                </div>
            }
        />
    );
}
