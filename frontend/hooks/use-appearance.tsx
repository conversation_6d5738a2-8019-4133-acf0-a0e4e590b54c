import { useCallback, useEffect, useState } from "react";

export type Appearance = "light" | "dark" | "system";

const prefersDark = () => {
    if (typeof window === "undefined") {
        return false;
    }

    return window.matchMedia("(prefers-color-scheme: dark)").matches;
};

const setCookie = (name: string, value: string, days = 365) => {
    if (typeof document === "undefined") {
        return;
    }

    const maxAge = days * 24 * 60 * 60;

    document.cookie = `${name}=${value};path=/;max-age=${maxAge};SameSite=Lax`;
};

const applyTheme = (appearance: Appearance) => {
    const isDark = appearance === "dark" || (appearance === "system" && prefersDark());

    document.documentElement.classList.toggle("dark", isDark);
};

const mediaQuery = () => {
    if (typeof window === "undefined") {
        return null;
    }

    return window.matchMedia("(prefers-color-scheme: dark)");
};

export function initializeTheme() {
    const savedAppearance = (localStorage.getItem("appearance") as Appearance) || "system";

    applyTheme(savedAppearance);
}

export function useAppearance() {
    const [appearance, setAppearance] = useState<Appearance>("system");

    const updateAppearance = useCallback((mode: Appearance) => {
        setAppearance(mode);
        localStorage.setItem("appearance", mode);
        setCookie("appearance", mode);
        applyTheme(mode);
    }, []);

    useEffect(() => {
        // Get initial appearance from localStorage
        const savedAppearance = localStorage.getItem("appearance") as Appearance | null;
        const initialAppearance = savedAppearance || "system";

        setAppearance(initialAppearance);
        applyTheme(initialAppearance);

        // Set up system theme change listener
        const handleSystemThemeChange = () => {
            const currentAppearance = localStorage.getItem("appearance") as Appearance;

            // Only react to system changes if user preference is "system"
            if (currentAppearance === "system" || !currentAppearance) {
                applyTheme("system");
            }
        };

        const mq = mediaQuery();

        if (mq) {
            mq.addEventListener("change", handleSystemThemeChange);

            return () => mq.removeEventListener("change", handleSystemThemeChange);
        }
    }, []);

    return { appearance, updateAppearance } as const;
}
