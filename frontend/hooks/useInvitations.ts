import { useState, useCallback } from "react";
import { toast } from "sonner";

import { 
    ValidateInvitationTokenResponse,
    AcceptInvitationWithAccountRequest,
    MemberDto
} from "@/types";
import * as MembershipService from "@/services/MembershipService";

export const useInvitations = () => {
    const [isValidating, setIsValidating] = useState(false);
    const [isAccepting, setIsAccepting] = useState(false);

    const validateToken = useCallback(async (token: string): Promise<ValidateInvitationTokenResponse | null> => {
        setIsValidating(true);
        try {
            const response = await MembershipService.validateInvitationToken({ token });
            
            if (response.success && response.data?.valid) {
                return response.data;
            } else {
                toast.error("Invalid or expired invitation token");
                return null;
            }
        } catch (error) {
            toast.error("Failed to validate invitation token");
            return null;
        } finally {
            setIsValidating(false);
        }
    }, []);

    const acceptWithAccount = useCallback(async (
        token: string, 
        accountData: AcceptInvitationWithAccountRequest
    ): Promise<{ success: boolean; member?: MemberDto }> => {
        setIsAccepting(true);
        try {
            const response = await MembershipService.acceptInvitationWithAccount(token, accountData);

            if (response.success && response.data) {
                toast.success("Account created and invitation accepted successfully!");
                return { success: true, member: response.data };
            } else {
                response.errors?.forEach(error => toast.error(error.message));
                return { success: false };
            }
        } catch (error) {
            toast.error("Failed to accept invitation");
            return { success: false };
        } finally {
            setIsAccepting(false);
        }
    }, []);

    const acceptForExistingUser = useCallback(async (
        invitationCode: string,
        userId?: string
    ): Promise<{ success: boolean; member?: MemberDto }> => {
        setIsAccepting(true);
        try {
            const response = await MembershipService.acceptInvitation(invitationCode, { user_id: userId });

            if (response.success && response.data) {
                toast.success("Invitation accepted successfully!");
                return { success: true, member: response.data };
            } else {
                response.errors?.forEach(error => toast.error(error.message));
                return { success: false };
            }
        } catch (error) {
            toast.error("Failed to accept invitation");
            return { success: false };
        } finally {
            setIsAccepting(false);
        }
    }, []);

    return {
        isValidating,
        isAccepting,
        validateToken,
        acceptWithAccount,
        acceptForExistingUser,
    };
};