import { useState, useCallback } from "react";

import api from "@/config/api.config";
import http from "@/config/http";
import { httpResponse } from "@/utils/common";

export interface SystemConfigurationData {
    group: string;
    properties: Record<string, any>;
}

interface UseSystemConfigurationReturn {
    loading: boolean;
    updating: boolean;
    error: string | null;
    data: SystemConfigurationData;
    loadConfiguration: (group: string) => Promise<void>;
    updateConfiguration: (data: SystemConfigurationData) => Promise<void>;
}

export const useSystemConfiguration = (): UseSystemConfigurationReturn => {
    const [loading, setLoading] = useState(false);
    const [updating, setUpdating] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [data, setData] = useState<SystemConfigurationData>({} as SystemConfigurationData);

    const loadConfiguration = useCallback(async (group: string) => {
        try {
            setLoading(true);
            setError(null);

            const response = httpResponse(await http.get(api.v1.settings.system_configuration + `?group=${group}`));

            if (!response.success) {
                setError(response?.errors?.join(", ") || "Failed to load configuration");

                return;
            }
            setData(response.data as SystemConfigurationData);
        } catch (err) {
            setError(err instanceof Error ? err.message : "Failed to load system configuration");
        } finally {
            setLoading(false);
        }
    }, []);

    const updateConfiguration = useCallback(async (configData: SystemConfigurationData) => {
        try {
            setUpdating(true);
            setError(null);

            const response = httpResponse(await http.put(api.v1.settings.system_configuration, configData));

            if (!response.success) {
                setError(response?.errors?.join(", ") || "Failed to update configuration");

                return;
            }
            setData(response.data as SystemConfigurationData);
        } catch (err) {
            setError(err instanceof Error ? err.message : "Failed to update system configuration");
        } finally {
            setUpdating(false);
        }
    }, []);

    return {
        loading,
        updating,
        error,
        data,
        loadConfiguration,
        updateConfiguration,
    };
};
