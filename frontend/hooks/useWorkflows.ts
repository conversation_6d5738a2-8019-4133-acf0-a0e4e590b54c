import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

import * as workflowService from "@/services/WorkflowService";
import {
    TemplateFilter,
    TemplateRequest,
    TemplateStageFilter,
    TemplateStageRequest,
    TemplateStageRoleRequest,
    TemplateStageTriggerRequest,
} from "@/types";

// Template hooks
export function useWorkflowTemplates(filter: Partial<TemplateFilter> = {}) {
    return useQuery({
        queryKey: ["workflow-templates", filter],
        queryFn: async () => {
            const response = await workflowService.fetchTemplates(filter);

            if (response.errors && response.errors.length > 0) {
                for (const error of response.errors) {
                    toast.error(error.message);
                }

                return [];
            }
            return response.data || [];
        },
        staleTime: 5 * 60 * 1000,
        gcTime: 10 * 60 * 1000,
        retry: 2,
        refetchOnWindowFocus: false,
    });
}

export function useWorkflowTemplate(templateId: string) {
    return useQuery({
        queryKey: ["workflow-template", templateId],
        queryFn: async () => {
            const response = await workflowService.getTemplateById(templateId);

            if (response.errors && response.errors.length > 0) {
                for (const error of response.errors) {
                    toast.error(error.message);
                }

                return null;
            }
            return response.data;
        },
        enabled: !!templateId,
        staleTime: 5 * 60 * 1000,
        gcTime: 10 * 60 * 1000,
        retry: 2,
        refetchOnWindowFocus: false,
    });
}

export function useCreateTemplate() {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: (data: TemplateRequest) => workflowService.createTemplate(data),
        onSuccess: (response) => {
            if (response.errors && response.errors.length > 0) {
                for (const error of response.errors) {
                    toast.error(error.message);
                }

                return;
            }

            toast.success("Workflow template created successfully");
            queryClient.invalidateQueries({ queryKey: ["workflow-templates"] });
        },
        onError: () => {
            toast.error("Failed to create workflow template");
        },
    });
}

export function useUpdateTemplate() {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: ({ id, data }: { id: string; data: TemplateRequest }) =>
            workflowService.updateTemplate(id, data),
        onSuccess: (response, { id }) => {
            if (response.errors && response.errors.length > 0) {
                for (const error of response.errors) {
                    toast.error(error.message);
                }

                return;
            }

            toast.success("Workflow template updated successfully");
            queryClient.invalidateQueries({ queryKey: ["workflow-templates"] });
            queryClient.invalidateQueries({ queryKey: ["workflow-template", id] });
        },
        onError: () => {
            toast.error("Failed to update workflow template");
        },
    });
}

export function useDeleteTemplate() {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: ({ id, reason }: { id: string; reason: string }) =>
            workflowService.voidTemplate(id, reason),
        onSuccess: (response) => {
            if (response.errors && response.errors.length > 0) {
                for (const error of response.errors) {
                    toast.error(error.message);
                }

                return;
            }

            toast.success("Workflow template deleted successfully");
            queryClient.invalidateQueries({ queryKey: ["workflow-templates"] });
        },
        onError: () => {
            toast.error("Failed to delete workflow template");
        },
    });
}

// Template Stage hooks
export function useTemplateStages(templateId: string, filter: Partial<TemplateStageFilter> = {}) {
    return useQuery({
        queryKey: ["template-stages", templateId, filter],
        queryFn: async () => {
            const response = await workflowService.fetchTemplateStages(templateId, filter);

            if (response.errors && response.errors.length > 0) {
                for (const error of response.errors) {
                    toast.error(error.message);
                }

                return [];
            }
            return response.data || [];
        },
        enabled: !!templateId,
        staleTime: 5 * 60 * 1000,
        gcTime: 10 * 60 * 1000,
        retry: 2,
        refetchOnWindowFocus: false,
    });
}

export function useCreateTemplateStage() {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: ({ templateId, data }: { templateId: string; data: TemplateStageRequest }) =>
            workflowService.createTemplateStage(templateId, data),
        onSuccess: (response, { templateId }) => {
            if (response.errors && response.errors.length > 0) {
                for (const error of response.errors) {
                    toast.error(error.message);
                }

                return;
            }

            toast.success("Stage created successfully");
            queryClient.invalidateQueries({ queryKey: ["template-stages", templateId] });
            queryClient.invalidateQueries({ queryKey: ["workflow-template", templateId] });
        },
        onError: () => {
            toast.error("Failed to create stage");
        },
    });
}

export function useUpdateTemplateStage() {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: ({ 
            templateId, 
            stageId, 
            data 
        }: { 
      templateId: string; 
      stageId: string; 
      data: TemplateStageRequest 
        }) => workflowService.updateTemplateStage(templateId, stageId, data),
        onSuccess: (response, { templateId }) => {
            if (response.errors && response.errors.length > 0) {
                for (const error of response.errors) {
                    toast.error(error.message);
                }

                return;
            }

            toast.success("Stage updated successfully");
            queryClient.invalidateQueries({ queryKey: ["template-stages", templateId] });
            queryClient.invalidateQueries({ queryKey: ["workflow-template", templateId] });
        },
        onError: () => {
            toast.error("Failed to update stage");
        },
    });
}

export function useDeleteTemplateStage() {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: ({ 
            templateId, 
            stageId, 
            reason 
        }: { 
      templateId: string; 
      stageId: string; 
      reason: string 
            workflowService.voidTemplateStage(templateId, stageId, reason),
        onSuccess: (response, { templateId }) => {
            if (response.errors && response.errors.length > 0) {
                for (const error of response.errors) {
                    toast.error(error.message);
                }

                return;
            }

            toast.success("Stage deleted successfully");
            queryClient.invalidateQueries({ queryKey: ["template-stages", templateId] });
            queryClient.invalidateQueries({ queryKey: ["workflow-template", templateId] });
        },
        onError: () => {
            toast.error("Failed to delete stage");
        },
    });
}

// Template Stage Role hooks
export function useTemplateStageRoles(templateId: string, stageId: string) {
    return useQuery({
        queryKey: ["template-stage-roles", templateId, stageId],
        queryFn: async () => {
            const response = await workflowService.fetchTemplateStageRoles(templateId, stageId);

            if (response.errors && response.errors.length > 0) {
                for (const error of response.errors) {
                    toast.error(error.message);
                }

                return [];
            }
            return response.data || [];
        },
        enabled: !!templateId && !!stageId,
        staleTime: 5 * 60 * 1000,
        gcTime: 10 * 60 * 1000,
        retry: 2,
        refetchOnWindowFocus: false,
    });
}

export function useCreateTemplateStageRoles() {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: ({ 
            templateId, 
            stageId, 
            data 
        }: { 
      templateId: string; 
      stageId: string; 
      data: TemplateStageRoleRequest[] 
    }) => workflowService.createTemplateStageRoles(templateId, stageId, data),
        onSuccess: (response, { templateId, stageId }) => {
            if (response.errors && response.errors.length > 0) {
                for (const error of response.errors) {
                    toast.error(error.message);
                }

                return;
            }

            toast.success("Stage roles updated successfully");
            queryClient.invalidateQueries({ queryKey: ["template-stage-roles", templateId, stageId] });
            queryClient.invalidateQueries({ queryKey: ["template-stages", templateId] });
        },
        onError: () => {
            toast.error("Failed to update stage roles");
        },
    });
}

// Template Stage Trigger hooks
export function useTemplateStageTriggers(templateId: string, stageId: string) {
    return useQuery({
        queryKey: ["template-stage-triggers", templateId, stageId],
        queryFn: async () => {
            const response = await workflowService.fetchTemplateStageTriggers(templateId, stageId);

            if (response.errors && response.errors.length > 0) {
                for (const error of response.errors) {
                    toast.error(error.message);
                }

                return [];
            }
            return response.data || [];
        },
        enabled: !!templateId && !!stageId,
        staleTime: 5 * 60 * 1000,
        gcTime: 10 * 60 * 1000,
        retry: 2,
        refetchOnWindowFocus: false,
    });
}

export function useCreateTemplateStageTriggers() {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: ({ 
            templateId, 
            stageId, 
            data 
        }: { 
      templateId: string; 
      stageId: string; 
      data: TemplateStageTriggerRequest[] 
        }) => workflowService.createTemplateStageTriggers(templateId, stageId, data),
        onSuccess: (response, { templateId, stageId }) => {
            if (response.errors && response.errors.length > 0) {
                for (const error of response.errors) {
                    toast.error(error.message);
                }

                return;
            }

            toast.success("Stage triggers updated successfully");
            queryClient.invalidateQueries({ queryKey: ["template-stage-triggers", templateId, stageId] });
            queryClient.invalidateQueries({ queryKey: ["template-stages", templateId] });
        },
        onError: () => {
            toast.error("Failed to update stage triggers");
        },
    });
}
