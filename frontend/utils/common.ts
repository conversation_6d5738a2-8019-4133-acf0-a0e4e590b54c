import { AxiosResponse } from "axios";

import {
    AudioFileIcon,
    ExcelIcon,
    ImageFileIcon,
    Pdf2Icon,
    PowerPointIcon,
    TextSnippetIcon,
    VideocamIcon,
    WordIcon,
} from "@/components/icons";
import { HttpResponse } from "@/types";

export const capitalize = (str: string) => str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();

export function httpResponse<T>(response: AxiosResponse | any): HttpResponse<T> {
    if (!response) {
        return {
            data: undefined,
            success: false,
            errors: [],
            status: 500,
            total: 0,
            page: 0,
            size: 0,
        };
    }

    const hasDetail = response.data && Object.keys(response.data).includes("detail");
    const sourceData = hasDetail ? response.data.detail : response.data;

    return {
        data: sourceData?.data,
        success: sourceData?.success ?? false,
        errors: sourceData?.errors ?? [],
        status: response.status,
        total: sourceData?.total ?? 0,
        page: sourceData?.page ?? 0,
        size: sourceData?.size ?? 0,
    };
}

export function jsonToQueryParams(json: Record<string, any>): string {
    return Object.keys(json)
        .filter((key) => json[key] !== undefined && json[key] !== null)
        .map((key) => `${encodeURIComponent(key)}=${encodeURIComponent(json[key])}`)
        .join("&");
}

export function getTotalPages(totalCount: number, itemsPerPage: number): number {
    if (itemsPerPage <= 0) {
        throw new Error("itemsPerPage must be greater than 0");
    }

    return Math.ceil(totalCount / itemsPerPage);
}

export const formatFileSize = (bytes: number | string) => {
    if (typeof bytes === "string") {
        bytes = parseInt(bytes, 10);
    }

    if (isNaN(bytes) || bytes <= 0) return "0 Bytes";

    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB", "TB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
};

export const getFileIconAndColor = (filename: string) => {
    const anyDoc = {
        label: "Text Document",
        icon: TextSnippetIcon,
        color: "text-gray-400",
        bgColor: "bg-gray-50 dark:bg-gray-950/20",
    };

    if (!filename) return anyDoc;

    const extension = filename.split(".").pop()?.toLowerCase();

    switch (extension) {
        case "pdf":
            return {
                label: "PDF Document",
                icon: Pdf2Icon,
                color: "text-red-500",
                bgColor: "bg-red-50 dark:bg-red-950/20",
            };
        case "doc":
        case "docx":
            return {
                label: "Word Document",
                icon: WordIcon,
                color: "text-blue-500",
                bgColor: "bg-blue-50 dark:bg-blue-950/20",
            };
        case "xls":
        case "xlsx":
        case "csv":
            return {
                label: "Excel Document",
                icon: ExcelIcon,
                color: "text-green-500",
                bgColor: "bg-green-50 dark:bg-green-950/20",
            };
        case "ppt":
        case "pptx":
            return {
                label: "Powerpoint Presentation",
                icon: PowerPointIcon,
                color: "text-orange-500",
                bgColor: "bg-orange-50 dark:bg-orange-950/20",
            };
        case "jpg":
        case "jpeg":
        case "png":
        case "gif":
        case "webp":
        case "svg":
            return {
                label: "Image",
                icon: ImageFileIcon,
                color: "text-purple-500",
                bgColor: "bg-purple-50 dark:bg-purple-950/20",
            };
        case "mp4":
        case "avi":
        case "mov":
        case "wmv":
        case "flv":
        case "webm":
            return {
                label: "Video",
                icon: VideocamIcon,
                color: "text-pink-500",
                bgColor: "bg-pink-50 dark:bg-pink-950/20",
            };
        case "mp3":
        case "wav":
        case "flac":
        case "aac":
            return {
                label: "Audio",
                icon: AudioFileIcon,
                color: "text-indigo-500",
                bgColor: "bg-indigo-50 dark:bg-indigo-950/20",
            };
        case "txt":
        case "rtf":
        case "md":
            return {
                label: "Text Document",
                icon: TextSnippetIcon,
                color: "text-gray-500",
                bgColor: "bg-gray-50 dark:bg-gray-950/20",
            };
        default:
            return anyDoc;
    }
};

export const getFileTypeLabel = (mimeType: string) => {
    if (mimeType.includes("pdf")) return "PDF Document";
    if (mimeType.includes("word") || mimeType.includes("document")) return "Word Document";
    if (mimeType.includes("spreadsheet") || mimeType.includes("excel")) return "Excel Spreadsheet";
    if (mimeType.includes("presentation") || mimeType.includes("powerpoint")) return "PowerPoint Presentation";
    if (mimeType.includes("image/jpeg")) return "JPEG Image";
    if (mimeType.includes("image/png")) return "PNG Image";
    if (mimeType.includes("text")) return "Text File";
    if (mimeType.includes("csv")) return "CSV File";

    return "Document";
};

export const createFile = (dataUrl: string, filename: string, fileType: string): File => {
    const blob = dataURItoBlob(dataUrl, fileType);

    return new File([blob], filename);
};

export const dataURItoBlob = (dataURI: string, fileType: string): Blob => {
    const base64Data = dataURI.split(",")[1];

    const byteCharacters = atob(base64Data);
    const byteNumbers = new Array(byteCharacters.length);

    for (let i = 0; i < byteCharacters.length; i++) {
        byteNumbers[i] = byteCharacters.charCodeAt(i);
    }

    const byteArray = new Uint8Array(byteNumbers);

    return new Blob([byteArray], { type: fileType });
};

export const base64ToBlob = (base64: string, mimeType: string): Blob => {
    const binaryString = window.atob(base64);
    const bytes = new Uint8Array(binaryString.length);

    for (let i = 0; i < binaryString.length; i++) {
        bytes[i] = binaryString.charCodeAt(i);
    }

    return new Blob([bytes], { type: mimeType });
};

export const downloadFile = (blob: Blob, filename: string): void => {
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");

    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
};

export const blobToDataUrl = async (blob: Blob | string): Promise<string> => {
    if (blob instanceof Blob) {
        return new Promise<string>((resolve, reject) => {
            const reader = new FileReader();

            reader.onload = () => resolve(reader.result as string);
            reader.onerror = reject;
            reader.readAsDataURL(blob);
        });
    }

    if (typeof blob === "string" && blob.startsWith("blob:")) {
        const fetchedBlob = await fetch(blob).then((response) => response.blob());

        return blobToDataUrl(fetchedBlob);
    }

    throw new Error("Invalid input type. Expected Blob or Blob URL.");
};
