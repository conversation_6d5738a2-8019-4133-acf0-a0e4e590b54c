"use client";

import React from "react";
import { useState, useMemo } from "react";

import {
    <PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>eader,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON>Footer,
    DialogDescription,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";
import { AuditDto } from "@/types/audit.dto";

type Props = {
    open: boolean;
    onOpenChange: (v: boolean) => void;
    payload: AuditDto | null; // audit payload with old/new
};

function isObject(v: any) {
    return v && typeof v === "object" && !Array.isArray(v);
}

function flatten(obj: any, prefix = "") {
    const res: Record<string, any> = {};

    if (!isObject(obj)) return { [prefix || "root"]: obj };
    for (const k of Object.keys(obj)) {
        const val = obj[k];
        const key = prefix ? `${prefix}.${k}` : k;

        if (isObject(val)) Object.assign(res, flatten(val, key));
        else res[key] = val;
    }

    return res;
}

export function AuditDiffDialog({ open, onOpenChange, payload }: Props) {
    // Support multiple audit shapes. If payload looks like the whole audit record,
    // it may contain old_values/new_values (JSON strings) or old/new fields.
    const parse = (p: any) => {
        if (!p) return { old_values: {}, new_values: {} };
        // case: audit record with old_values/new_values
        if (p.hasOwnProperty("old_values") || p.hasOwnProperty("new_values")) {
            let old = p.old_values;
            let newly = p.new_values;

            try {
                old = old ? JSON.parse(old) : {};
            } catch (e) {
                old = old;
            }
            try {
                newly = newly ? JSON.parse(newly) : {};
            } catch (e) {
                newly = newly;
            }

            return { old_values: old || {}, new_values: newly || {} };
        }

        // case: nested payload like { old: {...}, new: {...} }
        if (p.old_values || p.new_values) return { old_values: p.old_values || {}, new_values: p.new_values || {} };

        // otherwise assume p itself is the payload
        return { old_values: p || {}, new_values: {} };
    };

    const parsed = parse(payload);
    const oldObj = parsed.old_values;
    const newObj = parsed.new_values;

    const [showOnlyChanged, setShowOnlyChanged] = useState(false);

    const left = flatten(oldObj);
    const right = flatten(newObj);

    const allKeys = useMemo(
        () => Array.from(new Set([...Object.keys(left), ...Object.keys(right)])).sort(),
        [left, right],
    );

    const changedKeys = allKeys.filter((k) => String(left[k] ?? "") !== String(right[k] ?? ""));

    return (
        <Dialog open={open} onOpenChange={onOpenChange}>
            <DialogContent className="max-w-full w-[50%]">
                <DialogHeader>
                    <div className="flex items-start justify-between w-full">
                        <div>
                            <DialogTitle>Change details</DialogTitle>
                            <DialogDescription className="py-6">
                                {payload?.table_name && (
                                    <span className="mr-2">
                                        Table: <strong>{payload.table_name}</strong>
                                    </span>
                                )}
                                {payload?.action && (
                                    <span className="mr-2">
                                        Action: <strong>{payload.action}</strong>
                                    </span>
                                )}
                                {payload?.record_id && (
                                    <span className="text-muted-foreground">ID: {payload.record_id}</span>
                                )}
                            </DialogDescription>
                        </div>
                    </div>
                </DialogHeader>

                <div className="grid grid-cols-12 gap-4">
                    <div className="col-span-5">
                        <div className="flex items-center justify-between mb-2">
                            <h4 className="text-sm font-semibold">Old</h4>
                            <Badge variant="secondary">Before</Badge>
                        </div>
                        <div className="space-y-2 divide-y rounded border bg-muted/20 p-3 max-h-[60vh] overflow-auto">
                            {allKeys
                                .filter((k) => !showOnlyChanged || changedKeys.includes(k))
                                .map((k) => (
                                    <div key={k} className={cn("py-2 text-sm")}>
                                        <div className="text-xs text-muted-foreground">{k}</div>
                                        <div className="font-medium break-words">{String(left[k] ?? "—")}</div>
                                    </div>
                                ))}
                        </div>
                    </div>

                    <div className="col-span-2 flex items-center justify-center">
                        <div className="flex flex-col items-center text-sm text-muted-foreground">
                            <span className="text-xl font-semibold">→</span>
                            <span className="mt-2">Diff</span>
                        </div>
                    </div>

                    <div className="col-span-5">
                        <div className="flex items-center justify-between mb-2">
                            <h4 className="text-sm font-semibold">New</h4>
                            <Badge>After</Badge>
                        </div>
                        <div className="space-y-2 divide-y rounded border bg-muted/20 p-3 max-h-[60vh] overflow-auto">
                            {allKeys.map((k) => (
                                <div key={k} className={cn("py-2 text-sm")}>
                                    <div className="text-xs text-muted-foreground">{k}</div>
                                    <div className="font-medium break-words">{String(right[k] ?? "—")}</div>
                                </div>
                            ))}
                        </div>
                    </div>
                </div>

                <DialogFooter className="mt-4">
                    <Button variant="ghost" onClick={() => onOpenChange(false)}>
                        Close
                    </Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    );
}

export default AuditDiffDialog;
