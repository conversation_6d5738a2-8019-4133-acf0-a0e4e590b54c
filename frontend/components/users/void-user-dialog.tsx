import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { UserDto } from "@/types";

interface VoidUserDialogProps {
    open: boolean;
    onClose: () => void;
    onConfirm: () => void;
    user: UserDto | null;
    isLoading: boolean;
}

export default function VoidUserDialog({ open, onClose, onConfirm, user, isLoading }: VoidUserDialogProps) {
    const formatName = (user: UserDto) => {
        const parts = [user.first_name, user.middle_name, user.last_name].filter(Boolean);

        return parts.join(" ") || "N/A";
    };

    return (
        <AlertDialog open={open} onOpenChange={onClose}>
            <AlertDialogContent>
                <AlertDialogHeader>
                    <AlertDialogTitle>Delete User</AlertDialogTitle>
                    <AlertDialogDescription>
                        Are you sure you want to delete{" "}
                        <span className="font-semibold">{user ? formatName(user) : "this user"}</span>? This action
                        cannot be undone and will permanently remove the user from the system.
                    </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                    <AlertDialogCancel disabled={isLoading} onClick={onClose}>
                        Cancel
                    </AlertDialogCancel>
                    <AlertDialogAction
                        className="bg-red-600 hover:bg-red-700 focus:ring-red-600"
                        disabled={isLoading}
                        onClick={onConfirm}
                    >
                        {isLoading ? "Deleting..." : "Delete User"}
                    </AlertDialogAction>
                </AlertDialogFooter>
            </AlertDialogContent>
        </AlertDialog>
    );
}
