import { useState } from "react";
import { z } from "zod";

import { RadioGroup } from "../ui/radio-group";

import { ManagerInput } from "@/components/inputs/manager";
import { But<PERSON> } from "@/components/ui/button";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Gender, UserDto } from "@/types";

interface UserFormData {
    first_name: string;
    last_name: string;
    middle_name?: string;
    handle: string;
    email: string;
    phone?: string;
    password?: string;
    role_id?: string;
    department_id?: string;
    is_external: boolean;
    gender: "MALE" | "FEMALE" | "NONE";
}

interface UserFormDialogProps {
    open: boolean;
    onClose: () => void;
    onSubmit: (formData: UserFormData) => void;
    user: UserDto | null;
    formMode: "create" | "edit";
    isLoading: boolean;
}

const userSchema = z.object({
    first_name: z.string().min(1, "First name is required"),
    last_name: z.string().min(1, "Last name is required"),
    middle_name: z.string().optional(),
    handle: z.string().min(1, "Username is required"),
    email: z.string().email("Invalid email address"),
    phone: z.string().optional(),
    password: z.string().min(6, "Password must be at least 6 characters").optional(),
    role_id: z.string().optional(),
    department_id: z.string().optional(),
    is_external: z.boolean(),
    gender: z.enum(["FEMALE", "MALE"]),
});

export default function UserFormDialog({ open, onClose, onSubmit, user, formMode, isLoading }: UserFormDialogProps) {
    const [formData, setFormData] = useState<UserFormData>(() => ({
        first_name: user?.first_name || "Nejat",
        last_name: user?.last_name || "Mhango",
        middle_name: user?.middle_name || "",
        handle: user?.handle || "rootoer",
        email: user?.email || "<EMAIL>",
        phone: user?.phone || "",
        password: "Admin@123",
        role_id: user?.role_id || "",
        department_id: user?.department_id || "",
        is_external: user?.is_external || false,
        gender: user?.gender as Gender,
    }));

    const [errors, setErrors] = useState<Record<string, string>>({});

    const validateForm = () => {
        try {
            const validationData = { ...formData };

            if (formMode === "edit") {
                delete validationData.password;
            }
            userSchema.parse(validationData);
            setErrors({});

            return true;
        } catch (error) {
            console.log(error);
            if (error instanceof z.ZodError) {
                const formattedErrors: Record<string, string> = {};

                error.errors.forEach((err) => {
                    if (err.path[0]) {
                        formattedErrors[err.path[0].toString()] = err.message;
                    }
                });
                setErrors(formattedErrors);
            }

            return false;
        }
    };

    const handleChange = (field: keyof UserFormData, value: string | boolean | null) => {
        setFormData((prev) => ({ ...prev, [field]: value }));
        if (errors[field]) {
            setErrors((prev) => ({ ...prev, [field]: "" }));
        }
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        if (validateForm()) {
            onSubmit(formData);
        }
    };

    const resetForm = () => {
        setFormData({
            first_name: "",
            last_name: "",
            middle_name: "",
            handle: "",
            email: "",
            phone: "",
            password: "",
            role_id: "",
            department_id: "",
            is_external: false,
            gender: "NONE",
        });
        setErrors({});
    };

    const handleClose = () => {
        resetForm();
        onClose();
    };

    return (
        <Dialog open={open} onOpenChange={handleClose}>
            <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
                <DialogHeader>
                    <DialogTitle>{formMode === "create" ? "Add New User" : "Edit User"}</DialogTitle>
                    <DialogDescription>
                        {formMode === "create"
                            ? "Create a new user account with the required information."
                            : "Update the user's information below."}
                    </DialogDescription>
                </DialogHeader>

                <form className="space-y-4" onSubmit={handleSubmit}>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <Input
                            error={errors.first_name}
                            id="first_name"
                            label="First Name *"
                            value={formData.first_name}
                            onChange={(e) => handleChange("first_name", e.target.value)}
                        />

                        <Input
                            className={errors.last_name ? "border-red-500" : ""}
                            error={errors.last_name}
                            id="last_name"
                            label="Last Name *"
                            value={formData.last_name}
                            onChange={(e) => handleChange("last_name", e.target.value)}
                        />
                    </div>

                    <Input
                        id="middle_name"
                        label="Middle Name"
                        value={formData.middle_name}
                        onChange={(e) => handleChange("middle_name", e.target.value)}
                    />

                    <RadioGroup
                        label="Gender"
                        options={[
                            { value: "MALE", label: "Male" },
                            { value: "FEMALE", label: "Female" },
                        ]}
                        orientation="horizontal"
                        value={formData.gender}
                        onValueChange={(value) => handleChange("gender", value)}
                    />

                    <Input
                        error={errors.username}
                        id="username"
                        label="Username *"
                        value={formData.handle}
                        onChange={(e) => handleChange("handle", e.target.value)}
                    />

                    <Input
                        error={errors.email}
                        id="email"
                        label="Email *"
                        type="email"
                        value={formData.email}
                        onChange={(e) => handleChange("email", e.target.value)}
                    />

                    <div>
                        <Label htmlFor="phone">Phone</Label>
                        <Input
                            id="phone"
                            type="tel"
                            value={formData.phone}
                            onChange={(e) => handleChange("phone", e.target.value)}
                        />
                    </div>

                    {formMode === "create" && (
                        <Input
                            className={errors.password ? "border-red-500" : ""}
                            error={errors.password}
                            id="password"
                            label="Password *"
                            type="password"
                            value={formData.password}
                            onChange={(e) => handleChange("password", e.target.value)}
                        />
                    )}

                    <div className="flex items-center space-x-2">
                        <Switch
                            checked={formData.is_external}
                            id="is_external"
                            onCheckedChange={(checked) => handleChange("is_external", checked)}
                        />
                        <Label htmlFor="is_external">Is Internal User</Label>
                    </div>

                    {formData.is_external && (
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <ManagerInput
                                type="department"
                                value={formData.department_id}
                                onSelectionChange={(value) => handleChange("department_id", value)}
                            />
                            <ManagerInput
                                type="role"
                                value={formData.role_id}
                                onSelectionChange={(value) => handleChange("role_id", value)}
                            />
                        </div>
                    )}

                    <div className="flex justify-end gap-2 pt-4">
                        <Button type="button" variant="outline" onClick={handleClose}>
                            Cancel
                        </Button>
                        <Button disabled={isLoading} type="submit">
                            {isLoading
                                ? formMode === "create"
                                    ? "Creating..."
                                    : "Updating..."
                                : formMode === "create"
                                  ? "Create User"
                                  : "Update User"}
                        </Button>
                    </div>
                </form>
            </DialogContent>
        </Dialog>
    );
}
