"use client";

import { CalendarIcon } from "lucide-react";
import * as React from "react";

import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import { Label } from "@/components/ui/label";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { cn } from "@/lib/utils";

interface DatePickerProps {
    label?: string;
    placeholder?: string;
    value?: string;
    onChange?: (date: string | undefined) => void;
    isRequired?: boolean;
    isDisabled?: boolean;
    className?: string;
    id?: string;
}

export function DatePicker({
    label,
    placeholder = "Pick a date",
    value,
    onChange,
    isRequired = false,
    isDisabled = false,
    className,
    id,
    ...props
}: React.ComponentProps<typeof Calendar> & DatePickerProps) {
    const [open, setOpen] = React.useState(false);

    // Parse the date value properly
    const dateValue = React.useMemo(() => {
        if (!value) return undefined;
        try {
            // Handle both ISO date strings and regular date formats
            const parsed = new Date(value);
            return isNaN(parsed.getTime()) ? undefined : parsed;
        } catch {
            return undefined;
        }
    }, [value]);

    const handleSelect = (selectedDate: Date | undefined) => {
        if (selectedDate) {
            // Format date as YYYY-MM-DD using local timezone to avoid timezone shifts
            const year = selectedDate.getFullYear();
            const month = String(selectedDate.getMonth() + 1).padStart(2, '0');
            const day = String(selectedDate.getDate()).padStart(2, '0');
            const formattedDate = `${year}-${month}-${day}`;
            onChange?.(formattedDate);
        } else {
            onChange?.(undefined);
        }
        setOpen(false); // Close popover after selection
    };

    const formatDisplayDate = (date: Date) => {
        return date.toLocaleDateString(undefined, {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        });
    };

    return (
        <div className={cn("space-y-2", className)}>
            {label && (
                <Label
                    htmlFor={id}
                    className={cn(
                        "text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",
                        isRequired && "after:content-['*'] after:ml-0.5 after:text-destructive"
                    )}
                >
                    {label}
                </Label>
            )}
            <Popover open={open} onOpenChange={setOpen}>
                <PopoverTrigger asChild>
                    <Button
                        id={id}
                        variant="outline"
                        className={cn(
                            "w-full justify-start text-left font-normal",
                            !dateValue && "text-muted-foreground",
                            isDisabled && "cursor-not-allowed opacity-50"
                        )}
                        disabled={isDisabled}
                    >
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {dateValue ? (
                            formatDisplayDate(dateValue)
                        ) : (
                            <span>{placeholder}</span>
                        )}
                    </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                        mode="single"
                        selected={dateValue}
                        onSelect={handleSelect}
                        initialFocus
                        disabled={props?.disabled}
                    />
                </PopoverContent>
            </Popover>
        </div>
    );
}