import {
    ColumnDef,
    ColumnFiltersState,
    flexRender,
    getCoreRowModel,
    getFilteredRowModel,
    getPaginationRowModel,
    getSortedRowModel,
    PaginationState,
    SortingState,
    useReactTable,
} from "@tanstack/react-table";
import { ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight } from "lucide-react";
import React from "react";

import { Skeleton } from "./skeleton";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select } from "@/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";

interface DataTableProps<TData, TValue> {
    columns: ColumnDef<TData, TValue>[];
    data: TData[];
    searchKey?: string;
    searchPlaceholder?: string;
    enableSorting?: boolean;
    enableFiltering?: boolean;
    enablePagination?: boolean;
    pageSize?: number;
    className?: string;
    loading: boolean;
    // server-side pagination (optional) - when provided, table will be controlled

    serverPagination?: {
        pageSize: number;
        pageIndex: number; // zero-based
        total: number; // total number of rows available on server
        onPageChange: (pageIndex: number) => void;
        onPageSizeChange: (pageSize: number) => void;
    };
}

export function DataTable<TData, TValue>({
    columns,
    data,
    searchKey,
    searchPlaceholder = "Search...",
    enableSorting = true,
    enableFiltering = true,
    enablePagination = true,
    pageSize = 10,
    className,
    serverPagination,
    loading,
}: DataTableProps<TData, TValue>) {
    const [sorting, setSorting] = React.useState<SortingState>([]);
    const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>([]);
    const [pagination, setPagination] = React.useState<PaginationState>(() => ({
        pageIndex: serverPagination?.pageIndex ?? 0,
        pageSize,
    }));

    const pageCount =
        typeof serverPagination?.total === "number" && serverPagination?.pageSize > 0
            ? Math.max(1, Math.ceil(serverPagination?.total / (serverPagination?.pageSize || 1)))
            : undefined;

    const table = useReactTable({
        data,
        columns,
        getCoreRowModel: getCoreRowModel(),
        ...(enableSorting && {
            onSortingChange: setSorting,
            getSortedRowModel: getSortedRowModel(),
        }),
        ...(enableFiltering && {
            onColumnFiltersChange: setColumnFilters,
            getFilteredRowModel: getFilteredRowModel(),
        }),
        ...(enablePagination && {
            onPaginationChange: (updater) => {
                // keep internal state for uncontrolled mode
                setPagination((prev) => {
                    const next = typeof updater === "function" ? updater(prev) : updater;

                    return next;
                });

                // propagate to parent when controlled
                if (typeof serverPagination?.pageIndex === "number" && serverPagination?.onPageChange) {
                    serverPagination.onPageChange(
                        typeof updater === "function"
                            ? updater({ pageIndex: serverPagination.pageIndex, pageSize }).pageIndex
                            : updater.pageIndex,
                    );
                }

                if (typeof serverPagination?.pageSize === "number" && serverPagination?.onPageSizeChange) {
                    const nextPageSize =
                        typeof updater === "function"
                            ? updater({
                                pageIndex: serverPagination.pageIndex ?? 0,
                                pageSize: serverPagination.pageSize,
                            }).pageSize
                            : updater.pageSize;

                    serverPagination.onPageSizeChange(nextPageSize);
                }
            },
            getPaginationRowModel: getPaginationRowModel(),
        }),
        state: {
            ...(enableSorting && { sorting }),
            ...(enableFiltering && { columnFilters }),
            ...(enablePagination && {
                pagination: {
                    pageIndex: serverPagination?.pageIndex ?? pagination.pageIndex,
                    pageSize: pagination.pageSize,
                },
            }),
        },
        manualPagination: true,
        pageCount,
    });

    // keep internal pagination in-sync when controlled
    React.useEffect(() => {
        setPagination((prev) => ({ ...prev, pageIndex: serverPagination?.pageIndex ?? prev.pageIndex, pageSize }));
    }, [serverPagination]);

    const searchValue = searchKey ? ((table.getColumn(searchKey)?.getFilterValue() as string) ?? "") : "";

    const handleSearchChange = (value: string) => {
        if (searchKey) {
            table.getColumn(searchKey)?.setFilterValue(value);
        }
    };

    return (
        <div className={className}>
            {/* Search Input */}
            {enableFiltering && searchKey && (
                <div className="flex items-center py-4">
                    <Input
                        className="max-w-sm"
                        placeholder={searchPlaceholder}
                        value={searchValue}
                        onChange={(event) => handleSearchChange(event.target.value)}
                    />
                </div>
            )}

            {/* Table */}
            <div className="rounded-md border">
                <Table>
                    <TableHeader>
                        {table.getHeaderGroups().map((headerGroup) => (
                            <TableRow key={headerGroup.id}>
                                {headerGroup.headers.map((header) => (
                                    <TableHead key={header.id}>
                                        {header.isPlaceholder
                                            ? null
                                            : flexRender(header.column.columnDef.header, header.getContext())}
                                    </TableHead>
                                ))}
                            </TableRow>
                        ))}
                    </TableHeader>
                    <TableBody>
                        {loading ? (
                            Array.from({ length: 10 }).map((_, index) => (
                                <TableRow key={index}>
                                    {table.getAllColumns().map((column) => (
                                        <TableCell key={column.id}>
                                            <Skeleton className="h-5 rounded-md bg-gray-600" />
                                        </TableCell>
                                    ))}
                                </TableRow>
                            ))
                        ) : table.getRowModel().rows?.length ? (
                            table.getRowModel().rows.map((row) => (
                                <TableRow key={row.id} data-state={row.getIsSelected() && "selected"}>
                                    {row.getVisibleCells().map((cell) => (
                                        <TableCell key={cell.id}>
                                            {flexRender(cell.column.columnDef.cell, cell.getContext())}
                                        </TableCell>
                                    ))}
                                </TableRow>
                            ))
                        ) : (
                            <TableRow>
                                <TableCell className="h-24 text-center" colSpan={columns.length}>
                                    No results.
                                </TableCell>
                            </TableRow>
                        )}
                    </TableBody>
                </Table>
            </div>

            {/* Pagination */}
            {enablePagination && (
                <div className="flex items-center justify-between space-x-2 py-4">
                    <div className="flex items-center space-x-2">
                        <Select
                            label="Rows per page"
                            options={[10, 20, 30, 40, 50].map((pageSize) => ({
                                value: pageSize.toString(),
                                label: pageSize.toString(),
                            }))}
                            value={pagination.pageSize.toString()}
                            onValueChange={(value) => {
                                const newSize = Number(value);

                                if (serverPagination?.onPageSizeChange) {
                                    serverPagination?.onPageSizeChange(newSize);
                                } else {
                                    setPagination((prev) => ({ ...prev, pageSize: newSize }));
                                }
                            }}
                        />
                    </div>
                    <div className="flex items-center space-x-6 lg:space-x-8">
                        <div className="flex w-[100px] items-center justify-center text-sm font-medium">
                            Page {table.getState().pagination.pageIndex + 1} of {pageCount}
                        </div>
                        <div className="flex items-center space-x-2">
                            <Button
                                className="hidden h-8 w-8 p-0 lg:flex"
                                disabled={!table.getCanPreviousPage()}
                                variant="outline"
                                onClick={() => {
                                    if (serverPagination?.onPageChange) {
                                        serverPagination?.onPageChange(0);
                                    } else {
                                        table.setPageIndex(0);
                                    }
                                }}
                            >
                                <span className="sr-only">Go to first page</span>
                                <ChevronsLeft className="h-4 w-4" />
                            </Button>
                            <Button
                                className="h-8 w-8 p-0"
                                disabled={!table.getCanPreviousPage()}
                                variant="outline"
                                onClick={() => {
                                    const prev = Math.max(0, table.getState().pagination.pageIndex - 1);

                                    if (serverPagination?.onPageChange) {
                                        serverPagination?.onPageChange(prev);
                                    } else {
                                        table.previousPage();
                                    }
                                }}
                            >
                                <span className="sr-only">Go to previous page</span>
                                <ChevronLeft className="h-4 w-4" />
                            </Button>
                            <Button
                                className="h-8 w-8 p-0"
                                disabled={!table.getCanNextPage()}
                                variant="outline"
                                onClick={() => {
                                    const next = Math.min(
                                        table.getPageCount() - 1,
                                        table.getState().pagination.pageIndex + 1,
                                    );

                                    if (serverPagination?.onPageChange) {
                                        serverPagination?.onPageChange(next);
                                    } else {
                                        table.nextPage();
                                    }
                                }}
                            >
                                <span className="sr-only">Go to next page</span>
                                <ChevronRight className="h-4 w-4" />
                            </Button>
                            <Button
                                className="hidden h-8 w-8 p-0 lg:flex"
                                disabled={!table.getCanNextPage()}
                                variant="outline"
                                onClick={() => {
                                    const last = table.getPageCount() - 1;

                                    if (serverPagination?.onPageChange) {
                                        serverPagination?.onPageChange(last);
                                    } else {
                                        table.setPageIndex(last);
                                    }
                                }}
                            >
                                <span className="sr-only">Go to last page</span>
                                <ChevronsRight className="h-4 w-4" />
                            </Button>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
}
