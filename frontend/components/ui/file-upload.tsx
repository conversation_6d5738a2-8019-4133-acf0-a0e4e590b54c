"use client";

import { Eye, Upload, X } from "lucide-react";
import * as React from "react";

import { But<PERSON> } from "./button";

import { cn } from "@/lib/utils";
import { formatFileSize, getFileIconAndColor } from "@/utils/common";

export interface FileUploadProps
    extends Omit<React.InputHTMLAttributes<HTMLInputElement>, "type" | "value" | "onChange"> {
    label?: string;
    labelRight?: React.ReactNode;
    error?: string;
    containerClassName?: string;
    labelClassName?: string;
    labelContainerClassName?: string;
    errorClassName?: string;
    files?: File[];
    onFilesChange?: (files: File[]) => void;
    maxFiles?: number;
    maxSize?: number; // in bytes
    acceptedFileTypes?: string[];
    showPreview?: boolean;
    allowMultiple?: boolean;
}

const FileUpload = React.forwardRef<HTMLInputElement, FileUploadProps>(
    (
        {
            className,
            label,
            labelRight,
            error,
            containerClassName,
            labelClassName,
            labelContainerClassName,
            errorClassName,
            id,
            files = [],
            onFilesChange,
            maxFiles = 10,
            maxSize = 10 * 1024 * 1024,
            acceptedFileTypes,
            showPreview = true,
            allowMultiple = true,
            disabled,
            ...props
        },
        ref,
    ) => {
        const [dragActive, setDragActive] = React.useState(false);
        const inputRef = React.useRef<HTMLInputElement>(null);
        const hasError = Boolean(error);

        React.useImperativeHandle(ref, () => inputRef.current!);

        const inputId = id || (label ? `file-upload-${Math.random().toString(36).substr(2, 9)}` : undefined);

        const validateFile = (file: File): string | null => {
            if (maxSize && file.size > maxSize) {
                return `File size exceeds ${formatFileSize(maxSize)}`;
            }

            if (acceptedFileTypes && acceptedFileTypes.length > 0) {
                const fileExtension = file.name.split(".").pop()?.toLowerCase();
                const isAccepted = acceptedFileTypes.some((type) => {
                    if (type.startsWith(".")) {
                        // Handle file extensions like .pdf, .doc
                        return type.slice(1) === fileExtension;
                    }

                    if (type.includes("/*")) {
                        // Handle wildcard MIME types like image/*, video/*
                        const baseType = type.split("/")[0];
                        return file.type.startsWith(baseType + "/");
                    }

                    // Handle specific MIME types like image/png, application/pdf
                    return file.type === type || file.type.includes(type);
                });

                if (!isAccepted) {
                    return `File type not accepted. Accepted types: ${acceptedFileTypes.join(", ")}`;
                }
            }

            return null;
        };

        const handleFiles = (newFiles: FileList) => {
            const validFiles: File[] = [];
            const errors: string[] = [];

            Array.from(newFiles).forEach((file) => {
                const error = validateFile(file);

                if (error) {
                    errors.push(`${file.name}: ${error}`);
                } else {
                    validFiles.push(file);
                }
            });

            if (validFiles.length > 0) {
                let updatedFiles = allowMultiple ? [...files, ...validFiles] : validFiles;

                if (maxFiles && updatedFiles.length > maxFiles) {
                    updatedFiles = updatedFiles.slice(0, maxFiles);
                }

                onFilesChange?.(updatedFiles);
            }

            if (errors.length > 0) {
                console.warn("File upload errors:", errors);
            }
        };

        const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
            if (e.target.files && e.target.files.length > 0) {
                handleFiles(e.target.files);
            }
        };

        const handleDrop = (e: React.DragEvent) => {
            e.preventDefault();
            e.stopPropagation();
            setDragActive(false);

            if (disabled) return;

            if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
                handleFiles(e.dataTransfer.files);
            }
        };

        const handleDragOver = (e: React.DragEvent) => {
            e.preventDefault();
            e.stopPropagation();
            if (!disabled) {
                setDragActive(true);
            }
        };

        const handleDragLeave = (e: React.DragEvent) => {
            e.preventDefault();
            e.stopPropagation();
            setDragActive(false);
        };

        const removeFile = (index: number) => {
            const newFiles = files.filter((_, i) => i !== index);

            onFilesChange?.(newFiles);
        };

        const openFileDialog = () => {
            if (!disabled) {
                inputRef.current?.click();
            }
        };

        const previewFile = (file: File) => {
            if (file.type.startsWith("image/")) {
                const url = URL.createObjectURL(file);

                window.open(url, "_blank");
                setTimeout(() => URL.revokeObjectURL(url), 100);
            }
        };

        return (
            <div className={cn("w-full", containerClassName)}>
                {/* Label */}
                {label && (
                    <div className={cn("flex items-center justify-between mb-1", labelContainerClassName)}>
                        <label className={cn("block text-sm font-medium", labelClassName)} htmlFor={inputId}>
                            {label}
                        </label>
                        {labelRight && <div className="text-sm">{labelRight}</div>}
                    </div>
                )}

                {/* Upload Area */}
                <div
                    className={cn(
                        "relative w-full min-h-32 border-2 border-dashed rounded-lg transition-colors",
                        "flex flex-col items-center justify-center gap-2 p-6 cursor-pointer",
                        "hover:border-primary/50 hover:bg-accent/50",
                        dragActive && "border-primary bg-accent/70",
                        hasError && "border-destructive bg-destructive/5",
                        disabled && "cursor-not-allowed opacity-50",
                        className,
                    )}
                    role="button"
                    tabIndex={0}
                    onClick={openFileDialog}
                    onDragLeave={handleDragLeave}
                    onDragOver={handleDragOver}
                    onDrop={handleDrop}
                    onKeyDown={() => ({})}
                >
                    <input
                        ref={inputRef}
                        accept={acceptedFileTypes?.join(",")}
                        aria-describedby={hasError && inputId ? `${inputId}-error` : undefined}
                        aria-invalid={hasError}
                        className="sr-only"
                        disabled={disabled}
                        id={inputId}
                        multiple={allowMultiple}
                        type="file"
                        onChange={handleInputChange}
                        {...props}
                    />

                    <Upload className="h-8 w-8 text-muted-foreground" />
                    <div className="text-center">
                        <p className="text-sm font-medium">Click to upload or drag and drop</p>
                        <p className="text-xs text-muted-foreground mt-1">
                            {acceptedFileTypes
                                ? `Accepted types: ${acceptedFileTypes.join(", ")}`
                                : "All file types accepted"}
                        </p>
                        {maxSize && (
                            <p className="text-xs text-muted-foreground">Max size: {formatFileSize(maxSize)}</p>
                        )}
                        {maxFiles > 1 && <p className="text-xs text-muted-foreground">Max files: {maxFiles}</p>}
                    </div>
                </div>

                {/* File Preview */}
                {showPreview && files.length > 0 && (
                    <div className="mt-4 space-y-2">
                        <h4 className="text-sm font-medium">Uploaded Files</h4>
                        <div className="space-y-2">
                            {files.map((file, index) => {
                                const {
                                    icon: FileIconComponent,
                                    color,
                                    bgColor,
                                    label: fileLabel,
                                } = getFileIconAndColor(file.name);

                                return (
                                    <div
                                        key={`${file.name}-${index}`}
                                        className={cn("flex items-center gap-3 p-3 rounded-lg border")}
                                    >
                                        <div className={cn("flex-shrink-0", color)}>
                                            <FileIconComponent size={20} />
                                        </div>

                                        <div className="flex-1 min-w-0">
                                            <div className="flex items-center gap-2">
                                                <p className="text-sm font-medium truncate">{file.name}</p>
                                                <span className={cn("text-xs px-1.5 py-0.5 rounded", bgColor, color)}>
                                                    {fileLabel}
                                                </span>
                                            </div>
                                            <p className="text-xs text-muted-foreground">{formatFileSize(file.size)}</p>
                                        </div>

                                        <div className="flex items-center gap-1">
                                            {file.type.startsWith("image/") && (
                                                <Button
                                                    className="h-8 w-8 p-0"
                                                    size="sm"
                                                    type="button"
                                                    variant="ghost"
                                                    onClick={(e) => {
                                                        e.stopPropagation();
                                                        previewFile(file);
                                                    }}
                                                >
                                                    <Eye className="h-4 w-4" />
                                                </Button>
                                            )}

                                            <Button
                                                className="h-8 w-8 p-0 text-destructive hover:text-destructive"
                                                disabled={disabled}
                                                size="sm"
                                                type="button"
                                                variant="ghost"
                                                onClick={(e) => {
                                                    e.stopPropagation();
                                                    removeFile(index);
                                                }}
                                            >
                                                <X className="h-4 w-4" />
                                            </Button>
                                        </div>
                                    </div>
                                );
                            })}
                        </div>
                    </div>
                )}

                {/* Error Message */}
                {error && (
                    <p
                        className={cn("mt-1 text-sm text-destructive", errorClassName)}
                        id={inputId ? `${inputId}-error` : undefined}
                    >
                        {error}
                    </p>
                )}
            </div>
        );
    },
);

FileUpload.displayName = "FileUpload";
export { FileUpload };
