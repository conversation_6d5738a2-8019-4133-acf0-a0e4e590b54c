"use client";

import * as RadioGroupPrimitive from "@radix-ui/react-radio-group";
import * as React from "react";

import { Label } from "./label";

import { cn } from "@/lib/utils";

export interface RadioOption {
    value: string;
    label: string;
    description?: string;
    disabled?: boolean;
}

export interface RadioGroupProps
    extends Omit<React.ComponentPropsWithoutRef<typeof RadioGroupPrimitive.Root>, "children"> {
    options: RadioOption[];
    label?: string;
    labelRight?: React.ReactNode;
    error?: string;
    containerClassName?: string;
    labelClassName?: string;
    labelContainerClassName?: string;
    errorClassName?: string;
    itemClassName?: string;
    itemContainerClassName?: string;
    orientation?: "horizontal" | "vertical";
    size?: "sm" | "md" | "lg";
}

const RadioGroup = React.forwardRef<React.ElementRef<typeof RadioGroupPrimitive.Root>, RadioGroupProps>(
    (
        {
            className,
            options,
            label,
            labelRight,
            error,
            containerClassName,
            labelClassName,
            labelContainerClassName,
            errorClassName,
            itemClassName,
            itemContainerClassName,
            orientation = "vertical",
            size = "md",
            ...props
        },
        ref,
    ) => {
        const hasError = Boolean(error);

        const groupId = React.useId();
        const errorId = hasError ? `${groupId}-error` : undefined;

        const sizeClasses = {
            sm: {
                item: "h-3 w-3",
                indicator: "h-1.5 w-1.5",
                text: "text-xs",
                gap: "gap-1.5",
            },
            md: {
                item: "h-4 w-4",
                indicator: "h-2 w-2",
                text: "text-sm",
                gap: "gap-2",
            },
            lg: {
                item: "h-5 w-5",
                indicator: "h-2.5 w-2.5",
                text: "text-base",
                gap: "gap-2.5",
            },
        };

        const currentSize = sizeClasses[size];

        return (
            <div className={cn("w-full", containerClassName)}>
                {/* Label */}
                {label && (
                    <div className={cn("flex items-center justify-between mb-2", labelContainerClassName)}>
                        <Label className={cn("block text-sm font-medium", labelClassName)} id={`${groupId}-label`}>
                            {label}
                        </Label>
                        {labelRight && <div className="text-sm">{labelRight}</div>}
                    </div>
                )}

                <RadioGroupPrimitive.Root
                    ref={ref}
                    aria-describedby={errorId}
                    aria-invalid={hasError}
                    aria-labelledby={label ? `${groupId}-label` : undefined}
                    className={cn(
                        orientation === "horizontal" ? "flex items-center flex-wrap gap-4" : "grid gap-3",
                        hasError && "aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40",
                        className,
                    )}
                    {...props}
                >
                    {options.map((option) => (
                        <div
                            key={option.value}
                            className={cn("flex items-start space-x-2", currentSize.gap, itemContainerClassName)}
                        >
                            <RadioGroupPrimitive.Item
                                className={cn(
                                    "aspect-square rounded-full border border-primary text-primary ring-offset-background focus:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 mt-0.5",
                                    "data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",
                                    hasError && "border-red-300 data-[state=checked]:bg-red-500",
                                    currentSize.item,
                                    itemClassName,
                                )}
                                disabled={option.disabled}
                                value={option.value}
                            >
                                <RadioGroupPrimitive.Indicator className="flex items-center justify-center">
                                    <div className={cn("rounded-full bg-current", currentSize.indicator)} />
                                </RadioGroupPrimitive.Indicator>
                            </RadioGroupPrimitive.Item>

                            <div className="flex flex-col">
                                <Label
                                    className={cn(
                                        "font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer",
                                        option.disabled && "opacity-50 cursor-not-allowed",
                                        currentSize.text,
                                    )}
                                    htmlFor={option.value}
                                    onClick={() => {
                                        if (!option.disabled) {
                                            const radioItem = document.querySelector(
                                                `[value="${option.value}"]`,
                                            ) as HTMLElement;

                                            radioItem?.click();
                                        }
                                    }}
                                >
                                    {option.label}
                                </Label>
                                {option.description && (
                                    <p
                                        className={cn(
                                            "text-muted-foreground mt-0.5",
                                            size === "sm" ? "text-xs" : "text-xs",
                                        )}
                                    >
                                        {option.description}
                                    </p>
                                )}
                            </div>
                        </div>
                    ))}
                </RadioGroupPrimitive.Root>

                {/* Error Message */}
                {error && (
                    <p className={cn("mt-2 text-sm text-red-600", errorClassName)} id={errorId}>
                        {error}
                    </p>
                )}
            </div>
        );
    },
);

RadioGroup.displayName = "RadioGroup";

// Legacy exports for backward compatibility
const RadioGroupItem = React.forwardRef<
    React.ElementRef<typeof RadioGroupPrimitive.Item>,
    React.ComponentPropsWithoutRef<typeof RadioGroupPrimitive.Item>
>(({ className, ...props }, ref) => {
    return (
        <RadioGroupPrimitive.Item
            ref={ref}
            className={cn(
                "aspect-square h-4 w-4 rounded-full border border-primary text-primary ring-offset-background focus:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",
                className,
            )}
            {...props}
        >
            <RadioGroupPrimitive.Indicator className="flex items-center justify-center">
                <div className="h-2 w-2 rounded-full bg-current" />
            </RadioGroupPrimitive.Indicator>
        </RadioGroupPrimitive.Item>
    );
});

RadioGroupItem.displayName = RadioGroupPrimitive.Item.displayName;

export { RadioGroup, RadioGroupItem };
