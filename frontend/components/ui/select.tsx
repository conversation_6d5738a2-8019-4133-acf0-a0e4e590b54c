import * as SelectPrimitive from "@radix-ui/react-select";
import { CheckIcon, ChevronDownIcon, ChevronUpIcon } from "lucide-react";
import * as React from "react";

import { cn } from "@/lib/utils";

export interface SelectOption {
    value: string | number;
    label: string;
    disabled?: boolean;
    icon?: React.ReactNode;
}

export interface SelectProps extends React.ComponentProps<typeof SelectPrimitive.Root> {
    label?: string;
    labelRight?: React.ReactNode;
    error?: string;
    containerClassName?: string;
    labelClassName?: string;
    labelContainerClassName?: string;
    errorClassName?: string;
    placeholder?: string;
    children?: React.ReactNode;
    triggerClassName?: string;
    contentClassName?: string;

    options?: SelectOption[] | string[] | number[] | Record<string, any>[];
    getOptionValue?: (option: any) => string | number;
    getOptionLabel?: (option: any) => string;
    getOptionDisabled?: (option: any) => boolean;
    getOptionIcon?: (option: any) => React.ReactNode;
    emptyMessage?: string;
}

export interface SelectItemProps extends React.ComponentProps<typeof SelectPrimitive.Item> {
    children: React.ReactNode;
    icon?: React.ReactNode;
}

const Select = React.forwardRef<React.ElementRef<typeof SelectPrimitive.Root>, SelectProps>(
    (
        {
            label,
            labelRight,
            error,
            containerClassName,
            labelClassName,
            labelContainerClassName,
            errorClassName,
            placeholder,
            children,
            triggerClassName,
            contentClassName,
            options,
            getOptionValue,
            getOptionLabel,
            getOptionDisabled,
            getOptionIcon,
            emptyMessage = "No options available",
            ...props
        },
        ref,
    ) => {
        const hasError = Boolean(error);
        const selectId = React.useId();

        const renderOptions = () => {
            if (!options || options.length === 0) {
                return (
                    <SelectItem disabled value="">
                        {emptyMessage}
                    </SelectItem>
                );
            }

            return options.map((option, index) => {
                let value: string | number;
                let label: string;
                let disabled = false;
                let icon: React.ReactNode = undefined;

                if (typeof option === "string" || typeof option === "number") {
                    value = option;
                    label = String(option);
                } else if (option && typeof option === "object") {
                    if (option.value === "") return null;
                    if ("value" in option && "label" in option) {
                        value = option.value;
                        label = option.label;
                        disabled = option.disabled || false;
                        icon = option.icon;
                    } else {
                        value = getOptionValue ? getOptionValue(option) : option.id || option.value || index;
                        label = getOptionLabel ? getOptionLabel(option) : option.label || option.name || String(value);
                        disabled = getOptionDisabled ? getOptionDisabled(option) : option.disabled || false;
                        icon = getOptionIcon ? getOptionIcon(option) : option.icon;
                    }
                } else {
                    // Fallback
                    value = index;
                    label = String(option);
                }

                return (
                    <SelectItem key={String(value)} disabled={disabled} icon={icon} value={String(value)}>
                        {label}
                    </SelectItem>
                );
            });
        };

        return (
            <div className={cn("w-full", containerClassName)}>
                {/* Label */}
                {label && (
                    <div className={cn("flex items-center justify-between mb-1", labelContainerClassName)}>
                        <label className={cn("block text-sm font-medium", labelClassName)} htmlFor={selectId}>
                            {label}
                        </label>
                        {labelRight && <div className="text-sm">{labelRight}</div>}
                    </div>
                )}

                {/* Select */}
                <SelectPrimitive.Root data-slot="select" {...props}>
                    <SelectTrigger
                        aria-describedby={hasError ? `${selectId}-error` : undefined}
                        aria-invalid={hasError}
                        className={cn(
                            hasError && "border-red-300 focus-visible:border-red-500 focus-visible:ring-red-500/20",
                            triggerClassName,
                        )}
                        id={selectId}
                    >
                        <SelectValue placeholder={placeholder} />
                    </SelectTrigger>
                    <SelectContent className={contentClassName}>{options ? renderOptions() : children}</SelectContent>
                </SelectPrimitive.Root>

                {/* Error Message */}
                {error && (
                    <p className={cn("mt-1 text-sm text-red-600", errorClassName)} id={`${selectId}-error`}>
                        {error}
                    </p>
                )}
            </div>
        );
    },
);

Select.displayName = "Select";

// Enhanced SelectItem with icon support
const SelectItem = React.forwardRef<React.ElementRef<typeof SelectPrimitive.Item>, SelectItemProps>(
    ({ className, children, icon, ...props }, ref) => (
        <SelectPrimitive.Item
            ref={ref}
            className={cn(
                "focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",
                className,
            )}
            data-slot="select-item"
            {...props}
        >
            {icon && <span className="flex items-center justify-center shrink-0">{icon}</span>}
            <SelectPrimitive.ItemText className="flex-1">{children}</SelectPrimitive.ItemText>
            <span className="absolute right-2 flex size-3.5 items-center justify-center">
                <SelectPrimitive.ItemIndicator>
                    <CheckIcon className="size-4" />
                </SelectPrimitive.ItemIndicator>
            </span>
        </SelectPrimitive.Item>
    ),
);

SelectItem.displayName = "SelectItem";

function SelectGroup({ ...props }: React.ComponentProps<typeof SelectPrimitive.Group>) {
    return <SelectPrimitive.Group data-slot="select-group" {...props} />;
}

function SelectValue({ ...props }: React.ComponentProps<typeof SelectPrimitive.Value>) {
    return <SelectPrimitive.Value data-slot="select-value" {...props} />;
}

function SelectTrigger({ className, children, ...props }: React.ComponentProps<typeof SelectPrimitive.Trigger>) {
    return (
        <SelectPrimitive.Trigger
            className={cn(
                "border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive flex h-9 w-full items-center justify-between rounded-md border px-3 py-2 text-sm shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 [&>span]:line-clamp-1 button-select",
                className,
            )}
            data-slot="select-trigger"
            {...props}
        >
            {children}
            <SelectPrimitive.Icon asChild>
                <ChevronDownIcon className="size-4 opacity-50" />
            </SelectPrimitive.Icon>
        </SelectPrimitive.Trigger>
    );
}

function SelectContent({
    className,
    children,
    position = "popper",
    ...props
}: React.ComponentProps<typeof SelectPrimitive.Content>) {
    return (
        <SelectPrimitive.Portal>
            <SelectPrimitive.Content
                className={cn(
                    "bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border shadow-md",
                    position === "popper" &&
                        "data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",
                    className,
                )}
                data-slot="select-content"
                position={position}
                {...props}
            >
                <SelectScrollUpButton />
                <SelectPrimitive.Viewport
                    className={cn(
                        "p-1",
                        position === "popper" &&
                            "h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1",
                    )}
                >
                    {children}
                </SelectPrimitive.Viewport>
                <SelectScrollDownButton />
            </SelectPrimitive.Content>
        </SelectPrimitive.Portal>
    );
}

function SelectLabel({ className, ...props }: React.ComponentProps<typeof SelectPrimitive.Label>) {
    return (
        <SelectPrimitive.Label
            className={cn("px-2 py-1.5 text-sm font-medium", className)}
            data-slot="select-label"
            {...props}
        />
    );
}

function SelectSeparator({ className, ...props }: React.ComponentProps<typeof SelectPrimitive.Separator>) {
    return (
        <SelectPrimitive.Separator
            className={cn("bg-border pointer-events-none -mx-1 my-1 h-px", className)}
            data-slot="select-separator"
            {...props}
        />
    );
}

function SelectScrollUpButton({ className, ...props }: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {
    return (
        <SelectPrimitive.ScrollUpButton
            className={cn("flex cursor-default items-center justify-center py-1", className)}
            data-slot="select-scroll-up-button"
            {...props}
        >
            <ChevronUpIcon className="size-4" />
        </SelectPrimitive.ScrollUpButton>
    );
}

function SelectScrollDownButton({
    className,
    ...props
}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {
    return (
        <SelectPrimitive.ScrollDownButton
            className={cn("flex cursor-default items-center justify-center py-1", className)}
            data-slot="select-scroll-down-button"
            {...props}
        >
            <ChevronDownIcon className="size-4" />
        </SelectPrimitive.ScrollDownButton>
    );
}

export {
    Select,
    SelectContent,
    SelectGroup,
    SelectItem,
    SelectLabel,
    SelectScrollDownButton,
    SelectScrollUpButton,
    SelectSeparator,
    SelectTrigger,
    SelectValue,
};
