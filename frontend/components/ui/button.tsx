import { Slot } from "@radix-ui/react-slot";
import { cva, type VariantProps } from "class-variance-authority";
import { Loader2 } from "lucide-react";
import * as React from "react";

import { cn } from "@/lib/utils";

const buttonVariants = cva(
    "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-[color,box-shadow] disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",
    {
        variants: {
            variant: {
                default: "bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",
                destructive:
                    "bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40",
                outline: "border border-input bg-background shadow-xs hover:bg-accent hover:text-accent-foreground",
                secondary: "bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",
                ghost: "hover:bg-accent hover:text-accent-foreground",
                link: "text-primary underline-offset-4 hover:underline",
                transparent: "bg-transparent text-foreground shadow-none",
            },
            size: {
                default: "h-10 px-4 py-2",
                sm: "h-9 rounded-md px-3",
                lg: "h-11 rounded-md px-8",
                icon: "size-10",
                "icon-sm": "size-9",
                "icon-lg": "size-11",
            },
        },
        defaultVariants: {
            variant: "default",
            size: "default",
        },
    },
);

export interface ButtonProps
    extends React.ButtonHTMLAttributes<HTMLButtonElement>,
        VariantProps<typeof buttonVariants> {
    asChild?: boolean;
    loading?: boolean;
    loadingText?: string;
    iconLeft?: React.ReactNode;
    iconRight?: React.ReactNode;
    iconOnly?: boolean;
    loadingIcon?: React.ReactNode;
    containerClass?: string;
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
    (
        {
            className,
            variant,
            size,
            asChild = false,
            loading = false,
            loadingText,
            iconLeft,
            iconRight,
            iconOnly = false,
            loadingIcon,
            disabled,
            children,
            containerClass = "",
            ...props
        },
        ref,
    ) => {
        const Comp = asChild ? Slot : "button";

        const defaultLoadingIcon = <Loader2 className="animate-spin" />;
        const renderLoadingIcon = loadingIcon || defaultLoadingIcon;

        const isDisabled = disabled || loading;

        const isIconOnlyButton = iconOnly || size === "icon";

        const renderContent = () => {
            if (loading) {
                if (isIconOnlyButton) {
                    return renderLoadingIcon;
                }

                return (
                    <div className={cn("flex items-center gap-1", containerClass)}>
                        {renderLoadingIcon}
                        {loadingText || children}
                    </div>
                );
            }

            if (isIconOnlyButton) {
                return iconLeft || iconRight || children;
            }

            return (
                <div className={cn("flex items-center gap-1", containerClass)}>
                    {iconLeft}
                    {children}
                    {iconRight}
                </div>
            );
        };

        return (
            <Comp
                ref={ref}
                className={cn(buttonVariants({ variant, size }), loading && "cursor-not-allowed", className)}
                data-slot="button"
                disabled={isDisabled}
                {...props}
            >
                {renderContent()}
            </Comp>
        );
    },
);

Button.displayName = "Button";

export { Button, buttonVariants };
