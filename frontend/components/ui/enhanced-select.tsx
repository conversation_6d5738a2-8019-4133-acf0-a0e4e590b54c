"use client";

import * as React from "react";

import { Select } from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { cn } from "@/lib/utils";

interface SelectOption {
    value: string;
    label: string;
    disabled?: boolean;
}

interface EnhancedSelectProps {
    label?: string;
    placeholder?: string;
    options: SelectOption[];
    value?: string;
    defaultValue?: string;
    onValueChange?: (value: string) => void;
    onChange?: (value: string) => void;
    error?: string;
    disabled?: boolean;
    required?: boolean;
    className?: string;
    containerClassName?: string;
    labelClassName?: string;
    errorClassName?: string;
}

export const EnhancedSelect: React.FC<EnhancedSelectProps> = ({
    label,
    placeholder = "Select an option",
    options,
    value,
    defaultValue,
    onValueChange,
    onChange,
    error,
    disabled = false,
    required = false,
    className,
    containerClassName,
    labelClassName,
    errorClassName,
}) => {
    const handleValueChange = (newValue: string) => {
        onValueChange?.(newValue);
        onChange?.(newValue);
    };

    const selectElement = (
        <Select
            defaultValue={defaultValue}
            disabled={disabled}
            options={options.filter((option) => option.value !== "")}
            value={value}
            onValueChange={handleValueChange}
        />
    );

    if (!label && !error) {
        return selectElement;
    }

    return (
        <div className={cn("space-y-2", containerClassName)}>
            {label && (
                <Label className={cn("block text-sm font-medium", labelClassName)}>
                    {label}
                    {required && <span className="text-destructive ml-1">*</span>}
                </Label>
            )}
            {selectElement}
            {error && <p className={cn("text-sm text-destructive", errorClassName)}>{error}</p>}
        </div>
    );
};
