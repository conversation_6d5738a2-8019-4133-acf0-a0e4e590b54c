import * as React from "react";

import { cn } from "@/lib/utils";

export interface TextareaProps extends React.ComponentProps<"textarea"> {
    label?: string;
    error?: string;
    containerClassName?: string;
    labelClassName?: string;
    errorClassName?: string;
}

function Textarea({
    className,
    label,
    error,
    containerClassName,
    labelClassName,
    errorClassName,
    ...props
}: TextareaProps) {
    const textareaElement = (
        <textarea
            aria-invalid={error ? "true" : "false"}
            className={cn(
                "border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",
                error && "border-destructive",
                className,
            )}
            data-slot="textarea"
            {...props}
        />
    );

    if (!label && !error) {
        return textareaElement;
    }

    return (
        <div className={cn("space-y-2", containerClassName)}>
            {label && <label className={cn("block text-sm font-medium", labelClassName)}>{label}</label>}
            {textareaElement}
            {error && <p className={cn("text-sm text-destructive", errorClassName)}>{error}</p>}
        </div>
    );
}

export { Textarea };
