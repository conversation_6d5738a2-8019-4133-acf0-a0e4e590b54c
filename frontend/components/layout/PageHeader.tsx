// =============================================================================
// PAGE HEADER COMPONENT
// =============================================================================

interface PageHeaderProps {
    title: string;
    subtitle?: string;
    breadcrumbs?: Array<{ label: string; href?: string }>;
    actions?: React.ReactNode;
    tabs?: Array<{ id: string; label: string; current?: boolean; href?: string }>;
    className?: string;
}

export const PageHeader: React.FC<PageHeaderProps> = ({
    title,
    subtitle,
    breadcrumbs,
    actions,
    tabs,
    className = "",
}) => {
    return (
        <div className={`space-y-4 ${className}`}>
            {/* Breadcrumbs */}
            {breadcrumbs && breadcrumbs.length > 0 && (
                <nav aria-label="Breadcrumb" className="flex">
                    <ol className="flex items-center space-x-4">
                        {breadcrumbs.map((crumb, index) => (
                            <li key={index}>
                                <div className="flex items-center">
                                    {index > 0 && (
                                        <svg
                                            aria-hidden="true"
                                            className="flex-shrink-0 h-5 w-5 text-gray-300 mr-4"
                                            fill="currentColor"
                                            viewBox="0 0 20 20"
                                            xmlns="http://www.w3.org/2000/svg"
                                        >
                                            <path d="M5.555 17.776l8-16 .894.448-8 16-.894-.448z" />
                                        </svg>
                                    )}
                                    {crumb.href ? (
                                        <a
                                            className="text-sm font-medium text-gray-500 hover:text-gray-700"
                                            href={crumb.href}
                                        >
                                            {crumb.label}
                                        </a>
                                    ) : (
                                        <span className="text-sm font-medium text-gray-900">{crumb.label}</span>
                                    )}
                                </div>
                            </li>
                        ))}
                    </ol>
                </nav>
            )}
            {/* Title and Actions */}
            <div className="flex items-center justify-between">
                <div>
                    <h1 className="text-2xl font-bold text-gray-900">{title}</h1>
                    {subtitle && <p className="mt-1 text-sm text-gray-600">{subtitle}</p>}
                </div>
                {actions && <div className="flex space-x-3">{actions}</div>}
            </div>

            {/* Tabs */}
            {tabs && tabs.length > 0 && (
                <div className="border-b border-gray-200">
                    <nav className="-mb-px flex space-x-8">
                        {tabs.map((tab) => (
                            <a
                                key={tab.id}
                                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                                    tab.current
                                        ? "border-blue-500 text-blue-600"
                                        : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                                }`}
                                href={tab.href || `#${tab.id}`}
                            >
                                {tab.label}
                            </a>
                        ))}
                    </nav>
                </div>
            )}
        </div>
    );
};
