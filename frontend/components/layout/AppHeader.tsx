"use client";

import { <PERSON> } from "lucide-react";
import { useState } from "react";

import { Logo } from "../icons";
import { NavUser } from "../nav-user";

import { Breadcrumbs } from "./Breadcrumbs";

import { Button } from "@/components/ui/button";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Separator } from "@/components/ui/separator";
import { SidebarTrigger } from "@/components/ui/sidebar";
import { useAuth } from "@/composables/useStore";
import Link from "next/link";

const mockNotifications = [
    {
        id: 1,
        title: "New Organization Application",
        message: "MANJA Foundation has submitted a new application for review.",
        time: "5 minutes ago",
        read: false,
    },
    {
        id: 2,
        title: "Application Approved",
        message: "Care International application has been approved and moved to next stage.",
        time: "1 hour ago",
        read: false,
    },
    {
        id: 3,
        title: "System Maintenance",
        message: "Scheduled system maintenance tonight from 2:00 AM to 4:00 AM.",
        time: "3 hours ago",
        read: true,
    },
];

export function AppHeader() {
    const { session } = useAuth();
    const [notifications] = useState(mockNotifications);
    const [notificationsOpen, setNotificationsOpen] = useState(false);

    const unreadCount = notifications.filter((n) => !n.read).length;

    const headerClasses =
        "sticky top-0 z-50 flex h-16 shrink-0 items-center gap-2 transition-[width,height] ease-linear group-has-data-[collapsible=icon]/sidebar-wrapper:h-12 bg-background/85 backdrop-blur-xl backdrop-saturate-150 border-b border-border/30 shadow-sm supports-[backdrop-filter]:bg-background/70 dark:bg-background/90 dark:supports-[backdrop-filter]:bg-background/80";

    return (
        <header className={headerClasses}>
            {session ? (
                <>
                    <div className="flex items-center gap-2 px-4 flex-1">
                        <SidebarTrigger className="-ml-1" />
                        <Separator className="mr-2 data-[orientation=vertical]:h-4" orientation="vertical" />
                        <div className="hidden md:block">
                            <Breadcrumbs />
                        </div>
                    </div>

                    {/* Right side - Notifications and User Profile */}
                    <div className="flex items-center gap-2 px-4">
                        {/* Notifications */}
                        <Popover open={notificationsOpen} onOpenChange={setNotificationsOpen}>
                            <PopoverTrigger asChild>
                                <Button className="relative h-8 w-8 p-0" size="sm" variant="ghost">
                                    <Bell className="h-4 w-4" />
                                    {unreadCount > 0 && (
                                        <span className="absolute -top-1 -right-1 h-4 w-4 rounded-full bg-red-500 text-[10px] font-medium text-white flex items-center justify-center">
                                            {unreadCount > 9 ? "9+" : unreadCount}
                                        </span>
                                    )}
                                </Button>
                            </PopoverTrigger>
                            <PopoverContent align="end" className="w-80 p-0">
                                <div className="p-4 border-b">
                                    <h4 className="font-semibold">Notifications</h4>
                                    <p className="text-sm text-muted-foreground">
                                        You have {unreadCount} unread notifications
                                    </p>
                                </div>
                                <div className="max-h-80 overflow-y-auto">
                                    {notifications.length === 0 ? (
                                        <div className="p-4 text-center text-sm text-muted-foreground">
                                            No notifications
                                        </div>
                                    ) : (
                                        <div className="space-y-1">
                                            {notifications.map((notification) => (
                                                <div
                                                    key={notification.id}
                                                    className={`p-3 hover:bg-accent cursor-pointer border-l-2 ${
                                                        notification.read
                                                            ? "border-transparent"
                                                            : "border-primary bg-primary/5"
                                                    }`}
                                                >
                                                    <div className="font-medium text-sm">{notification.title}</div>
                                                    <div className="text-sm text-muted-foreground line-clamp-2">
                                                        {notification.message}
                                                    </div>
                                                    <div className="text-xs text-muted-foreground mt-1">
                                                        {notification.time}
                                                    </div>
                                                </div>
                                            ))}
                                        </div>
                                    )}
                                </div>
                                <div className="p-2 border-t">
                                    <Button className="w-full" size="sm" variant="ghost">
                                        View all notifications
                                    </Button>
                                </div>
                            </PopoverContent>
                        </Popover>

                        {/* User Profile Dropdown */}
                        <NavUser position="top" session={session} />
                    </div>
                </>
            ) : (
                <div className="flex items-center justify-center w-full">
                    <Link href="/">
                        <Logo />
                    </Link>
                </div>
            )}
        </header>
    );
}
