"use client";

import { ChevronRight, Home } from "lucide-react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { useMemo } from "react";

import {
    Breadcrumb,
    BreadcrumbItem,
    BreadcrumbLink,
    BreadcrumbList,
    BreadcrumbPage,
    BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";

interface BreadcrumbItem {
    title: string;
    href?: string;
    isLast?: boolean;
}

const routeLabels: Record<string, string> = {
    dashboard: "Dashboard",
    organizations: "Organizations",
    create: "Create Organization",
    workflows: "Workflows",
    management: "Management",
    users: "Users",
    departments: "Departments",
    roles: "Roles",
    notices: "Notices",
    districts: "Districts",
    zones: "Zones",
    regions: "Regions",
    settings: "Settings",
    finance: "Finance",
    fees: "Fees",
    notifications: "Notifications",
    auth: "Authentication",
    login: "Login",
    register: "Register",
    "2fa": "Two Factor Authentication",
    "password-reset": "Password Reset",
    "confirm-password": "Confirm Password",
};

export function Breadcrumbs() {
    const pathname = usePathname();

    const breadcrumbs: BreadcrumbItem[] = useMemo(() => {
        // Don't show breadcrumbs on auth pages or home page
        if (pathname === "/" || pathname.startsWith("/auth")) {
            return [];
        }

        const pathSegments = pathname.split("/").filter(Boolean);
        const breadcrumbItems: BreadcrumbItem[] = [];

        // Always add Dashboard as the first item (except for dashboard page itself)
        if (pathname !== "/dashboard") {
            breadcrumbItems.push({
                title: "Dashboard",
                href: "/dashboard",
            });
        }

        // Build breadcrumbs from path segments
        let currentPath = "";

        pathSegments.forEach((segment, index) => {
            currentPath += `/${segment}`;
            const isLast = index === pathSegments.length - 1;

            // Skip dynamic route parameters (segments that look like IDs)
            if (/^[a-f\d]{24}$/i.test(segment) || /^\d+$/.test(segment)) {
                return;
            }

            const title = routeLabels[segment] || segment.charAt(0).toUpperCase() + segment.slice(1);

            breadcrumbItems.push({
                title,
                href: isLast ? undefined : currentPath,
                isLast,
            });
        });

        return breadcrumbItems;
    }, [pathname]);

    // Don't render if no breadcrumbs
    if (breadcrumbs.length === 0) {
        return null;
    }

    return (
        <Breadcrumb>
            <BreadcrumbList>
                {breadcrumbs.map((crumb, index) => (
                    <div key={crumb.title} className="flex items-center">
                        {index > 0 && (
                            <BreadcrumbSeparator>
                                <ChevronRight className="h-4 w-4" />
                            </BreadcrumbSeparator>
                        )}
                        <BreadcrumbItem>
                            {crumb.isLast || !crumb.href ? (
                                <BreadcrumbPage className="font-medium">{crumb.title}</BreadcrumbPage>
                            ) : (
                                <BreadcrumbLink asChild>
                                    <Link
                                        className="flex items-center gap-1 text-sm font-medium text-muted-foreground hover:text-foreground transition-colors"
                                        href={crumb.href}
                                    >
                                        {crumb.title === "Dashboard" && <Home className="h-3 w-3" />}
                                        {crumb.title}
                                    </Link>
                                </BreadcrumbLink>
                            )}
                        </BreadcrumbItem>
                    </div>
                ))}
            </BreadcrumbList>
        </Breadcrumb>
    );
}
