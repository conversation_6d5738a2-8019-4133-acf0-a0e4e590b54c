"use client";

import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { Mail, Clock, UserX, RefreshCw } from "lucide-react";
import { toast } from "sonner";
import { format } from "date-fns";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { MemberInvitationDto } from "@/types";
import * as membershipService from "@/services/MembershipService";

interface PendingInvitationsProps {
    organizationId: string;
}

const statusConfig = {
    PENDING: { label: "Pending", color: "bg-yellow-100 text-yellow-800 border-yellow-200" },
    ACCEPTED: { label: "Accepted", color: "bg-green-100 text-green-800 border-green-200" },
    REJECTED: { label: "Rejected", color: "bg-red-100 text-red-800 border-red-200" },
};

const roleConfig = {
    OWNER: { label: "Owner", color: "bg-purple-100 text-purple-800 border-purple-200" },
    ADMIN: { label: "Admin", color: "bg-blue-100 text-blue-800 border-blue-200" },
    MEMBER: { label: "Member", color: "bg-gray-100 text-gray-800 border-gray-200" },
};

export const PendingInvitations = ({ organizationId }: PendingInvitationsProps) => {
    const queryClient = useQueryClient();

    const { 
        data: invitations, 
        isLoading, 
        error,
        refetch 
    } = useQuery({
        queryKey: ["organization-invitations", organizationId],
        queryFn: async () => {
            const response = await membershipService.getMemberInvitations(organizationId);
            if (!response.success) {
                throw new Error(response.errors?.[0]?.message || "Failed to load invitations");
            }
            return response.data || [];
        },
    });

    const resendInvitationMutation = useMutation({
        mutationFn: async (invitationData: { email: string; role: "MEMBER" | "ADMIN" }) => {
            const response = await membershipService.inviteMember(organizationId, invitationData);
            if (!response.success) {
                throw new Error(response.errors?.[0]?.message || "Failed to resend invitation");
            }
            return response.data;
        },
        onSuccess: () => {
            toast.success("Invitation resent successfully");
            queryClient.invalidateQueries({ queryKey: ["organization-invitations", organizationId] });
        },
        onError: (error: Error) => {
            toast.error(error.message);
        },
    });

    if (isLoading) {
        return (
            <Card>
                <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                        <Mail className="h-5 w-5" />
                        Pending Invitations
                    </CardTitle>
                </CardHeader>
                <CardContent>
                    <div className="flex items-center justify-center py-8">
                        <RefreshCw className="h-6 w-6 animate-spin text-gray-400" />
                    </div>
                </CardContent>
            </Card>
        );
    }

    if (error) {
        return (
            <Card>
                <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                        <Mail className="h-5 w-5" />
                        Pending Invitations
                    </CardTitle>
                </CardHeader>
                <CardContent>
                    <div className="text-center py-8 text-gray-500">
                        <p>Failed to load invitations</p>
                        <Button 
                            variant="outline" 
                            size="sm" 
                            className="mt-2" 
                            onClick={() => refetch()}
                        >
                            Try Again
                        </Button>
                    </div>
                </CardContent>
            </Card>
        );
    }

    const pendingInvitations = invitations?.filter(inv => inv.status === "PENDING") || [];

    if (pendingInvitations.length === 0) {
        return null; // Don't show the card if no pending invitations
    }

    return (
        <Card>
            <CardHeader>
                <CardTitle className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                        <Mail className="h-5 w-5" />
                        Pending Invitations
                        <Badge variant="secondary">{pendingInvitations.length}</Badge>
                    </div>
                    <Button 
                        variant="outline" 
                        size="sm" 
                        onClick={() => refetch()}
                    >
                        <RefreshCw className="h-4 w-4" />
                    </Button>
                </CardTitle>
            </CardHeader>
            <CardContent>
                <div className="space-y-3">
                    {pendingInvitations.map((invitation) => (
                        <div
                            key={invitation.id}
                            className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                        >
                            <div className="flex items-center space-x-3">
                                <div className="flex-shrink-0">
                                    <div className="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center">
                                        <Mail className="h-5 w-5 text-gray-500" />
                                    </div>
                                </div>
                                <div className="flex-1">
                                    <div className="flex items-center gap-2">
                                        <p className="font-medium text-gray-900">
                                            {invitation.invited_email}
                                        </p>
                                        <Badge 
                                            variant="outline" 
                                            className={roleConfig[invitation.role]?.color}
                                        >
                                            {roleConfig[invitation.role]?.label}
                                        </Badge>
                                    </div>
                                    <div className="flex items-center gap-4 mt-1">
                                        <div className="flex items-center gap-1 text-sm text-gray-500">
                                            <Clock className="h-3 w-3" />
                                            Expires {format(new Date(invitation.expires_at), "MMM d, yyyy")}
                                        </div>
                                        <Badge 
                                            variant="outline" 
                                            className={statusConfig[invitation.status]?.color}
                                        >
                                            {statusConfig[invitation.status]?.label}
                                        </Badge>
                                    </div>
                                </div>
                            </div>

                            <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                    <Button variant="ghost" size="sm">
                                        <span className="sr-only">Open menu</span>
                                        <Mail className="h-4 w-4" />
                                    </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end">
                                    <DropdownMenuItem
                                        onClick={() => {
                                            resendInvitationMutation.mutate({
                                                email: invitation.invited_email,
                                                role: invitation.role as "MEMBER" | "ADMIN"
                                            });
                                        }}
                                        disabled={resendInvitationMutation.isPending}
                                    >
                                        <RefreshCw className="mr-2 h-4 w-4" />
                                        Resend Invitation
                                    </DropdownMenuItem>
                                    <DropdownMenuItem className="text-red-600">
                                        <UserX className="mr-2 h-4 w-4" />
                                        Cancel Invitation
                                    </DropdownMenuItem>
                                </DropdownMenuContent>
                            </DropdownMenu>
                        </div>
                    ))}
                </div>
                
                {pendingInvitations.length > 0 && (
                    <div className="mt-4 pt-4 border-t border-gray-200">
                        <p className="text-xs text-gray-500 text-center">
                            Invitations will expire automatically. Invited users will receive an email 
                            with instructions to join the organization.
                        </p>
                    </div>
                )}
            </CardContent>
        </Card>
    );
};