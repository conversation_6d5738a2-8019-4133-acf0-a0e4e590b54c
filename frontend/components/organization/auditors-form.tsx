import { Plus, Trash2 } from "lucide-react";

import { Button } from "../ui/button";
import { Card } from "../ui/card";
import { Checkbox } from "../ui/checkbox";
import { Input } from "../ui/input";

import { OrganizationAuditorRequest } from "@/types";

const AuditorsForm = ({
    auditors,
    setAuditors,
}: {
    auditors: OrganizationAuditorRequest[];
    setAuditors: (auditors: OrganizationAuditorRequest[]) => void;
}) => {
    const addAuditor = () => {
        setAuditors([
            ...auditors,
            {
                name: "",
                email: "",
                phone: "",
                address: "",
                is_active: true,
            },
        ]);
    };

    const updateAuditor = (index: number, field: keyof OrganizationAuditorRequest, value: any) => {
        const updated = [...auditors];

        updated[index] = { ...updated[index], [field]: value };
        setAuditors(updated);
    };

    const removeAuditor = (index: number) => {
        setAuditors(auditors.filter((_, i) => i !== index));
    };

    return (
        <div className="space-y-4">
            <div className="flex justify-between items-center">
                <h3 className="text-lg font-semibold">Organization Auditors</h3>
                <Button size="sm" onClick={addAuditor}>
                    <Plus className="w-4 h-4 mr-2" />
                    Add Auditor
                </Button>
            </div>

            {auditors.map((auditor, index) => (
                <Card key={index} className="p-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <Input
                            label="Full Name"
                            placeholder="Auditor's full name"
                            value={auditor.name}
                            onChange={(e) => updateAuditor(index, "name", e.target.value)}
                        />
                        <Input
                            label="Email"
                            placeholder="<EMAIL>"
                            type="email"
                            value={auditor.email}
                            onChange={(e) => updateAuditor(index, "email", e.target.value)}
                        />
                        <Input
                            label="Phone"
                            placeholder="Phone number"
                            value={auditor.phone}
                            onChange={(e) => updateAuditor(index, "phone", e.target.value)}
                        />
                        <Input
                            label="Address"
                            placeholder="Physical address"
                            value={auditor.address}
                            onChange={(e) => updateAuditor(index, "address", e.target.value)}
                        />
                        <Checkbox
                            checked={auditor.is_active}
                            id={`auditor-active-${index}`}
                            label="Active"
                            onCheckedChange={(checked) => updateAuditor(index, "is_active", checked)}
                        />
                    </div>
                    <Button className="mt-2" size="sm" variant="destructive" onClick={() => removeAuditor(index)}>
                        <Trash2 className="w-4 h-4 mr-2" />
                        Remove Auditor
                    </Button>
                </Card>
            ))}
        </div>
    );
};

export default AuditorsForm;
