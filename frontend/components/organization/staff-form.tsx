import { Plus, Trash2 } from "lucide-react";

import LoadableItemInput from "../inputs/loadable-item";
import { Button } from "../ui/button";
import { Card } from "../ui/card";
import { Checkbox } from "../ui/checkbox";
import { Input } from "../ui/input";

import { OrganizationStaffRequest } from "@/types";

const StaffForm = ({
    staff,
    setStaff,
}: {
    staff: OrganizationStaffRequest[];
    setStaff: (staff: OrganizationStaffRequest[]) => void;
}) => {
    const addStaff = () => {
        setStaff([
            ...staff,
            {
                staff_type_id: "",
                is_active: true,
                total_female: 0,
                total_male: 0,
            },
        ]);
    };

    const updateStaff = (index: number, field: keyof OrganizationStaffRequest, value: any) => {
        const updated = [...staff];

        updated[index] = { ...updated[index], [field]: value };
        setStaff(updated);
    };

    const removeStaff = (index: number) => {
        setStaff(staff.filter((_, i) => i !== index));
    };

    return (
        <div className="space-y-4">
            <div className="flex justify-between items-center">
                <h3 className="text-lg font-semibold">Organization Staff</h3>
                <Button size="sm" onClick={addStaff}>
                    <Plus className="w-4 h-4 mr-2" />
                    Add Staff Type
                </Button>
            </div>

            {staff.map((staffItem, index) => (
                <Card key={index} className="p-4">
                    <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                        <LoadableItemInput
                            label="Staff Type"
                            placeholder="Select Staff type"
                            type="STAFF_TYPE"
                            value={staffItem.staff_type_id}
                            onItemSelect={(e) => updateStaff(index, "staff_type_id", e?.id!)}
                        />
                        <Input
                            label="Total Females"
                            min="0"
                            placeholder="Number of female staff"
                            type="number"
                            value={staffItem.total_female}
                            onChange={(e) => updateStaff(index, "total_female", parseInt(e.target.value) || 0)}
                        />
                        <Input
                            label="Total Males"
                            min="0"
                            placeholder="Number of male staff"
                            type="number"
                            value={staffItem.total_male}
                            onChange={(e) => updateStaff(index, "total_male", parseInt(e.target.value) || 0)}
                        />
                        <Checkbox
                            checked={staffItem.is_active}
                            id={`staff-active-${index}`}
                            label="Active"
                            onCheckedChange={(checked) => updateStaff(index, "is_active", checked)}
                        />
                    </div>

                    {/* Total Summary */}
                    <div className="mt-3 p-2 rounded-md">
                        <span className="text-sm text-gray-600">
                            Total Staff: {staffItem.total_female + staffItem.total_male}({staffItem.total_female}{" "}
                            female, {staffItem.total_male} male)
                        </span>
                    </div>

                    <Button className="mt-2" size="sm" variant="destructive" onClick={() => removeStaff(index)}>
                        <Trash2 className="w-4 h-4 mr-2" />
                        Remove Staff Type
                    </Button>
                </Card>
            ))}
        </div>
    );
};

export default StaffForm;
