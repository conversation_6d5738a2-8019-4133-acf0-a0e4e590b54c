import { Plus, Trash2 } from "lucide-react";

import { ManagerInput } from "../inputs/manager";
import { Button } from "../ui/button";
import { Card } from "../ui/card";
import { Input } from "../ui/input";

import { LocationActivityRequest } from "@/types";

const LocationActivitiesForm = ({
    locationActivities,
    setLocationActivities,
}: {
    locationActivities: LocationActivityRequest[];
    setLocationActivities: (activities: LocationActivityRequest[]) => void;
}) => {
    const addLocationActivity = () => {
        setLocationActivities([
            ...locationActivities,
            {
                district_id: "",
                vdc_id: "",
                adc_id: "",
            },
        ]);
    };

    const updateLocationActivity = (index: number, field: keyof LocationActivityRequest, value: string) => {
        const updated = [...locationActivities];

        updated[index] = { ...updated[index], [field]: value };
        setLocationActivities(updated);
    };

    const removeLocationActivity = (index: number) => {
        setLocationActivities(locationActivities.filter((_, i) => i !== index));
    };

    return (
        <div className="space-y-4">
            <div className="flex justify-between items-center">
                <h3 className="text-lg font-semibold">Location Activities</h3>
                <Button size="sm" onClick={addLocationActivity}>
                    <Plus className="w-4 h-4 mr-2" />
                    Add Location
                </Button>
            </div>

            {locationActivities.map((activity, index) => (
                <Card key={index} className="p-4">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <ManagerInput
                            label="District"
                            placeholder="District"
                            type="district"
                            value={activity.district_id}
                            onItemSelect={(e) => updateLocationActivity(index, "district_id", e?.id!)}
                        />
                        <Input
                            label="Village Development Committee"
                            placeholder="VDC ID"
                            value={activity.vdc_id || ""}
                            onChange={(e) => updateLocationActivity(index, "vdc_id", e.target.value)}
                        />
                        <Input
                            label="Area Development Committee"
                            placeholder="ADC ID"
                            value={activity.adc_id || ""}
                            onChange={(e) => updateLocationActivity(index, "adc_id", e.target.value)}
                        />
                    </div>
                    <Button
                        className="mt-2"
                        size="sm"
                        variant="destructive"
                        onClick={() => removeLocationActivity(index)}
                    >
                        <Trash2 className="w-4 h-4 mr-2" />
                        Remove Location
                    </Button>
                </Card>
            ))}
        </div>
    );
};

export default LocationActivitiesForm;
