"use client";

import { useMutation, useQueryClient } from "@tanstack/react-query";
import { Plus, Trash2, CreditCard, Edit, Save, X } from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";

import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import LoadableItemInput from "@/components/inputs/loadable-item";
import * as organizationService from "@/services/OrganizationService";
import { BankDetailsDto } from "@/types/organization.dto";

interface BankDetailsManagementProps {
    organizationId: string;
    bankDetails: BankDetailsDto[];
    canEdit: boolean;
}

interface BankDetailFormData {
    account_number: string;
    branch_name: string;
    bank_id: string;
}

const emptyBankDetail: BankDetailFormData = {
    account_number: "",
    branch_name: "",
    bank_id: "",
};

export default function BankDetailsManagement({ organizationId, bankDetails, canEdit }: BankDetailsManagementProps) {
    const [isDialogOpen, setIsDialogOpen] = useState(false);
    const [editingDetail, setEditingDetail] = useState<BankDetailsDto | null>(null);
    const [formData, setFormData] = useState<BankDetailFormData>(emptyBankDetail);
    const queryClient = useQueryClient();

    const addBankDetailMutation = useMutation({
        mutationFn: (data: BankDetailFormData) => organizationService.addBankDetails(organizationId, [data]),
        onSuccess: () => {
            toast.success("Bank details added successfully!");
            queryClient.invalidateQueries({ queryKey: ["organization-bank-details", organizationId] });
            setIsDialogOpen(false);
            setFormData(emptyBankDetail);
        },
        onError: (error: any) => {
            toast.error(error?.message || "Failed to add bank details");
        },
    });

    const updateBankDetailMutation = useMutation({
        mutationFn: ({ detailId, data }: { detailId: string; data: BankDetailFormData }) =>
            organizationService.updateBankDetail(organizationId, detailId, data),
        onSuccess: () => {
            toast.success("Bank details updated successfully!");
            queryClient.invalidateQueries({ queryKey: ["organization-bank-details", organizationId] });
            setIsDialogOpen(false);
            setEditingDetail(null);
            setFormData(emptyBankDetail);
        },
        onError: (error: any) => {
            toast.error(error?.message || "Failed to update bank details");
        },
    });

    const deleteBankDetailMutation = useMutation({
        mutationFn: (detailId: string) => organizationService.deleteBankDetail(organizationId, detailId),
        onSuccess: () => {
            toast.success("Bank details removed successfully!");
            queryClient.invalidateQueries({ queryKey: ["organization-bank-details", organizationId] });
        },
        onError: (error: any) => {
            toast.error(error?.message || "Failed to remove bank details");
        },
    });

    const handleAdd = () => {
        setEditingDetail(null);
        setFormData(emptyBankDetail);
        setIsDialogOpen(true);
    };

    const handleEdit = (detail: BankDetailsDto) => {
        setEditingDetail(detail);
        setFormData({
            account_number: detail.account_number || "",
            branch_name: detail.branch_name || "",
            bank_id: detail.bank_id || "",
        });
        setIsDialogOpen(true);
    };

    const handleSubmit = () => {
        if (editingDetail) {
            updateBankDetailMutation.mutate({ detailId: editingDetail.id, data: formData });
        } else {
            addBankDetailMutation.mutate(formData);
        }
    };

    const handleDelete = (detailId: string) => {
        if (confirm("Are you sure you want to remove this bank detail?")) {
            deleteBankDetailMutation.mutate(detailId);
        }
    };

    const updateFormData = (field: keyof BankDetailFormData, value: string) => {
        setFormData((prev) => ({ ...prev, [field]: value }));
    };

    const isPending = addBankDetailMutation.isPending || updateBankDetailMutation.isPending;

    return (
        <Card className="border-border/50 bg-card/30 backdrop-blur-sm shadow-lg">
            <CardHeader>
                <div className="flex justify-between items-center">
                    <CardTitle className="flex items-center gap-2 text-foreground">
                        <CreditCard className="w-5 h-5 text-primary" />
                        Bank Details ({bankDetails.length})
                    </CardTitle>
                    {canEdit && (
                        <Button size="sm" onClick={handleAdd}>
                            <Plus className="w-4 h-4 mr-2" />
                            Add Bank Details
                        </Button>
                    )}
                </div>
            </CardHeader>
            <CardContent>
                {bankDetails.length > 0 ? (
                    <div className="grid gap-4">
                        {bankDetails.map((detail) => (
                            <Card key={detail.id} className="border-border/50 bg-card/20 backdrop-blur-sm">
                                <CardContent className="p-4">
                                    <div className="flex justify-between items-start">
                                        <div className="flex-grow">
                                            <h3 className="font-semibold text-foreground">
                                                {detail.bank?.name || "Bank"}
                                            </h3>
                                            <p className="text-sm text-muted-foreground">
                                                Account: {detail.account_number}
                                            </p>
                                            <p className="text-sm text-muted-foreground">
                                                Branch: {detail.branch_name}
                                            </p>
                                        </div>
                                        {canEdit && (
                                            <div className="flex gap-2">
                                                <Button size="sm" variant="ghost" onClick={() => handleEdit(detail)}>
                                                    <Edit className="w-4 h-4" />
                                                </Button>
                                                <Button
                                                    disabled={deleteBankDetailMutation.isPending}
                                                    size="sm"
                                                    variant="ghost"
                                                    onClick={() => handleDelete(detail.id)}
                                                >
                                                    <Trash2 className="w-4 h-4" />
                                                </Button>
                                            </div>
                                        )}
                                    </div>
                                </CardContent>
                            </Card>
                        ))}
                    </div>
                ) : (
                    <div className="text-center py-8">
                        <CreditCard className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                        <p className="text-muted-foreground mb-4">No bank details added yet.</p>
                        {canEdit && (
                            <Button onClick={handleAdd}>
                                <Plus className="w-4 h-4 mr-2" />
                                Add Bank Details
                            </Button>
                        )}
                    </div>
                )}
            </CardContent>

            {/* Add/Edit Bank Details Dialog */}
            <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
                <DialogContent className="max-w-md">
                    <DialogHeader>
                        <DialogTitle>{editingDetail ? "Edit Bank Details" : "Add Bank Details"}</DialogTitle>
                    </DialogHeader>
                    <div className="space-y-4 py-4">
                        <div className="space-y-2">
                            <LoadableItemInput
                                label="Bank *"
                                type="BANK"
                                value={formData.bank_id}
                                onItemSelect={(item) => updateFormData("bank_id", item?.id || "")}
                            />
                        </div>
                        <div className="space-y-2">
                            <Label htmlFor="account_number">Account Number *</Label>
                            <Input
                                required
                                id="account_number"
                                placeholder="Account number"
                                value={formData.account_number}
                                onChange={(e) => updateFormData("account_number", e.target.value)}
                            />
                        </div>
                        <div className="space-y-2">
                            <Label htmlFor="branch_name">Branch Name *</Label>
                            <Input
                                required
                                id="branch_name"
                                placeholder="Branch name"
                                value={formData.branch_name}
                                onChange={(e) => updateFormData("branch_name", e.target.value)}
                            />
                        </div>
                    </div>
                    <div className="flex justify-end gap-2">
                        <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
                            <X className="w-4 h-4 mr-2" />
                            Cancel
                        </Button>
                        <Button
                            disabled={
                                !formData.bank_id || !formData.account_number || !formData.branch_name || isPending
                            }
                            onClick={handleSubmit}
                        >
                            {isPending ? (
                                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                            ) : (
                                <Save className="w-4 h-4 mr-2" />
                            )}
                            {editingDetail ? "Update" : "Add"} Bank Details
                        </Button>
                    </div>
                </DialogContent>
            </Dialog>
        </Card>
    );
}
