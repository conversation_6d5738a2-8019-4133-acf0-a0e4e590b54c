import { useQuery } from "@tanstack/react-query";
import { X } from "lucide-react";
import { useMemo } from "react";

import LoadableItemInput from "../inputs/loadable-item";
import { Button } from "../ui/button";

import { fetchLoadableItems } from "@/services/SettingsService";
import { LoadableItemDto } from "@/types";
import { OrganizationSectorDto } from "@/types/organization.dto";

interface SectorsFormProps {
    sectors: string[];
    setSectors: (sectors: string[]) => void;
    initialSectorData?: OrganizationSectorDto[];
}

const SectorsForm = ({ sectors, setSectors, initialSectorData = [] }: SectorsFormProps) => {
    const {
        data: allSectors = [],
        isLoading: isLoadingSectors,
        error: sectorsError,
    } = useQuery({
        queryKey: ["sectors", "SECTOR"],
        queryFn: async () => {
            const response = await fetchLoadableItems({
                type: "SECTOR",
                size: 50,
            });

            if (response.errors && response.errors.length > 0) {
                throw new Error(response.errors[0].message || "Failed to fetch sectors");
            }

            return response.data || [];
        },
        staleTime: 10 * 60 * 1000,
        gcTime: 30 * 60 * 1000,
        retry: 2,
        refetchOnWindowFocus: false,
        enabled: sectors.length > 0 || initialSectorData.length === 0,
    });

    const sectorLookup = useMemo(() => {
        const lookup: Record<string, Partial<OrganizationSectorDto>> = {};

        initialSectorData.forEach((sector) => {
            lookup[sector.sector_id] = sector;
        });

        allSectors.forEach((sector) => {
            lookup[sector.id] = {
                id: sector.id,
                name: sector.display_value,
                display_value: sector.display_value,
            };
        });

        return lookup;
    }, [allSectors, initialSectorData]);

    const addSector = (sector: LoadableItemDto | null) => {
        if (!sector) {
            return;
        }

        const all = sectors.map((item: string | LoadableItemDto) => (typeof item === "object" ? item.id : item));

        const isAlreadySelected = all.includes(sector.id);

        if (!isAlreadySelected) {
            setSectors([...all, sector.id]);
        }
    };

    const removeSector = (sectorId: string) => {
        const remainingSectors = sectors
            .filter((sector: string | LoadableItemDto) => {
                return typeof sector === "object" ? sector.id !== sectorId : sector !== sectorId;
            })
            .map((sector: string | LoadableItemDto) => {
                return typeof sector === "object" ? sector.id : sector;
            });

        setSectors(remainingSectors);
    };

    return (
        <div className="space-y-4">
            <div>
                <h3 className="text-lg font-semibold">Organization Sectors</h3>
                <p className="text-sm mt-1">Select the sectors your organization operates in</p>

                {/* Error message for API failures */}
                {sectorsError && (
                    <div className="mt-2 p-3 bg-red-50 border border-red-200 rounded-lg">
                        <p className="text-sm text-red-800">Failed to load sectors: {sectorsError.message}</p>
                    </div>
                )}
            </div>

            {/* Selected Sectors Display */}
            {sectors.length > 0 && (
                <div className="space-y-2">
                    <h4 className="text-sm font-medium text-gray-700">Selected Sectors ({sectors.length})</h4>
                    <div className="flex flex-wrap gap-2">
                        {sectors.map((sector, index) => {
                            const sectorData: Partial<OrganizationSectorDto> =
                                typeof sector === "object" ? sector : sectorLookup[sector];

                            if (!sectorData && isLoadingSectors) {
                                return (
                                    <div
                                        key={index}
                                        className="flex items-center gap-2 px-3 py-2 rounded-xl bg-foreground/10 transition-colors animate-pulse"
                                    >
                                        <span className="font-medium text-muted-foreground">Loading...</span>
                                        <Button
                                            disabled
                                            aria-label="Loading sector data"
                                            className="h-4 w-4 p-0 ml-1 text-white rounded-full opacity-50"
                                            size="sm"
                                            variant="ghost"
                                        >
                                            <X className="h-3 w-3" />
                                        </Button>
                                    </div>
                                );
                            }

                            if (!sectorData && sectorsError) {
                                return (
                                    <div
                                        key={index}
                                        className="flex items-center gap-2 px-3 py-2 rounded-xl bg-red-100 border border-red-200 text-red-800"
                                    >
                                        <span className="font-medium text-xs">Error loading sector</span>
                                        <Button
                                            aria-label={`Remove sector ${sector}`}
                                            className="h-4 w-4 p-0 ml-1 hover:bg-red-500 text-red-600 rounded-full"
                                            size="sm"
                                            variant="ghost"
                                            onClick={() => removeSector(sector)}
                                        >
                                            <X className="h-3 w-3" />
                                        </Button>
                                    </div>
                                );
                            }

                            if (!sectorData) {
                                return null;
                            }

                            return (
                                <div
                                    key={sectorData.id}
                                    className="flex items-center gap-2 px-3 py-2 rounded-xl bg-foreground/10 transition-colors"
                                >
                                    <span className="font-medium">{sectorData.display_value}</span>
                                    {sectorData.code && <span className="text-xs opacity-75">({sectorData.code})</span>}
                                    <Button
                                        aria-label={`Remove ${sectorData.display_value} sector`}
                                        className="h-4 w-4 p-0 ml-1 hover:bg-red-500 text-white rounded-full"
                                        size="sm"
                                        variant="ghost"
                                        onClick={() => removeSector(sectorData.id!)}
                                    >
                                        <X className="h-3 w-3" />
                                    </Button>
                                </div>
                            );
                        })}
                    </div>
                </div>
            )}

            {/* Sector Selection */}
            <div className="space-y-2">
                <LoadableItemInput
                    className="w-full"
                    label="Add New Sector"
                    placeholder="Search and select a sector..."
                    type="SECTOR"
                    onItemSelect={addSector}
                />
            </div>

            {/* Empty State */}
            {sectors.length === 0 && (
                <div className="text-center py-8 rounded-lg border-2 border-dashed border-white/20">
                    <div className="text-sm">No sectors selected yet</div>
                    <div className="text-xs mt-1">Use the search above to add sectors</div>
                </div>
            )}
        </div>
    );
};

export default SectorsForm;
