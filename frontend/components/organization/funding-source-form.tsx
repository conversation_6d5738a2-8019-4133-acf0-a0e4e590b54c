import { Plus, Trash2 } from "lucide-react";

import LoadableItemInput from "../inputs/loadable-item";
import { ManagerInput } from "../inputs/manager";
import { Button } from "../ui/button";
import { Card } from "../ui/card";
import { Input } from "../ui/input";

import { FundingSourceRequest } from "@/types";

const FundingSourceForm = ({
    fundingSources,
    setFundingSources,
}: {
    fundingSources: FundingSourceRequest[];
    setFundingSources: (sources: FundingSourceRequest[]) => void;
}) => {
    const addFundingSource = () => {
        setFundingSources([
            ...fundingSources,
            {
                donor_id: "",
                currency_id: "",
                contact_person: "",
                amount: 0,
            },
        ]);
    };

    const updateFundingSource = (index: number, field: keyof FundingSourceRequest, value: any) => {
        const updated = [...fundingSources];

        updated[index] = { ...updated[index], [field]: value };
        setFundingSources(updated);
    };

    const removeFundingSource = (index: number) => {
        setFundingSources(fundingSources.filter((_, i) => i !== index));
    };

    return (
        <div className="space-y-4">
            <div className="flex justify-between items-center">
                <h3 className="text-lg font-semibold">Funding Sources</h3>
                <Button size="sm" onClick={addFundingSource}>
                    <Plus className="w-4 h-4 mr-2" />
                    Add Source
                </Button>
            </div>

            {fundingSources.map((source, index) => (
                <Card key={index} className="p-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <LoadableItemInput
                            label="Donor Name"
                            placeholder="Enter donor"
                            type="DONOR"
                            value={source.donor_id}
                            onItemSelect={(e) => updateFundingSource(index, "donor_id", e?.id!)}
                        />
                        <ManagerInput
                            label="Currency"
                            placeholder="Select currency"
                            type="currency"
                            value={source.currency_id}
                            onItemSelect={(e) => updateFundingSource(index, "currency_id", e?.id)}
                        />
                        <Input
                            label="Contact Person"
                            placeholder="Contact person name"
                            value={source.contact_person}
                            onChange={(e) => updateFundingSource(index, "contact_person", e.target.value)}
                        />
                        <Input
                            label="Amount"
                            placeholder="Funding amount"
                            type="number"
                            value={source.amount}
                            onChange={(e) => updateFundingSource(index, "amount", parseFloat(e.target.value) || 0)}
                        />
                    </div>
                    <Button className="mt-2" size="sm" variant="destructive" onClick={() => removeFundingSource(index)}>
                        <Trash2 className="w-4 h-4 mr-2" />
                        Remove
                    </Button>
                </Card>
            ))}
        </div>
    );
};

export default FundingSourceForm;
