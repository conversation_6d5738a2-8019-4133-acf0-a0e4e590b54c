"use client";

import { useMutation } from "@tanstack/react-query";
import { Crown, MoreVertical, Trash2, User, Users } from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";

import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
} from "@/components/ui/dialog";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import { Pagination } from "@/components/ui/pagination";
import * as membershipService from "@/services/MembershipService";

const roleConfig = {
    OWNER: { label: "Owner", icon: Crown, color: "bg-yellow-100 text-yellow-800 border-yellow-200" },
    MEMBER: { label: "Member", icon: User, color: "bg-gray-100 text-gray-800 border-gray-200" },
};

interface Member {
    id: string;
    user_name?: string;
    user_email?: string;
    user_avatar?: string;
    role: "OWNER" | "MEMBER";
    joined_at: string;
}

interface MembersTabProps {
    organizationId: string;
    members: Member[];
    membersTotal: number;
    membersSearch: string;
    setMembersSearch: (value: string) => void;
    membersPagination: { page: number; size: number };
    handleMembersPageChange: (page: number) => void;
    setShowInviteDialog: (value: boolean) => void;
    ownerCount: number;
    onDataChange: () => void;
}

export default function MembersTab({
    organizationId,
    members,
    membersTotal,
    membersSearch,
    setMembersSearch,
    membersPagination,
    handleMembersPageChange,
    setShowInviteDialog,
    ownerCount,
    onDataChange,
}: MembersTabProps) {
    const [showRemoveMemberDialog, setShowRemoveMemberDialog] = useState(false);
    const [memberToRemove, setMemberToRemove] = useState<{ id: string; name: string } | null>(null);

    const [showChangeRoleDialog, setShowChangeRoleDialog] = useState(false);
    const [memberToChangeRole, setMemberToChangeRole] = useState<{
        id: string;
        name: string;
        currentRole: "OWNER" | "MEMBER";
    } | null>(null);
    const [newRole, setNewRole] = useState<"OWNER" | "MEMBER">("MEMBER");

    const removeMemberMutation = useMutation({
        mutationFn: (memberId: string) => membershipService.removeMember(organizationId, memberId),
        onSuccess: (response) => {
            if (response.success) {
                toast.success("Member removed successfully!");
                setShowRemoveMemberDialog(false);
                setMemberToRemove(null);
                onDataChange();
            } else {
                toast.error(response.errors?.[0]?.message || "Failed to remove member");
            }
        },
        onError: (error: any) => {
            toast.error(error?.message || "Failed to remove member");
        },
    });

    const changeMemberRoleMutation = useMutation({
        mutationFn: ({ memberId, role }: { memberId: string; role: "OWNER" | "MEMBER" }) =>
            membershipService.changeMemberRole(organizationId, memberId, role),
        onSuccess: (response) => {
            if (response.success) {
                toast.success("Member role updated successfully!");
                setShowChangeRoleDialog(false);
                setMemberToChangeRole(null);
                onDataChange();
            } else {
                toast.error(response.errors?.[0]?.message || "Failed to update member role");
            }
        },
        onError: (error: any) => {
            toast.error(error?.message || "Failed to update member role");
        },
    });

    const handleRemoveMember = (memberId: string, memberName: string) => {
        setMemberToRemove({ id: memberId, name: memberName });
        setShowRemoveMemberDialog(true);
    };

    const handleChangeRole = (memberId: string, memberName: string, currentRole: "OWNER" | "MEMBER") => {
        setMemberToChangeRole({ id: memberId, name: memberName, currentRole });
        setNewRole(currentRole === "OWNER" ? "MEMBER" : "OWNER");
        setShowChangeRoleDialog(true);
    };

    const confirmRemoveMember = () => {
        if (memberToRemove) {
            removeMemberMutation.mutate(memberToRemove.id);
        }
    };

    const confirmChangeRole = () => {
        if (memberToChangeRole) {
            changeMemberRoleMutation.mutate({
                memberId: memberToChangeRole.id,
                role: newRole,
            });
        }
    };

    return (
        <Card className="border-border/50 bg-card/30 backdrop-blur-sm">
            <CardHeader>
                <div className="flex items-center justify-between">
                    <CardTitle className="flex items-center gap-2">
                        <Users className="w-5 h-5 text-primary" />
                        Team Members
                    </CardTitle>
                    <div className="flex items-center gap-2">
                        <Input
                            className="max-w-xs"
                            placeholder="Search members..."
                            value={membersSearch}
                            onChange={(e) => setMembersSearch(e.target.value)}
                        />
                    </div>
                </div>
            </CardHeader>
            <CardContent>
                {members.length === 0 ? (
                    <div className="text-center py-8">
                        <Users className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                        <p className="text-muted-foreground mb-4">No members yet</p>
                        <Button onClick={() => setShowInviteDialog(true)}>Invite your first member</Button>
                    </div>
                ) : (
                    <div className="space-y-4">
                        {members.map((member) => {
                            const roleInfo = roleConfig[member.role as keyof typeof roleConfig];
                            const RoleIcon = roleInfo.icon;

                            return (
                                <div
                                    key={member.id}
                                    className="flex items-center justify-between p-4 bg-card/20 rounded-lg border border-border/50"
                                >
                                    <div className="flex items-center space-x-4">
                                        <Avatar className="h-10 w-10">
                                            <AvatarImage src={member.user_avatar || undefined} />
                                            <AvatarFallback className="bg-primary/10 text-primary">
                                                {member.user_name
                                                    ? member.user_name
                                                          .split(" ")
                                                          .map((n) => n[0])
                                                          .join("")
                                                    : member.user_email?.charAt(0).toUpperCase()}
                                            </AvatarFallback>
                                        </Avatar>
                                        <div>
                                            <div className="flex items-center space-x-2">
                                                <p className="font-medium text-foreground">
                                                    {member.user_name || member.user_email}
                                                </p>
                                                <Badge className={`${roleInfo.color} border text-xs`}>
                                                    <RoleIcon className="w-3 h-3 mr-1" />
                                                    {roleInfo.label}
                                                </Badge>
                                                <Badge className="bg-green-100 text-green-800 border-green-200 text-xs">
                                                    Active
                                                </Badge>
                                            </div>
                                            <p className="text-sm text-muted-foreground">
                                                {member.user_email}
                                                <span className="ml-2">
                                                    • Joined {new Date(member.joined_at).toLocaleDateString()}
                                                </span>
                                            </p>
                                        </div>
                                    </div>

                                    {member.role !== "OWNER" || (member.role === "OWNER" && ownerCount > 1) ? (
                                        <DropdownMenu>
                                            <DropdownMenuTrigger asChild>
                                                <Button size="sm" variant="ghost">
                                                    <MoreVertical className="w-4 h-4" />
                                                </Button>
                                            </DropdownMenuTrigger>
                                            <DropdownMenuContent align="end">
                                                <DropdownMenuItem
                                                    onClick={() =>
                                                        handleChangeRole(
                                                            member.id,
                                                            member.user_name || member.user_email || "",
                                                            member.role,
                                                        )
                                                    }
                                                >
                                                    <Crown className="w-4 h-4 mr-2" />
                                                    {member.role === "OWNER" ? "Change to Member" : "Promote to Owner"}
                                                </DropdownMenuItem>
                                                {member.role !== "OWNER" && (
                                                    <>
                                                        <DropdownMenuSeparator />
                                                        <DropdownMenuItem
                                                            className="text-destructive"
                                                            onClick={() =>
                                                                handleRemoveMember(
                                                                    member.id,
                                                                    member.user_name || member.user_email || "",
                                                                )
                                                            }
                                                        >
                                                            <Trash2 className="w-4 h-4 mr-2" />
                                                            Remove Member
                                                        </DropdownMenuItem>
                                                    </>
                                                )}
                                            </DropdownMenuContent>
                                        </DropdownMenu>
                                    ) : null}
                                </div>
                            );
                        })}
                    </div>
                )}
                <Pagination
                    className="mt-4"
                    currentPage={membersPagination.page}
                    pageSize={membersPagination.size}
                    showInfo={true}
                    totalItems={membersTotal}
                    totalPages={Math.ceil(membersTotal / membersPagination.size)}
                    onPageChange={handleMembersPageChange}
                />
            </CardContent>

            {/* Remove Member Dialog */}
            <Dialog open={showRemoveMemberDialog} onOpenChange={setShowRemoveMemberDialog}>
                <DialogContent>
                    <DialogHeader>
                        <DialogTitle>Remove Member</DialogTitle>
                        <DialogDescription>
                            Are you sure you want to remove <strong>{memberToRemove?.name || "this member"}</strong>{" "}
                            from the organization? This action cannot be undone and they will lose access to the
                            organization.
                        </DialogDescription>
                    </DialogHeader>
                    <DialogFooter>
                        <Button variant="outline" onClick={() => setShowRemoveMemberDialog(false)}>
                            Cancel
                        </Button>
                        <Button
                            disabled={removeMemberMutation.isPending}
                            variant="destructive"
                            onClick={confirmRemoveMember}
                        >
                            {removeMemberMutation.isPending ? (
                                <>
                                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2" />
                                    Removing...
                                </>
                            ) : (
                                <>
                                    <Trash2 className="w-4 h-4 mr-2" />
                                    Remove Member
                                </>
                            )}
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>

            {/* Change Role Dialog */}
            <Dialog open={showChangeRoleDialog} onOpenChange={setShowChangeRoleDialog}>
                <DialogContent>
                    <DialogHeader>
                        <DialogTitle>Change Member Role</DialogTitle>
                        <DialogDescription>
                            Are you sure you want to change <strong>{memberToChangeRole?.name}</strong>&apos;s role from{" "}
                            <strong>{memberToChangeRole?.currentRole === "OWNER" ? "Owner" : "Member"}</strong> to{" "}
                            <strong>{newRole === "OWNER" ? "Owner" : "Member"}</strong>?
                            {newRole === "OWNER" && (
                                <>
                                    <br />
                                    <br />
                                    <span className="text-sm text-muted-foreground">
                                        This will give them full administrative access to the organization.
                                    </span>
                                </>
                            )}
                        </DialogDescription>
                    </DialogHeader>
                    <DialogFooter>
                        <Button variant="outline" onClick={() => setShowChangeRoleDialog(false)}>
                            Cancel
                        </Button>
                        <Button disabled={changeMemberRoleMutation.isPending} onClick={confirmChangeRole}>
                            {changeMemberRoleMutation.isPending ? (
                                <>
                                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2" />
                                    Updating...
                                </>
                            ) : (
                                <>
                                    <Crown className="w-4 h-4 mr-2" />
                                    Change Role
                                </>
                            )}
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        </Card>
    );
}
