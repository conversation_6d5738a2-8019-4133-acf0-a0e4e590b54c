"use client";

import { useMutation } from "@tanstack/react-query";
import { Crown, Mail, MoreVertical, RotateCcw, Trash2, User, UserX } from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";

import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
} from "@/components/ui/dialog";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import { Pagination } from "@/components/ui/pagination";
import { Select } from "@/components/ui/select";
import * as membershipService from "@/services/MembershipService";
import { MemberInvitationDto } from "@/types";

const roleConfig = {
    OWNER: { label: "Owner", icon: Crown, color: "bg-yellow-100 text-yellow-800 border-yellow-200" },
    MEMBER: { label: "Member", icon: User, color: "bg-gray-100 text-gray-800 border-gray-200" },
};

const statusConfig = {
    ACTIVE: { label: "Active", color: "bg-green-100 text-green-800 border-green-200" },
    PENDING: { label: "Pending", color: "bg-yellow-100 text-yellow-800 border-yellow-200" },
    ACCEPTED: { label: "Accepted", color: "bg-green-100 text-green-800 border-green-200" },
    REJECTED: { label: "Rejected", color: "bg-red-100 text-red-800 border-red-200" },
    CANCELLED: { label: "Cancelled", color: "bg-gray-100 text-gray-800 border-gray-200" },
};

const invitationStatusOptions = [
    { value: "all", label: "All Invitations" },
    { value: "PENDING", label: "Pending" },
    { value: "ACCEPTED", label: "Accepted" },
    { value: "REJECTED", label: "Rejected" },
    { value: "CANCELLED", label: "Cancelled" },
];

interface InvitationsTabProps {
    organizationId: string;
    invitations: MemberInvitationDto[];
    invitationsTotal: number;
    invitationsSearch: string;
    setInvitationsSearch: (value: string) => void;
    invitationStatusFilter: string;
    setInvitationStatusFilter: (value: string) => void;
    invitationsPagination: { page: number; size: number };
    handleInvitationsPageChange: (page: number) => void;
    setShowInviteDialog: (value: boolean) => void;
    onDataChange: () => void;
}

export default function InvitationsTab({
    organizationId,
    invitations,
    invitationsTotal,
    invitationsSearch,
    setInvitationsSearch,
    invitationStatusFilter,
    setInvitationStatusFilter,
    invitationsPagination,
    handleInvitationsPageChange,
    setShowInviteDialog,
    onDataChange,
}: InvitationsTabProps) {
    const [showCancelInvitationDialog, setShowCancelInvitationDialog] = useState(false);
    const [invitationToCancel, setInvitationToCancel] = useState<{ id: string; email: string } | null>(null);

    const [showRemoveInvitationDialog, setShowRemoveInvitationDialog] = useState(false);
    const [invitationToRemove, setInvitationToRemove] = useState<{ id: string; email: string } | null>(null);

    const cancelInvitationMutation = useMutation({
        mutationFn: (invitationId: string) => membershipService.cancelInvitation(organizationId, invitationId),
        onSuccess: (response) => {
            if (response.success) {
                toast.success("Invitation cancelled successfully!");
                setShowCancelInvitationDialog(false);
                setInvitationToCancel(null);
                onDataChange();
            } else {
                toast.error(response.errors?.[0]?.message || "Failed to cancel invitation");
            }
        },
        onError: (error: any) => {
            toast.error(error?.message || "Failed to cancel invitation");
        },
    });

    const retryInvitationMutation = useMutation({
        mutationFn: ({ email, role }: { email: string; role: "OWNER" | "MEMBER" }) =>
            membershipService.retryInvitation(organizationId, email, role),
        onSuccess: (response) => {
            if (response.success) {
                toast.success("Invitation resent successfully!");
                onDataChange();
            } else {
                toast.error(response.errors?.[0]?.message || "Failed to resend invitation");
            }
        },
        onError: (error: any) => {
            toast.error(error?.message || "Failed to resend invitation");
        },
    });

    const removeInvitationMutation = useMutation({
        mutationFn: (invitationId: string) => membershipService.removeInvitation(organizationId, invitationId),
        onSuccess: (response) => {
            if (response.success) {
                toast.success("Invitation removed successfully!");
                setShowRemoveInvitationDialog(false);
                setInvitationToRemove(null);
                onDataChange();
            } else {
                toast.error(response.errors?.[0]?.message || "Failed to remove invitation");
            }
        },
        onError: (error: any) => {
            toast.error(error?.message || "Failed to remove invitation");
        },
    });

    const handleCancelInvitation = (invitationId: string, email: string) => {
        setInvitationToCancel({ id: invitationId, email });
        setShowCancelInvitationDialog(true);
    };

    const handleRetryInvitation = (invitation: MemberInvitationDto) => {
        retryInvitationMutation.mutate({
            email: invitation.invited_email,
            role: invitation.role,
        });
    };

    const handleRemoveInvitation = (invitationId: string, email: string) => {
        setInvitationToRemove({ id: invitationId, email });
        setShowRemoveInvitationDialog(true);
    };

    const confirmCancelInvitation = () => {
        if (invitationToCancel) {
            cancelInvitationMutation.mutate(invitationToCancel.id);
        }
    };

    const confirmRemoveInvitation = () => {
        if (invitationToRemove) {
            removeInvitationMutation.mutate(invitationToRemove.id);
        }
    };

    return (
        <Card className="border-border/50 bg-card/30 backdrop-blur-sm">
            <CardHeader>
                <div className="flex items-center justify-between">
                    <CardTitle className="flex items-center gap-2">
                        <Mail className="w-5 h-5 text-primary" />
                        Member Invitations
                    </CardTitle>
                    <div className="flex items-center gap-2">
                        <Input
                            className="max-w-xs"
                            placeholder="Search invitations..."
                            value={invitationsSearch}
                            onChange={(e) => setInvitationsSearch(e.target.value)}
                        />
                        <Select
                            options={invitationStatusOptions}
                            placeholder="Filter by status"
                            value={invitationStatusFilter}
                            onValueChange={setInvitationStatusFilter}
                        />
                    </div>
                </div>
            </CardHeader>
            <CardContent>
                {invitations.length === 0 ? (
                    <div className="text-center py-8">
                        <Mail className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                        <p className="text-muted-foreground mb-4">
                            {invitationStatusFilter === "all"
                                ? "No invitations sent"
                                : `No ${invitationStatusFilter.toLowerCase()} invitations`}
                        </p>
                        <Button onClick={() => setShowInviteDialog(true)}>Send your first invitation</Button>
                    </div>
                ) : (
                    <div className="space-y-4">
                        {invitations.map((invitation) => {
                            const roleInfo = roleConfig[invitation.role as keyof typeof roleConfig];
                            const statusInfo = statusConfig[invitation.status as keyof typeof statusConfig];
                            const RoleIcon = roleInfo.icon;

                            return (
                                <div
                                    key={invitation.id}
                                    className="flex items-center justify-between p-4 bg-card/20 rounded-lg border border-border/50"
                                >
                                    <div className="flex items-center space-x-4">
                                        <Avatar className="h-10 w-10">
                                            <AvatarFallback className="bg-primary/10 text-primary">
                                                {invitation.invited_email.charAt(0).toUpperCase()}
                                            </AvatarFallback>
                                        </Avatar>
                                        <div>
                                            <div className="flex items-center space-x-2">
                                                <p className="font-medium text-foreground">
                                                    {invitation.invited_email}
                                                </p>
                                                <Badge className={`${roleInfo.color} border text-xs`}>
                                                    <RoleIcon className="w-3 h-3 mr-1" />
                                                    {roleInfo.label}
                                                </Badge>
                                                <Badge className={`${statusInfo.color} border text-xs`}>
                                                    {statusInfo.label}
                                                </Badge>
                                            </div>
                                            <p className="text-sm text-muted-foreground">
                                                Invited by {invitation.inviter_name} •{" "}
                                                {new Date(invitation.created_at).toLocaleDateString()}
                                                {invitation.status === "PENDING" && invitation.expires_at && (
                                                    <span className="ml-2">
                                                        • Expires {new Date(invitation.expires_at).toLocaleDateString()}
                                                    </span>
                                                )}
                                            </p>
                                        </div>
                                    </div>

                                    <DropdownMenu>
                                        <DropdownMenuTrigger asChild>
                                            <Button size="sm" variant="ghost">
                                                <MoreVertical className="w-4 h-4" />
                                            </Button>
                                        </DropdownMenuTrigger>
                                        <DropdownMenuContent align="end">
                                            {invitation.status === "PENDING" && (
                                                <DropdownMenuItem
                                                    onClick={() =>
                                                        handleCancelInvitation(invitation.id, invitation.invited_email)
                                                    }
                                                >
                                                    <UserX className="w-4 h-4 mr-2" />
                                                    Cancel Invitation
                                                </DropdownMenuItem>
                                            )}

                                            {(invitation.status === "CANCELLED" ||
                                                invitation.status === "REJECTED") && (
                                                <>
                                                    <DropdownMenuItem onClick={() => handleRetryInvitation(invitation)}>
                                                        <RotateCcw className="w-4 h-4 mr-2" />
                                                        Retry Invitation
                                                    </DropdownMenuItem>
                                                    <DropdownMenuSeparator />
                                                </>
                                            )}

                                            {(invitation.status === "CANCELLED" ||
                                                invitation.status === "REJECTED" ||
                                                invitation.status === "ACCEPTED") && (
                                                <DropdownMenuItem
                                                    className="text-destructive"
                                                    onClick={() =>
                                                        handleRemoveInvitation(invitation.id, invitation.invited_email)
                                                    }
                                                >
                                                    <Trash2 className="w-4 h-4 mr-2" />
                                                    Remove from List
                                                </DropdownMenuItem>
                                            )}
                                        </DropdownMenuContent>
                                    </DropdownMenu>
                                </div>
                            );
                        })}
                    </div>
                )}
                <Pagination
                    className="mt-4"
                    currentPage={invitationsPagination.page}
                    pageSize={invitationsPagination.size}
                    showInfo={true}
                    totalItems={invitationsTotal}
                    totalPages={Math.ceil(invitationsTotal / invitationsPagination.size)}
                    onPageChange={handleInvitationsPageChange}
                />
            </CardContent>

            {/* Cancel Invitation Dialog */}
            <Dialog open={showCancelInvitationDialog} onOpenChange={setShowCancelInvitationDialog}>
                <DialogContent>
                    <DialogHeader>
                        <DialogTitle>Cancel Invitation</DialogTitle>
                        <DialogDescription>
                            Are you sure you want to cancel the invitation for{" "}
                            <strong>{invitationToCancel?.email}</strong>? This action cannot be undone.
                        </DialogDescription>
                    </DialogHeader>
                    <DialogFooter>
                        <Button variant="outline" onClick={() => setShowCancelInvitationDialog(false)}>
                            Keep Invitation
                        </Button>
                        <Button
                            disabled={cancelInvitationMutation.isPending}
                            variant="destructive"
                            onClick={confirmCancelInvitation}
                        >
                            {cancelInvitationMutation.isPending ? (
                                <>
                                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2" />
                                    Cancelling...
                                </>
                            ) : (
                                <>
                                    <UserX className="w-4 h-4 mr-2" />
                                    Cancel Invitation
                                </>
                            )}
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>

            {/* Remove Invitation Dialog */}
            <Dialog open={showRemoveInvitationDialog} onOpenChange={setShowRemoveInvitationDialog}>
                <DialogContent>
                    <DialogHeader>
                        <DialogTitle>Remove Invitation</DialogTitle>
                        <DialogDescription>
                            Are you sure you want to remove the invitation for{" "}
                            <strong>{invitationToRemove?.email}</strong>? This action cannot be undone and the
                            invitation record will be permanently deleted.
                        </DialogDescription>
                    </DialogHeader>
                    <DialogFooter>
                        <Button variant="outline" onClick={() => setShowRemoveInvitationDialog(false)}>
                            Cancel
                        </Button>
                        <Button
                            disabled={removeInvitationMutation.isPending}
                            variant="destructive"
                            onClick={confirmRemoveInvitation}
                        >
                            {removeInvitationMutation.isPending ? (
                                <>
                                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2" />
                                    Removing...
                                </>
                            ) : (
                                <>
                                    <Trash2 className="w-4 h-4 mr-2" />
                                    Remove Invitation
                                </>
                            )}
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        </Card>
    );
}
