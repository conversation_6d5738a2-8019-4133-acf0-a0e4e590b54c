import { Plus, Trash2 } from "lucide-react";

import LoadableItemInput from "../inputs/loadable-item";
import { Button } from "../ui/button";
import { Card } from "../ui/card";
import { Checkbox } from "../ui/checkbox";
import { Label } from "../ui/label";

import { TargetGroupRequest } from "@/types";

const TargetGroupsForm = ({
    targetGroups,
    setTargetGroups,
}: {
    targetGroups: TargetGroupRequest[];
    setTargetGroups: (groups: TargetGroupRequest[]) => void;
}) => {
    const addTargetGroup = () => {
        setTargetGroups([
            ...targetGroups,
            {
                type_id: "",
                is_active: true,
            },
        ]);
    };

    const updateTargetGroup = (index: number, field: keyof TargetGroupRequest, value: any) => {
        const updated = [...targetGroups];

        updated[index] = { ...updated[index], [field]: value };
        setTargetGroups(updated);
    };

    const removeTargetGroup = (index: number) => {
        setTargetGroups(targetGroups.filter((_, i) => i !== index));
    };

    return (
        <div className="space-y-4">
            <div className="flex justify-between items-center">
                <h3 className="text-lg font-semibold">Target Groups</h3>
                <Button size="sm" onClick={addTargetGroup}>
                    <Plus className="w-4 h-4 mr-2" />
                    Add Target Group
                </Button>
            </div>

            {targetGroups.map((group, index) => (
                <Card key={index} className="p-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <LoadableItemInput
                            label="Target Group Type"
                            placeholder="Target group type"
                            type="TARGET_GROUP"
                            value={group.type_id}
                            onItemSelect={(e) => updateTargetGroup(index, "type_id", e?.id!)}
                        />
                        <div className="flex items-center space-x-2">
                            <Checkbox
                                checked={group.is_active}
                                id={`active-${index}`}
                                onCheckedChange={(checked) => updateTargetGroup(index, "is_active", checked)}
                            />
                            <Label htmlFor={`active-${index}`}>Active</Label>
                        </div>
                    </div>
                    <Button className="mt-2" size="sm" variant="destructive" onClick={() => removeTargetGroup(index)}>
                        <Trash2 className="w-4 h-4 mr-2" />
                        Remove Target Group
                    </Button>
                </Card>
            ))}
        </div>
    );
};

export default TargetGroupsForm;
