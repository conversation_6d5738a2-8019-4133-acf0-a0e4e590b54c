import { Plus, Trash2 } from "lucide-react";

import LoadableItemInput from "../inputs/loadable-item";
import { ManagerInput } from "../inputs/manager";
import { Button } from "../ui/button";
import { Card } from "../ui/card";
import { Checkbox } from "../ui/checkbox";
import { Input } from "../ui/input";

import { OrganizationDonorRequest } from "@/types";

const DonorsForm = ({
    donors,
    setDonors,
}: {
    donors: OrganizationDonorRequest[];
    setDonors: (donors: OrganizationDonorRequest[]) => void;
}) => {
    const addDonor = () => {
        setDonors([
            ...donors,
            {
                donor_id: "",
                is_active: true,
                amount: 0,
                currency_id: "",
            },
        ]);
    };

    const updateDonor = (index: number, field: keyof OrganizationDonorRequest, value: any) => {
        const updated = [...donors];

        updated[index] = { ...updated[index], [field]: value };
        setDonors(updated);
    };

    const removeDonor = (index: number) => {
        setDonors(donors.filter((_, i) => i !== index));
    };

    return (
        <div className="space-y-4">
            <div className="flex justify-between items-center">
                <h3 className="text-lg font-semibold">Organization Donors</h3>
                <Button size="sm" onClick={addDonor}>
                    <Plus className="w-4 h-4 mr-2" />
                    Add Donor
                </Button>
            </div>

            {donors.map((donor, index) => (
                <Card key={index} className="p-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <LoadableItemInput
                            label="Donor Name"
                            placeholder="Enter donor"
                            type="DONOR"
                            value={donor.donor_id}
                            onItemSelect={(e) => updateDonor(index, "donor_id", e?.id!)}
                        />
                        <ManagerInput
                            label="Currency"
                            name={donor.currency_name || ""}
                            placeholder="Select Currency"
                            type="currency"
                            value={donor.currency_id || ""}
                            onItemSelect={(e) => updateDonor(index, "currency_id", e?.id!)}
                        />
                        <Input
                            label="Amount"
                            placeholder="Donation amount"
                            type="number"
                            value={donor.amount}
                            onChange={(e) => updateDonor(index, "amount", parseFloat(e.target.value) || 0)}
                        />
                        <Checkbox
                            checked={donor.is_active}
                            id={`donor-active-${index}`}
                            label="Active"
                            onCheckedChange={(checked) => updateDonor(index, "is_active", checked)}
                        />
                    </div>
                    <Button className="mt-2" size="sm" variant="destructive" onClick={() => removeDonor(index)}>
                        <Trash2 className="w-4 h-4 mr-2" />
                        Remove Donor
                    </Button>
                </Card>
            ))}
        </div>
    );
};

export default DonorsForm;
