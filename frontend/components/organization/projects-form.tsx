import { Plus, Trash2 } from "lucide-react";

import { But<PERSON> } from "../ui/button";
import { Card } from "../ui/card";
import { Checkbox } from "../ui/checkbox";
import { Input } from "../ui/input";
import LoadableItemInput from "../inputs/loadable-item";

import { OrganizationProjectRequest } from "@/types";

const ProjectsForm = ({
    projects,
    setProjects,
}: {
    projects: OrganizationProjectRequest[];
    setProjects: (projects: OrganizationProjectRequest[]) => void;
}) => {
    const addProject = () => {
        setProjects([
            ...projects,
            {
                name: "",
                thematic_area_id: "",
                number_of_beneficiaries: 0,
                is_active: true,
            },
        ]);
    };

    const updateProject = (index: number, field: keyof OrganizationProjectRequest, value: any) => {
        const updated = [...projects];

        updated[index] = { ...updated[index], [field]: value };
        setProjects(updated);
    };

    const removeProject = (index: number) => {
        setProjects(projects.filter((_, i) => i !== index));
    };

    return (
        <div className="space-y-4">
            <div className="flex justify-between items-center">
                <h3 className="text-lg font-semibold">Organization Projects</h3>
                <Button size="sm" onClick={addProject}>
                    <Plus className="w-4 h-4 mr-2" />
                    Add Project
                </Button>
            </div>

            {projects.map((project, index) => (
                <Card key={index} className="p-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <Input
                            label="Project Name"
                            placeholder="Project name"
                            value={project.name}
                            onChange={(e) => updateProject(index, "name", e.target.value)}
                        />
                        <LoadableItemInput
                            label="Thematic Area"
                            placeholder="Thematic area"
                            type="THEMATIC_AREA"
                            value={project.thematic_area_id}
                            onItemSelect={(e) => updateProject(index, "thematic_area_id", e?.id)}
                        />
                        <Input
                            label="Number of Beneficiaries"
                            placeholder="Number of beneficiaries"
                            type="number"
                            value={project.number_of_beneficiaries}
                            onChange={(e) =>
                                updateProject(index, "number_of_beneficiaries", parseInt(e.target.value) || 0)
                            }
                        />
                        <Checkbox
                            checked={project.is_active}
                            id={`project-active-${index}`}
                            label="Active"
                            onCheckedChange={(checked) => updateProject(index, "is_active", checked)}
                        />
                    </div>
                    <Button className="mt-2" size="sm" variant="destructive" onClick={() => removeProject(index)}>
                        <Trash2 className="w-4 h-4 mr-2" />
                        Remove Project
                    </Button>
                </Card>
            ))}
        </div>
    );
};

export default ProjectsForm;
