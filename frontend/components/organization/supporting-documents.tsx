import { useQuery } from "@tanstack/react-query";
import { AlertCircle, Check, Eye, Loader2, Plus, X } from "lucide-react";
import React, { useCallback, useEffect, useMemo, useRef, useState } from "react";

import { FileUpload } from "../ui/file-upload";

import { DocumentViewer } from "@/components/document/DocumentViewer";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import api from "@/config/api.config";
import { ALLOWED_DOCUMENT_TYPES } from "@/config/site";
import { cn } from "@/lib/utils";
import * as settingsService from "@/services/SettingsService";
import { LoadableItemDto } from "@/types";
import { ApplicationDocumentDto } from "@/types/document.dto";
import { formatFileSize, getFileIconAndColor } from "@/utils/common";

interface DocumentUpload {
    id: string;
    documentType: LoadableItemDto | null;
    file: File | null;
    isRequired: boolean;
    previewUrl?: string;
    existingDocument?: ApplicationDocumentDto;
}

export type SupportingDocumentOutput = { documentTypes: string[]; files: File[] };

interface SupportingDocumentsProps {
    onDocumentsChange: (documents: SupportingDocumentOutput) => void;
    className?: string;
    initialDocumentTypes?: string[];
    initialFiles?: File[];
    existingDocuments?: ApplicationDocumentDto[];
    applicationMode?: "registration" | "renewal";
}

interface DocumentCardProps {
    doc: DocumentUpload;
    onFileChange: (id: string, file: File | null) => void;
    onPreview: (file: File) => void;
    onPreviewExisting?: (document: ApplicationDocumentDto) => void;
    onReplace: (id: string) => void;
}

const DocumentCard = ({ doc, onFileChange, onPreview, onPreviewExisting, onReplace }: DocumentCardProps) => {
    const { icon: IconComponent, label } = getFileIconAndColor(doc.file?.name!);
    const fileUploadRef = useRef<HTMLInputElement>(null);

    const canPreviewFile = (mimeType: string) => {
        return mimeType.includes("pdf") || mimeType.includes("image") || mimeType.includes("word");
    };

    return (
        <Card key={doc.id}>
            <CardContent className="p-6">
                <div className="space-y-4">
                    {/* Document Header */}
                    <div className="flex items-start gap-4">
                        <div className="flex-shrink-0">
                            <IconComponent />
                        </div>
                        <div className="flex-1 min-w-0">
                            <Label className="text-base font-semibold">
                                {doc.documentType?.description}
                                {doc.isRequired && <span className="text-destructive ml-1">*</span>}
                            </Label>
                            <p className="text-sm text-muted-foreground mt-1">{doc.documentType?.display_value}</p>
                        </div>
                    </div>
                    {/* File Upload/Display Section */}
                    <div className="space-y-3">
                        {doc.file ? (
                            /* New File Display */
                            <div className="border border-muted rounded-lg p-4">
                                <div className="flex items-center justify-between">
                                    <div className="flex items-center gap-3 flex-1 min-w-0">
                                        <div className="flex-shrink-0">
                                            <IconComponent className="text-green-600" />
                                        </div>
                                        <div className="flex-1 min-w-0">
                                            <p className="text-sm font-medium truncate">{doc.file.name}</p>
                                            <div className="flex items-center gap-4 text-xs text-muted-foreground mt-1">
                                                <span>{label}</span>
                                                <span>•</span>
                                                <span>{formatFileSize(doc.file.size)}</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div className="flex items-center gap-2">
                                        {canPreviewFile(doc.file.type) && (
                                            <Button size="sm" variant="outline" onClick={() => onPreview(doc.file!)}>
                                                <Eye className="w-4 h-4 mr-1" />
                                                Preview
                                            </Button>
                                        )}
                                        <Button size="sm" variant="ghost" onClick={() => onFileChange(doc.id, null)}>
                                            <X className="w-4 h-4" />
                                        </Button>
                                    </div>
                                </div>
                            </div>
                        ) : doc.existingDocument ? (
                            <div className="border border-muted rounded-lg p-4">
                                <div className="flex items-center justify-between">
                                    <div className="flex items-center gap-3 flex-1 min-w-0">
                                        <div className="flex-shrink-0">
                                            <Check className="w-5 h-5 text-green-600" />
                                        </div>
                                        <div className="flex-1 min-w-0">
                                            <p className="text-sm font-medium truncate">
                                                {doc.existingDocument.document?.original_name}
                                            </p>
                                            <div className="flex items-center gap-4 text-muted-foreground text-xs mt-1">
                                                <span>Previously uploaded</span>
                                                <span>•</span>
                                                <span>
                                                    {formatFileSize(
                                                        parseInt(doc.existingDocument.document?.size || "0"),
                                                    )}
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                    <div className="flex items-center gap-2">
                                        {doc.existingDocument.document?.mimetype &&
                                            canPreviewFile(doc.existingDocument.document.mimetype) &&
                                            onPreviewExisting && (
                                            <Button
                                                size="sm"
                                                variant="outline"
                                                onClick={() => onPreviewExisting(doc.existingDocument!)}
                                            >
                                                <Eye className="w-4 h-4 mr-1" />
                                                    Preview
                                            </Button>
                                        )}
                                        <Button
                                            size="sm"
                                            variant="ghost"
                                            onClick={() => {
                                                onReplace(doc.id);
                                                // Small delay to ensure state update, then trigger file selector
                                                setTimeout(() => {
                                                    fileUploadRef.current?.click();
                                                }, 100);
                                            }}
                                        >
                                            Replace
                                        </Button>
                                    </div>
                                </div>
                            </div>
                        ) : (
                            /* File Upload */
                            <FileUpload
                                ref={fileUploadRef}
                                accept=".pdf,.docx,.jpg,.jpeg,.png,.odt"
                                onFilesChange={(files) => onFileChange(doc.id, files?.[0] || null)}
                            />
                        )}
                    </div>
                </div>
            </CardContent>
        </Card>
    );
};

const SelectedFile = ({ file }: { file: File }) => {
    const { icon: IconComponent } = getFileIconAndColor(file.name);

    return (
        <>
            <IconComponent />
            <span className="truncate">{file.name}</span>
        </>
    );
};

const SupportingDocuments: React.FC<SupportingDocumentsProps> = ({
    onDocumentsChange,
    className = "",
    initialDocumentTypes = [],
    initialFiles = [],
    existingDocuments = [],
    applicationMode = "registration",
}) => {
    const [requiredDocuments, setRequiredDocuments] = useState<DocumentUpload[]>([]);
    const [optionalDocuments, setOptionalDocuments] = useState<DocumentUpload[]>([]);
    const [fileError, setFileError] = useState<string | null>(null);
    const [selectedFile, setSelectedFile] = useState<File | null>(null);
    const [previewDialogOpen, setPreviewDialogOpen] = useState(false);
    const [previewUrl, setPreviewUrl] = useState<string | null>(null);

    const REQUIRED_DOC_CODES = useMemo(() => {
        if (applicationMode === "registration") {
            return ["DOCT01", "DOCT02", "DOCT03", "DOCT04", "DOCT05", "DOCT06", "DOCT07"];
        }

        return ["DOCT01", "DOCT09", "DOCT10"];
    }, []);

    const { data: documentTypes = [], isLoading: loading } = useQuery({
        queryKey: ["document-types"],
        queryFn: async () => {
            const response = await settingsService.fetchLoadableItems({
                type: "DOCUMENT_TYPE",
            });

            if (response.success && response.data) {
                return response.data;
            } else {
                console.error("Failed to fetch document types:", response.errors);

                return [];
            }
        },
        enabled: true,
    });

    useEffect(() => {
        if (documentTypes.length > 0) {
            if (requiredDocuments.length === 0) {
                const requiredDocs = REQUIRED_DOC_CODES.map((code, index) => {
                    const docType = documentTypes.find((item) => item.code === code);

                    // Find the corresponding file by matching document type ID
                    let existingFile = null;

                    if (docType?.id) {
                        const docTypeIndex = initialDocumentTypes.indexOf(docType.id);

                        if (docTypeIndex >= 0 && docTypeIndex < initialFiles.length) {
                            existingFile = initialFiles[docTypeIndex];
                        }
                    }

                    const existingDocument = existingDocuments.find((doc) => doc.document_type?.code === code);

                    return {
                        id: `doc-required-${index}`,
                        documentType: docType || null,
                        file: existingFile,
                        isRequired: true,
                        existingDocument,
                    };
                });

                setRequiredDocuments(requiredDocs);
            }

            if (initialDocumentTypes.length > 0 && optionalDocuments.length === 0) {
                const optionalDocs: DocumentUpload[] = [];

                initialDocumentTypes.forEach((docTypeId, index) => {
                    const docType = documentTypes.find((dt) => dt.id === docTypeId);

                    if (docType && !REQUIRED_DOC_CODES.includes(docType.code)) {
                        // Find the corresponding file by matching the document type position in the arrays
                        const file = initialFiles[index] || null;
                        const existingDocument = existingDocuments.find((doc) => doc.document_type?.id === docTypeId);

                        optionalDocs.push({
                            id: `doc-optional-${optionalDocs.length}`,
                            documentType: docType,
                            file: file,
                            isRequired: false,
                            existingDocument,
                        });
                    }
                });

                if (optionalDocs.length > 0) {
                    setOptionalDocuments(optionalDocs);
                }
            }
        }
    }, [
        documentTypes,
        REQUIRED_DOC_CODES,
        requiredDocuments.length,
        optionalDocuments.length,
        initialDocumentTypes,
        initialFiles,
        existingDocuments,
    ]);

    const handleFileChange = useCallback(
        (id: string, file: File | null) => {
            setFileError(null);

            if (file && !ALLOWED_DOCUMENT_TYPES.includes(file.type)) {
                setFileError(`File type "${file.type}" is not supported. Please upload a document or image.`);

                return;
            }

            const updateDocList = (
                docs: DocumentUpload[],
                setDocs: React.Dispatch<React.SetStateAction<DocumentUpload[]>>,
            ) => {
                const newDocuments = docs.map((doc) => (doc.id === id ? { ...doc, file } : doc));

                setDocs(newDocuments);

                const updatedRequiredDocs = id.startsWith("doc-required") ? newDocuments : requiredDocuments;
                const updatedOptionalDocs = id.startsWith("doc-optional") ? newDocuments : optionalDocuments;
                const allDocuments = [...updatedRequiredDocs, ...updatedOptionalDocs];

                // Build ordered arrays maintaining document-to-file correspondence
                const orderedDocumentTypes: string[] = [];
                const orderedFiles: File[] = [];

                allDocuments.forEach((doc) => {
                    if (doc.documentType?.id && doc.file) {
                        orderedDocumentTypes.push(doc.documentType.id);
                        orderedFiles.push(doc.file);
                    }
                });

                onDocumentsChange({
                    documentTypes: orderedDocumentTypes,
                    files: orderedFiles,
                });
            };

            if (id.startsWith("doc-required")) {
                updateDocList(requiredDocuments, setRequiredDocuments);
            } else {
                updateDocList(optionalDocuments, setOptionalDocuments);
            }
        },
        [requiredDocuments, optionalDocuments, onDocumentsChange, ALLOWED_DOCUMENT_TYPES],
    );

    const addOptionalDocument = useCallback(() => {
        setOptionalDocuments((prevDocs) => {
            const updatedDocs = [
                ...prevDocs,
                {
                    id: `doc-optional-${prevDocs.length}`,
                    documentType: null,
                    file: null,
                    isRequired: false,
                },
            ];

            const allDocuments = [...requiredDocuments, ...updatedDocs];

            onDocumentsChange({
                documentTypes: allDocuments.map((d) => d.documentType?.id).filter(Boolean) as string[],
                files: allDocuments.map((d) => d.file).filter(Boolean) as File[],
            });

            return updatedDocs;
        });
    }, [requiredDocuments, onDocumentsChange]);

    const allDocuments = [...requiredDocuments, ...optionalDocuments];
    const completedRequired = allDocuments.filter(
        (doc) => doc.isRequired && doc.documentType && (doc.file || doc.existingDocument),
    ).length;
    const totalRequired = allDocuments.filter((doc) => doc.isRequired).length;

    const handlePreviewFile = (file: File) => {
        setSelectedFile(file);
        setPreviewUrl(null);
        setPreviewDialogOpen(true);
    };

    const handleExistingDocumentPreview = (document: ApplicationDocumentDto) => {
        if (document.document?.location) {
            const url = api.bucket(document.document.location);

            setPreviewUrl(url);
            setSelectedFile(null);
            setPreviewDialogOpen(true);
        }
    };

    const handleReplace = useCallback(
        (id: string) => {
            const updateDocList = (
                docs: DocumentUpload[],
                setDocs: React.Dispatch<React.SetStateAction<DocumentUpload[]>>,
            ) => {
                const newDocuments = docs.map((doc) =>
                    doc.id === id ? { ...doc, file: null, existingDocument: undefined } : doc,
                );

                setDocs(newDocuments);

                const updatedRequiredDocs = id.startsWith("doc-required") ? newDocuments : requiredDocuments;
                const updatedOptionalDocs = id.startsWith("doc-optional") ? newDocuments : optionalDocuments;
                const allDocuments = [...updatedRequiredDocs, ...updatedOptionalDocs];

                // Build ordered arrays maintaining document-to-file correspondence
                const orderedDocumentTypes: string[] = [];
                const orderedFiles: File[] = [];

                allDocuments.forEach((doc) => {
                    if (doc.documentType?.id && doc.file) {
                        orderedDocumentTypes.push(doc.documentType.id);
                        orderedFiles.push(doc.file);
                    }
                });

                onDocumentsChange({
                    documentTypes: orderedDocumentTypes,
                    files: orderedFiles,
                });
            };

            if (id.startsWith("doc-required")) {
                updateDocList(requiredDocuments, setRequiredDocuments);
            } else {
                updateDocList(optionalDocuments, setOptionalDocuments);
            }
        },
        [requiredDocuments, optionalDocuments, onDocumentsChange],
    );

    useEffect(() => {
        return () => {
            [...requiredDocuments, ...optionalDocuments].forEach((doc) => {
                if (doc.previewUrl) {
                    URL.revokeObjectURL(doc.previewUrl);
                }
            });
        };
    }, [requiredDocuments, optionalDocuments]);

    if (loading) {
        return (
            <div className="flex items-center justify-center p-8">
                <Loader2 className="animate-spin h-8 w-8 text-primary" />
                <p className="ml-2">Loading documents...</p>
            </div>
        );
    }

    return (
        <div className={cn("space-y-6", className)}>
            <div className="space-y-4">
                {requiredDocuments.map((doc) => (
                    <DocumentCard
                        key={doc.id}
                        doc={doc}
                        onFileChange={handleFileChange}
                        onPreview={handlePreviewFile}
                        onPreviewExisting={handleExistingDocumentPreview}
                        onReplace={handleReplace}
                    />
                ))}

                {optionalDocuments.map((doc) => (
                    <DocumentCard
                        key={doc.id}
                        doc={doc}
                        onFileChange={handleFileChange}
                        onPreview={handlePreviewFile}
                        onPreviewExisting={handleExistingDocumentPreview}
                        onReplace={handleReplace}
                    />
                ))}

                <Button type="button" variant="outline" onClick={addOptionalDocument}>
                    <div className="flex gap-3">
                        <Plus className="w-4 h-4 mr-2" />
                        Add Additional Document
                    </div>
                </Button>
            </div>

            {/* Validation Error */}
            {fileError && (
                <div className="p-4 bg-red-50 dark:bg-red-950/30 border border-red-200 dark:border-red-800 rounded-lg">
                    <div className="flex items-center gap-2">
                        <AlertCircle className="w-5 h-5 text-red-500" />
                        <p className="text-sm text-red-700 dark:text-red-300 font-medium">{fileError}</p>
                    </div>
                </div>
            )}

            {/* Summary */}
            {completedRequired === totalRequired && totalRequired > 0 && (
                <div className="p-4 bg-green-50 dark:bg-green-950/30 border border-green-200 dark:border-green-800 rounded-lg">
                    <div className="flex items-center gap-2">
                        <Check className="w-5 h-5 text-green-500" />
                        <p className="text-sm text-green-700 dark:text-green-300 font-medium">
                            All required documents have been uploaded
                        </p>
                    </div>
                </div>
            )}

            {/* Document Preview Dialog */}
            <Dialog open={previewDialogOpen} onOpenChange={setPreviewDialogOpen}>
                <DialogContent className="max-w-4xl max-h-[80vh]">
                    <DialogHeader>
                        <DialogTitle className="flex items-center gap-2">
                            {selectedFile && <SelectedFile file={selectedFile} />}
                            {previewUrl && !selectedFile && (
                                <>
                                    <div className="p-2 rounded-lg bg-green-100 text-green-600">
                                        <Check className="w-5 h-5" />
                                    </div>
                                    <span>Existing Document Preview</span>
                                </>
                            )}
                        </DialogTitle>
                    </DialogHeader>
                    <div className="flex-1 min-h-[500px] border rounded-lg overflow-hidden">
                        {selectedFile && <DocumentViewer height="500px" url={selectedFile} width="100%" />}
                        {previewUrl && !selectedFile && <DocumentViewer height="500px" url={previewUrl} width="100%" />}
                    </div>
                </DialogContent>
            </Dialog>
        </div>
    );
};

export default SupportingDocuments;
