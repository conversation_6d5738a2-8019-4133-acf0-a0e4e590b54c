"use client";

import {
    Briefcase,
    Building,
    Check,
    ChevronLeft,
    ChevronRight,
    DollarSign,
    FileText,
    MapPin,
    User<PERSON>he<PERSON>,
    Users,
} from "lucide-react";
import { useRouter } from "next/navigation";
import { useEffect, useRef, useState } from "react";
import { toast } from "sonner";

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "../ui/tabs";

import AuditorsForm from "./auditors-form";
import BankDetailsForm from "./bank-details-form";
import BasicInformationForm from "./basic-information-form";
import ContactsForm from "./contacts-form";
import DirectorsForm from "./directors-form";
import DonorsForm from "./donors-form";
import FundingSourceForm from "./funding-source-form";
import LocationActivitiesForm from "./location-activities-form";
import ProjectsForm from "./projects-form";
import SectorsForm from "./sectors-form";
import StaffForm from "./staff-form";
import SupportingDocuments, { SupportingDocumentOutput } from "./supporting-documents";
import TargetGroupsForm from "./target-groups-form";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import * as organizationService from "@/services/OrganizationService";
import { useOrganizationFormStore } from "@/stores/organization-form";

interface OrganizationFormWizardProps {
    initialData?: any;
    isSubmitting?: boolean;
    mode?: string;
    onSubmit?: (formData: FormData) => Promise<void>;
}

const OrganizationFormWizard = ({
    initialData,
    isSubmitting: externalIsSubmitting,
    mode,
    onSubmit,
}: OrganizationFormWizardProps) => {
    const {
        // Form Data
        name,
        abbreviation,
        organization_type_id,
        district_id,
        financial_start_month,
        financial_end_month,
        registration_type_id,
        charity_number,
        annual_income,
        biography,
        vision,
        motto,
        objectives,
        directors,
        fundingSources,
        bankDetails,
        auditors,
        locationActivities,
        targetGroups,
        staff,
        donors,
        projects,
        contacts,
        sectors,
        documentTypes,
        documents,

        // Form State
        currentStep,
        completedSteps: rawCompletedSteps,
        isSubmitting: storeIsSubmitting,

        // Actions
        updateFormData,
        updateArrayField,
        setCurrentStep,
        addCompletedStep,
        setIsSubmitting,
        validateCurrentStep,
        resetForm,
        initializeForm,
    } = useOrganizationFormStore();

    const completedSteps = Array.isArray(rawCompletedSteps) ? rawCompletedSteps : [];
    const isSubmitting = externalIsSubmitting ?? storeIsSubmitting;

    const [localDocuments, setLocalDocuments] = useState<File[]>([]);
    const initializedRef = useRef(false);
    const lastInitialDataRef = useRef<any>(null);

    // Initialize form with initial data if provided
    useEffect(() => {
        if (initialData && initializeForm) {
            // Only initialize if we haven't initialized before OR if the data has actually changed
            const dataChanged = JSON.stringify(lastInitialDataRef.current) !== JSON.stringify(initialData);

            if (!initializedRef.current || dataChanged) {
                initializeForm({
                    ...initialData,
                    mode: mode === "renewal" ? "renewal" : "registration",
                });
                initializedRef.current = true;
                lastInitialDataRef.current = initialData;
            }
        }
    }, [initialData, mode]);

    useEffect(() => {
        if (documents.length === 0 && localDocuments.length > 0) {
            updateArrayField("documents", localDocuments);
        }
    }, [localDocuments, documents]);

    const steps = [
        {
            title: "Basic Info",
            icon: Building,
            description: "Organization details",
            color: "from-blue-500 to-blue-600",
        },
        {
            title: "Leadership",
            icon: Users,
            description: "Directors & key personnel",
            color: "from-purple-500 to-purple-600",
        },
        {
            title: "Operations",
            icon: MapPin,
            description: "Locations & activities",
            color: "from-green-500 to-green-600",
        },
        {
            title: "Financial",
            icon: DollarSign,
            description: "Banking & funding",
            color: "from-orange-500 to-orange-600",
        },
        {
            title: "Management",
            icon: Briefcase,
            description: "Staff & projects",
            color: "from-pink-500 to-pink-600",
        },
        {
            title: "Documents",
            icon: FileText,
            description: "Supporting files",
            color: "from-indigo-500 to-indigo-600",
        },
    ];
    const router = useRouter();

    const formData = {
        name,
        abbreviation,
        organization_type_id,
        district_id,
        financial_start_month,
        financial_end_month,
        registration_type_id,
        charity_number,
        annual_income,
        biography,
        vision,
        motto,
        objectives,
    };

    const handleDocumentsChange = (supportingDocs: SupportingDocumentOutput) => {
        updateArrayField("documentTypes", supportingDocs.documentTypes);
        setLocalDocuments(supportingDocs.files);
        updateArrayField("documents", supportingDocs.files);
    };

    const nextStep = () => {
        if (validateCurrentStep() && currentStep < steps.length - 1) {
            if (!completedSteps.includes(currentStep)) {
                completedSteps.push(currentStep);
                addCompletedStep(currentStep);
            }

            setCurrentStep(currentStep + 1);
        }
    };

    const prevStep = () => {
        if (currentStep > 0) {
            setCurrentStep(currentStep - 1);
        }
    };

    const goToStep = (stepIndex: any) => {
        if (stepIndex <= currentStep || completedSteps.includes(stepIndex)) {
            setCurrentStep(stepIndex);
        }
    };

    const handleSubmit = async () => {
        if (!externalIsSubmitting) {
            setIsSubmitting(true);
        }

        const submissionData = new FormData();

        for (const field in formData) {
            const item = field as keyof typeof formData;
            let value = formData[item] as string;

            if (Array.isArray(formData[item]) || typeof formData[item] === "object") {
                value = JSON.stringify(value);
            }

            submissionData.append(field, value);
        }

        submissionData.append("directors", JSON.stringify(directors));
        submissionData.append("funding_sources", JSON.stringify(fundingSources));
        submissionData.append("bank_details", JSON.stringify(bankDetails));
        submissionData.append("auditors", JSON.stringify(auditors));
        submissionData.append("location_activities", JSON.stringify(locationActivities));
        submissionData.append("target_groups", JSON.stringify(targetGroups));
        submissionData.append("staff", JSON.stringify(staff));
        submissionData.append("donors", JSON.stringify(donors));
        submissionData.append("projects", JSON.stringify(projects));
        submissionData.append("contacts", JSON.stringify(contacts));
        submissionData.append("sectors", JSON.stringify(sectors));
        submissionData.append("document_types", JSON.stringify(documentTypes));

        const filesToUpload = localDocuments.length > 0 ? localDocuments : documents;

        filesToUpload.forEach((file) => {
            submissionData.append("supporting_documents", file);
        });

        try {
            if (onSubmit) {
                await onSubmit(submissionData);
            } else {
                const response = await organizationService.create(submissionData, (e) => {
                    if (e.total) {
                        const percent = Math.round((e.loaded * 100) / e.total);

                        console.log(`Upload Progress: ${percent}%`);
                    }
                });

                for (const error of response.errors) {
                    toast.error(error.message);
                }

                if (response.data) {
                    resetForm();
                    router.push(`/organizations/${response.data.id}`);
                }
            }
        } catch (error) {
            alert("Submission failed. Please try again.");
        } finally {
            if (!externalIsSubmitting) {
                setIsSubmitting(false);
            }
        }
    };

    const renderStepContent = () => {
        const contentClass = "animate-in slide-in-from-right-5 duration-300";

        switch (currentStep) {
            case 0:
                return <BasicInformationForm formData={formData} updateFormData={updateFormData} />;

            case 1:
                return (
                    <div className={`space-y-8 ${contentClass}`}>
                        <div className="text-center space-y-2">
                            <div className="w-16 h-16 mx-auto bg-gradient-to-r from-purple-500 to-purple-600 rounded-2xl flex items-center justify-center">
                                <Users className="w-8 h-8 text-white" />
                            </div>
                            <h2 className="text-2xl font-bold">Leadership Team</h2>
                            <p className="text-muted-foreground">Add your directors and key personnel</p>
                        </div>
                        <div className="text-center py-12">
                            <DirectorsForm
                                directors={directors}
                                setDirectors={(data) => updateArrayField("directors", data)}
                            />
                        </div>
                    </div>
                );

            case 2:
                return (
                    <div className={`space-y-8 ${contentClass}`}>
                        <div className="text-center space-y-2">
                            <div className="w-16 h-16 mx-auto bg-gradient-to-r from-green-500 to-green-600 rounded-2xl flex items-center justify-center">
                                <MapPin className="w-8 h-8 text-white" />
                            </div>
                            <h2 className="text-2xl font-bold">Operations</h2>
                            <p className="text-muted-foreground">Define your operational areas and target groups</p>
                        </div>
                        <Tabs className="w-full" defaultValue="locations">
                            <TabsList className="grid w-full grid-cols-3 h-12">
                                <TabsTrigger className="h-10" value="locations">
                                    Locations
                                </TabsTrigger>
                                <TabsTrigger className="h-10" value="targets">
                                    Target Groups
                                </TabsTrigger>
                                <TabsTrigger className="h-10" value="sectors">
                                    Sectors
                                </TabsTrigger>
                            </TabsList>
                            <TabsContent className="mt-6 space-y-4" value="locations">
                                <div className="text-center py-12">
                                    <LocationActivitiesForm
                                        locationActivities={locationActivities}
                                        setLocationActivities={(data) => updateArrayField("locationActivities", data)}
                                    />
                                </div>
                            </TabsContent>
                            <TabsContent className="mt-6 space-y-4" value="targets">
                                <div className="text-center py-12">
                                    <TargetGroupsForm
                                        setTargetGroups={(data) => updateArrayField("targetGroups", data)}
                                        targetGroups={targetGroups}
                                    />
                                </div>
                            </TabsContent>
                            <TabsContent className="mt-6 space-y-4" value="sectors">
                                <div className="text-center py-12">
                                    <SectorsForm
                                        sectors={sectors}
                                        setSectors={(data) => updateArrayField("sectors", data)}
                                    />
                                </div>
                            </TabsContent>
                        </Tabs>
                    </div>
                );

            case 3:
                return (
                    <div className={`space-y-8 ${contentClass}`}>
                        <div className="text-center space-y-2">
                            <div className="w-16 h-16 mx-auto bg-gradient-to-r from-orange-500 to-orange-600 rounded-2xl flex items-center justify-center">
                                <DollarSign className="w-8 h-8 text-white" />
                            </div>
                            <h2 className="text-2xl font-bold">Financial Information</h2>
                            <p className="text-muted-foreground">Banking details, funding sources, and auditors</p>
                        </div>
                        <Tabs className="w-full" defaultValue="banking">
                            <TabsList className="grid w-full grid-cols-3 h-12">
                                <TabsTrigger className="h-10" value="banking">
                                    Bank Details
                                </TabsTrigger>
                                <TabsTrigger className="h-10" value="funding">
                                    Funding
                                </TabsTrigger>
                                <TabsTrigger className="h-10" value="auditors">
                                    Auditors
                                </TabsTrigger>
                            </TabsList>
                            <TabsContent className="mt-6 space-y-4" value="banking">
                                <div className="text-center py-12">
                                    <BankDetailsForm
                                        bankDetails={bankDetails}
                                        setBankDetails={(data) => updateArrayField("bankDetails", data)}
                                    />
                                </div>
                            </TabsContent>
                            <TabsContent className="mt-6 space-y-4" value="funding">
                                <div className="text-center py-12">
                                    <FundingSourceForm
                                        fundingSources={fundingSources}
                                        setFundingSources={(data) => updateArrayField("fundingSources", data)}
                                    />
                                </div>
                            </TabsContent>
                            <TabsContent className="mt-6 space-y-4" value="auditors">
                                <div className="text-center py-12">
                                    <AuditorsForm
                                        auditors={auditors}
                                        setAuditors={(data) => updateArrayField("auditors", data)}
                                    />
                                </div>
                            </TabsContent>
                        </Tabs>
                    </div>
                );

            case 4:
                return (
                    <div className={`space-y-8 ${contentClass}`}>
                        <div className="text-center space-y-2">
                            <div className="w-16 h-16 mx-auto bg-gradient-to-r from-pink-500 to-pink-600 rounded-2xl flex items-center justify-center">
                                <Briefcase className="w-8 h-8 text-white" />
                            </div>
                            <h2 className="text-2xl font-bold">Management</h2>
                            <p className="text-muted-foreground">Staff, projects, donors, and contacts</p>
                        </div>
                        <Tabs className="w-full" defaultValue="staff">
                            <TabsList className="grid w-full grid-cols-4 h-12">
                                <TabsTrigger className="h-10" value="staff">
                                    Staff
                                </TabsTrigger>
                                <TabsTrigger className="h-10" value="projects">
                                    Projects
                                </TabsTrigger>
                                <TabsTrigger className="h-10" value="donors">
                                    Donors
                                </TabsTrigger>
                                <TabsTrigger className="h-10" value="contacts">
                                    Contacts
                                </TabsTrigger>
                            </TabsList>
                            <TabsContent className="mt-6 space-y-4" value="staff">
                                <div className="text-center py-12">
                                    <StaffForm setStaff={(data) => updateArrayField("staff", data)} staff={staff} />
                                </div>
                            </TabsContent>
                            <TabsContent className="mt-6 space-y-4" value="projects">
                                <div className="text-center py-12">
                                    <ProjectsForm
                                        projects={projects}
                                        setProjects={(data) => updateArrayField("projects", data)}
                                    />
                                </div>
                            </TabsContent>
                            <TabsContent className="mt-6 space-y-4" value="donors">
                                <div className="text-center py-12">
                                    <DonorsForm
                                        donors={donors}
                                        setDonors={(data) => updateArrayField("donors", data)}
                                    />
                                </div>
                            </TabsContent>
                            <TabsContent className="mt-6 space-y-4" value="contacts">
                                <div className="text-center py-12">
                                    <ContactsForm
                                        contacts={contacts}
                                        setContacts={(data) => updateArrayField("contacts", data)}
                                    />
                                </div>
                            </TabsContent>
                        </Tabs>
                    </div>
                );

            case 5:
                return (
                    <div className={`space-y-8 ${contentClass}`}>
                        <div className="text-center space-y-2">
                            <div className="w-16 h-16 mx-auto bg-gradient-to-r from-indigo-500 to-indigo-600 rounded-2xl flex items-center justify-center">
                                <FileText className="w-8 h-8 text-white" />
                            </div>
                            <h2 className="text-2xl font-bold">Supporting Documents</h2>
                            <p className="text-muted-foreground">Upload required and additional supporting documents</p>
                        </div>
                        <SupportingDocuments
                            applicationMode={mode === "renewal" ? "renewal" : "registration"}
                            existingDocuments={initialData?.existing_documents || []}
                            initialDocumentTypes={documentTypes}
                            initialFiles={localDocuments.length > 0 ? localDocuments : documents}
                            onDocumentsChange={handleDocumentsChange}
                        />
                    </div>
                );

            default:
                return null;
        }
    };

    return (
        <div className="min-h-screen p-4">
            <div className="max-w-full mx-auto">
                {/* Header */}
                {/* <div className="text-center mb-8">
                    <h1 className="text-3xl md:text-4xl font-bold mb-2">Organization Registration</h1>
                    <p className="text-muted-foreground">Complete all steps to register your organization</p>
                </div> */}

                {/* Progress Steps */}
                <div className="mb-8 bg-background">
                    <div className="flex items-center justify-between mb-6 overflow-x-auto pb-2">
                        {steps.map((step, index) => {
                            const StepIcon = step.icon;
                            const isCompleted = completedSteps.includes(index);
                            const isCurrent = index === currentStep;
                            const isAccessible = index <= currentStep || isCompleted;

                            return (
                                <div key={index} className="flex flex-col items-center min-w-0 flex-1 mx-2">
                                    <Button
                                        className={`relative w-12 h-12 rounded-full flex items-center justify-center border-2 transition-all duration-300 mb-2 ${
                                            isCompleted
                                                ? "bg-green-500 border-green-500 text-white"
                                                : isCurrent
                                                  ? `bg-gradient-to-r ${step.color} border-transparent text-white shadow-lg scale-110`
                                                  : isAccessible
                                                    ? "border-primary/30 text-primary hover:border-primary/50 hover:scale-105"
                                                    : "border-muted text-muted-foreground cursor-not-allowed"
                                        }`}
                                        disabled={!isAccessible}
                                        onClick={() => goToStep(index)}
                                    >
                                        {isCompleted ? (
                                            <Check className="w-6 h-6" />
                                        ) : (
                                            <StepIcon className="w-5 h-5 text-foreground" />
                                        )}

                                        {/* Connection Line */}
                                        {index < steps.length - 1 && (
                                            <div
                                                className={`absolute left-full w-8 md:w-16 h-0.5 top-1/2 -translate-y-1/2 transition-colors duration-300 ${
                                                    completedSteps.includes(index) ? "bg-green-500" : "bg-muted"
                                                }`}
                                            />
                                        )}
                                    </Button>
                                    <div className="text-center">
                                        <p
                                            className={`text-xs md:text-sm font-medium transition-colors ${
                                                isCurrent
                                                    ? "text-primary"
                                                    : isCompleted
                                                      ? "text-green-600"
                                                      : "text-muted-foreground"
                                            }`}
                                        >
                                            {step.title}
                                        </p>
                                        <p className="text-xs text-muted-foreground/80 hidden md:block">
                                            {step.description}
                                        </p>
                                    </div>
                                </div>
                            );
                        })}
                    </div>

                    {/* Progress Bar */}
                    <div className="w-full bg-muted rounded-full h-2">
                        <div
                            className="bg-gradient-to-r from-primary to-primary/80 h-2 rounded-full transition-all duration-500 ease-out"
                            style={{ width: `${((currentStep + 1) / steps.length) * 100}%` }}
                        />
                    </div>
                </div>

                {/* Step Content */}
                <Card className="mb-8">
                    <CardContent>{renderStepContent()}</CardContent>
                </Card>

                {/* Navigation */}
                <div className="flex flex-col sm:flex-row justify-between gap-4">
                    <Button
                        className="h-12 px-6 order-2 sm:order-1"
                        disabled={currentStep === 0}
                        variant="outline"
                        onClick={prevStep}
                    >
                        <div className="flex gap-3">
                            {" "}
                            <ChevronLeft className="w-4 h-4 mr-2" />
                            Previous
                        </div>
                    </Button>

                    <div className="flex-1 flex justify-center order-1 sm:order-2">
                        <div className="text-center">
                            <p className="text-sm text-muted-foreground">
                                Step {currentStep + 1} of {steps.length}
                            </p>
                            <div className="flex gap-1 mt-1">
                                {Array.from({ length: steps.length }, (_, i) => (
                                    <div
                                        key={i}
                                        className={`w-2 h-2 rounded-full transition-all duration-300 ${
                                            i === currentStep
                                                ? "bg-primary scale-125"
                                                : i < currentStep
                                                  ? "bg-green-500 text-white"
                                                  : "bg-muted"
                                        }`}
                                    />
                                ))}
                            </div>
                        </div>
                    </div>

                    {currentStep === steps.length - 1 ? (
                        <Button
                            className="h-12 px-8 bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 shadow-lg hover:shadow-xl transition-all duration-300 order-3"
                            disabled={isSubmitting || !validateCurrentStep()}
                            onClick={handleSubmit}
                        >
                            {isSubmitting ? (
                                <>
                                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                                    Submitting...
                                </>
                            ) : (
                                <>
                                    Submit Application
                                    <UserCheck className="w-4 h-4 ml-2" />
                                </>
                            )}
                        </Button>
                    ) : (
                        <Button
                            className="h-12 px-6 bg-gradient-to-r from-primary to-primary/90 hover:shadow-lg transition-all duration-300 order-3"
                            disabled={!validateCurrentStep()}
                            onClick={nextStep}
                        >
                            <div className="flex gap-3">
                                Next Step
                                <ChevronRight className="w-4 h-4 ml-2" />
                            </div>
                        </Button>
                    )}
                </div>

                {/* Validation Message */}
                {!validateCurrentStep() && (
                    <div className="mt-4 p-4 bg-white/10 border border-amber-200 rounded-lg">
                        <p className="text-sm text-yellow-600">
                            Please complete all required fields to continue to the next step.
                        </p>
                    </div>
                )}
            </div>
        </div>
    );
};

export default OrganizationFormWizard;
