"use client";

import { useMutation, useQueryClient } from "@tanstack/react-query";
import { Calendar, Edit, FileText, Mail, Phone, Save, Trash2, UserPlus, Users, X } from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";

import CountryInput from "@/components/inputs/country";
import LoadableItemInput from "@/components/inputs/loadable-item";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardTitle } from "@/components/ui/card";
import { Di<PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select } from "@/components/ui/select";
import * as organizationService from "@/services/OrganizationService";
import { DirectorDto } from "@/types/organization.dto";

interface DirectorsManagementProps {
    organizationId: string;
    directors: DirectorDto[];
    canEdit: boolean;
}

interface DirectorFormData {
    fullname: string;
    email: string;
    phone: string;
    gender: "MALE" | "FEMALE";
    position: string;
    country_id: string;
    occupation: string;
    timeframe: string;
    qualification_id: string;
    national_identifier?: string;
    passport_number?: string;
}

const emptyDirector: DirectorFormData = {
    fullname: "",
    email: "",
    phone: "",
    gender: "MALE",
    position: "",
    country_id: "",
    occupation: "",
    timeframe: "",
    qualification_id: "",
    national_identifier: "",
    passport_number: "",
};

export default function DirectorsManagement({ organizationId, directors, canEdit }: DirectorsManagementProps) {
    const [isDialogOpen, setIsDialogOpen] = useState(false);
    const [editingDirector, setEditingDirector] = useState<DirectorDto | null>(null);
    const [formData, setFormData] = useState<DirectorFormData>(emptyDirector);
    const queryClient = useQueryClient();

    const addDirectorMutation = useMutation({
        mutationFn: (data: DirectorFormData) => organizationService.addDirectors(organizationId, [data]),
        onSuccess: () => {
            toast.success("Director added successfully!");
            queryClient.invalidateQueries({ queryKey: ["organization-directors", organizationId] });
            setIsDialogOpen(false);
            setFormData(emptyDirector);
        },
        onError: (error: any) => {
            toast.error(error?.message || "Failed to add director");
        },
    });

    const updateDirectorMutation = useMutation({
        mutationFn: ({ directorId, data }: { directorId: string; data: DirectorFormData }) =>
            organizationService.updateDirector(organizationId, directorId, data),
        onSuccess: () => {
            toast.success("Director updated successfully!");
            queryClient.invalidateQueries({ queryKey: ["organization-directors", organizationId] });
            setIsDialogOpen(false);
            setEditingDirector(null);
            setFormData(emptyDirector);
        },
        onError: (error: any) => {
            toast.error(error?.message || "Failed to update director");
        },
    });

    const deleteDirectorMutation = useMutation({
        mutationFn: (directorId: string) => organizationService.deleteDirector(organizationId, directorId),
        onSuccess: () => {
            toast.success("Director removed successfully!");
            queryClient.invalidateQueries({ queryKey: ["organization-directors", organizationId] });
        },
        onError: (error: any) => {
            toast.error(error?.message || "Failed to remove director");
        },
    });

    const handleAdd = () => {
        setEditingDirector(null);
        setFormData(emptyDirector);
        setIsDialogOpen(true);
    };

    const handleEdit = (director: DirectorDto) => {
        setEditingDirector(director);
        setFormData({
            fullname: director.fullname || "",
            email: director.email || "",
            phone: director.phone || "",
            gender: director.gender || "MALE",
            position: director.position || "",
            country_id: director.country_id || "",
            occupation: director.occupation || "",
            timeframe: director.timeframe || "",
            qualification_id: director.qualification_id || "",
            national_identifier: director.national_identifier || "",
            passport_number: director.passport_number || "",
        });
        setIsDialogOpen(true);
    };

    const handleSubmit = () => {
        if (editingDirector) {
            updateDirectorMutation.mutate({ directorId: editingDirector.id, data: formData });
        } else {
            addDirectorMutation.mutate(formData);
        }
    };

    const handleDelete = (directorId: string) => {
        if (confirm("Are you sure you want to remove this director?")) {
            deleteDirectorMutation.mutate(directorId);
        }
    };

    const updateFormData = (field: keyof DirectorFormData, value: any) => {
        setFormData((prev) => ({ ...prev, [field]: value }));
    };

    const isPending = addDirectorMutation.isPending || updateDirectorMutation.isPending;

    return (
        <div className="border-0">
            <div className="flex justify-between items-center">
                <CardTitle className="flex items-center gap-2 text-foreground">
                    <Users className="w-5 h-5 text-primary" />
                    Board of Directors ({directors.length})
                </CardTitle>
                {canEdit && (
                    <Button size="sm" onClick={handleAdd}>
                        <UserPlus className="w-4 h-4 mr-2" />
                        Add Director
                    </Button>
                )}
            </div>
            <div className="pt-6">
                {directors.length > 0 ? (
                    <div className="grid gap-4">
                        {directors.map((director: DirectorDto) => (
                            <Card key={director.id} className="border-border/50">
                                <CardContent className="p-6">
                                    <div className="flex items-start gap-4">
                                        <Avatar className="w-12 h-12">
                                            <AvatarFallback className="bg-gradient-to-br from-primary to-primary/80 text-primary-foreground">
                                                {director.fullname
                                                    ?.split(" ")
                                                    .map((n) => n[0])
                                                    .join("") || "?"}
                                            </AvatarFallback>
                                        </Avatar>
                                        <div className="flex-grow">
                                            <div className="flex justify-between items-start">
                                                <div>
                                                    <h3 className="font-semibold text-foreground">
                                                        {director.fullname}
                                                    </h3>
                                                    <p className="text-primary font-medium">{director.position}</p>
                                                    {director.occupation && (
                                                        <p className="text-sm text-muted-foreground">
                                                            {director.occupation}
                                                        </p>
                                                    )}
                                                </div>
                                                {canEdit && (
                                                    <div className="flex gap-2">
                                                        <Button
                                                            size="sm"
                                                            variant="ghost"
                                                            onClick={() => handleEdit(director)}
                                                        >
                                                            <Edit className="w-4 h-4" />
                                                        </Button>
                                                        <Button
                                                            disabled={deleteDirectorMutation.isPending}
                                                            size="sm"
                                                            variant="ghost"
                                                            onClick={() => handleDelete(director.id)}
                                                        >
                                                            <Trash2 className="w-4 h-4" />
                                                        </Button>
                                                    </div>
                                                )}
                                            </div>
                                            <div className="mt-3 grid grid-cols-1 md:grid-cols-2 gap-2 text-sm text-muted-foreground">
                                                {director.email && (
                                                    <div className="flex items-center gap-1">
                                                        <Mail className="w-4 h-4" />
                                                        {director.email}
                                                    </div>
                                                )}
                                                {director.phone && (
                                                    <div className="flex items-center gap-1">
                                                        <Phone className="w-4 h-4" />
                                                        {director.phone}
                                                    </div>
                                                )}
                                                {director.timeframe && (
                                                    <div className="flex items-center gap-1">
                                                        <Calendar className="w-4 h-4" />
                                                        Term: {director.timeframe}
                                                    </div>
                                                )}
                                                {director.qualification && (
                                                    <div className="flex items-center gap-1">
                                                        <FileText className="w-4 h-4" />
                                                        {director.qualification}
                                                    </div>
                                                )}
                                            </div>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>
                        ))}
                    </div>
                ) : (
                    <div className="text-center py-8">
                        <Users className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                        <p className="text-muted-foreground mb-4">No directors added yet.</p>
                        {canEdit && (
                            <Button onClick={handleAdd}>
                                <UserPlus className="w-4 h-4 mr-2" />
                                Add First Director
                            </Button>
                        )}
                    </div>
                )}
            </div>

            {/* Add/Edit Director Dialog */}
            <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
                <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
                    <DialogHeader>
                        <DialogTitle>{editingDirector ? "Edit Director" : "Add Director"}</DialogTitle>
                    </DialogHeader>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 py-4">
                        <div className="space-y-2">
                            <Label htmlFor="fullname">Full Name *</Label>
                            <Input
                                required
                                id="fullname"
                                placeholder="Director's full name"
                                value={formData.fullname}
                                onChange={(e) => updateFormData("fullname", e.target.value)}
                            />
                        </div>
                        <div className="space-y-2">
                            <Label htmlFor="email">Email *</Label>
                            <Input
                                required
                                id="email"
                                placeholder="<EMAIL>"
                                type="email"
                                value={formData.email}
                                onChange={(e) => updateFormData("email", e.target.value)}
                            />
                        </div>
                        <div className="space-y-2">
                            <Label htmlFor="phone">Phone *</Label>
                            <Input
                                required
                                id="phone"
                                placeholder="Phone number"
                                value={formData.phone}
                                onChange={(e) => updateFormData("phone", e.target.value)}
                            />
                        </div>
                        <div className="space-y-2">
                            <Label htmlFor="gender">Gender *</Label>
                            <Select
                                options={[
                                    { value: "MALE", label: "MALE" },
                                    { value: "FEMALE", label: "FEMALE" },
                                ]}
                                value={formData.gender}
                                onValueChange={(value) => updateFormData("gender", value)}
                            />
                        </div>
                        <div className="space-y-2">
                            <Label htmlFor="position">Position *</Label>
                            <Input
                                required
                                id="position"
                                placeholder="e.g., Chairman, Secretary"
                                value={formData.position}
                                onChange={(e) => updateFormData("position", e.target.value)}
                            />
                        </div>
                        <div className="space-y-2">
                            <Label htmlFor="occupation">Occupation</Label>
                            <Input
                                id="occupation"
                                placeholder="Director's occupation"
                                value={formData.occupation}
                                onChange={(e) => updateFormData("occupation", e.target.value)}
                            />
                        </div>
                        <div className="space-y-2">
                            <Label htmlFor="timeframe">Term Period</Label>
                            <Input
                                id="timeframe"
                                placeholder="e.g., 2020-2025"
                                value={formData.timeframe}
                                onChange={(e) => updateFormData("timeframe", e.target.value)}
                            />
                        </div>
                        <div className="space-y-2">
                            <CountryInput
                                label="Country *"
                                name={editingDirector?.country?.name}
                                value={formData.country_id}
                                onCountrySelect={(country) => updateFormData("country_id", country?.id)}
                            />
                        </div>
                        <div className="space-y-2">
                            <LoadableItemInput
                                label="Qualification"
                                type="QUALIFICATION"
                                value={formData.qualification_id}
                                onItemSelect={(item) => updateFormData("qualification_id", item?.id)}
                            />
                        </div>
                        <div className="space-y-2">
                            <Label htmlFor="national_identifier">National ID</Label>
                            <Input
                                id="national_identifier"
                                placeholder="National ID number"
                                value={formData.national_identifier}
                                onChange={(e) => updateFormData("national_identifier", e.target.value)}
                            />
                        </div>
                        <div className="space-y-2">
                            <Label htmlFor="passport_number">Passport Number</Label>
                            <Input
                                id="passport_number"
                                placeholder="Passport number"
                                value={formData.passport_number}
                                onChange={(e) => updateFormData("passport_number", e.target.value)}
                            />
                        </div>
                    </div>
                    <div className="flex justify-end gap-2 pt-4">
                        <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
                            <X className="w-4 h-4 mr-2" />
                            Cancel
                        </Button>
                        <Button disabled={isPending} onClick={handleSubmit}>
                            {isPending ? (
                                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                            ) : (
                                <Save className="w-4 h-4 mr-2" />
                            )}
                            {editingDirector ? "Update" : "Add"} Director
                        </Button>
                    </div>
                </DialogContent>
            </Dialog>
        </div>
    );
}
