import { Globe, Mail, MapPin, Phone, Plus, Share2, Trash2 } from "lucide-react";

import {
    DiscordIcon,
    FacebookIcon,
    GithubIcon,
    InstagramIcon,
    LinkedinIcon,
    TikTokIcon,
    TwitterIcon,
    YoutubeIcon,
} from "../icons";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select } from "@/components/ui/select";
import { ContactType, OrganizationContact } from "@/types";

const ContactsForm = ({
    contacts,
    setContacts,
}: {
    contacts: OrganizationContact[];
    setContacts: (contacts: OrganizationContact[]) => void;
}) => {
    const addContact = () => {
        setContacts([
            ...contacts,
            {
                type: "EMAIL",
                details: [{ label: "", value: "" }],
            },
        ]);
    };

    const updateContact = (index: number, field: keyof OrganizationContact, value: any) => {
        const updated = [...contacts];

        updated[index] = { ...updated[index], [field]: value };
        setContacts(updated);
    };

    const updateContactDetail = (contactIndex: number, detailIndex: number, field: string, value: string) => {
        const updated = [...contacts];

        updated[contactIndex].details[detailIndex] = {
            ...updated[contactIndex].details[detailIndex],
            [field]: value,
        };
        setContacts(updated);
    };

    const addContactDetail = (contactIndex: number) => {
        const updated = [...contacts];

        updated[contactIndex].details.push({ label: "", value: "" });
        setContacts(updated);
    };

    const removeContact = (index: number) => {
        setContacts(contacts.filter((_, i) => i !== index));
    };

    const removeContactDetail = (contactIndex: number, detailIndex: number) => {
        const updated = [...contacts];

        updated[contactIndex].details = updated[contactIndex].details.filter((_, i) => i !== detailIndex);
        setContacts(updated);
    };

    const getContactIcon = (type: ContactType) => {
        switch (type) {
            case "EMAIL":
                return <Mail className="w-4 h-4" />;
            case "PHONE":
                return <Phone className="w-4 h-4" />;
            case "ADDRESS":
                return <MapPin className="w-4 h-4" />;
            case "WEBSITE":
                return <Globe className="w-4 h-4" />;
            case "SOCIAL_MEDIA":
                return <Share2 className="w-4 h-4" />;
            default:
                return <Mail className="w-4 h-4" />;
        }
    };

    const getPlaceholderText = (type: ContactType) => {
        switch (type) {
            case "EMAIL":
                return "<EMAIL>";
            case "PHONE":
                return "+****************";
            case "ADDRESS":
                return "123 Main Street, City, State, ZIP";
            case "WEBSITE":
                return "https://www.organization.com";
            case "SOCIAL_MEDIA":
                return "https://github.com/organization";
            default:
                return "Contact value";
        }
    };

    const getLabelSuggestions = (type: ContactType) => {
        switch (type) {
            case "EMAIL":
                return ["Primary", "Secondary", "Admin", "Support", "Info"];
            case "PHONE":
                return ["Office", "Mobile", "Fax", "Emergency", "Toll-free"];
            case "ADDRESS":
                return ["Headquarters", "Mailing", "Branch Office", "Regional Office"];
            case "WEBSITE":
                return ["Main Website", "Portal", "Social Media", "Blog"];
            case "SOCIAL_MEDIA":
                return ["GitHub", "Twitter", "Facebook", "Instagram", "LinkedIn", "YouTube", "TikTok", "Discord"];
            default:
                return ["Primary", "Secondary"];
        }
    };

    const getSocialMediaIcon = (platform: string) => {
        const platformLower = platform.toLowerCase();

        switch (platformLower) {
            case "github":
                return <GithubIcon className="w-4 h-4" />;
            case "twitter":
            case "x":
                return <TwitterIcon className="w-4 h-4" />;
            case "facebook":
                return <FacebookIcon className="w-4 h-4" />;
            case "instagram":
                return <InstagramIcon className="w-4 h-4" />;
            case "linkedin":
                return <LinkedinIcon className="w-4 h-4" />;
            case "youtube":
                return <YoutubeIcon className="w-4 h-4" />;
            case "tiktok":
                return <TikTokIcon className="w-4 h-4" />;
            case "discord":
                return <DiscordIcon className="w-4 h-4" />;
            default:
                return <Share2 className="w-4 h-4" />;
        }
    };

    const contactTypeOptions = [
        { value: "EMAIL", label: "Email", icon: <Mail className="w-4 h-4" /> },
        { value: "PHONE", label: "Phone", icon: <Phone className="w-4 h-4" /> },
        { value: "ADDRESS", label: "Address", icon: <MapPin className="w-4 h-4" /> },
        { value: "WEBSITE", label: "Website", icon: <Globe className="w-4 h-4" /> },
        { value: "SOCIAL_MEDIA", label: "Social Media", icon: <Share2 className="w-4 h-4" /> },
    ];

    return (
        <div className="space-y-4">
            <div className="flex justify-between items-center">
                <h3 className="text-lg font-semibold flex items-center gap-2">
                    <Mail className="w-5 h-5" />
                    Organization Contacts
                </h3>
                <Button size="sm" onClick={addContact}>
                    <Plus className="w-4 h-4 mr-2" />
                    Add Contact
                </Button>
            </div>

            {contacts.length === 0 && (
                <div className="text-center py-8 text-gray-500">
                    <Mail className="w-12 h-12 mx-auto mb-2 opacity-50" />
                    <p>No contacts added yet. Click &quot;Add Contact&quot; to get started.</p>
                </div>
            )}

            {contacts.map((contact, contactIndex) => (
                <Card key={contactIndex} className="p-4 border-l-4 border-l-blue-500">
                    <div className="space-y-4">
                        <div className="flex items-center justify-between">
                            <div className="flex items-center gap-2">
                                {getContactIcon(contact.type)}
                                <Label className="text-base font-medium">Contact Type</Label>
                            </div>
                            <Button
                                className="h-8"
                                size="sm"
                                variant="destructive"
                                onClick={() => removeContact(contactIndex)}
                            >
                                <Trash2 className="w-4 h-4" />
                            </Button>
                        </div>

                        <div>
                            <Select
                                options={contactTypeOptions}
                                value={contact.type}
                                onValueChange={(value) => updateContact(contactIndex, "type", value as ContactType)}
                            />
                        </div>

                        <div>
                            <div className="flex justify-between items-center mb-3">
                                <Label className="text-sm font-medium">{contact.type} Details</Label>
                                <Button
                                    className="h-8 text-xs"
                                    size="sm"
                                    variant="outline"
                                    onClick={() => addContactDetail(contactIndex)}
                                >
                                    <Plus className="w-3 h-3 mr-1" />
                                    Add Detail
                                </Button>
                            </div>

                            <div className="space-y-3">
                                {contact.details.map((detail, detailIndex) => (
                                    <div
                                        key={detailIndex}
                                        className="grid grid-cols-1 md:grid-cols-3 gap-2 p-3 bg-white/10 rounded-lg"
                                    >
                                        <div>
                                            <Select
                                                label="Platform"
                                                options={[
                                                    ...getLabelSuggestions(contact.type).map((suggestion) => ({
                                                        value: suggestion,
                                                        label: suggestion,
                                                        icon:
                                                            contact.type === "SOCIAL_MEDIA"
                                                                ? getSocialMediaIcon(suggestion)
                                                                : undefined,
                                                    })),
                                                    { value: "custom", label: "Custom..." },
                                                ]}
                                                placeholder="Select platform"
                                                value={detail.label || ""}
                                                onValueChange={(value) =>
                                                    updateContactDetail(contactIndex, detailIndex, "label", value)
                                                }
                                            />
                                            {detail.label === "custom" && (
                                                <Input
                                                    className="mt-2"
                                                    label="Platform"
                                                    placeholder={
                                                        contact.type === "SOCIAL_MEDIA"
                                                            ? "Enter platform name"
                                                            : "Enter custom label"
                                                    }
                                                    value={detail.customLabel || ""}
                                                    onChange={(e) =>
                                                        updateContactDetail(
                                                            contactIndex,
                                                            detailIndex,
                                                            "customLabel",
                                                            e.target.value,
                                                        )
                                                    }
                                                />
                                            )}
                                        </div>

                                        <Input
                                            label={contact.type === "SOCIAL_MEDIA" ? "Profile URL" : "Value"}
                                            placeholder={getPlaceholderText(contact.type)}
                                            type={
                                                contact.type === "EMAIL"
                                                    ? "email"
                                                    : contact.type === "WEBSITE" || contact.type === "SOCIAL_MEDIA"
                                                      ? "url"
                                                      : "text"
                                            }
                                            value={detail.value || ""}
                                            onChange={(e) =>
                                                updateContactDetail(contactIndex, detailIndex, "value", e.target.value)
                                            }
                                        />

                                        <div className="flex items-end">
                                            {contact.details.length > 1 && (
                                                <Button
                                                    className="h-9 w-full"
                                                    size="sm"
                                                    variant="outline"
                                                    onClick={() => removeContactDetail(contactIndex, detailIndex)}
                                                >
                                                    <Trash2 className="w-4 h-4" />
                                                </Button>
                                            )}
                                        </div>
                                    </div>
                                ))}
                            </div>

                            {contact.details.length === 0 && (
                                <div className="text-center py-4 text-gray-400 border-2 border-dashed border-gray-200 rounded-lg">
                                    <p className="text-sm">No details added yet</p>
                                </div>
                            )}
                        </div>
                    </div>
                </Card>
            ))}

            {contacts.length > 0 && (
                <div className="text-sm text-gray-500 bg-white/10 mt-4 p-3 rounded-lg">
                    <div className="flex items-start gap-2">
                        <div className="w-4 h-4 rounded-full flex-shrink-0 mt-0.5" />
                        <div>
                            <p className="font-bold text-left text-lg text-foreground">Contact Information Tips:</p>
                            <ul className="mt-1 space-y-1 text-left">
                                <li>
                                    • Add multiple details for each contact type (e.g., primary and secondary email)
                                </li>
                                <li>• Use descriptive labels to identify different contacts</li>
                                <li>• Ensure email addresses and websites are properly formatted</li>
                                <li>• Include country codes for international phone numbers</li>
                            </ul>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
};

export default ContactsForm;
