import { Avatar, AvatarFallback, AvatarImage } from "@radix-ui/react-avatar";
import { useMutation } from "@tanstack/react-query";
import { Badge, Bell, Calendar, DollarSign, MoreHorizontal, Send, Settings, Share2, UserPlus } from "lucide-react";
import { useState } from "react";

import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import * as applicationService from "@/services/ApplicationService";
import { OrganizationDto, OrganizationStatusConfig } from "@/types/organization.dto";

export type ProfileHeaderProps = {
    canEdit: boolean;
    isApproved: boolean;
    organization: OrganizationDto;
    statusConfig: OrganizationStatusConfig;
};

export const ProfileHeader = ({ canEdit, isApproved, organization, statusConfig }: ProfileHeaderProps) => {
    const [loading, setLoading] = useState(false);

    const submitMutation = useMutation({
        mutationFn: () => applicationService.submitForReview(organization.id),
        onMutate: () => {
            setLoading(true);
        },
        onSuccess: (data) => {
            console.log(data);
            setLoading(false);
        },
        onError: (error) => {
            console.error(error);
            setLoading(false);
        },
    });

    const submitForReview = () => {
        submitMutation.mutate();
    };

    return (
        <div className="relative">
            <div className="absolute inset-0 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-2xl opacity-90" />
            <Card className="relative border-0 backdrop-blur">
                <CardContent className="p-8">
                    <div className="flex flex-col lg:flex-row gap-6">
                        {/* Organization Avatar & Basic Info */}
                        <div className="flex-shrink-0">
                            <Avatar className="w-24 h-24 border-0">
                                <AvatarImage src="/api/placeholder/100/100" />
                                <AvatarFallback className="text-2xl font-bold bg-gradient-to-br from-blue-500 to-indigo-600 text-white">
                                    {organization.abbreviation}
                                </AvatarFallback>
                            </Avatar>
                        </div>

                        <div className="flex-grow">
                            <div className="flex flex-col lg:flex-row lg:items-start justify-between gap-4">
                                <div>
                                    <h1 className="text-3xl font-bold mb-2">{organization.name}</h1>
                                    <div className="flex items-center gap-2 mb-3">
                                        <Badge className={`${statusConfig.color} px-3 py-1`}>
                                            {statusConfig.icon}
                                            <span className="ml-1">{statusConfig.label}</span>
                                        </Badge>
                                        <span className="text-sm text-gray-500">
                                            Reg: {organization.registration_number}
                                        </span>
                                    </div>
                                    <p className="text-gray-500 text-lg mb-4 max-w-2xl">{organization.biography}</p>
                                    <div className="flex flex-wrap gap-4 text-sm text-gray-500">
                                        <div className="flex items-center gap-1">
                                            <Calendar className="w-4 h-4" />
                                            Financial Year: {organization.financial_start_month} -{" "}
                                            {organization.financial_end_month}
                                        </div>
                                        <div className="flex items-center gap-1">
                                            <DollarSign className="w-4 h-4" />
                                            Annual Income: ${organization.annual_income.toLocaleString()}
                                        </div>
                                    </div>
                                </div>

                                {/* Action Buttons */}
                                <div className="flex flex-col gap-2">
                                    {canEdit && (
                                        <Button
                                            className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white shadow-lg"
                                            loading={loading}
                                            onClick={submitForReview}
                                        >
                                            <Send className="w-4 h-4 mr-2" />
                                            Submit for Review
                                        </Button>
                                    )}
                                    {isApproved && (
                                        <>
                                            <Button
                                                className="border-blue-200 text-blue-700 hover:bg-blue-50"
                                                variant="outline"
                                            >
                                                <UserPlus className="w-4 h-4 mr-2" />
                                                Invite Members
                                            </Button>
                                            <Button
                                                className="border-blue-200 text-blue-700 hover:bg-blue-50"
                                                variant="outline"
                                            >
                                                <Bell className="w-4 h-4 mr-2" />
                                                Notifications
                                            </Button>
                                        </>
                                    )}
                                    <div className="flex gap-1">
                                        <Button variant="ghost">
                                            <Settings className="w-4 h-4" />
                                        </Button>
                                        <Button variant="ghost">
                                            <Share2 className="w-4 h-4" />
                                        </Button>
                                        <Button variant="ghost">
                                            <MoreHorizontal className="w-4 h-4" />
                                        </Button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </CardContent>
            </Card>
        </div>
    );
};
