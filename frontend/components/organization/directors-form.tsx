import { Plus, Trash2 } from "lucide-react";

import CountryInput from "../inputs/country";
import LoadableItemInput from "../inputs/loadable-item";
import { Button } from "../ui/button";
import { Card } from "../ui/card";
import { Input } from "../ui/input";
import { Select } from "../ui/select";

import { DirectorRequest } from "@/types";

const DirectorsForm = ({
    directors,
    setDirectors,
}: {
    directors: DirectorRequest[];
    setDirectors: (directors: DirectorRequest[]) => void;
}) => {
    const addDirector = () => {
        setDirectors([
            ...directors,
            {
                fullname: "",
                email: "",
                phone: "",
                gender: "MALE",
                position: "",
                country_id: "",
                occupation: "",
                timeframe: "",
                qualification_id: "",
            },
        ]);
    };

    const updateDirector = (index: number, field: keyof DirectorRequest, value: any) => {
        const updated = [...directors];

        updated[index] = { ...updated[index], [field]: value };
        setDirectors(updated);
    };

    const removeDirector = (index: number) => {
        setDirectors(directors.filter((_, i) => i !== index));
    };

    return (
        <div className="space-y-4">
            <div className="flex justify-between items-center">
                <h3 className="text-lg font-semibold">Directors</h3>
                <Button size="sm" onClick={addDirector}>
                    <Plus className="w-4 h-4 mr-2" />
                    Add Director
                </Button>
            </div>

            {directors.map((director, index) => (
                <Card key={index} className="p-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <Input
                            label="Full Name"
                            placeholder="Director's full name"
                            value={director.fullname}
                            onChange={(e) => updateDirector(index, "fullname", e.target.value)}
                        />
                        <Input
                            label="Email"
                            placeholder="<EMAIL>"
                            type="email"
                            value={director.email}
                            onChange={(e) => updateDirector(index, "email", e.target.value)}
                        />
                        <Input
                            label="Phone"
                            placeholder="Phone number"
                            value={director.phone}
                            onChange={(e) => updateDirector(index, "phone", e.target.value)}
                        />
                        <Select
                            label="Gender"
                            options={["FEMALE", "MALE"]}
                            value={director.gender}
                            onValueChange={(value) => updateDirector(index, "gender", value)}
                        />
                        <Input
                            label="Position"
                            placeholder="Director position"
                            value={director.position}
                            onChange={(e) => updateDirector(index, "position", e.target.value)}
                        />
                        <Input
                            label="Occupation"
                            placeholder="Director's occupation"
                            value={director.occupation}
                            onChange={(e) => updateDirector(index, "occupation", e.target.value)}
                        />
                        <Input
                            label="Timeframe"
                            placeholder="e.g., 2020-2025"
                            value={director.timeframe}
                            onChange={(e) => updateDirector(index, "timeframe", e.target.value)}
                        />

                        <CountryInput
                            label="Directors country"
                            name={director.country?.name}
                            value={director.country_id}
                            onCountrySelect={(e) => updateDirector(index, "country_id", e?.id)}
                        />

                        <LoadableItemInput
                            label="Qualification"
                            type="QUALIFICATION"
                            value={director.qualification_id}
                            onItemSelect={(e) => updateDirector(index, "qualification_id", e?.id)}
                        />
                        <Input
                            label="National Identifier (Optional)"
                            placeholder="National ID"
                            value={director.national_identifier || ""}
                            onChange={(e) => updateDirector(index, "national_identifier", e.target.value)}
                        />
                    </div>
                    <Button className="mt-2" size="sm" variant="destructive" onClick={() => removeDirector(index)}>
                        <Trash2 className="w-4 h-4 mr-2" />
                        Remove Director
                    </Button>
                </Card>
            ))}
        </div>
    );
};

export default DirectorsForm;
