"use client";

import { useMutation, useQueryClient } from "@tanstack/react-query";
import { Plus, Save, Target, Trash2, X } from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";

import LoadableItemInput from "@/components/inputs/loadable-item";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import * as organizationService from "@/services/OrganizationService";
import { OrganizationSectorDto } from "@/types/organization.dto";

interface SectorsManagementProps {
    organizationId: string;
    sectors: OrganizationSectorDto[];
    canEdit: boolean;
}

export default function SectorsManagement({ organizationId, sectors, canEdit }: SectorsManagementProps) {
    const [isDialogOpen, setIsDialogOpen] = useState(false);
    const [selectedSectorId, setSelectedSectorId] = useState<string>("");
    const queryClient = useQueryClient();

    const addSectorMutation = useMutation({
        mutationFn: (sectorId: string) => organizationService.addSectors(organizationId, [sectorId]),
        onSuccess: () => {
            toast.success("Sector added successfully!");
            queryClient.invalidateQueries({ queryKey: ["organization-sectors", organizationId] });
            setIsDialogOpen(false);
            setSelectedSectorId("");
        },
        onError: (error: any) => {
            toast.error(error?.message || "Failed to add sector");
        },
    });

    const deleteSectorMutation = useMutation({
        mutationFn: (sectorId: string) => organizationService.deleteSector(organizationId, sectorId),
        onSuccess: () => {
            toast.success("Sector removed successfully!");
            queryClient.invalidateQueries({ queryKey: ["organization-sectors", organizationId] });
        },
        onError: (error: any) => {
            toast.error(error?.message || "Failed to remove sector");
        },
    });

    const handleAdd = () => {
        setSelectedSectorId("");
        setIsDialogOpen(true);
    };

    const handleSubmit = () => {
        if (selectedSectorId) {
            addSectorMutation.mutate(selectedSectorId);
        }
    };

    const handleDelete = (sectorId: string) => {
        if (confirm("Are you sure you want to remove this sector?")) {
            deleteSectorMutation.mutate(sectorId);
        }
    };

    return (
        <Card className="border-border/50">
            <CardHeader>
                <div className="flex justify-between items-center">
                    <CardTitle className="flex items-center gap-2 text-foreground">
                        <Target className="w-5 h-5 text-primary" />
                        Operational Sectors ({sectors.length})
                    </CardTitle>
                    {canEdit && (
                        <Button size="sm" onClick={handleAdd}>
                            <Plus className="w-4 h-4 mr-2" />
                            Add Sector
                        </Button>
                    )}
                </div>
            </CardHeader>
            <CardContent>
                {sectors.length > 0 ? (
                    <div className="flex flex-wrap gap-2">
                        {sectors.map((sector) => (
                            <div key={sector.id} className="flex items-center gap-2">
                                <Badge
                                    className="px-3 py-1 bg-primary/10 text-primary border-primary/20 flex items-center gap-2"
                                    variant="secondary"
                                >
                                    {sector?.name || "Unknown Sector"}
                                    {canEdit && (
                                        <button
                                            className="ml-1 hover:text-destructive transition-colors"
                                            disabled={deleteSectorMutation.isPending}
                                            onClick={() => handleDelete(sector.id)}
                                        >
                                            <Trash2 className="w-3 h-3" />
                                        </button>
                                    )}
                                </Badge>
                            </div>
                        ))}
                    </div>
                ) : (
                    <div className="text-center py-8">
                        <Target className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                        <p className="text-muted-foreground mb-4">No sectors defined yet.</p>
                        {canEdit && (
                            <Button onClick={handleAdd}>
                                <Plus className="w-4 h-4 mr-2" />
                                Add First Sector
                            </Button>
                        )}
                    </div>
                )}
            </CardContent>

            {/* Add Sector Dialog */}
            <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
                <DialogContent className="max-w-md">
                    <DialogHeader>
                        <DialogTitle>Add Sector</DialogTitle>
                    </DialogHeader>
                    <div className="py-4">
                        <LoadableItemInput
                            label="Select Sector"
                            type="SECTOR"
                            value={selectedSectorId}
                            onItemSelect={(item) => setSelectedSectorId(item?.id || "")}
                        />
                    </div>
                    <div className="flex justify-end gap-2">
                        <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
                            <X className="w-4 h-4 mr-2" />
                            Cancel
                        </Button>
                        <Button disabled={!selectedSectorId || addSectorMutation.isPending} onClick={handleSubmit}>
                            {addSectorMutation.isPending ? (
                                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                            ) : (
                                <Save className="w-4 h-4 mr-2" />
                            )}
                            Add Sector
                        </Button>
                    </div>
                </DialogContent>
            </Dialog>
        </Card>
    );
}
