"use client";

import {
    <PERSON>ert<PERSON>ircle,
    Building2,
    Camera,
    CheckCircle,
    Clock,
    Edit,
    Edit2Icon,
    Eye,
    ImageIcon,
    MapPin,
    Send,
} from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { useState } from "react";

import { Label } from "../ui/label";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { OrganizationDto } from "@/types/organization.dto";

interface OrganizationProfileBannerProps {
    organization: OrganizationDto;
    canEdit: boolean;
    canSubmit: boolean;
    onSubmitForReview: () => void;
    isSubmitting: boolean;
}

const getStatusConfig = (status: string) => {
    switch (status) {
        case "DRAFT":
        case "RENEWAL_DRAFT":
            return {
                color: "bg-yellow-100 text-yellow-800 border-yellow-200",
                icon: <Edit className="w-4 h-4" />,
                label: "Draft",
                description: "Organization profile is being prepared",
            };
        case "PENDING":
            return {
                color: "bg-blue-100 text-blue-800 border-blue-200",
                icon: <Clock className="w-4 h-4" />,
                label: "Pending Review",
                description: "Application submitted and awaiting review",
            };
        case "REVIEW":
        case "IN_REVIEW":
            return {
                color: "bg-purple-100 text-purple-800 border-purple-200",
                icon: <Eye className="w-4 h-4" />,
                label: "Under Review",
                description: "Application is being reviewed by authorities",
            };
        case "REGISTERED":
        case "ACTIVE":
            return {
                color: "bg-green-100 text-green-800 border-green-200",
                icon: <CheckCircle className="w-4 h-4" />,
                label: "Active",
                description: "Organization is approved and operational",
            };
        case "SUSPENDED":
            return {
                color: "bg-red-100 text-red-800 border-red-200",
                icon: <AlertCircle className="w-4 h-4" />,
                label: "Suspended",
                description: "Organization activities are temporarily suspended",
            };
        case "INACTIVE":
            return {
                color: "bg-gray-100 text-gray-800 border-gray-200",
                icon: <AlertCircle className="w-4 h-4" />,
                label: "Inactive",
                description: "Organization is no longer active",
            };
        case "REJECTED":
            return {
                color: "bg-red-100 text-red-800 border-red-200",
                icon: <AlertCircle className="w-4 h-4" />,
                label: "Rejected",
                description: "Application was rejected and needs revision",
            };
        default:
            return {
                color: "bg-gray-100 text-gray-800 border-gray-200",
                icon: <AlertCircle className="w-4 h-4" />,
                label: status,
                description: "Unknown status",
            };
    }
};

export default function OrganizationProfileBanner({
    organization,
    canEdit,
    canSubmit,
    onSubmitForReview,
    isSubmitting,
}: OrganizationProfileBannerProps) {
    const statusConfig = getStatusConfig(organization.status);
    const [isImageDialogOpen, setIsImageDialogOpen] = useState(false);

    const bannerImage = organization?.banner || "https://placehold.co/1200x400/png";
    const organizationLogo = organization?.logo || "https://placehold.co/400x400/png";

    return (
        <div className="">
            {/* Navigation */}
            <div className="flex items-center justify-end">
                <div className="flex items-center gap-2">
                    {organization.status === "RENEWAL_DRAFT" && (
                        <Button disabled={isSubmitting} variant="outline" onClick={onSubmitForReview}>
                            <Link
                                className="flex items-center gap-2 text-yellow-600"
                                href={`/reporting/${organization.id}`}
                            >
                                <Edit2Icon />
                                Edit Renewal Application
                            </Link>
                        </Button>
                    )}

                    {canSubmit && (
                        <Button
                            className="bg-purple-600  hover:bg-purple-700 text-white"
                            disabled={isSubmitting}
                            onClick={onSubmitForReview}
                        >
                            {isSubmitting ? (
                                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                            ) : (
                                <Send className="w-4 h-4 mr-2" />
                            )}
                            Submit for Review
                        </Button>
                    )}
                </div>
            </div>

            {/* Organization Banner */}
            <Card className="border-border/50 bg-card/80 backdrop-blur-sm overflow-hidden p-0">
                {/* Banner Image Section */}
                <div className="relative h-48 md:h-64 overflow-hidden">
                    <div
                        className="absolute inset-0 bg-cover bg-center bg-no-repeat"
                        style={{
                            backgroundImage: `linear-gradient(to bottom, rgba(0,0,0,0.3), rgba(0,0,0,0.6)), url(${bannerImage})`,
                        }}
                    >
                        {/* Banner Overlay Content */}
                        <div className="absolute inset-0 flex items-end">
                            <div className="w-full p-6 md:p-8">
                                <div className="flex items-end gap-6">
                                    {/* Organization Logo */}
                                    <div className="relative flex-shrink-0">
                                        <div className="w-20 h-20 md:w-24 md:h-24 rounded-full border-4 border-white/20 bg-white/10 backdrop-blur-sm overflow-hidden">
                                            <Image
                                                alt={``}
                                                className="w-full h-full object-cover"
                                                height={400}
                                                src={organizationLogo}
                                                width={1200}
                                                onError={(e: any) => {
                                                    e.currentTarget.style.display = "none";
                                                    e.currentTarget.nextElementSibling!.style.display = "flex";
                                                }}
                                            />
                                            <div
                                                className="w-full h-full bg-gradient-to-br from-blue-500 to-indigo-600 text-white flex items-center justify-center text-xl font-bold"
                                                style={{ display: "none" }}
                                            >
                                                <Building2 className="w-8 h-8" />
                                            </div>
                                        </div>
                                        {canEdit && (
                                            <Button
                                                className="absolute -bottom-2 -right-2 w-8 h-8 rounded-full p-0 bg-white/90 hover:bg-white"
                                                size="sm"
                                                variant="secondary"
                                                onClick={() => setIsImageDialogOpen(true)}
                                            >
                                                <Camera className="w-4 h-4" />
                                            </Button>
                                        )}
                                    </div>

                                    {/* Organization Info */}
                                    <div className="flex-1 min-w-0">
                                        <h1 className="text-2xl md:text-3xl font-bold text-white mb-2 break-words">
                                            {organization.name}
                                        </h1>
                                        <div className="flex flex-wrap items-center gap-4 text-white/90 text-sm">
                                            <span className="flex items-center gap-1">
                                                <Building2 className="w-4 h-4" />
                                                {organization.abbreviation}
                                            </span>
                                            <span className="flex items-center gap-1">
                                                <MapPin className="w-4 h-4" />
                                                {organization.registration_number}
                                            </span>
                                        </div>
                                    </div>

                                    {/* Status and Actions */}
                                    <div className="flex flex-col items-end gap-3 flex-shrink-0">
                                        <Badge
                                            className={`${statusConfig.color} border flex items-center gap-2 px-3 py-1 text-sm font-medium`}
                                        >
                                            {statusConfig.icon}
                                            {statusConfig.label}
                                        </Badge>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {/* Banner Edit Button */}
                        {canEdit && (
                            <Button
                                className="absolute top-4 right-4 bg-white/10 hover:bg-white/20 text-white border-white/20"
                                size="sm"
                                variant="secondary"
                                onClick={() => setIsImageDialogOpen(true)}
                            >
                                <ImageIcon className="w-4 h-4 mr-2" />
                                Change Banner
                            </Button>
                        )}
                    </div>
                </div>

                {/* Content Section */}
                <CardContent className="p-6 md:p-8">
                    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                        {/* Organization Details */}
                        <div className="lg:col-span-2 space-y-4">
                            {/* Status Description */}
                            <div className="flex items-center justify-between">
                                <p className="text-sm text-muted-foreground">{statusConfig.description}</p>
                                {canEdit && (
                                    <Button size="sm" variant="outline">
                                        <Edit className="w-4 h-4 mr-2" />
                                        Edit Profile
                                    </Button>
                                )}
                            </div>

                            {/* Organization Biography */}
                            {organization.biography && (
                                <div>
                                    <h3 className="font-semibold text-foreground mb-2">About</h3>
                                    <p className="text-muted-foreground leading-relaxed">{organization.biography}</p>
                                </div>
                            )}

                            {/* Key Information */}
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                                <div>
                                    <span className="text-muted-foreground">Registration Number:</span>
                                    <p className="font-medium">{organization.registration_number}</p>
                                </div>
                                {organization.charity_number && (
                                    <div>
                                        <span className="text-muted-foreground">Charity Number:</span>
                                        <p className="font-medium">{organization.charity_number}</p>
                                    </div>
                                )}
                                <div>
                                    <span className="text-muted-foreground">Created:</span>
                                    <p className="font-medium">
                                        {new Date(organization.created_at).toLocaleDateString()}
                                    </p>
                                </div>
                                <div>
                                    <span className="text-muted-foreground">Organization Type:</span>
                                    <p className="font-medium">{organization?.type || "N/A"}</p>
                                </div>
                            </div>
                        </div>

                        {/* Quick Stats */}
                        <div className="space-y-4">
                            <h3 className="font-semibold text-foreground">Key Metrics</h3>
                            <div className="space-y-3">
                                <div className="p-4 rounded-lg bg-muted/30 border border-border/50">
                                    <div className="text-xl font-bold text-foreground">
                                        ${organization.annual_income?.toLocaleString() || "0"}
                                    </div>
                                    <div className="text-sm text-muted-foreground">Annual Income</div>
                                </div>
                                <div className="p-4 rounded-lg bg-muted/30 border border-border/50">
                                    <div className="text-lg font-bold text-foreground">
                                        {organization.financial_start_month} - {organization.financial_end_month}
                                    </div>
                                    <div className="text-sm text-muted-foreground">Financial Year</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </CardContent>
            </Card>

            {/* Image Upload Dialog */}
            <Dialog open={isImageDialogOpen} onOpenChange={setIsImageDialogOpen}>
                <DialogContent className="max-w-md">
                    <DialogHeader>
                        <DialogTitle>Update Organization Images</DialogTitle>
                    </DialogHeader>
                    <div className="space-y-4 py-4">
                        <div className="space-y-2">
                            <Label className="text-sm font-medium">Banner Image</Label>
                            <div className="border-2 border-dashed border-border rounded-lg p-6 text-center">
                                <ImageIcon className="w-12 h-12 text-muted-foreground mx-auto mb-2" />
                                <p className="text-sm text-muted-foreground">Click to upload banner image</p>
                                <p className="text-xs text-muted-foreground mt-1">Recommended: 1200x400px</p>
                            </div>
                        </div>
                        <div className="space-y-2">
                            <Label className="text-sm font-medium">Organization Logo</Label>
                            <div className="border-2 border-dashed border-border rounded-lg p-6 text-center">
                                <Camera className="w-12 h-12 text-muted-foreground mx-auto mb-2" />
                                <p className="text-sm text-muted-foreground">Click to upload logo</p>
                                <p className="text-xs text-muted-foreground mt-1">Recommended: 400x400px (square)</p>
                            </div>
                        </div>
                    </div>
                    <div className="flex justify-end gap-2">
                        <Button variant="outline" onClick={() => setIsImageDialogOpen(false)}>
                            Cancel
                        </Button>
                        <Button onClick={() => setIsImageDialogOpen(false)}>Save Changes</Button>
                    </div>
                </DialogContent>
            </Dialog>
        </div>
    );
}
