import { Plus, Trash2 } from "lucide-react";

import LoadableItemInput from "../inputs/loadable-item";
import { Button } from "../ui/button";
import { Card } from "../ui/card";
import { Input } from "../ui/input";

import { BankDetailsRequest } from "@/types";

const BankDetailsForm = ({
    bankDetails,
    setBankDetails,
}: {
    bankDetails: BankDetailsRequest[];
    setBankDetails: (details: BankDetailsRequest[]) => void;
}) => {
    const addBankDetail = () => {
        setBankDetails([
            ...bankDetails,
            {
                account_number: "",
                branch_name: "",
                bank_id: "",
            },
        ]);
    };

    const updateBankDetail = (index: number, field: keyof BankDetailsRequest, value: string) => {
        const updated = [...bankDetails];

        updated[index] = { ...updated[index], [field]: value };
        setBankDetails(updated);
    };

    const removeBankDetail = (index: number) => {
        setBankDetails(bankDetails.filter((_, i) => i !== index));
    };

    return (
        <div className="space-y-4">
            <div className="flex justify-between items-center">
                <h3 className="text-lg font-semibold">Bank Details</h3>
                <Button size="sm" onClick={addBankDetail}>
                    <Plus className="w-4 h-4 mr-2" />
                    Add Bank Account
                </Button>
            </div>

            {bankDetails.map((detail, index) => (
                <Card key={index} className="p-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <LoadableItemInput
                            label="Bank Name"
                            placeholder="Bank Name"
                            type="BANK"
                            value={detail.bank_id}
                            onItemSelect={(e) => updateBankDetail(index, "bank_id", e?.id!)}
                        />
                        <Input
                            label="Account Number"
                            placeholder="Bank account number"
                            value={detail.account_number}
                            onChange={(e) => updateBankDetail(index, "account_number", e.target.value)}
                        />
                        <Input
                            label="Branch Name"
                            placeholder="Bank branch name"
                            value={detail.branch_name}
                            onChange={(e) => updateBankDetail(index, "branch_name", e.target.value)}
                        />
                    </div>
                    <Button className="mt-2" size="sm" variant="destructive" onClick={() => removeBankDetail(index)}>
                        <Trash2 className="w-4 h-4 mr-2" />
                        Remove Account
                    </Button>
                </Card>
            ))}
        </div>
    );
};

export default BankDetailsForm;
