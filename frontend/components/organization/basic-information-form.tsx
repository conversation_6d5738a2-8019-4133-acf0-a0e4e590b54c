"use client";

import { Building, Plus, Trash2 } from "lucide-react";

import LoadableItemInput from "../inputs/loadable-item";
import { ManagerInput } from "../inputs/manager";
import { Button } from "../ui/button";
import { Input } from "../ui/input";
import { Label } from "../ui/label";
import { Select } from "../ui/select";
import { Textarea } from "../ui/textarea";

import { OrganizationFormData } from "@/stores/organization-form";

interface BasicInformationFormData {
    name: string;
    abbreviation: string;
    organization_type_id: string;
    district_id: string;
    financial_start_month: string;
    financial_end_month: string;
    registration_type_id: string;
    charity_number: string;
    annual_income: number;
    biography: string;
    vision: string;
    motto: string;
    objectives: string[];
}

interface BasicInformationFormProps {
    formData: BasicInformationFormData;
    updateFormData: (field: keyof OrganizationFormData, value: any) => void;
    className?: string;
}

const BasicInformationForm = ({ formData, updateFormData, className = "" }: BasicInformationFormProps) => {
    const months = ["JAN", "FEB", "MAR", "APR", "MAY", "JUN", "JUL", "AUG", "SEP", "OCT", "NOV", "DEC"];

    const addObjective = () => {
        updateFormData("objectives", [...formData.objectives, ""]);
    };

    const updateObjective = (index: number, value: string) => {
        const newObjectives = formData.objectives.map((obj, i) => (i === index ? value : obj));

        updateFormData("objectives", newObjectives);
    };

    const removeObjective = (index: number) => {
        const newObjectives = formData.objectives.filter((_, i) => i !== index);

        updateFormData("objectives", newObjectives);
    };

    return (
        <div className={`animate-in slide-in-from-right-5 duration-300 ${className}`}>
            {/* Header Section */}
            <div className="text-center space-y-2">
                <div className="w-16 h-16 mx-auto 0 rounded-2xl flex items-center justify-center">
                    <Building className="w-8 h-8" />
                </div>
                <h2 className="text-2xl font-bold">Basic Information</h2>
                <p className="text-muted-foreground">Tell us about your organization</p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <Input
                    id="name"
                    label="Organization Name *"
                    placeholder="Enter organization name"
                    value={formData.name}
                    onChange={(e) => updateFormData("name", e.target.value)}
                />
                <Input
                    id="abbreviation"
                    label="Abbreviation *"
                    placeholder="e.g., NGO, NPO"
                    value={formData.abbreviation}
                    onChange={(e) => updateFormData("abbreviation", e.target.value)}
                />
                <LoadableItemInput
                    label="Organization Type"
                    type="ORGANIZATION_TYPE"
                    value={formData.organization_type_id}
                    onItemSelect={(e) => updateFormData("organization_type_id", e?.id)}
                />
                <ManagerInput
                    initialFetchSize={32}
                    placeholder="District *"
                    searchFetchSize={32}
                    type="district"
                    value={formData.district_id}
                    onItemSelect={(e) => updateFormData("district_id", e?.id)}
                />

                <LoadableItemInput
                    label="Registration type *"
                    type="REGISTRATION_TYPE"
                    value={formData.registration_type_id}
                    onItemSelect={(e) => updateFormData("registration_type_id", e?.id)}
                />

                <Input
                    id="charity_number"
                    label="Charity Number"
                    placeholder="Optional registration number"
                    value={formData.charity_number}
                    onChange={(e) => updateFormData("charity_number", e.target.value)}
                />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-6">
                <Select
                    label="Financial Year Start"
                    options={months}
                    value={formData.financial_start_month}
                    onValueChange={(value) => updateFormData("financial_start_month", value)}
                />
                <Select
                    label="Financial Year End"
                    options={months}
                    value={formData.financial_end_month}
                    onValueChange={(value) => updateFormData("financial_end_month", value)}
                />
                <Input
                    id="annual_income"
                    label="Annual Income"
                    placeholder="0"
                    type="number"
                    value={formData.annual_income}
                    onChange={(e) => updateFormData("annual_income", parseFloat(e.target.value) || 0)}
                />
            </div>

            <div className="mt-6">
                <Textarea
                    className="resize-none"
                    id="biography"
                    label="Biography"
                    maxLength={300}
                    placeholder="Tell us about your organization's background and mission..."
                    rows={3}
                    value={formData.biography}
                    onChange={(e) => updateFormData("biography", e.target.value)}
                />
            </div>

            <div className="mt-6">
                <Textarea
                    className="resize-none"
                    id="vision"
                    label="Vision Statement"
                    placeholder="Your organization's vision for the future..."
                    rows={2}
                    value={formData.vision}
                    onChange={(e) => updateFormData("vision", e.target.value)}
                />
            </div>

            <div className="mt-6">
                <Input
                    id="motto"
                    label="Motto"
                    placeholder="Your inspiring motto..."
                    value={formData.motto}
                    onChange={(e) => updateFormData("motto", e.target.value)}
                />
            </div>

            <div className="space-y-4 mt-6">
                <div className="flex justify-between items-center">
                    <Label className="text-sm font-medium">Objectives</Label>
                    <Button className="h-8" size="sm" onClick={addObjective}>
                        <Plus className="w-4 h-4 mr-1" />
                        Add
                    </Button>
                </div>
                <div className="space-y-3">
                    {formData.objectives.map((objective, index) => (
                        <div key={index} className="flex gap-3 group">
                            <div className="flex-1">
                                <Input
                                    placeholder={`Objective ${index + 1}`}
                                    value={objective}
                                    onChange={(e) => updateObjective(index, e.target.value)}
                                />
                            </div>
                            {formData.objectives.length > 1 && (
                                <Button
                                    className="h-11 w-11 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                                    size="sm"
                                    variant="outline"
                                    onClick={() => removeObjective(index)}
                                >
                                    <Trash2 className="w-4 h-4" />
                                </Button>
                            )}
                        </div>
                    ))}
                </div>
            </div>
        </div>
    );
};

export default BasicInformationForm;
