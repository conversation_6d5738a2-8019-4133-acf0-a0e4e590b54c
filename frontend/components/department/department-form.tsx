import { useEffect, useState } from "react";
import z from "zod";

import { DepartmentDto } from "@/types";
import { Button } from "@/components/ui/button";
import { DialogFooter } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";

export const departmentSchema = z.object({
    code: z.string().min(3, "Code must be at least 3 characters"),
    name: z.string().min(3, "Name must be at least 3 characters"),
    description: z
        .string()
        .min(3, "Description must be at least 3 characters")
        .max(150, "Description must be at most 150 characters")
        .optional()
        .or(z.literal("")),
});

export type DepartmentSchema = z.infer<typeof departmentSchema>;

export type DepartmentFormProps = {
    onClose: () => void;
    onAdd: (data: any) => void;
    onUpdate: (data: any) => void;
    user: DepartmentDto | null; // Keep this prop name for compatibility
    formMode: "create" | "edit";
    isSubmitting?: boolean;
};

export const DepartmentForm = ({
    onClose,
    onAdd,
    user: department, // Rename for clarity
    formMode,
    onUpdate,
    isSubmitting,
}: DepartmentFormProps) => {
    const [errors, setErrors] = useState<Record<string, string>>({});
    const [isSubmittingInternal, setIsSubmittingInternal] = useState(false);
    const [formData, setFormData] = useState<DepartmentSchema>({
        name: "",
        code: "",
        description: "",
    });

    // Initialize form data when editing
    useEffect(() => {
        if (formMode === "edit" && department) {
            setFormData({
                name: department.name || "",
                code: department.code || "",
                description: department.description || "",
            });
        } else {
            setFormData({
                name: "",
                code: "",
                description: "",
            });
        }
    }, [formMode, department]);

    const validateForm = () => {
        try {
            departmentSchema.parse(formData);
            setErrors({});

            return true;
        } catch (error) {
            if (error instanceof z.ZodError) {
                const formattedErrors: Record<string, string> = {};

                error.errors.forEach((err) => {
                    if (err.path[0]) {
                        formattedErrors[err.path[0].toString()] = err.message;
                    }
                });
                setErrors(formattedErrors);
            }

            return false;
        }
    };

    const handleChange = (field: keyof DepartmentSchema, value: string) => {
        setFormData((prev) => ({ ...prev, [field]: value }));

        // Clear error for this field when user starts typing
        if (errors[field]) {
            setErrors((prev) => ({ ...prev, [field]: "" }));
        }
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();

        if (!validateForm()) return;

        // Use an internal flag if parent didn't provide isSubmitting
        setIsSubmittingInternal(true);

        const payload = { ...formData };

        if (formMode === "create") {
            onAdd(payload);
        } else if (formMode === "edit" && department) {
            onUpdate(payload);
        }

        setIsSubmittingInternal(false);
    };

    return (
        <form className="space-y-4" onSubmit={handleSubmit}>
            <Input
                disabled={isSubmitting}
                error={errors.name}
                label="Department Name"
                placeholder="Enter department name"
                value={formData.name}
                onChange={(e) => handleChange("name", e.target.value)}
            />

            <Input
                disabled={isSubmitting}
                error={errors.code}
                label="Department Code"
                placeholder="Enter department code"
                value={formData.code}
                onChange={(e) => handleChange("code", e.target.value)}
            />

            <Textarea
                disabled={isSubmitting}
                error={errors.description}
                label="Description"
                maxLength={150}
                placeholder="Enter department description (optional)"
                value={formData.description}
                onChange={(e) => handleChange("description", e.target.value)}
            />

            <DialogFooter>
                <Button
                    disabled={isSubmitting || isSubmittingInternal}
                    type="button"
                    variant="outline"
                    onClick={onClose}
                >
                    Cancel
                </Button>
                <Button disabled={isSubmitting || isSubmittingInternal} type="submit">
                    {isSubmitting || isSubmittingInternal
                        ? formMode === "create"
                            ? "Creating..."
                            : "Updating..."
                        : formMode === "create"
                          ? "Create Department"
                          : "Update Department"}
                </Button>
            </DialogFooter>
        </form>
    );
};
