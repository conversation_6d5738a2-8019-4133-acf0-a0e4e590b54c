import { Building2, Check, Users } from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useState } from "react";

import { Avatar, AvatarFallback, AvatarImage } from "../ui/avatar";
import { Badge } from "../ui/badge";
import { Button } from "../ui/button";
import { Card, CardContent } from "../ui/card";

import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { useAuth } from "@/composables/useStore";
import { MembershipSummaryDto } from "@/types";

const MembershipCard = ({
    membership,
    onSelect,
    isSelected,
}: {
    membership: MembershipSummaryDto;
    onSelect: () => void;
    isSelected: boolean;
}) => {
    return (
        <Card
            className={`cursor-pointer transition-colors hover:bg-accent/50 ${
                isSelected ? "ring-2 ring-primary bg-accent/30" : ""
            }`}
            onClick={onSelect}
        >
            <CardContent className="px-4">
                <div className="flex items-center gap-3">
                    <Avatar className="h-12 w-12">
                        <AvatarImage alt={membership.organization.name} src={membership.organization.logo} />
                        <AvatarFallback>
                            <Building2 className="h-6 w-6" />
                        </AvatarFallback>
                    </Avatar>

                    <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2">
                            <h3 className="font-medium text-foreground truncate">{membership.organization.name}</h3>
                            {isSelected && <Check className="h-4 w-4 text-primary" />}
                        </div>

                        <div className="flex items-center gap-2 mt-1">
                            <Badge className="text-xs" variant={membership.role === "OWNER" ? "default" : "secondary"}>
                                {membership.role}
                            </Badge>
                            {membership.organization.abbreviation && (
                                <span className="text-xs text-muted-foreground">
                                    {membership.organization.abbreviation}
                                </span>
                            )}
                        </div>

                        {membership.organization.registration_number && (
                            <p className="text-xs text-muted-foreground mt-1">
                                Reg: {membership.organization.registration_number}
                            </p>
                        )}
                    </div>
                </div>
            </CardContent>
        </Card>
    );
};

export const ContextSetter = () => {
    const router = useRouter();
    const { session, isAuthenticated, setContext } = useAuth();
    const [selectedMembership, setSelectedMembership] = useState<MembershipSummaryDto | null>(null);
    const [isOpen, setIsOpen] = useState(true);

    const handleMembershipSelect = (membership: MembershipSummaryDto) => {
        setSelectedMembership(membership);
    };

    const handleConfirm = () => {
        if (selectedMembership) {
            setContext(selectedMembership);
            setIsOpen(false);
        }
    };

    const onOpenChange = (open: boolean) => {
        if (!open) {
            if (!isAuthenticated || !session) {
                router.push("/auth/login");

                return;
            }

            if (!session?.memberships?.length) {
                router.back();

                return;
            }

            setIsOpen(false);
        } else {
            setIsOpen(true);
        }
    };

    if (!isAuthenticated || !session) {
        return (
            <Dialog open={isOpen} onOpenChange={onOpenChange}>
                <DialogContent>
                    <DialogHeader>
                        <DialogTitle>You are not logged in</DialogTitle>
                        <DialogDescription>Please log in to continue.</DialogDescription>
                    </DialogHeader>
                    <div className="flex justify-end mt-4">
                        <Button asChild>
                            <Link href="/auth/login">Go to login</Link>
                        </Button>
                    </div>
                </DialogContent>
            </Dialog>
        );
    }

    if (!session.memberships?.length) {
        return (
            <Dialog open={isOpen} onOpenChange={onOpenChange}>
                <DialogContent>
                    <DialogHeader>
                        <DialogTitle>No Organization Memberships</DialogTitle>
                        <DialogDescription>You are not currently connected to any organization.</DialogDescription>
                    </DialogHeader>
                    <div className="space-y-4 mt-4">
                        <div className="p-6 rounded-xl text-red-900 bg-red-500/10 text-center">
                            <Building2 className="h-12 w-12 mx-auto mb-2 text-red-600" />
                            <p>You need to be a member of an organization to continue.</p>
                        </div>
                        <div className="flex justify-center">
                            <Button asChild>
                                <Link href="/organizations/create">Create Organization</Link>
                            </Button>
                        </div>
                    </div>
                </DialogContent>
            </Dialog>
        );
    }

    return (
        <Dialog open={isOpen} onOpenChange={onOpenChange}>
            <DialogContent className="max-w-5xl max-h-[700px] overflow-y-auto">
                <DialogHeader>
                    <DialogTitle className="flex items-center gap-2">
                        <Users className="h-5 w-5" />
                        Select Organization
                    </DialogTitle>
                    <DialogDescription>Choose which organization you&apos;d like to work with.</DialogDescription>
                </DialogHeader>

                <div className="space-y-3 mt-4">
                    {session.memberships.map((membership) => (
                        <MembershipCard
                            key={membership.id}
                            isSelected={selectedMembership?.id === membership.id}
                            membership={membership}
                            onSelect={() => handleMembershipSelect(membership)}
                        />
                    ))}
                </div>

                <div className="flex justify-end gap-2 mt-6">
                    <Button variant="outline" onClick={() => setIsOpen(false)}>
                        Cancel
                    </Button>
                    <Button disabled={!selectedMembership} onClick={handleConfirm}>
                        Continue
                    </Button>
                </div>
            </DialogContent>
        </Dialog>
    );
};
