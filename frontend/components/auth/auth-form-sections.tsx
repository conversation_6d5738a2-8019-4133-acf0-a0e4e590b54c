import { <PERSON>Lef<PERSON> } from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";

import { Logo } from "../icons";
import { Button } from "../ui/button";
import { Divider } from "../ui/divider";

type PageName = "login" | "register" | "recovery" | "2fa" | "password-reset";

export const AuthFormHeader = ({ page }: { page?: PageName }) => (
    <div>
        <div className="text-center mb-8">
            <div className="flex items-center justify-center">
                <Logo />
            </div>
            {["login", "register"].includes(page!) ? (
                <>
                    {" "}
                    <h2 className="text-3xl font-bold mt-4">Welcome to myNGO</h2>
                    <p className="text-neutral-500 mt-2">NGO Regulatory Authority Information System</p>
                </>
            ) : (
                ""
            )}
        </div>

        {/* Security notice */}
        {page !== "2fa" ? (
            <div className="bg-blue-50 dark:bg-neutral-900 border border-blue-200 dark:border-neutral-800 rounded-lg p-4 mb-8">
                <div className="flex">
                    <svg
                        className="h-5 w-5 text-blue-500 mt-0.5 mr-2"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                        xmlns="http://www.w3.org/2000/svg"
                    >
                        <path
                            clipRule="evenodd"
                            d="M2.166 4.999A11.954 11.954 0 0010 1.944 11.954 11.954 0 0017.834 5c.11.65.166 1.32.166 2.001 0 5.225-3.34 9.67-8 11.317C5.34 16.67 2 12.225 2 7c0-.682.057-1.35.166-2.001zm11.541 3.708a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                            fillRule="evenodd"
                        />
                    </svg>
                    <div>
                        <h3 className="text-sm font-medium text-blue-800 dark:text-white">Secure Access</h3>
                        <p className="text-xs text-blue-700 dark:text-white mt-0.5">
                            This is a secure government system. All activities are monitored and recorded.
                        </p>
                    </div>
                </div>
            </div>
        ) : (
            ""
        )}
    </div>
);

export const AuthFormFooter = ({ page }: { page: PageName }) => {
    const router = useRouter();

    return (
        <div>
            {/* Divider */}
            {page !== "password-reset" ? <Divider label="New to myNGO?" /> : ""}

            <div className="flex justify-center">
                {page == "login" ? (
                    <Link
                        className="flex items-center justify-center px-4 py-3 border border-gray-300 shadow-sm rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 w-full"
                        href="/auth/register"
                    >
                        Create a new account
                        <svg
                            className="ml-2 -mr-1 h-4 w-4"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                            xmlns="http://www.w3.org/2000/svg"
                        >
                            <path
                                d="M14 5l7 7m0 0l-7 7m7-7H3"
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                            />
                        </svg>
                    </Link>
                ) : (
                    ""
                )}

                {page == "register" ? (
                    <div className="flex justify-center">
                        <Button
                            className="text-muted-foreground hover:text-foreground"
                            type="button"
                            variant="ghost"
                            onClick={() => router.push("/auth/login")}
                        >
                            <ArrowLeft className="w-4 h-4 mr-2" />
                            Back to Login
                        </Button>
                    </div>
                ) : (
                    ""
                )}
            </div>

            {/* Help and support */}
            <div className="mt-8 text-center text-sm text-gray-500">
                <p>Need help? Contact our support team at:</p>
                <a className="font-medium text-blue-600 hover:text-blue-500" href="mailto:<EMAIL>">
                    <EMAIL>
                </a>
            </div>
        </div>
    );
};
