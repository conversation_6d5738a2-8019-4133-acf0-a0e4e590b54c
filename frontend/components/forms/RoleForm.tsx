// =============================================================================
// ROLE FORM COMPONENT
// =============================================================================

import { CheckIcon } from "lucide-react";
import { useState } from "react";

import { Alert } from "../ui.old/Alert";
import { Button } from "../ui.old/Button";
import { Card } from "../ui.old/Card";
import { Input } from "../ui.old/Input";
import { Label } from "../ui/label";

type Permissions = {
    id: string;
    code: string;
    display_value: string;
    description?: string;
    type?: string;
};

type Role = {
    id: string;
    code: string;
    name: string;
    description?: string;
    permissions: Permissions[];
    created_at?: string;
    updated_at?: string;
};

interface RoleFormProps {
    initialData?: Partial<Role>;
    availablePermissions?: Permissions[];
    isEdit?: boolean;
    loading?: boolean;
    error?: string;
    onSubmit?: (data: Omit<Role, "id" | "created_at" | "updated_at">) => Promise<void>;
    onCancel?: () => void;
}

export const RoleForm: React.FC<RoleFormProps> = ({
    initialData = {},
    availablePermissions = [],
    isEdit = false,
    loading = false,
    error,
    onSubmit,
    onCancel,
}) => {
    const [formData, setFormData] = useState({
        code: "",
        name: "",
        description: "",
        permissions: [] as Permissions[],
        ...initialData,
    });

    const [errors, setErrors] = useState<Record<string, string>>({});
    const [selectedPermissions, setSelectedPermissions] = useState<Set<string>>(
        new Set(formData.permissions.map((p) => p.id)),
    );

    // Group permissions by type
    const permissionGroups = availablePermissions.reduce(
        (groups, permission) => {
            const type = permission.type || "OTHER";

            if (!groups[type]) {
                groups[type] = [];
            }
            groups[type].push(permission);

            return groups;
        },
        {} as Record<string, Permissions[]>,
    );

    const validateForm = (): boolean => {
        const newErrors: Record<string, string> = {};

        if (!formData.code.trim()) newErrors.code = "Code is required";
        if (!formData.name.trim()) newErrors.name = "Name is required";

        // Code should be uppercase and contain only letters, numbers, and underscores
        if (formData.code && !/^[A-Z0-9_]+$/.test(formData.code)) {
            newErrors.code = "Code should contain only uppercase letters, numbers, and underscores";
        }

        setErrors(newErrors);

        return Object.keys(newErrors).length === 0;
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();

        if (!validateForm()) return;

        const selectedPerms = availablePermissions.filter((p) => selectedPermissions.has(p.id));

        try {
            await onSubmit?.({
                ...formData,
                permissions: selectedPerms,
            });
        } catch (err) {
            // Error handling done by parent
        }
    };

    const updateField = (field: string, value: any) => {
        setFormData((prev) => ({ ...prev, [field]: value }));
        if (errors[field]) {
            setErrors((prev) => ({ ...prev, [field]: "" }));
        }
    };

    const togglePermission = (permissionId: string) => {
        const newSelected = new Set(selectedPermissions);

        if (newSelected.has(permissionId)) {
            newSelected.delete(permissionId);
        } else {
            newSelected.add(permissionId);
        }
        setSelectedPermissions(newSelected);
    };

    const toggleAllInGroup = (groupPermissions: Permissions[]) => {
        const groupIds = groupPermissions.map((p) => p.id);
        const allSelected = groupIds.every((id) => selectedPermissions.has(id));

        const newSelected = new Set(selectedPermissions);

        if (allSelected) {
            groupIds.forEach((id) => newSelected.delete(id));
        } else {
            groupIds.forEach((id) => newSelected.add(id));
        }
        setSelectedPermissions(newSelected);
    };

    return (
        <Card title={isEdit ? "Edit Role" : "Create New Role"}>
            {error && <Alert dismissible className="mb-6" message={error} type="error" />}

            <form className="space-y-6" onSubmit={handleSubmit}>
                {/* Basic Information */}
                <div>
                    <h4 className="text-lg font-medium text-gray-900 mb-4">Basic Information</h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <Input
                            required
                            disabled={loading}
                            error={errors.code}
                            label="Role Code"
                            placeholder="ROLE_CODE"
                            value={formData.code}
                            onChange={(value: string) => updateField("code", value.toUpperCase())}
                        />
                        <Input
                            required
                            disabled={loading}
                            error={errors.name}
                            label="Role Name"
                            placeholder="Display name for the role"
                            value={formData.name}
                            onChange={(value: string) => updateField("name", value)}
                        />
                    </div>
                    <div className="mt-4">
                        <Label className="block text-sm font-medium text-gray-700 mb-1" htmlFor="description">
                            Description
                        </Label>
                        <textarea
                            className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                            disabled={loading}
                            name="description"
                            placeholder="Role description..."
                            rows={3}
                            value={formData.description}
                            onChange={(e) => updateField("description", e.target.value)}
                        />
                    </div>
                </div>

                {/* Permissions */}
                <div>
                    <h4 className="text-lg font-medium text-gray-900 mb-4">
                        Permissions ({selectedPermissions.size} selected)
                    </h4>

                    <div className="space-y-6">
                        {Object.entries(permissionGroups).map(([groupType, permissions]) => {
                            const allSelected = permissions.every((p) => selectedPermissions.has(p.id));
                            const someSelected = permissions.some((p) => selectedPermissions.has(p.id));

                            return (
                                <div key={groupType} className="border border-gray-200 rounded-lg p-4">
                                    <div className="flex items-center justify-between mb-3">
                                        <h5 className="text-sm font-medium text-gray-900">
                                            {groupType.replace("_", " ")} ({permissions.length})
                                        </h5>
                                        <button
                                            className={`text-sm font-medium ${
                                                allSelected
                                                    ? "text-red-600 hover:text-red-700"
                                                    : "text-blue-600 hover:text-blue-700"
                                            }`}
                                            type="button"
                                            onClick={() => toggleAllInGroup(permissions)}
                                        >
                                            {allSelected ? "Deselect All" : "Select All"}
                                        </button>
                                    </div>

                                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                                        {permissions.map((permission) => (
                                            <Button
                                                key={permission.id}
                                                className={`border rounded-lg p-3 cursor-pointer transition-colors ${
                                                    selectedPermissions.has(permission.id)
                                                        ? "border-blue-500 bg-blue-50"
                                                        : "border-gray-200 hover:border-gray-300"
                                                }`}
                                                onClick={() => togglePermission(permission.id)}
                                            >
                                                <div className="flex items-start">
                                                    <div
                                                        className={`flex-shrink-0 w-5 h-5 rounded border-2 flex items-center justify-center mr-3 mt-0.5 ${
                                                            selectedPermissions.has(permission.id)
                                                                ? "bg-blue-600 border-blue-600"
                                                                : "border-gray-300"
                                                        }`}
                                                    >
                                                        {selectedPermissions.has(permission.id) && (
                                                            <CheckIcon className="w-3 h-3 text-white" />
                                                        )}
                                                    </div>
                                                    <div className="flex-1">
                                                        <h6 className="text-sm font-medium text-gray-900">
                                                            {permission.display_value}
                                                        </h6>
                                                        <p className="text-xs text-gray-500 mt-1">{permission.code}</p>
                                                        {permission.description && (
                                                            <p className="text-xs text-gray-400 mt-1">
                                                                {permission.description}
                                                            </p>
                                                        )}
                                                    </div>
                                                </div>
                                            </Button>
                                        ))}
                                    </div>
                                </div>
                            );
                        })}
                    </div>
                </div>

                {/* Form Actions */}
                <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
                    <Button disabled={loading} type="button" variant="outline" onClick={onCancel}>
                        Cancel
                    </Button>
                    <Button disabled={loading} loading={loading} type="submit" variant="primary">
                        {isEdit ? "Update Role" : "Create Role"}
                    </Button>
                </div>
            </form>
        </Card>
    );
};
