// =============================================================================
// LOADING SKELETON COMPONENT
// =============================================================================

interface LoadingSkeletonProps {
    rows?: number;
    columns?: number;
    className?: string;
}

export const LoadingSkeleton: React.FC<LoadingSkeletonProps> = ({ rows = 5, columns = 4, className = "" }) => {
    return (
        <div className={`animate-pulse ${className}`}>
            <div className="space-y-3">
                {/* Header */}
                <div className="grid grid-cols-4 gap-4">
                    {Array.from({ length: columns }).map((_, i) => (
                        <div key={i} className="h-4 bg-gray-200 rounded" />
                    ))}
                </div>

                {/* Rows */}
                {Array.from({ length: rows }).map((_, rowIndex) => (
                    <div key={rowIndex} className="grid grid-cols-4 gap-4">
                        {Array.from({ length: columns }).map((_, colIndex) => (
                            <div key={colIndex} className="h-4 bg-gray-200 rounded" />
                        ))}
                    </div>
                ))}
            </div>
        </div>
    );
};
