// =============================================================================
// SEARCH AND FILTER BAR
// =============================================================================

import { useState } from "react";

import { Button } from "../ui.old/Button";
import { Card } from "../ui.old/Card";
import { Input } from "../ui.old/Input";
import { Select } from "../ui.old/Select";

interface FilterOption {
    key: string;
    label: string;
    type: "select" | "input" | "date";
    options?: Array<{ value: string; label: string }>;
    placeholder?: string;
}

interface SearchFilterBarProps {
    searchPlaceholder?: string;
    filters?: FilterOption[];
    onSearch?: (term: string) => void;
    onFilter?: (filters: Record<string, string>) => void;
    onClear?: () => void;
    showExport?: boolean;
    onExport?: () => void;
    className?: string;
}

export const SearchFilterBar: React.FC<SearchFilterBarProps> = ({
    searchPlaceholder = "Search...",
    filters = [],
    onSearch,
    onFilter,
    onClear,
    showExport = false,
    onExport,
    className = "",
}) => {
    const [searchTerm, setSearchTerm] = useState("");
    const [filterValues, setFilterValues] = useState<Record<string, string>>({});

    const handleSearchChange = (value: string) => {
        setSearchTerm(value);
        onSearch?.(value);
    };

    const handleFilterChange = (key: string, value: string) => {
        const newFilters = { ...filterValues, [key]: value };

        setFilterValues(newFilters);
        onFilter?.(newFilters);
    };

    const handleClear = () => {
        setSearchTerm("");
        setFilterValues({});
        onClear?.();
    };

    return (
        <Card className={className}>
            <div className="grid grid-cols-1 lg:grid-cols-12 gap-4 items-end">
                {/* Search Input */}
                <div className="lg:col-span-4">
                    <Input placeholder={searchPlaceholder} value={searchTerm} onChange={handleSearchChange} />
                </div>

                {/* Filter Inputs */}
                {filters.map((filter) => (
                    <div key={filter.key} className="lg:col-span-2">
                        {filter.type === "select" ? (
                            <Select
                                options={filter.options || []}
                                placeholder={filter.placeholder}
                                value={filterValues[filter.key] || ""}
                                onChange={(value) => handleFilterChange(filter.key, value)}
                            />
                        ) : (
                            <Input
                                placeholder={filter.placeholder}
                                //type={filter.type === 'date' ? 'date' : 'text'}
                                value={filterValues[filter.key] || ""}
                                onChange={(value) => handleFilterChange(filter.key, value)}
                            />
                        )}
                    </div>
                ))}

                {/* Action Buttons */}
                <div className="lg:col-span-2 flex space-x-2">
                    <Button size="sm" variant="outline" onClick={handleClear}>
                        Clear
                    </Button>
                    {showExport && (
                        <Button size="sm" variant="secondary" onClick={onExport}>
                            Export
                        </Button>
                    )}
                </div>
            </div>
        </Card>
    );
};
