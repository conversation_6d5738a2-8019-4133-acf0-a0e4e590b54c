"use client";

import React, { useEffect, useState } from "react";
import { Setting<PERSON>, Trash2Icon } from "lucide-react";

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Select } from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { SystemConfigurationData } from "@/hooks/use-system-configuration";

interface NGOSettingsFormProps {
    initialData: SystemConfigurationData;
    loading?: boolean;
    onSubmit?: (data: any) => Promise<void>;
    onCancel?: () => void;
}

const statusOptions = [
    { value: "Approved", label: "Approved" },
    { value: "Pending", label: "Pending" },
    { value: "Rejected", label: "Rejected" },
];

const monthOptions = [
    { value: "January", label: "January" },
    { value: "February", label: "February" },
    { value: "March", label: "March" },
    { value: "April", label: "April" },
    { value: "May", label: "May" },
    { value: "June", label: "June" },
    { value: "July", label: "July" },
    { value: "August", label: "August" },
    { value: "September", label: "September" },
    { value: "October", label: "October" },
    { value: "November", label: "November" },
    { value: "December", label: "December" },
];

export const NGOSettingsForm: React.FC<NGOSettingsFormProps> = ({
    initialData,
    loading = false,
    onSubmit,
    onCancel,
}) => {
    const [formData, setFormData] = useState<Record<string, any>>({
        max_users_per_ngo: 3,
        reg_no_prefix: "",
        org_disable_duration: 3,
        accept_as_submitted_status: "Approved",
        submission_timeliness_deadline: "June",
        submission_window_start: null,
        submission_window_end: null,
    });
    // Reg. No. Format dynamic fields
    const [regNoFormat, setRegNoFormat] = useState<
        {
            field: string;
            chars: string | number;
            position: number;
        }[]
    >([
        { field: "Organization", chars: 1, position: 1 },
        { field: "Registration", chars: 2, position: 2 },
        { field: "Serial Number", chars: 3, position: 3 },
    ]);
    const [errors, setErrors] = useState<Record<string, string>>({});
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [positionsChanged, setPositionsChanged] = useState(false);
    const [positionError, setPositionError] = useState<string | null>(null);

    useEffect(() => {
        if (!initialData || typeof initialData !== "object") return;
        const props: Record<string, any> =
            initialData.properties && typeof initialData.properties === "object"
                ? (initialData.properties as Record<string, any>)
                : (initialData as Record<string, any>);

        if (!props || Object.keys(props).length === 0) return;
        setFormData(props);
        // Load regNoFormat from flat keys if present
        const loaded: typeof regNoFormat = [];

        Object.keys(props).forEach((key) => {
            if (key.startsWith("reg_no_format_field_")) {
                const pos = parseInt(key.split("_").pop() || "0", 10) - 1;
                const field = props[key];
                const charsKey = `reg_no_format_chars_${pos + 1}`;
                const positionKey = `reg_no_format_position_${pos + 1}`;

                loaded.push({
                    field,
                    chars: props[charsKey] || "",
                    position: props[positionKey] || pos + 1,
                });
            }
        });
        if (loaded.length > 0) setRegNoFormat(loaded.sort((a, b) => a.position - b.position));
    }, [initialData]);

    const handleInputChange = (field: string, value: any) => {
        setFormData((prev) => ({ ...prev, [field]: value }));
        if (errors[field]) setErrors((prev) => ({ ...prev, [field]: "" }));
    };

    const handleFormatFieldChange = (idx: number, value: string) => {
        setRegNoFormat((prev) => prev.map((item, i) => (i === idx ? { ...item, field: value } : item)));
    };
    const handleFormatCharsChange = (idx: number, value: string | number) => {
        setRegNoFormat((prev) => prev.map((item, i) => (i === idx ? { ...item, chars: value } : item)));
    };
    const handleFormatPositionChange = (idx: number, value: number) => {
        setRegNoFormat((prev) => {
            const updated = prev.map((item, i) => (i === idx ? { ...item, position: value } : item));

            setPositionsChanged(true);

            return updated;
        });
    };
    const handleAddFormat = () => {
        setRegNoFormat((prev) => [...prev, { field: "", chars: 1, position: prev.length + 1 }]);
        setPositionsChanged(true);
    };
    const handleRemoveFormat = (idx: number) => {
        setRegNoFormat((prev) => prev.filter((_, i) => i !== idx));
        setPositionsChanged(true);
    };

    const handleApplyPositions = () => {
        setPositionError(null);
        // Check for duplicate positions
        const positions = regNoFormat.map((item) => item.position);
        const hasDuplicates = new Set(positions).size !== positions.length;

        const fields = regNoFormat.map((item) => item.field);

        const duplicateField = new Set(fields).size !== fields.length;

        if (hasDuplicates) {
            setPositionError("Positions must be unique.");

            return;
        }

        if (duplicateField) {
            setPositionError("NGO Fields must be unique.");

            return;
        }

        // Sort by position
        setRegNoFormat((prev) => [...prev].sort((a, b) => a.position - b.position));
        setPositionsChanged(false);
    };

    const validateForm = (): boolean => {
        const newErrors: Record<string, string> = {};

        if (!formData.max_users_per_ngo) newErrors.max_users_per_ngo = "Required";
        if (!formData.reg_no_prefix) newErrors.reg_no_prefix = "Required";
        // ... add more validation as needed
        setErrors(newErrors);

        return Object.keys(newErrors).length === 0;
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        if (!validateForm()) return;
        setIsSubmitting(true);
        // Flatten regNoFormat into formData keys
        const regNoFormatFlat: Record<string, string | number> = {};

        regNoFormat.forEach((item, idx) => {
            const pos = idx + 1;

            regNoFormatFlat[`reg_no_format_field_${pos}`] = item.field;
            regNoFormatFlat[`reg_no_format_chars_${pos}`] = item.chars;
            regNoFormatFlat[`reg_no_format_position_${pos}`] = item.position;
        });
        const submitData = { ...formData, ...regNoFormatFlat };

        await onSubmit?.({ group: "ngo", properties: submitData });
        setIsSubmitting(false);
    };

    return (
        <div className="max-w-full mx-auto py-6">
            <div className="flex items-center gap-3 mb-6">
                <div className="p-2 bg-primary/10 rounded-lg">
                    <Settings className="h-6 w-6 text-primary" />
                </div>
                <div>
                    <h1 className="text-2xl font-bold">NGO Settings</h1>
                    <p className="text-muted-foreground">Configure your NGO settings and preferences</p>
                </div>
            </div>
            <form className="space-y-6" onSubmit={handleSubmit}>
                <Card>
                    <CardHeader>
                        <CardTitle>NGOs</CardTitle>
                        <CardDescription>NGO registration and user settings</CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-4">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <Input
                                    disabled={loading || isSubmitting}
                                    error={errors.max_users_per_ngo}
                                    label="Maximum Number of Users Per NGO *"
                                    type="number"
                                    value={formData.max_users_per_ngo}
                                    onChange={(e) => handleInputChange("max_users_per_ngo", e.target.value)}
                                />
                            </div>
                            <div>
                                <Input
                                    disabled={loading || isSubmitting}
                                    error={errors.reg_no_prefix}
                                    label="Reg. No. Prefix *"
                                    value={formData.reg_no_prefix}
                                    onChange={(e) => handleInputChange("reg_no_prefix", e.target.value)}
                                />
                            </div>
                        </div>
                    </CardContent>
                </Card>
                <Card>
                    <CardHeader>
                        <CardTitle>NGO Registration Number Format *</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="flex flex-col justify-between gap-2 mt-2">
                            {regNoFormat.map((item, idx) => (
                                <div key={idx} className="flex gap-2 items-end">
                                    <Select
                                        containerClassName="w-full"
                                        disabled={loading || isSubmitting}
                                        label={idx === 0 ? "NGO Field" : undefined}
                                        options={[
                                            { value: "", label: "---" },
                                            { value: "Organization", label: "Organization" },
                                            { value: "Registration", label: "Registration" },
                                            { value: "Serial Number", label: "Serial Number" },
                                            { value: "Organization Type", label: "Organization Type" },
                                            { value: "Registration Year", label: "Registration Year" },
                                        ]}
                                        value={item.field}
                                        onValueChange={(value) => handleFormatFieldChange(idx, value)}
                                    />
                                    <Input
                                        className="w-full"
                                        disabled={loading || isSubmitting}
                                        label={idx === 0 ? "No. of Characters" : undefined}
                                        type="number"
                                        value={item.chars}
                                        onChange={(e) => handleFormatCharsChange(idx, e.target.value)}
                                    />
                                    <Input
                                        className="w-full"
                                        disabled={loading || isSubmitting}
                                        label={idx === 0 ? "Position" : undefined}
                                        min={1}
                                        type="number"
                                        value={item.position}
                                        onChange={(e) => handleFormatPositionChange(idx, Number(e.target.value))}
                                    />
                                    <Button
                                        className="flex justify-end"
                                        disabled={loading || isSubmitting || regNoFormat.length === 1}
                                        type="button"
                                        variant="destructive"
                                        onClick={() => handleRemoveFormat(idx)}
                                    >
                                        <Trash2Icon />
                                    </Button>
                                </div>
                            ))}
                            {positionError && <div className="text-red-600 text-sm mt-1">{positionError}</div>}
                            {positionsChanged && (
                                <Button
                                    className="mt-2 w-fit"
                                    type="button"
                                    variant="secondary"
                                    onClick={handleApplyPositions}
                                >
                                    Apply Positions
                                </Button>
                            )}
                            <Button
                                className="mt-2 w-fit"
                                disabled={loading || isSubmitting}
                                type="button"
                                variant="outline"
                                onClick={handleAddFormat}
                            >
                                Add NGO Field
                            </Button>
                        </div>
                    </CardContent>
                </Card>
                <Card>
                    <CardHeader>
                        <CardTitle>Annual Returns</CardTitle>
                        <CardDescription>Annual return and compliance settings</CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-4">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <Input
                                    disabled={loading || isSubmitting}
                                    error={errors.org_disable_duration}
                                    label="Organization Disable Duration *"
                                    type="number"
                                    value={formData.org_disable_duration}
                                    onChange={(e) => handleInputChange("org_disable_duration", e.target.value)}
                                />
                            </div>
                            <div>
                                <Select
                                    disabled={loading || isSubmitting}
                                    error={errors.accept_as_submitted_status}
                                    label="Accept as Submitted When In *"
                                    options={statusOptions}
                                    value={formData.accept_as_submitted_status}
                                    onValueChange={(value) => handleInputChange("accept_as_submitted_status", value)}
                                />
                            </div>
                        </div>
                        <div className="grid grid-cols-1 md:grid-cols-1 gap-4">
                            <div>
                                <Select
                                    disabled={loading || isSubmitting}
                                    error={errors.submission_timeliness_deadline}
                                    label="Submission Timeliness Deadline *"
                                    options={monthOptions}
                                    value={formData.submission_timeliness_deadline}
                                    onValueChange={(value) =>
                                        handleInputChange("submission_timeliness_deadline", value)
                                    }
                                />
                            </div>
                        </div>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <Label>Submission Window Start *</Label>
                                <Input
                                    disabled={loading || isSubmitting}
                                    type="date"
                                    value={formData.submission_window_start || ""}
                                    onChange={(e) => handleInputChange("submission_window_start", e.target.value)}
                                />
                            </div>
                            <div>
                                <Label>Submission Window End *</Label>
                                <Input
                                    disabled={loading || isSubmitting}
                                    type="date"
                                    value={formData.submission_window_end || ""}
                                    onChange={(e) => handleInputChange("submission_window_end", e.target.value)}
                                />
                            </div>
                        </div>
                    </CardContent>
                </Card>
                <div className="flex justify-end gap-3 pt-6">
                    {onCancel && (
                        <Button disabled={loading || isSubmitting} type="button" variant="outline" onClick={onCancel}>
                            Cancel
                        </Button>
                    )}
                    <Button className="min-w-32" disabled={loading} loading={isSubmitting} type="submit">
                        Save Settings
                    </Button>
                </div>
            </form>
        </div>
    );
};
