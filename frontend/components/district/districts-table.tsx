import { ColumnDef } from "@tanstack/react-table";
import { MoreHorizontal, Pencil, Plus, Trash2 } from "lucide-react";
import { useState } from "react";

import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { DataTable } from "@/components/ui/data-table";
import { Input } from "@/components/ui/input";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { DistrictDto } from "@/types";

interface DistrictsTableProps {
    districts: DistrictDto[];
    loading: boolean;
    onEdit: (district: DistrictDto) => void;
    onDelete: (district: DistrictDto) => void;
    onCreate: () => void;
}

export function DistrictsTable({ districts, loading, onEdit, onDelete, onCreate }: DistrictsTableProps) {
    const [search, setSearch] = useState("");

    const filteredDistricts = districts.filter(
        (district) =>
            district.name.toLowerCase().includes(search.toLowerCase()) ||
            district.code.toLowerCase().includes(search.toLowerCase()) ||
            district.region_name?.toLowerCase().includes(search.toLowerCase()),
    );

    const columns: ColumnDef<DistrictDto>[] = [
        {
            accessorKey: "name",
            header: "District",
            cell: ({ row }) => (
                <div>
                    <div className="font-medium text-foreground">{row.original.name}</div>
                    <div className="text-muted-foreground text-xs font-mono">{row.original.code}</div>
                </div>
            ),
        },
        {
            accessorKey: "region_name",
            header: "Region",
            cell: ({ row }) => <div className="text-sm">{row.original.region_name || "—"}</div>,
        },
        {
            accessorKey: "created_at",
            header: "Created",
            cell: ({ row }) => (
                <div className="text-sm text-muted-foreground">
                    {row.original.created_at ? new Date(row.original.created_at).toLocaleDateString() : "—"}
                </div>
            ),
        },
        {
            id: "actions",
            header: "Actions",
            cell: ({ row }) => (
                <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                        <Button className="h-8 w-8 p-0" size="icon-sm" variant="ghost">
                            <span className="sr-only">Open menu</span>
                            <MoreHorizontal className="h-4 w-4" />
                        </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => onEdit(row.original)}>
                            <Pencil className="mr-2 h-4 w-4" /> Edit
                        </DropdownMenuItem>
                        <DropdownMenuItem className="text-red-600" onClick={() => onDelete(row.original)}>
                            <Trash2 className="mr-2 h-4 w-4" /> Delete
                        </DropdownMenuItem>
                    </DropdownMenuContent>
                </DropdownMenu>
            ),
        },
    ];

    return (
        <>
            <div className="flex items-center justify-between py-6">
                <div>
                    <CardTitle className="text-2xl">Districts</CardTitle>
                    <p className="text-muted-foreground">Manage districts and their associated regions</p>
                </div>
                <Button onClick={onCreate}>
                    <Plus className="mr-2 h-4 w-4" /> Create District
                </Button>
            </div>
            <Card>
                <CardHeader />
                <CardContent>
                    <Input
                        className="mb-4 max-w-sm"
                        placeholder="Search districts..."
                        value={search}
                        onChange={(e) => setSearch(e.target.value)}
                    />
                    <DataTable
                        columns={columns}
                        data={filteredDistricts}
                        enableFiltering={false}
                        enablePagination={true}
                        enableSorting={true}
                        pageSize={10}
                    />
                </CardContent>
            </Card>
        </>
    );
}
