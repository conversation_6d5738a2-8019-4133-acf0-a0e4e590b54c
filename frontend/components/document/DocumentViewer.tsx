"use client";
import { BukaViewer } from "@tumbati/bukajs";

import "@tumbati/bukajs/renderers/docx";
import "@tumbati/bukajs/renderers/pdf";

import { useEffect, useRef } from "react";

export type DocumentViewerProps = {
    url: string | File | Blob;
    height?: string;
    width?: string;
};

export const DocumentViewer = ({ url, width = "100%", height = "100%" }: DocumentViewerProps) => {
    const containerRef = useRef(null);
    const viewerRef = useRef<BukaViewer | null>(null);

    useEffect(() => {
        if (containerRef.current) {
            viewerRef.current = new BukaViewer(containerRef.current);
        }

        return () => viewerRef.current?.destroy();
    }, []);

    useEffect(() => {
        if (url && viewerRef.current) {
            viewerRef.current.load(url);
        }
    }, [url]);

    return <div ref={containerRef} style={{ width, height }} />;
};
