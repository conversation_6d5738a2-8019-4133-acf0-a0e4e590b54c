"use client";

import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { AlertTriangle, CheckCircle, Clock, Download, Eye, FileText } from "lucide-react";

import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { cn } from "@/lib/utils";
import { fetchFinanceWorkflowStages, fetchInvoices } from "@/services/FinancialService";
import { approveWorkflowStage } from "@/services/WorkflowService";
import { FinanceWorkflowStage, InvoiceDto } from "@/types";

export function FinanceWorkflowDashboard() {
    const queryClient = useQueryClient();

    const { data: workflowStages, isLoading: workflowLoading } = useQuery({
        queryKey: ["finance-workflow-stages"],
        queryFn: () => fetchFinanceWorkflowStages({ page: 1, size: 10, role_code: "FINANCE_OFFICER" }),
        refetchInterval: 30000,
    });

    const { data: invoicesData, isLoading: invoicesLoading } = useQuery({
        queryKey: ["invoices", { limit: 10 }],
        queryFn: () => fetchInvoices({ size: 10 }),
        refetchInterval: 60000,
    });

    const approveStagesMutation = useMutation({
        mutationFn: ({ workflowId, stageId }: { workflowId: string; stageId: string }) =>
            approveWorkflowStage(workflowId, stageId),
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ["finance-workflow-stages"] });
        },
    });

    const stages = workflowStages?.data || [];
    const invoices = invoicesData?.data || invoicesData?.data || [];

    const pendingStages = stages.filter((stage) => stage.status === "PENDING");
    const completedStages = stages.filter((stage) => stage.status === "COMPLETED" || stage.status === "APPROVED");
    const rejectedStages = stages.filter((stage) => stage.status === "REJECTED");

    const pendingInvoices = invoices.filter((inv: InvoiceDto) => inv.status === "PENDING");
    const paidInvoices = invoices.filter((inv: InvoiceDto) => inv.status === "PAID");
    const overdueInvoices = invoices.filter((inv: InvoiceDto) => inv.status === "OVERDUE");

    const getStatusColor = (status: string) => {
        switch (status) {
            case "PENDING":
                return "text-yellow-600 bg-yellow-100";
            case "COMPLETED":
            case "APPROVED":
                return "text-green-600 bg-green-100";
            case "REJECTED":
                return "text-red-600 bg-red-100";
            case "IN_REVIEW":
                return "text-blue-600 bg-blue-100";
            default:
                return "text-gray-600 bg-gray-100";
        }
    };

    const getStatusIcon = (status: string) => {
        switch (status) {
            case "PENDING":
                return <Clock className="h-4 w-4" />;
            case "COMPLETED":
            case "APPROVED":
                return <CheckCircle className="h-4 w-4" />;
            case "REJECTED":
                return <AlertTriangle className="h-4 w-4" />;
            default:
                return <FileText className="h-4 w-4" />;
        }
    };

    const handleApproveStage = (stage: FinanceWorkflowStage) => {
        approveStagesMutation.mutate({
            workflowId: stage.workflow_id,
            stageId: stage.id,
        });
    };

    return (
        <div className="space-y-6">
            {/* Statistics Cards */}
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Pending Reviews</CardTitle>
                        <Clock className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold text-yellow-600">{pendingStages.length}</div>
                        <p className="text-xs text-muted-foreground">Workflow stages awaiting finance approval</p>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Approved This Month</CardTitle>
                        <CheckCircle className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold text-green-600">{completedStages.length}</div>
                        <p className="text-xs text-muted-foreground">Completed finance reviews</p>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Pending Invoices</CardTitle>
                        <FileText className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold text-blue-600">{pendingInvoices.length}</div>
                        <p className="text-xs text-muted-foreground">Awaiting payment verification</p>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Overdue Payments</CardTitle>
                        <AlertTriangle className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold text-red-600">{overdueInvoices.length}</div>
                        <p className="text-xs text-muted-foreground">Require immediate attention</p>
                    </CardContent>
                </Card>
            </div>

            <div className="grid gap-6 md:grid-cols-2">
                {/* Workflow Stages for Finance Review */}
                <Card>
                    <CardHeader>
                        <CardTitle>Finance Workflow Stages</CardTitle>
                        <p className="text-sm text-muted-foreground">
                            Applications requiring finance review and payment verification
                        </p>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-4">
                            {workflowLoading ? (
                                <div className="text-center py-4">Loading...</div>
                            ) : stages.length === 0 ? (
                                <div className="text-center py-8 text-muted-foreground">
                                    <Clock className="h-12 w-12 mx-auto mb-4 opacity-50" />
                                    <p>No finance stages pending review</p>
                                </div>
                            ) : (
                                stages
                                    .slice(0, 5)
                                    .map((stage) => (
                                        <WorkflowStageCard
                                            key={stage.id}
                                            isApproving={approveStagesMutation.isPending}
                                            stage={stage}
                                            onApprove={handleApproveStage}
                                        />
                                    ))
                            )}
                            {stages.length > 5 && (
                                <div className="text-center pt-4 border-t">
                                    <Button asChild size="sm" variant="outline">
                                        <a href="/workflows?assigned_to_me=true">View All ({stages.length} total)</a>
                                    </Button>
                                </div>
                            )}
                        </div>
                    </CardContent>
                </Card>

                {/* Recent Invoices */}
                <Card>
                    <CardHeader>
                        <CardTitle>Recent Invoices</CardTitle>
                        <p className="text-sm text-muted-foreground">Latest invoices and payment status</p>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-4">
                            {invoicesLoading ? (
                                <div className="text-center py-4">Loading...</div>
                            ) : invoices.length === 0 ? (
                                <div className="text-center py-8 text-muted-foreground">
                                    <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
                                    <p>No invoices found</p>
                                </div>
                            ) : (
                                invoices
                                    .slice(0, 5)
                                    .map((invoice: InvoiceDto) => <InvoiceCard key={invoice.id} invoice={invoice} />)
                            )}
                            {invoices.length > 5 && (
                                <div className="text-center pt-4 border-t">
                                    <Button asChild size="sm" variant="outline">
                                        <a href="/financials/invoices">View All Invoices</a>
                                    </Button>
                                </div>
                            )}
                        </div>
                    </CardContent>
                </Card>
            </div>
        </div>
    );
}

interface WorkflowStageCardProps {
    stage: FinanceWorkflowStage;
    onApprove: (stage: FinanceWorkflowStage) => void;
    isApproving: boolean;
}

function WorkflowStageCard({ stage, onApprove, isApproving }: WorkflowStageCardProps) {
    const getStatusColor = (status: string) => {
        switch (status) {
            case "PENDING":
                return "text-yellow-600 bg-yellow-100";
            case "COMPLETED":
            case "APPROVED":
                return "text-green-600 bg-green-100";
            case "REJECTED":
                return "text-red-600 bg-red-100";
            case "IN_REVIEW":
                return "text-blue-600 bg-blue-100";
            default:
                return "text-gray-600 bg-gray-100";
        }
    };

    const getStatusIcon = (status: string) => {
        switch (status) {
            case "PENDING":
                return <Clock className="h-4 w-4" />;
            case "COMPLETED":
            case "APPROVED":
                return <CheckCircle className="h-4 w-4" />;
            case "REJECTED":
                return <AlertTriangle className="h-4 w-4" />;
            default:
                return <FileText className="h-4 w-4" />;
        }
    };

    return (
        <div className="flex items-start justify-between p-4 border rounded-lg">
            <div className="flex items-start gap-3">
                {getStatusIcon(stage.status)}
                <div className="space-y-1">
                    <div className="flex items-center gap-2">
                        <h4 className="font-medium text-sm">{stage.application.organization_name}</h4>
                        <Badge className={cn("text-xs", getStatusColor(stage.status))}>{stage.status}</Badge>
                    </div>
                    <p className="text-xs text-muted-foreground">
                        {stage.template_stage.name} • {stage.application.type}
                    </p>
                    {stage.invoice && (
                        <p className="text-xs text-muted-foreground">
                            Invoice: {stage.invoice.reference_number} • MWK{" "}
                            {stage.invoice.total_amount.toLocaleString()}
                        </p>
                    )}
                    {stage.proof_of_payment_document && (
                        <p className="text-xs text-green-600">
                            ✓ Proof of payment submitted: {stage.proof_of_payment_document.original_name}
                        </p>
                    )}
                </div>
            </div>
            <div className="flex items-center gap-1">
                <Button size="sm" variant="outline">
                    <Eye className="h-3 w-3 mr-1" />
                    View
                </Button>
                {stage.status === "PENDING" && (
                    <Button disabled={isApproving} size="sm" onClick={() => onApprove(stage)}>
                        <CheckCircle className="h-3 w-3 mr-1" />
                        Approve
                    </Button>
                )}
            </div>
        </div>
    );
}

interface InvoiceCardProps {
    invoice: InvoiceDto;
}

function InvoiceCard({ invoice }: InvoiceCardProps) {
    const getStatusColor = (status: string) => {
        switch (status) {
            case "PAID":
                return "text-green-600 bg-green-100";
            case "PENDING":
                return "text-blue-600 bg-blue-100";
            case "OVERDUE":
                return "text-red-600 bg-red-100";
            case "PARTIALLY_PAID":
                return "text-yellow-600 bg-yellow-100";
            default:
                return "text-gray-600 bg-gray-100";
        }
    };

    return (
        <div className="flex items-start justify-between p-4 border rounded-lg">
            <div className="space-y-1">
                <div className="flex items-center gap-2">
                    <h4 className="font-medium text-sm">{invoice.reference_number}</h4>
                    <Badge className={cn("text-xs", getStatusColor(invoice.status))}>{invoice.status}</Badge>
                </div>
                <p className="text-xs text-muted-foreground">
                    {invoice.organization?.name} • Due: {new Date(invoice.due_date).toLocaleDateString()}
                </p>
                <p className="text-sm font-medium">MWK {invoice.total_amount.toLocaleString()}</p>
            </div>
            <div className="flex items-center gap-1">
                <Button size="sm" variant="outline">
                    <Eye className="h-3 w-3 mr-1" />
                    View
                </Button>
                <Button size="sm" variant="outline">
                    <Download className="h-3 w-3 mr-1" />
                    PDF
                </Button>
            </div>
        </div>
    );
}
