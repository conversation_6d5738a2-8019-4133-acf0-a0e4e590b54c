"use client";

import { useQuery } from "@tanstack/react-query";
import { useState } from "react";
import {
    AlertTriangle,
    ArrowUpRight,
    ArrowDownRight,
    Search,
    Filter,
    RefreshCw,
    Download,
    Eye,
    CheckCircle,
    XCircle,
} from "lucide-react";

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Skeleton } from "@/components/ui/skeleton";
import { cn } from "@/lib/utils";
import { fetchPaymentReconciliationSummary, PaymentDiscrepancy } from "@/services/FinancialService";

export function PaymentDiscrepancies() {
    const [searchTerm, setSearchTerm] = useState("");
    const [selectedOrganization, setSelectedOrganization] = useState<string>("");

    const {
        data: reconciliationData,
        isLoading,
        isError,
        refetch,
    } = useQuery({
        queryKey: ["payment-reconciliation-summary", selectedOrganization],
        queryFn: () => fetchPaymentReconciliationSummary(selectedOrganization || undefined),
        refetchInterval: 300000, // Refresh every 5 minutes
    });

    const summary = reconciliationData?.data;
    const discrepancies = summary?.discrepancies || [];

    const filteredDiscrepancies = discrepancies.filter(
        (discrepancy) =>
            !searchTerm ||
            discrepancy.organization_id.toLowerCase().includes(searchTerm.toLowerCase()) ||
            discrepancy.application_id.toLowerCase().includes(searchTerm.toLowerCase()) ||
            discrepancy.payment_id.toLowerCase().includes(searchTerm.toLowerCase()),
    );

    if (isLoading) {
        return <DiscrepanciesSkeleton />;
    }

    if (isError) {
        return (
            <Card className="p-6">
                <div className="text-center">
                    <AlertTriangle className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <h3 className="text-lg font-semibold mb-2">Failed to load reconciliation data</h3>
                    <p className="text-muted-foreground mb-4">
                        There was an error loading the payment reconciliation data.
                    </p>
                    <Button onClick={() => refetch()}>
                        <RefreshCw className="h-4 w-4 mr-2" />
                        Retry
                    </Button>
                </div>
            </Card>
        );
    }

    return (
        <div className="space-y-6">
            {/* Summary Cards */}
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Total Discrepancies</CardTitle>
                        <AlertTriangle className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">{summary?.total_discrepancies || 0}</div>
                        <p className="text-xs text-muted-foreground">Require attention</p>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Overpayments</CardTitle>
                        <ArrowUpRight className="h-4 w-4 text-green-600" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold text-green-600">{summary?.overpayments_count || 0}</div>
                        <p className="text-xs text-muted-foreground">
                            MWK {summary?.total_overpayment_amount?.toLocaleString() || "0"}
                        </p>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Underpayments</CardTitle>
                        <ArrowDownRight className="h-4 w-4 text-red-600" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold text-red-600">{summary?.underpayments_count || 0}</div>
                        <p className="text-xs text-muted-foreground">
                            MWK {summary?.total_underpayment_amount?.toLocaleString() || "0"}
                        </p>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Immediate Attention</CardTitle>
                        <XCircle className="h-4 w-4 text-red-600" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold text-red-600">
                            {summary?.requires_immediate_attention || 0}
                        </div>
                        <p className="text-xs text-muted-foreground">High priority items</p>
                    </CardContent>
                </Card>
            </div>

            {/* Filters and Controls */}
            <Card>
                <CardHeader>
                    <div className="flex items-center justify-between">
                        <div>
                            <CardTitle>Payment Discrepancies</CardTitle>
                            <CardDescription>
                                Review and manage payment discrepancies requiring attention
                            </CardDescription>
                        </div>
                        <div className="flex items-center gap-2">
                            <Button size="sm" variant="outline">
                                <Download className="h-4 w-4 mr-2" />
                                Export
                            </Button>
                            <Button size="sm" variant="outline" onClick={() => refetch()}>
                                <RefreshCw className="h-4 w-4 mr-2" />
                                Refresh
                            </Button>
                        </div>
                    </div>
                </CardHeader>
                <CardContent className="space-y-4">
                    {/* Search and Filter Controls */}
                    <div className="flex items-center gap-4">
                        <div className="relative flex-1">
                            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                            <Input
                                className="pl-9"
                                placeholder="Search by organization, application, or payment ID..."
                                value={searchTerm}
                                onChange={(e) => setSearchTerm(e.target.value)}
                            />
                        </div>
                        <Button size="sm" variant="outline">
                            <Filter className="h-4 w-4 mr-2" />
                            Filters
                        </Button>
                    </div>

                    {/* Discrepancies Table */}
                    <div className="space-y-2">
                        {filteredDiscrepancies.length === 0 ? (
                            <div className="text-center py-8">
                                <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-4" />
                                <h3 className="text-lg font-semibold mb-2">No Discrepancies Found</h3>
                                <p className="text-muted-foreground">
                                    {searchTerm
                                        ? "No discrepancies match your search criteria."
                                        : "All payments are properly reconciled."}
                                </p>
                            </div>
                        ) : (
                            filteredDiscrepancies.map((discrepancy) => (
                                <DiscrepancyCard key={discrepancy.payment_id} discrepancy={discrepancy} />
                            ))
                        )}
                    </div>
                </CardContent>
            </Card>
        </div>
    );
}

interface DiscrepancyCardProps {
    discrepancy: PaymentDiscrepancy;
}

function DiscrepancyCard({ discrepancy }: DiscrepancyCardProps) {
    const isOverpaid = discrepancy.discrepancy_type === "overpaid";
    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString();
    };

    return (
        <Card
            className={cn(
                "border-l-4",
                isOverpaid ? "border-l-green-500" : "border-l-red-500",
                discrepancy.requires_follow_up && "ring-2 ring-yellow-200",
            )}
        >
            <CardContent className="p-4">
                <div className="flex items-start justify-between">
                    <div className="space-y-1">
                        <div className="flex items-center gap-2">
                            <Badge className="text-xs" variant={isOverpaid ? "default" : "destructive"}>
                                {isOverpaid ? "Overpaid" : "Underpaid"}
                            </Badge>
                            {discrepancy.requires_follow_up && (
                                <Badge className="text-xs" variant="outline">
                                    Follow-up Required
                                </Badge>
                            )}
                        </div>

                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                            <div>
                                <p className="text-muted-foreground">Payment ID</p>
                                <p className="font-medium">{discrepancy.payment_id}</p>
                            </div>
                            <div>
                                <p className="text-muted-foreground">Organization</p>
                                <p className="font-medium">{discrepancy.organization_id}</p>
                            </div>
                            <div>
                                <p className="text-muted-foreground">Application</p>
                                <p className="font-medium">{discrepancy.application_id}</p>
                            </div>
                            <div>
                                <p className="text-muted-foreground">Date</p>
                                <p className="font-medium">{formatDate(discrepancy.created_at)}</p>
                            </div>
                        </div>

                        <div className="grid grid-cols-3 gap-4 text-sm mt-3 pt-3 border-t">
                            <div>
                                <p className="text-muted-foreground">Paid Amount</p>
                                <p className="font-medium">MWK {discrepancy.payment_amount.toLocaleString()}</p>
                            </div>
                            <div>
                                <p className="text-muted-foreground">Required Amount</p>
                                <p className="font-medium">MWK {discrepancy.calculated_fees.toLocaleString()}</p>
                            </div>
                            <div>
                                <p className="text-muted-foreground">Discrepancy</p>
                                <p className={cn("font-medium", isOverpaid ? "text-green-600" : "text-red-600")}>
                                    {isOverpaid ? "+" : "-"}MWK{" "}
                                    {Math.abs(discrepancy.discrepancy_amount).toLocaleString()}
                                </p>
                            </div>
                        </div>
                    </div>

                    <div className="flex items-center gap-2">
                        <Button size="sm" variant="outline">
                            <Eye className="h-4 w-4 mr-2" />
                            View
                        </Button>
                        <Button size="sm">Resolve</Button>
                    </div>
                </div>
            </CardContent>
        </Card>
    );
}

function DiscrepanciesSkeleton() {
    return (
        <div className="space-y-6">
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                {Array.from({ length: 4 }).map((_, i) => (
                    <Card key={i}>
                        <CardHeader className="space-y-0 pb-2">
                            <Skeleton className="h-4 w-[120px]" />
                        </CardHeader>
                        <CardContent>
                            <Skeleton className="h-8 w-[60px] mb-2" />
                            <Skeleton className="h-3 w-[100px]" />
                        </CardContent>
                    </Card>
                ))}
            </div>

            <Card>
                <CardHeader>
                    <Skeleton className="h-6 w-[200px]" />
                    <Skeleton className="h-4 w-[300px]" />
                </CardHeader>
                <CardContent>
                    <div className="space-y-4">
                        <Skeleton className="h-10 w-full" />
                        {Array.from({ length: 3 }).map((_, i) => (
                            <Card key={i}>
                                <CardContent className="p-4">
                                    <Skeleton className="h-20 w-full" />
                                </CardContent>
                            </Card>
                        ))}
                    </div>
                </CardContent>
            </Card>
        </div>
    );
}
