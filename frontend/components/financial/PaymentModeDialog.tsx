"use client";

import { Credit<PERSON>ard, Loader2 } from "lucide-react";
import { useState } from "react";

import LoadableItemInput from "@/components/inputs/loadable-item";
import { Button } from "@/components/ui/button";
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
} from "@/components/ui/dialog";
import { LoadableItemDto } from "@/types";

interface PaymentModeDialogProps {
    open: boolean;
    onOpenChange: (open: boolean) => void;
    onConfirm: (paymentModeId: string) => void;
    isLoading?: boolean;
    invoiceReference?: string;
}

export function PaymentModeDialog({
    open,
    onOpenChange,
    onConfirm,
    isLoading = false,
    invoiceReference,
}: PaymentModeDialogProps) {
    const [selectedPaymentMode, setSelectedPaymentMode] = useState<string>("");

    const handleConfirm = () => {
        if (selectedPaymentMode) {
            onConfirm(selectedPaymentMode);
        }
    };

    const handleOpenChange = (newOpen: boolean) => {
        if (!newOpen) {
            setSelectedPaymentMode("");
        }
        onOpenChange(newOpen);
    };

    const handlePaymentModeSelect = (item: LoadableItemDto | null) => {
        if (item) {
            setSelectedPaymentMode(item.id.toString());
        } else {
            setSelectedPaymentMode("");
        }
    };

    return (
        <Dialog open={open} onOpenChange={handleOpenChange}>
            <DialogContent className="sm:max-w-md">
                <DialogHeader>
                    <DialogTitle className="flex items-center gap-2">
                        <CreditCard className="h-5 w-5" />
                        Mark Invoice as Paid
                    </DialogTitle>
                    <DialogDescription>
                        {invoiceReference && (
                            <>
                                Select the payment method used for invoice{" "}
                                <span className="font-medium">{invoiceReference}</span>.
                            </>
                        )}
                        {!invoiceReference && "Select the payment method used for this invoice."}
                    </DialogDescription>
                </DialogHeader>

                <div className="py-4">
                    <LoadableItemInput
                        isRequired
                        className="w-full"
                        label="Payment Method"
                        placeholder="Select payment method..."
                        type="PAYMENT_MODE"
                        value={selectedPaymentMode}
                        onItemSelect={handlePaymentModeSelect}
                    />
                </div>

                <DialogFooter>
                    <Button disabled={isLoading} variant="outline" onClick={() => handleOpenChange(false)}>
                        Cancel
                    </Button>
                    <Button disabled={!selectedPaymentMode || isLoading} onClick={handleConfirm}>
                        {isLoading && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
                        Mark as Paid
                    </Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    );
}
