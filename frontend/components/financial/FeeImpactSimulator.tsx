"use client";

import { useState } from "react";
import { useMutation } from "@tanstack/react-query";
import {
    Calculator,
    TrendingUp,
    TrendingDown,
    AlertTriangle,
    CheckCircle,
    DollarSign,
    Users,
    BarChart3,
    RefreshCw,
} from "lucide-react";

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { cn } from "@/lib/utils";
import { simulateFeeChangeImpact } from "@/services/FinancialService";

interface FeeImpactSimulatorProps {
    feeId?: string;
    currentAmount?: number;
    feeName?: string;
}

export function FeeImpactSimulator({
    feeId = "fee-001",
    currentAmount = 50000,
    feeName = "Sample Fee",
}: FeeImpactSimulatorProps) {
    const [newAmount, setNewAmount] = useState<string>(currentAmount.toString());
    const [simulationResults, setSimulationResults] = useState<any>(null);

    const simulateMutation = useMutation({
        mutationFn: () => simulateFeeChangeImpact(feeId, parseFloat(newAmount)),
        onSuccess: (data) => {
            setSimulationResults(data.data);
        },
    });

    const handleSimulate = () => {
        const amount = parseFloat(newAmount);

        if (isNaN(amount) || amount <= 0) {
            return;
        }
        simulateMutation.mutate();
    };

    const clearResults = () => {
        setSimulationResults(null);
        setNewAmount(currentAmount.toString());
    };

    const getImpactColor = (change: number) => {
        if (change > 0) return "text-green-600";
        if (change < 0) return "text-red-600";

        return "text-gray-600";
    };

    const getImpactIcon = (change: number) => {
        if (change > 0) return <TrendingUp className="h-4 w-4 text-green-600" />;
        if (change < 0) return <TrendingDown className="h-4 w-4 text-red-600" />;

        return <div className="h-4 w-4" />;
    };

    return (
        <div className="space-y-6">
            {/* Header */}
            <div className="flex items-center justify-between">
                <div>
                    <h3 className="text-lg font-semibold flex items-center gap-2">
                        <Calculator className="h-5 w-5" />
                        Fee Impact Simulator
                    </h3>
                    <p className="text-sm text-muted-foreground">
                        Model the impact of fee changes on revenue and organizations
                    </p>
                </div>
                <Button variant="outline" onClick={clearResults}>
                    <RefreshCw className="h-4 w-4 mr-2" />
                    Reset
                </Button>
            </div>

            <div className="grid gap-6 lg:grid-cols-2">
                {/* Simulation Input */}
                <Card>
                    <CardHeader>
                        <CardTitle>Fee Change Simulation</CardTitle>
                        <CardDescription>Enter new fee amount to see potential impact</CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-4">
                        <div className="space-y-2">
                            <Label htmlFor="feeName">Fee Name</Label>
                            <Input disabled className="bg-muted" id="feeName" value={feeName} />
                        </div>

                        <div className="grid grid-cols-2 gap-4">
                            <div className="space-y-2">
                                <Label htmlFor="currentAmount">Current Amount (MWK)</Label>
                                <Input
                                    disabled
                                    className="bg-muted"
                                    id="currentAmount"
                                    value={currentAmount.toLocaleString()}
                                />
                            </div>
                            <div className="space-y-2">
                                <Label htmlFor="newAmount">New Amount (MWK)</Label>
                                <Input
                                    id="newAmount"
                                    placeholder="Enter new amount"
                                    type="number"
                                    value={newAmount}
                                    onChange={(e) => setNewAmount(e.target.value)}
                                />
                            </div>
                        </div>

                        <div className="pt-4">
                            <Button
                                className="w-full"
                                disabled={simulateMutation.isPending || !newAmount}
                                onClick={handleSimulate}
                            >
                                {simulateMutation.isPending ? (
                                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                                ) : (
                                    <Calculator className="h-4 w-4 mr-2" />
                                )}
                                Simulate Impact
                            </Button>
                        </div>

                        {simulateMutation.isError && (
                            <div className="p-3 bg-red-50 border border-red-200 rounded text-red-700 text-sm">
                                <div className="flex items-center gap-2">
                                    <AlertTriangle className="h-4 w-4" />
                                    <span>Simulation failed</span>
                                </div>
                                <p className="mt-1">Unable to simulate fee change. Please try again.</p>
                            </div>
                        )}
                    </CardContent>
                </Card>

                {/* Quick Scenarios */}
                <Card>
                    <CardHeader>
                        <CardTitle>Quick Scenarios</CardTitle>
                        <CardDescription>Common fee adjustment scenarios</CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-3">
                        <Button
                            className="w-full justify-between"
                            variant="outline"
                            onClick={() => setNewAmount((currentAmount * 1.1).toString())}
                        >
                            <span>10% Increase</span>
                            <span className="text-green-600">MWK {(currentAmount * 1.1).toLocaleString()}</span>
                        </Button>

                        <Button
                            className="w-full justify-between"
                            variant="outline"
                            onClick={() => setNewAmount((currentAmount * 1.25).toString())}
                        >
                            <span>25% Increase</span>
                            <span className="text-green-600">MWK {(currentAmount * 1.25).toLocaleString()}</span>
                        </Button>

                        <Button
                            className="w-full justify-between"
                            variant="outline"
                            onClick={() => setNewAmount((currentAmount * 0.9).toString())}
                        >
                            <span>10% Decrease</span>
                            <span className="text-red-600">MWK {(currentAmount * 0.9).toLocaleString()}</span>
                        </Button>

                        <Button
                            className="w-full justify-between"
                            variant="outline"
                            onClick={() => setNewAmount((currentAmount * 0.8).toString())}
                        >
                            <span>20% Decrease</span>
                            <span className="text-red-600">MWK {(currentAmount * 0.8).toLocaleString()}</span>
                        </Button>

                        <Separator />

                        <div className="pt-2">
                            <p className="text-sm text-muted-foreground mb-2">Custom amount:</p>
                            <div className="flex gap-2">
                                <Input
                                    placeholder="Custom amount"
                                    type="number"
                                    value={newAmount}
                                    onChange={(e) => setNewAmount(e.target.value)}
                                />
                                <Button size="sm" onClick={() => setNewAmount("")}>
                                    Clear
                                </Button>
                            </div>
                        </div>
                    </CardContent>
                </Card>
            </div>

            {/* Simulation Results */}
            {simulationResults && (
                <div className="space-y-6">
                    {/* Impact Summary */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <BarChart3 className="h-5 w-5" />
                                Impact Summary
                            </CardTitle>
                            <CardDescription>Overall impact of the proposed fee change</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                                <div className="text-center p-4 border rounded">
                                    <div className="flex items-center justify-center gap-2 mb-2">
                                        <DollarSign className="h-4 w-4 text-muted-foreground" />
                                        <span className="text-sm font-medium">Revenue Impact</span>
                                    </div>
                                    <div className="flex items-center justify-center gap-2">
                                        {getImpactIcon(simulationResults.revenue_impact)}
                                        <span
                                            className={cn(
                                                "text-lg font-bold",
                                                getImpactColor(simulationResults.revenue_impact),
                                            )}
                                        >
                                            {simulationResults.revenue_impact >= 0 ? "+" : ""}MWK{" "}
                                            {simulationResults.revenue_impact.toLocaleString()}
                                        </span>
                                    </div>
                                    <p className="text-xs text-muted-foreground mt-1">
                                        {simulationResults.revenue_change_percentage >= 0 ? "+" : ""}
                                        {simulationResults.revenue_change_percentage.toFixed(1)}% change
                                    </p>
                                </div>

                                <div className="text-center p-4 border rounded">
                                    <div className="flex items-center justify-center gap-2 mb-2">
                                        <Users className="h-4 w-4 text-muted-foreground" />
                                        <span className="text-sm font-medium">Affected Applications</span>
                                    </div>
                                    <span className="text-lg font-bold">
                                        {simulationResults.affected_applications_count}
                                    </span>
                                    <p className="text-xs text-muted-foreground mt-1">Organizations impacted</p>
                                </div>

                                <div className="text-center p-4 border rounded">
                                    <div className="flex items-center justify-center gap-2 mb-2">
                                        <DollarSign className="h-4 w-4 text-muted-foreground" />
                                        <span className="text-sm font-medium">Current Fee</span>
                                    </div>
                                    <span className="text-lg font-bold">
                                        MWK {simulationResults.current_amount.toLocaleString()}
                                    </span>
                                </div>

                                <div className="text-center p-4 border rounded">
                                    <div className="flex items-center justify-center gap-2 mb-2">
                                        <DollarSign className="h-4 w-4 text-muted-foreground" />
                                        <span className="text-sm font-medium">New Fee</span>
                                    </div>
                                    <span className="text-lg font-bold">
                                        MWK {simulationResults.new_amount.toLocaleString()}
                                    </span>
                                </div>
                            </div>

                            {/* Recommendation */}
                            <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded">
                                <div className="flex items-start gap-2">
                                    <CheckCircle className="h-5 w-5 text-blue-600 mt-0.5" />
                                    <div>
                                        <p className="font-medium text-blue-800">Recommendation</p>
                                        <p className="text-sm text-blue-700 mt-1">{simulationResults.recommendation}</p>
                                    </div>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Organization Impact Details */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Organization Impact Details</CardTitle>
                            <CardDescription>How the fee change affects individual organizations</CardDescription>
                        </CardHeader>
                        <CardContent>
                            {simulationResults.organization_impact &&
                            simulationResults.organization_impact.length > 0 ? (
                                    <div className="space-y-3">
                                        {simulationResults.organization_impact
                                            .slice(0, 10)
                                            .map((org: any, index: number) => (
                                                <div
                                                    key={index}
                                                    className="grid grid-cols-5 gap-4 p-3 border rounded text-sm"
                                                >
                                                    <div>
                                                        <p className="font-medium">{org.organization_id}</p>
                                                        <p className="text-xs text-muted-foreground">Organization</p>
                                                    </div>
                                                    <div className="text-center">
                                                        <p className="font-medium">
                                                        MWK {org.current_fee.toLocaleString()}
                                                        </p>
                                                        <p className="text-xs text-muted-foreground">Current</p>
                                                    </div>
                                                    <div className="text-center">
                                                        <p className="font-medium">MWK {org.new_fee.toLocaleString()}</p>
                                                        <p className="text-xs text-muted-foreground">New</p>
                                                    </div>
                                                    <div className="text-center">
                                                        <p className={cn("font-medium", getImpactColor(org.impact))}>
                                                            {org.impact >= 0 ? "+" : ""}MWK {org.impact.toLocaleString()}
                                                        </p>
                                                        <p className="text-xs text-muted-foreground">Impact</p>
                                                    </div>
                                                    <div className="text-center">
                                                        <div className="flex items-center justify-center gap-1">
                                                            {getImpactIcon(org.percentage_change)}
                                                            <span
                                                                className={cn(
                                                                    "font-medium",
                                                                    getImpactColor(org.percentage_change),
                                                                )}
                                                            >
                                                                {org.percentage_change >= 0 ? "+" : ""}
                                                                {org.percentage_change.toFixed(1)}%
                                                            </span>
                                                        </div>
                                                        <p className="text-xs text-muted-foreground">Change</p>
                                                    </div>
                                                </div>
                                            ))}

                                        {simulationResults.organization_impact.length > 10 && (
                                            <div className="text-center py-3 text-sm text-muted-foreground">
                                            And {simulationResults.organization_impact.length - 10} more
                                            organizations...
                                            </div>
                                        )}
                                    </div>
                                ) : (
                                    <div className="text-center py-8 text-muted-foreground">
                                        <AlertTriangle className="h-12 w-12 mx-auto mb-4" />
                                        <p>No organization impact data available</p>
                                    </div>
                                )}
                        </CardContent>
                    </Card>
                </div>
            )}
        </div>
    );
}
