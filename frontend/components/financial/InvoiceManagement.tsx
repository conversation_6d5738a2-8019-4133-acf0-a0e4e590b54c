"use client";

import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import {
    AlertTriangle,
    CheckCircle,
    Clock,
    DollarSign,
    Download,
    FileText,
    RefreshCw,
    Search,
    XCircle,
} from "lucide-react";
import Link from "next/link";
import { useState } from "react";

import { Select } from "../ui/select";

import { PaymentModeDialog } from "./PaymentModeDialog";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { cn } from "@/lib/utils";
import { fetchInvoices, generateInvoicePDF, markInvoiceAsPaid } from "@/services/FinancialService";
import { InvoiceDto } from "@/types";
import { base64ToBlob, downloadFile } from "@/utils/common";

export function InvoiceManagement() {
    const [searchTerm, setSearchTerm] = useState("");
    const [statusFilter, setStatusFilter] = useState("all");
    const [paymentModeDialogOpen, setPaymentModeDialogOpen] = useState(false);
    const [invoiceToMarkAsPaid, setInvoiceToMarkAsPaid] = useState<InvoiceDto | null>(null);

    const queryClient = useQueryClient();

    const {
        data: invoicesResponse,
        isLoading: invoicesLoading,
        refetch: refetchInvoices,
    } = useQuery({
        queryKey: ["invoices", { status: statusFilter !== "all" ? statusFilter : undefined }],
        queryFn: () =>
            fetchInvoices({
                status: statusFilter !== "all" ? statusFilter.toUpperCase() : undefined,
                size: 10,
                segments: "organization",
            }),
    });

    const pdfMutation = useMutation({
        mutationFn: (invoiceId: string) => generateInvoicePDF(invoiceId),
        onSuccess: (response) => {
            const pdfData = response.data;

            if (pdfData) {
                const blob = base64ToBlob(pdfData.pdf_content, "application/pdf");
                const filename = pdfData.filename || `invoice-${pdfData.invoice_id}.pdf`;

                downloadFile(blob, filename);
            }
        },
    });

    const markAsPaidMutation = useMutation({
        mutationFn: ({ invoiceId, paymentModeId }: { invoiceId: string; paymentModeId: string }) =>
            markInvoiceAsPaid(invoiceId, paymentModeId),
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ["invoices"] });
            setPaymentModeDialogOpen(false);
            setInvoiceToMarkAsPaid(null);
        },
    });

    const allInvoices = invoicesResponse?.data || invoicesResponse?.data || [];

    const filteredInvoices = allInvoices.filter((invoice: InvoiceDto) => {
        const matchesSearch =
            invoice.reference_number.toLowerCase().includes(searchTerm.toLowerCase()) ||
            (invoice.organization?.name || "").toLowerCase().includes(searchTerm.toLowerCase()) ||
            invoice.organization_id.toLowerCase().includes(searchTerm.toLowerCase());

        const matchesStatus = statusFilter === "all" || invoice.status.toLowerCase() === statusFilter;

        return matchesSearch && matchesStatus;
    });

    const handleDownloadPDF = (invoiceId: string) => {
        pdfMutation.mutate(invoiceId);
    };

    const handleMarkAsPaid = (invoice: InvoiceDto) => {
        setInvoiceToMarkAsPaid(invoice);
        setPaymentModeDialogOpen(true);
    };

    const handleConfirmPayment = (paymentModeId: string) => {
        if (invoiceToMarkAsPaid) {
            markAsPaidMutation.mutate({
                invoiceId: invoiceToMarkAsPaid.id,
                paymentModeId,
            });
        }
    };

    const getInvoiceStats = () => {
        const total = allInvoices.length;
        const paid = allInvoices.filter((inv: InvoiceDto) => inv.status === "PAID").length;
        const pending = allInvoices.filter((inv: InvoiceDto) => inv.status === "PENDING").length;
        const overdue = allInvoices.filter((inv: InvoiceDto) => inv.status === "OVERDUE").length;
        const totalAmount = allInvoices.reduce((sum: number, inv: InvoiceDto) => sum + inv.total_amount, 0);
        const paidAmount = allInvoices
            .filter((inv: InvoiceDto) => inv.status === "PAID")
            .reduce((sum: number, inv: InvoiceDto) => sum + inv.total_amount, 0);

        return { total, paid, pending, overdue, totalAmount, paidAmount };
    };

    const stats = getInvoiceStats();

    return (
        <div className="space-y-6">
            {/* Header */}
            <div className="flex items-center justify-end">
                <div className="flex items-center gap-2">
                    <Button disabled={invoicesLoading} variant="outline" onClick={() => refetchInvoices()}>
                        <RefreshCw className={cn("h-4 w-4 mr-2", invoicesLoading && "animate-spin")} />
                        Refresh
                    </Button>
                </div>
            </div>

            {/* Summary Cards */}
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Total Invoices</CardTitle>
                        <FileText className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">{stats.total}</div>
                        <p className="text-xs text-muted-foreground">All time</p>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Total Amount</CardTitle>
                        <DollarSign className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">MWK {stats.totalAmount.toLocaleString()}</div>
                        <p className="text-xs text-muted-foreground">
                            MWK {stats.paidAmount.toLocaleString()} collected
                        </p>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Payment Rate</CardTitle>
                        <CheckCircle className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold text-green-600">
                            {((stats.paid / stats.total) * 100).toFixed(1)}%
                        </div>
                        <p className="text-xs text-muted-foreground">
                            {stats.paid} of {stats.total} paid
                        </p>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Overdue</CardTitle>
                        <AlertTriangle className="h-4 w-4 text-red-600" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold text-red-600">{stats.overdue}</div>
                        <p className="text-xs text-muted-foreground">Require immediate attention</p>
                    </CardContent>
                </Card>
            </div>

            {/* Main Invoice Interface */}
            <div className="space-y-4">
                {/* Filters and Search */}
                <Card>
                    <CardContent className="p-4">
                        <div className="flex items-center gap-4">
                            <div className="relative flex-1">
                                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                                <Input
                                    className="pl-9"
                                    placeholder="Search invoices by number, organization, or ID..."
                                    value={searchTerm}
                                    onChange={(e) => setSearchTerm(e.target.value)}
                                />
                            </div>
                            <div>
                                <Select
                                    options={[
                                        { value: "all", label: "All Status" },
                                        { value: "paid", label: "Paid" },
                                        { value: "pending", label: "Pending" },
                                        { value: "overdue", label: "Overdue" },
                                        { value: "partially_paid", label: "Partially Paid" },
                                        { value: "cancelled", label: "Cancelled" },
                                    ]}
                                    value={statusFilter}
                                    onValueChange={(e) => setStatusFilter(e)}
                                />
                            </div>
                        </div>
                    </CardContent>
                </Card>

                {/* Invoice List */}
                <div className="space-y-3">
                    {filteredInvoices.length === 0 ? (
                        <Card>
                            <CardContent className="p-8 text-center">
                                <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                                <h3 className="text-lg font-semibold mb-2">No invoices found</h3>
                                <p className="text-muted-foreground">
                                    {searchTerm
                                        ? "Try adjusting your search criteria."
                                        : "No invoices match the selected filters."}
                                </p>
                            </CardContent>
                        </Card>
                    ) : (
                        filteredInvoices.map((invoice: InvoiceDto) => (
                            <InvoiceCard
                                key={invoice.id}
                                invoice={invoice}
                                isDownloading={pdfMutation.isPending}
                                isUpdating={markAsPaidMutation.isPending}
                                onDownloadPDF={handleDownloadPDF}
                                onMarkAsPaid={handleMarkAsPaid}
                            />
                        ))
                    )}
                </div>
            </div>

            <PaymentModeDialog
                invoiceReference={invoiceToMarkAsPaid?.reference_number}
                isLoading={markAsPaidMutation.isPending}
                open={paymentModeDialogOpen}
                onConfirm={handleConfirmPayment}
                onOpenChange={setPaymentModeDialogOpen}
            />
        </div>
    );
}

interface InvoiceCardProps {
    invoice: InvoiceDto;
    onDownloadPDF: (invoiceId: string) => void;
    onMarkAsPaid: (invoice: InvoiceDto) => void;
    isDownloading: boolean;
    isUpdating: boolean;
}

function InvoiceCard({ invoice, onDownloadPDF, onMarkAsPaid, isDownloading, isUpdating }: InvoiceCardProps) {
    const getStatusBadge = (status: string) => {
        switch (status) {
            case "PAID":
                return <Badge className="bg-green-100 text-green-800">Paid</Badge>;
            case "PENDING":
                return <Badge className="bg-blue-100 text-blue-800">Pending</Badge>;
            case "overdue":
                return <Badge variant="destructive">Overdue</Badge>;
            case "partially_paid":
                return <Badge className="bg-yellow-100 text-yellow-800">Partially Paid</Badge>;
            case "cancelled":
                return (
                    <Badge className="text-gray-600" variant="outline">
                        Cancelled
                    </Badge>
                );
            default:
                return <Badge variant="outline">{status}</Badge>;
        }
    };

    const getStatusIcon = (status: string) => {
        switch (status) {
            case "PAID":
                return <CheckCircle className="h-4 w-4 text-green-600" />;
            case "PENDING":
                return <Clock className="h-4 w-4 text-blue-600" />;
            case "overdue":
                return <AlertTriangle className="h-4 w-4 text-red-600" />;
            case "partially_paid":
                return <Clock className="h-4 w-4 text-yellow-600" />;
            case "cancelled":
                return <XCircle className="h-4 w-4 text-gray-600" />;
            default:
                return <FileText className="h-4 w-4 text-muted-foreground" />;
        }
    };

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString();
    };

    const getDaysOverdue = () => {
        if (invoice.status.toLowerCase() !== "overdue") return 0;
        const today = new Date();
        const dueDate = new Date(invoice.due_date);

        return Math.ceil((today.getTime() - dueDate.getTime()) / (1000 * 60 * 60 * 24));
    };

    return (
        <Card
            className={cn(
                "transition-shadow hover:shadow-md",
                invoice.status.toLowerCase() === "overdue" && "border-red-200 bg-red-50",
            )}
        >
            <CardContent className="p-6">
                <div className="flex items-start justify-between">
                    <div className="flex items-start gap-3">
                        {getStatusIcon(invoice.status)}
                        <div className="space-y-1">
                            <div className="flex items-center gap-2">
                                <h3 className="font-semibold">{invoice.reference_number}</h3>
                                {getStatusBadge(invoice.status)}
                                {invoice.status.toLowerCase() === "overdue" && (
                                    <Badge className="text-xs" variant="destructive">
                                        {getDaysOverdue()} days overdue
                                    </Badge>
                                )}
                            </div>
                            <p className="text-sm text-muted-foreground">
                                <Link className="hover:underline" href={`/organizations/${invoice.organization_id}`}>
                                    {invoice.organization?.name || "Unknown Organization"} •{" "}
                                    {invoice.organization?.abbreviation}
                                </Link>
                            </p>
                            <p className="text-sm">{invoice.description}</p>
                        </div>
                    </div>

                    <div className="text-right">
                        <p className="text-lg font-bold">MWK {invoice.total_amount.toLocaleString()}</p>
                    </div>
                </div>

                <div className="grid grid-cols-2 md:grid-cols-3 gap-4 mt-4 pt-4 border-t text-sm">
                    <div>
                        <p className="text-muted-foreground">Issue Date</p>
                        <p className="font-medium">{formatDate(invoice.created_at)}</p>
                    </div>
                    <div>
                        <p className="text-muted-foreground">Due Date</p>
                        <p className="font-medium">{formatDate(invoice.due_date)}</p>
                    </div>
                    <div>
                        <p className="text-muted-foreground">Status</p>
                        <p className="font-medium">{invoice.status}</p>
                    </div>
                </div>

                <div className="flex items-center justify-between mt-4 pt-4 border-t">
                    <div className="text-xs text-muted-foreground">ID: {invoice.id}</div>
                    <div className="flex items-center gap-2">
                        <Button
                            disabled={isDownloading}
                            size="sm"
                            variant="outline"
                            onClick={() => onDownloadPDF(invoice.id)}
                        >
                            {isDownloading ? (
                                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                            ) : (
                                <Download className="h-4 w-4 mr-2" />
                            )}
                            PDF
                        </Button>
                        {invoice.status.toLowerCase() === "pending" && (
                            <Button disabled={isUpdating} size="sm" onClick={() => onMarkAsPaid(invoice)}>
                                {isUpdating ? (
                                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                                ) : (
                                    <CheckCircle className="h-4 w-4 mr-2" />
                                )}
                                Mark as Paid
                            </Button>
                        )}
                    </div>
                </div>
            </CardContent>
        </Card>
    );
}
