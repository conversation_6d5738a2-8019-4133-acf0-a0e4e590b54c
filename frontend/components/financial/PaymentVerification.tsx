"use client";

import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { 
    CheckCircle, 
    XCircle, 
    Clock, 
    AlertTriangle, 
    Eye,
    FileText,
    DollarSign
} from "lucide-react";
import { useState } from "react";

import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { cn } from "@/lib/utils";
import { 
    fetchPayments, 
    PaymentDto, 
    verifyPayment,
    PaymentVerificationRequest
} from "@/services/FinancialService";

export function PaymentVerification() {
    const [selectedPayment, setSelectedPayment] = useState<PaymentDto | null>(null);
    const [verificationNotes, setVerificationNotes] = useState("");
    const [isDialogOpen, setIsDialogOpen] = useState(false);
    
    const queryClient = useQueryClient();

    // Fetch pending payments that need verification
    const { data: paymentsResponse, isLoading: paymentsLoading } = useQuery({
        queryKey: ["payments", { status: "PENDING" }],
        queryFn: () => fetchPayments({ status: "PENDING" }),
        refetchInterval: 30000, // Refresh every 30 seconds
    });

    const verifyPaymentMutation = useMutation({
        mutationFn: (request: PaymentVerificationRequest) => verifyPayment(request),
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ["payments"] });
            queryClient.invalidateQueries({ queryKey: ["invoices"] });
            queryClient.invalidateQueries({ queryKey: ["finance-workflow-stages"] });
            setIsDialogOpen(false);
            setSelectedPayment(null);
            setVerificationNotes("");
        },
    });

    const payments = paymentsResponse?.data?.data || paymentsResponse?.data || [];

    const handleVerifyPayment = (payment: PaymentDto, status: "VERIFIED" | "FAILED") => {
        verifyPaymentMutation.mutate({
            payment_id: payment.id,
            status,
            notes: verificationNotes || undefined,
        });
    };

    const openVerificationDialog = (payment: PaymentDto) => {
        setSelectedPayment(payment);
        setIsDialogOpen(true);
        setVerificationNotes("");
    };

    const getStatusColor = (status: string) => {
        switch (status.toLowerCase()) {
            case "verified":
                return "text-green-600 bg-green-100";
            case "pending":
                return "text-yellow-600 bg-yellow-100";
            case "failed":
                return "text-red-600 bg-red-100";
            default:
                return "text-gray-600 bg-gray-100";
        }
    };

    const getStatusIcon = (status: string) => {
        switch (status.toLowerCase()) {
            case "verified":
                return <CheckCircle className="h-4 w-4" />;
            case "pending":
                return <Clock className="h-4 w-4" />;
            case "failed":
                return <XCircle className="h-4 w-4" />;
            default:
                return <FileText className="h-4 w-4" />;
        }
    };

    return (
        <div className="space-y-6">
            <div className="flex items-center justify-between">
                <div>
                    <h2 className="text-2xl font-bold">Payment Verification</h2>
                    <p className="text-muted-foreground">
                        Review and verify payment submissions from organizations
                    </p>
                </div>
            </div>

            {/* Summary Stats */}
            <div className="grid gap-4 md:grid-cols-3">
                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Pending Verification</CardTitle>
                        <Clock className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold text-yellow-600">{payments.length}</div>
                        <p className="text-xs text-muted-foreground">Awaiting review</p>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Total Amount</CardTitle>
                        <DollarSign className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">
                            MWK {payments.reduce((sum: number, payment: PaymentDto) => sum + payment.amount, 0).toLocaleString()}
                        </div>
                        <p className="text-xs text-muted-foreground">Total pending amount</p>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">High Priority</CardTitle>
                        <AlertTriangle className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold text-red-600">
                            {payments.filter((p: PaymentDto) => p.amount > 100000).length}
                        </div>
                        <p className="text-xs text-muted-foreground">Large amounts (>100K)</p>
                    </CardContent>
                </Card>
            </div>

            {/* Payment List */}
            <Card>
                <CardHeader>
                    <CardTitle>Payments Pending Verification</CardTitle>
                    <p className="text-sm text-muted-foreground">
                        Review payment proof and verify transactions
                    </p>
                </CardHeader>
                <CardContent>
                    <div className="space-y-4">
                        {paymentsLoading ? (
                            <div className="text-center py-8">Loading payments...</div>
                        ) : payments.length === 0 ? (
                            <div className="text-center py-8 text-muted-foreground">
                                <CheckCircle className="h-12 w-12 mx-auto mb-4 opacity-50" />
                                <p>No payments pending verification</p>
                            </div>
                        ) : (
                            payments.map((payment: PaymentDto) => (
                                <PaymentCard
                                    key={payment.id}
                                    payment={payment}
                                    onVerify={() => openVerificationDialog(payment)}
                                />
                            ))
                        )}
                    </div>
                </CardContent>
            </Card>

            {/* Verification Dialog */}
            <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
                <DialogContent className="max-w-2xl">
                    <DialogHeader>
                        <DialogTitle>Verify Payment</DialogTitle>
                    </DialogHeader>
                    {selectedPayment && (
                        <div className="space-y-6">
                            <div className="grid grid-cols-2 gap-4">
                                <div>
                                    <label className="text-sm font-medium">Organization</label>
                                    <p className="text-sm text-muted-foreground">
                                        {selectedPayment.organization?.name || "Unknown"}
                                    </p>
                                </div>
                                <div>
                                    <label className="text-sm font-medium">Amount</label>
                                    <p className="text-sm font-medium">
                                        MWK {selectedPayment.amount.toLocaleString()}
                                    </p>
                                </div>
                                <div>
                                    <label className="text-sm font-medium">Transaction Number</label>
                                    <p className="text-sm text-muted-foreground">
                                        {selectedPayment.transaction_number}
                                    </p>
                                </div>
                                <div>
                                    <label className="text-sm font-medium">Payment Method</label>
                                    <p className="text-sm text-muted-foreground">
                                        {selectedPayment.payment_mode}
                                    </p>
                                </div>
                                <div>
                                    <label className="text-sm font-medium">Paid By</label>
                                    <p className="text-sm text-muted-foreground">
                                        {selectedPayment.paid_by}
                                    </p>
                                </div>
                            </div>

                            <div>
                                <label className="text-sm font-medium">Verification Notes</label>
                                <Textarea
                                    value={verificationNotes}
                                    onChange={(e) => setVerificationNotes(e.target.value)}
                                    placeholder="Add any notes about the payment verification..."
                                    className="mt-1"
                                />
                            </div>

                            <div className="flex items-center gap-3 pt-4 border-t">
                                <Button
                                    onClick={() => handleVerifyPayment(selectedPayment, "VERIFIED")}
                                    disabled={verifyPaymentMutation.isPending}
                                    className="bg-green-600 hover:bg-green-700"
                                >
                                    <CheckCircle className="h-4 w-4 mr-2" />
                                    Verify Payment
                                </Button>
                                <Button
                                    variant="destructive"
                                    onClick={() => handleVerifyPayment(selectedPayment, "FAILED")}
                                    disabled={verifyPaymentMutation.isPending}
                                >
                                    <XCircle className="h-4 w-4 mr-2" />
                                    Reject Payment
                                </Button>
                                <Button
                                    variant="outline"
                                    onClick={() => setIsDialogOpen(false)}
                                    disabled={verifyPaymentMutation.isPending}
                                >
                                    Cancel
                                </Button>
                            </div>
                        </div>
                    )}
                </DialogContent>
            </Dialog>
        </div>
    );
}

interface PaymentCardProps {
    payment: PaymentDto;
    onVerify: () => void;
}

function PaymentCard({ payment, onVerify }: PaymentCardProps) {
    const getStatusColor = (status: string) => {
        switch (status.toLowerCase()) {
            case "verified":
                return "text-green-600 bg-green-100";
            case "pending":
                return "text-yellow-600 bg-yellow-100";
            case "failed":
                return "text-red-600 bg-red-100";
            default:
                return "text-gray-600 bg-gray-100";
        }
    };

    const getStatusIcon = (status: string) => {
        switch (status.toLowerCase()) {
            case "verified":
                return <CheckCircle className="h-4 w-4" />;
            case "pending":
                return <Clock className="h-4 w-4" />;
            case "failed":
                return <XCircle className="h-4 w-4" />;
            default:
                return <FileText className="h-4 w-4" />;
        }
    };

    return (
        <div className="flex items-start justify-between p-4 border rounded-lg hover:bg-muted/50 transition-colors">
            <div className="flex items-start gap-3">
                {getStatusIcon(payment.status)}
                <div className="space-y-1">
                    <div className="flex items-center gap-2">
                        <h4 className="font-medium text-sm">
                            {payment.organization?.name || "Unknown Organization"}
                        </h4>
                        <Badge className={cn("text-xs", getStatusColor(payment.status))}>
                            {payment.status}
                        </Badge>
                        {payment.amount > 100000 && (
                            <Badge variant="destructive" className="text-xs">
                                High Amount
                            </Badge>
                        )}
                    </div>
                    <p className="text-xs text-muted-foreground">
                        Transaction: {payment.transaction_number}
                    </p>
                    <p className="text-xs text-muted-foreground">
                        Method: {payment.payment_mode} • Paid by: {payment.paid_by}
                    </p>
                </div>
            </div>
            <div className="text-right">
                <p className="text-lg font-bold">MWK {payment.amount.toLocaleString()}</p>
                <div className="flex items-center gap-1 mt-2">
                    <Button size="sm" variant="outline">
                        <Eye className="h-3 w-3 mr-1" />
                        View Proof
                    </Button>
                    <Button size="sm" onClick={onVerify}>
                        <CheckCircle className="h-3 w-3 mr-1" />
                        Verify
                    </Button>
                </div>
            </div>
        </div>
    );
}