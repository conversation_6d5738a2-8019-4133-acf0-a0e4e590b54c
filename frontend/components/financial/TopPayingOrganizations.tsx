"use client";

import { <PERSON>, TrendingUp, <PERSON>, Refresh<PERSON><PERSON>, Medal, Award, Star } from "lucide-react";

import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { Progress } from "@/components/ui/progress";

interface TopPayingOrganization {
    organization_id: string;
    organization_name: string;
    total_paid: number;
    payment_count: number;
}

interface TopPayingOrganizationsProps {
    data?: TopPayingOrganization[];
    limit?: number;
    showHeader?: boolean;
    isLoading?: boolean;
}

export function TopPayingOrganizations({
    data = [],
    limit = 5,
    showHeader = true,
    isLoading = false,
}: TopPayingOrganizationsProps) {
    // Mock data fallback when no data is provided
    const mockData: TopPayingOrganization[] = [
        {
            organization_id: "org-001",
            organization_name: "Education for All Malawi",
            total_paid: 850000,
            payment_count: 12,
        },
        {
            organization_id: "org-002",
            organization_name: "Hope Children Foundation",
            total_paid: 720000,
            payment_count: 9,
        },
        {
            organization_id: "org-003",
            organization_name: "Rural Development Initiative",
            total_paid: 650000,
            payment_count: 8,
        },
        {
            organization_id: "org-004",
            organization_name: "Community Health Partners",
            total_paid: 480000,
            payment_count: 6,
        },
        {
            organization_id: "org-005",
            organization_name: "Youth Empowerment Network",
            total_paid: 320000,
            payment_count: 5,
        },
        {
            organization_id: "org-006",
            organization_name: "Women's Rights Advocacy",
            total_paid: 290000,
            payment_count: 4,
        },
    ];

    const organizations = data.length > 0 ? data : mockData;
    const displayOrganizations = organizations.slice(0, limit);
    const maxAmount = Math.max(...displayOrganizations.map((org) => org.total_paid));

    const getRankIcon = (index: number) => {
        switch (index) {
            case 0:
                return <Crown className="h-4 w-4 text-yellow-600" />;
            case 1:
                return <Medal className="h-4 w-4 text-gray-500" />;
            case 2:
                return <Award className="h-4 w-4 text-amber-600" />;
            default:
                return <Star className="h-4 w-4 text-blue-600" />;
        }
    };

    const getRankBadge = (index: number) => {
        switch (index) {
            case 0:
                return <Badge className="bg-yellow-100 text-yellow-800 text-xs">Top Contributor</Badge>;
            case 1:
                return <Badge className="bg-gray-100 text-gray-800 text-xs">2nd Place</Badge>;
            case 2:
                return <Badge className="bg-amber-100 text-amber-800 text-xs">3rd Place</Badge>;
            default:
                return null;
        }
    };

    if (isLoading) {
        return (
            <div className="space-y-3">
                {showHeader && (
                    <div className="flex items-center justify-between">
                        <Skeleton className="h-5 w-[160px]" />
                        <Skeleton className="h-8 w-[60px]" />
                    </div>
                )}
                {Array.from({ length: limit }).map((_, i) => (
                    <Skeleton key={i} className="h-20 w-full" />
                ))}
            </div>
        );
    }

    return (
        <div className="space-y-3">
            {showHeader && (
                <div className="flex items-center justify-between">
                    <h4 className="font-medium">Top Contributors</h4>
                    <Button size="sm" variant="ghost">
                        <RefreshCw className="h-4 w-4" />
                    </Button>
                </div>
            )}

            <div className="space-y-2">
                {displayOrganizations.length === 0 ? (
                    <div className="text-center py-6 text-muted-foreground">
                        <TrendingUp className="h-8 w-8 mx-auto mb-2" />
                        <p className="text-sm">No payment data available</p>
                    </div>
                ) : (
                    displayOrganizations.map((org, index) => (
                        <OrganizationItem
                            key={org.organization_id}
                            maxAmount={maxAmount}
                            organization={org}
                            rank={index + 1}
                            showBadge={index < 3}
                        />
                    ))
                )}
            </div>

            {organizations.length > limit && (
                <div className="text-center pt-2">
                    <Button size="sm" variant="ghost">
                        <Eye className="h-4 w-4 mr-2" />
                        View All Organizations
                    </Button>
                </div>
            )}
        </div>
    );
}

interface OrganizationItemProps {
    organization: TopPayingOrganization;
    rank: number;
    maxAmount: number;
    showBadge: boolean;
}

function OrganizationItem({ organization, rank, maxAmount, showBadge }: OrganizationItemProps) {
    const progressPercentage = (organization.total_paid / maxAmount) * 100;

    const getRankIcon = (rank: number) => {
        switch (rank) {
            case 1:
                return <Crown className="h-4 w-4 text-yellow-600" />;
            case 2:
                return <Medal className="h-4 w-4 text-gray-500" />;
            case 3:
                return <Award className="h-4 w-4 text-amber-600" />;
            default:
                return <span className="text-sm font-medium text-muted-foreground w-4 text-center">#{rank}</span>;
        }
    };

    const getRankBadge = (rank: number) => {
        switch (rank) {
            case 1:
                return <Badge className="bg-yellow-100 text-yellow-800 text-xs">Top</Badge>;
            case 2:
                return <Badge className="bg-gray-100 text-gray-800 text-xs">2nd</Badge>;
            case 3:
                return <Badge className="bg-amber-100 text-amber-800 text-xs">3rd</Badge>;
            default:
                return null;
        }
    };

    return (
        <div className="p-3 border rounded-lg hover:bg-gray-50 transition-colors">
            <div className="flex items-start justify-between mb-2">
                <div className="flex items-center gap-2">
                    {getRankIcon(rank)}
                    <div className="min-w-0 flex-1">
                        <div className="flex items-center gap-2">
                            <p className="font-medium truncate">{organization.organization_name}</p>
                            {showBadge && getRankBadge(rank)}
                        </div>
                        <p className="text-xs text-muted-foreground">{organization.organization_id}</p>
                    </div>
                </div>
                <div className="text-right">
                    <p className="font-bold text-green-600">MWK {organization.total_paid.toLocaleString()}</p>
                    <p className="text-xs text-muted-foreground">{organization.payment_count} payments</p>
                </div>
            </div>

            <div className="space-y-1">
                <Progress className="h-2" value={progressPercentage} />
                <div className="flex justify-between text-xs text-muted-foreground">
                    <span>Revenue contribution</span>
                    <span>{progressPercentage.toFixed(1)}% of top contributor</span>
                </div>
            </div>
        </div>
    );
}
