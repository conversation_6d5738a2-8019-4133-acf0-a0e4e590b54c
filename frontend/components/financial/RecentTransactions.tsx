"use client";

import { useState } from "react";
import { ArrowUpRight, ArrowDownRight, CreditCard, Eye, RefreshCw } from "lucide-react";

import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { cn } from "@/lib/utils";

// Mock transaction data - in real app this would come from API
const mockTransactions = [
    {
        id: "txn-001",
        organizationName: "Hope Children Foundation",
        organizationId: "org-001",
        amount: 75000,
        currency: "MWK",
        type: "payment",
        status: "completed",
        paymentMethod: "Bank Transfer",
        transactionNumber: "TXN123456789",
        date: "2024-01-20T10:30:00Z",
        description: "Registration Fee Payment",
    },
    {
        id: "txn-002",
        organizationName: "Rural Development Initiative",
        organizationId: "org-002",
        amount: 45000,
        currency: "MWK",
        type: "payment",
        status: "pending",
        paymentMethod: "Mobile Money",
        transactionNumber: "MM987654321",
        date: "2024-01-19T14:15:00Z",
        description: "Amendment Fee Payment",
    },
    {
        id: "txn-003",
        organizationName: "Education for All Malawi",
        organizationId: "org-003",
        amount: 120000,
        currency: "MWK",
        type: "payment",
        status: "completed",
        paymentMethod: "Bank Transfer",
        transactionNumber: "TXN456789123",
        date: "2024-01-18T09:45:00Z",
        description: "Annual Renewal Fee",
    },
    {
        id: "txn-004",
        organizationName: "Community Health Partners",
        organizationId: "org-004",
        amount: 30000,
        currency: "MWK",
        type: "refund",
        status: "processing",
        paymentMethod: "Bank Transfer",
        transactionNumber: "REF789123456",
        date: "2024-01-17T16:20:00Z",
        description: "Overpayment Refund",
    },
    {
        id: "txn-005",
        organizationName: "Youth Empowerment Network",
        organizationId: "org-005",
        amount: 95000,
        currency: "MWK",
        type: "payment",
        status: "failed",
        paymentMethod: "Mobile Money",
        transactionNumber: "MM123789456",
        date: "2024-01-16T11:10:00Z",
        description: "Registration Fee Payment",
    },
];

interface RecentTransactionsProps {
    limit?: number;
    showHeader?: boolean;
    isLoading?: boolean;
}

export function RecentTransactions({ limit = 5, showHeader = true, isLoading = false }: RecentTransactionsProps) {
    const [refreshing, setRefreshing] = useState(false);

    const handleRefresh = async () => {
        setRefreshing(true);
        // Simulate API call
        await new Promise((resolve) => setTimeout(resolve, 1000));
        setRefreshing(false);
    };

    const getStatusBadge = (status: string) => {
        switch (status) {
            case "completed":
                return <Badge className="bg-green-100 text-green-800">Completed</Badge>;
            case "pending":
                return <Badge className="bg-yellow-100 text-yellow-800">Pending</Badge>;
            case "processing":
                return <Badge className="bg-blue-100 text-blue-800">Processing</Badge>;
            case "failed":
                return <Badge variant="destructive">Failed</Badge>;
            default:
                return <Badge variant="outline">{status}</Badge>;
        }
    };

    const getTransactionIcon = (type: string, status: string) => {
        if (type === "refund") {
            return <ArrowDownRight className="h-4 w-4 text-orange-600" />;
        }

        switch (status) {
            case "completed":
                return <ArrowUpRight className="h-4 w-4 text-green-600" />;
            case "failed":
                return <ArrowUpRight className="h-4 w-4 text-red-600" />;
            default:
                return <CreditCard className="h-4 w-4 text-blue-600" />;
        }
    };

    const formatDate = (dateString: string) => {
        const date = new Date(dateString);

        return date.toLocaleDateString("en-US", {
            month: "short",
            day: "numeric",
            hour: "2-digit",
            minute: "2-digit",
        });
    };

    const formatAmount = (amount: number, currency: string, type: string) => {
        const prefix = type === "refund" ? "-" : "+";

        return `${prefix}${currency} ${amount.toLocaleString()}`;
    };

    const displayTransactions = mockTransactions.slice(0, limit);

    if (isLoading) {
        return (
            <div className="space-y-3">
                {showHeader && (
                    <div className="flex items-center justify-between">
                        <Skeleton className="h-5 w-[140px]" />
                        <Skeleton className="h-8 w-[60px]" />
                    </div>
                )}
                {Array.from({ length: limit }).map((_, i) => (
                    <Skeleton key={i} className="h-16 w-full" />
                ))}
            </div>
        );
    }

    return (
        <div className="space-y-3">
            {showHeader && (
                <div className="flex items-center justify-between">
                    <h4 className="font-medium">Recent Activity</h4>
                    <Button disabled={refreshing} size="sm" variant="ghost" onClick={handleRefresh}>
                        <RefreshCw className={cn("h-4 w-4", refreshing && "animate-spin")} />
                    </Button>
                </div>
            )}

            <div className="space-y-2">
                {displayTransactions.length === 0 ? (
                    <div className="text-center py-6 text-muted-foreground">
                        <CreditCard className="h-8 w-8 mx-auto mb-2" />
                        <p className="text-sm">No recent transactions</p>
                    </div>
                ) : (
                    displayTransactions.map((transaction) => (
                        <TransactionItem key={transaction.id} transaction={transaction} />
                    ))
                )}
            </div>

            {mockTransactions.length > limit && (
                <div className="text-center pt-2">
                    <Button size="sm" variant="ghost">
                        <Eye className="h-4 w-4 mr-2" />
                        View All Transactions
                    </Button>
                </div>
            )}
        </div>
    );
}

interface TransactionItemProps {
    transaction: (typeof mockTransactions)[0];
}

function TransactionItem({ transaction }: TransactionItemProps) {
    const getStatusBadge = (status: string) => {
        switch (status) {
            case "completed":
                return <Badge className="bg-green-100 text-green-800 text-xs">Completed</Badge>;
            case "pending":
                return <Badge className="bg-yellow-100 text-yellow-800 text-xs">Pending</Badge>;
            case "processing":
                return <Badge className="bg-blue-100 text-blue-800 text-xs">Processing</Badge>;
            case "failed":
                return (
                    <Badge className="text-xs" variant="destructive">
                        Failed
                    </Badge>
                );
            default:
                return (
                    <Badge className="text-xs" variant="outline">
                        {status}
                    </Badge>
                );
        }
    };

    const getTransactionIcon = (type: string, status: string) => {
        if (type === "refund") {
            return <ArrowDownRight className="h-4 w-4 text-orange-600" />;
        }

        switch (status) {
            case "completed":
                return <ArrowUpRight className="h-4 w-4 text-green-600" />;
            case "failed":
                return <ArrowUpRight className="h-4 w-4 text-red-600" />;
            default:
                return <CreditCard className="h-4 w-4 text-blue-600" />;
        }
    };

    const formatDate = (dateString: string) => {
        const date = new Date(dateString);

        return date.toLocaleDateString("en-US", {
            month: "short",
            day: "numeric",
            hour: "2-digit",
            minute: "2-digit",
        });
    };

    const formatAmount = (amount: number, currency: string, type: string) => {
        const prefix = type === "refund" ? "-" : "+";

        return `${prefix}${currency} ${amount.toLocaleString()}`;
    };

    return (
        <div className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50 transition-colors">
            <div className="flex items-center gap-3">
                {getTransactionIcon(transaction.type, transaction.status)}
                <div className="min-w-0 flex-1">
                    <div className="flex items-center gap-2">
                        <p className="font-medium truncate">{transaction.organizationName}</p>
                        {getStatusBadge(transaction.status)}
                    </div>
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                        <span>{transaction.transactionNumber}</span>
                        <span>•</span>
                        <span>{transaction.paymentMethod}</span>
                    </div>
                    <p className="text-xs text-muted-foreground">{transaction.description}</p>
                </div>
            </div>
            <div className="text-right">
                <p
                    className={cn(
                        "font-medium",
                        transaction.type === "refund"
                            ? "text-orange-600"
                            : transaction.status === "completed"
                                ? "text-green-600"
                                : "text-foreground",
                    )}
                >
                    {formatAmount(transaction.amount, transaction.currency, transaction.type)}
                </p>
                <p className="text-xs text-muted-foreground">{formatDate(transaction.date)}</p>
            </div>
        </div>
    );
}
