"use client";

import { useQuery } from "@tanstack/react-query";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Skeleton } from "@/components/ui/skeleton";

import {
    DollarSign,
    TrendingDown,
    AlertTriangle,
    CreditCard,
    Target,
    FileText,
    Download,
    RefreshCw,
    BarChart3,
} from "lucide-react";

import { cn } from "@/lib/utils";
import {
    fetchOrganizationFinancialProfile,
    fetchOrganizationPaymentTimeline,
    OrganizationFinancialProfile as ProfileType,
} from "@/services/FinancialService";

interface OrganizationFinancialProfileProps {
    organizationId: string;
    organizationName?: string;
}

export function OrganizationFinancialProfile({ organizationId, organizationName }: OrganizationFinancialProfileProps) {
    const {
        data: profileData,
        isLoading: profileLoading,
        isError: profileError,
    } = useQuery({
        queryKey: ["organization-financial-profile", organizationId],
        queryFn: () => fetchOrganizationFinancialProfile(organizationId),
        enabled: !!organizationId,
    });

    const { data: timelineData, isLoading: timelineLoading } = useQuery({
        queryKey: ["organization-payment-timeline", organizationId],
        queryFn: () => fetchOrganizationPaymentTimeline(organizationId),
        enabled: !!organizationId,
    });

    const profile = profileData?.data;
    const timeline = timelineData?.data || [];

    if (profileLoading) {
        return <ProfileSkeleton />;
    }

    if (profileError || !profile) {
        return (
            <Card className="p-6">
                <div className="text-center">
                    <AlertTriangle className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <h3 className="text-lg font-semibold mb-2">Profile Not Found</h3>
                    <p className="text-muted-foreground mb-4">
                        Unable to load financial profile for this organization.
                    </p>
                    <Button onClick={() => window.location.reload()}>
                        <RefreshCw className="h-4 w-4 mr-2" />
                        Retry
                    </Button>
                </div>
            </Card>
        );
    }

    return (
        <div className="space-y-6">
            {/* Header */}
            <div className="flex items-center justify-between">
                <div>
                    <h2 className="text-2xl font-bold">{organizationName || organizationId}</h2>
                    <p className="text-muted-foreground">Financial Profile & Payment History</p>
                </div>
                <div className="flex items-center gap-2">
                    <Button size="sm" variant="outline">
                        <Download className="h-4 w-4 mr-2" />
                        Export Report
                    </Button>
                    <Button size="sm" variant="outline">
                        <RefreshCw className="h-4 w-4 mr-2" />
                        Refresh
                    </Button>
                </div>
            </div>

            {/* Financial Summary Cards */}
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Total Fees Paid</CardTitle>
                        <DollarSign className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">
                            MWK {profile.financial_summary.total_fees_paid.toLocaleString()}
                        </div>
                        <p className="text-xs text-muted-foreground">
                            {profile.financial_summary.total_payments} payments
                        </p>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Outstanding Balance</CardTitle>
                        <AlertTriangle className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold text-red-600">
                            MWK {profile.financial_summary.outstanding_balance.toLocaleString()}
                        </div>
                        <p className="text-xs text-muted-foreground">
                            {profile.outstanding_balances.invoice_count} outstanding invoices
                        </p>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Compliance Score</CardTitle>
                        <Target className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold text-green-600">
                            {profile.payment_compliance.compliance_score.toFixed(1)}%
                        </div>
                        <div className="mt-2">
                            <Progress className="h-2" value={profile.payment_compliance.compliance_score} />
                        </div>
                        <p className="text-xs text-muted-foreground mt-1">
                            {profile.payment_compliance.compliance_rating}
                        </p>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Risk Level</CardTitle>
                        <AlertTriangle className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">
                            <RiskBadge risk={profile.risk_assessment.risk_level} />
                        </div>
                        <p className="text-xs text-muted-foreground">Score: {profile.risk_assessment.risk_score}/100</p>
                    </CardContent>
                </Card>
            </div>

            {/* Main Profile Tabs */}
            <Tabs className="space-y-4" defaultValue="overview">
                <TabsList className="grid w-full grid-cols-5">
                    <TabsTrigger value="overview">Overview</TabsTrigger>
                    <TabsTrigger value="payments">Payment History</TabsTrigger>
                    <TabsTrigger value="compliance">Compliance</TabsTrigger>
                    <TabsTrigger value="behavior">Payment Behavior</TabsTrigger>
                    <TabsTrigger value="risk">Risk Assessment</TabsTrigger>
                </TabsList>

                <TabsContent className="space-y-4" value="overview">
                    <div className="grid gap-4 lg:grid-cols-2">
                        {/* Outstanding Balances */}
                        <Card>
                            <CardHeader>
                                <CardTitle>Outstanding Balances</CardTitle>
                                <CardDescription>Aging analysis of unpaid invoices</CardDescription>
                            </CardHeader>
                            <CardContent>
                                <div className="space-y-4">
                                    <div className="grid grid-cols-2 gap-4 text-center">
                                        <div className="p-3 bg-blue-50 rounded">
                                            <p className="text-lg font-bold text-blue-600">
                                                MWK{" "}
                                                {profile.outstanding_balances.aging_analysis.current.toLocaleString()}
                                            </p>
                                            <p className="text-xs text-muted-foreground">Current</p>
                                        </div>
                                        <div className="p-3 bg-yellow-50 rounded">
                                            <p className="text-lg font-bold text-yellow-600">
                                                MWK{" "}
                                                {profile.outstanding_balances.aging_analysis[
                                                    "30_days"
                                                ].toLocaleString()}
                                            </p>
                                            <p className="text-xs text-muted-foreground">30 Days</p>
                                        </div>
                                        <div className="p-3 bg-orange-50 rounded">
                                            <p className="text-lg font-bold text-orange-600">
                                                MWK{" "}
                                                {profile.outstanding_balances.aging_analysis[
                                                    "60_days"
                                                ].toLocaleString()}
                                            </p>
                                            <p className="text-xs text-muted-foreground">60 Days</p>
                                        </div>
                                        <div className="p-3 bg-red-50 rounded">
                                            <p className="text-lg font-bold text-red-600">
                                                MWK{" "}
                                                {profile.outstanding_balances.aging_analysis[
                                                    "90_plus_days"
                                                ].toLocaleString()}
                                            </p>
                                            <p className="text-xs text-muted-foreground">90+ Days</p>
                                        </div>
                                    </div>

                                    <div className="pt-4 border-t">
                                        <div className="flex justify-between text-sm">
                                            <span>Oldest Invoice</span>
                                            <span className="font-medium">
                                                {profile.outstanding_balances.oldest_invoice_days} days ago
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        {/* Payment Methods */}
                        <Card>
                            <CardHeader>
                                <CardTitle>Payment Methods Used</CardTitle>
                                <CardDescription>Preferred payment channels</CardDescription>
                            </CardHeader>
                            <CardContent>
                                <div className="space-y-3">
                                    {profile.payment_methods_used.map((method, index) => (
                                        <div key={index} className="flex items-center justify-between">
                                            <div className="flex items-center gap-3">
                                                <CreditCard className="h-4 w-4 text-muted-foreground" />
                                                <div>
                                                    <span className="text-sm font-medium">{method.payment_method}</span>
                                                    {method.is_preferred && (
                                                        <Badge className="ml-2 text-xs" variant="secondary">
                                                            Preferred
                                                        </Badge>
                                                    )}
                                                </div>
                                            </div>
                                            <div className="text-right">
                                                <p className="text-sm font-medium">{method.usage_count} uses</p>
                                                <p className="text-xs text-muted-foreground">
                                                    MWK {method.total_amount.toLocaleString()}
                                                </p>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            </CardContent>
                        </Card>
                    </div>
                </TabsContent>

                <TabsContent className="space-y-4" value="payments">
                    <Card>
                        <CardHeader>
                            <CardTitle>Payment Timeline</CardTitle>
                            <CardDescription>Recent payment activities and transactions</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <PaymentTimeline payments={profile.payment_history} isLoading={timelineLoading} />
                        </CardContent>
                    </Card>
                </TabsContent>

                <TabsContent className="space-y-4" value="compliance">
                    <div className="grid gap-4 lg:grid-cols-2">
                        <Card>
                            <CardHeader>
                                <CardTitle>Compliance Metrics</CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="space-y-3">
                                    <div>
                                        <div className="flex justify-between text-sm mb-1">
                                            <span>Total Invoices</span>
                                            <span className="font-medium">
                                                {profile.payment_compliance.total_invoices}
                                            </span>
                                        </div>
                                    </div>
                                    <div>
                                        <div className="flex justify-between text-sm mb-1">
                                            <span>Paid Invoices</span>
                                            <span className="font-medium text-green-600">
                                                {profile.payment_compliance.paid_invoices}
                                            </span>
                                        </div>
                                    </div>
                                    <div>
                                        <div className="flex justify-between text-sm mb-1">
                                            <span>Overdue Invoices</span>
                                            <span className="font-medium text-red-600">
                                                {profile.payment_compliance.overdue_invoices}
                                            </span>
                                        </div>
                                    </div>
                                    <div>
                                        <div className="flex justify-between text-sm mb-1">
                                            <span>Average Payment Delay</span>
                                            <span className="font-medium">
                                                {profile.payment_compliance.average_payment_delay_days} days
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader>
                                <CardTitle>Compliance Rating</CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="text-center space-y-4">
                                    <div className="relative w-32 h-32 mx-auto">
                                        <svg className="w-32 h-32 transform -rotate-90" viewBox="0 0 120 120">
                                            <circle
                                                className="text-gray-200"
                                                cx="60"
                                                cy="60"
                                                fill="transparent"
                                                r="54"
                                                stroke="currentColor"
                                                strokeWidth="12"
                                            />
                                            <circle
                                                className="text-green-600"
                                                cx="60"
                                                cy="60"
                                                fill="transparent"
                                                r="54"
                                                stroke="currentColor"
                                                strokeDasharray={`${profile.payment_compliance.compliance_score * 3.39} 339`}
                                                strokeWidth="12"
                                            />
                                        </svg>
                                        <div className="absolute inset-0 flex items-center justify-center">
                                            <span className="text-2xl font-bold">
                                                {profile.payment_compliance.compliance_score.toFixed(0)}%
                                            </span>
                                        </div>
                                    </div>
                                    <Badge
                                        className="text-sm"
                                        variant={
                                            profile.payment_compliance.compliance_score >= 90 ? "default" :
                                            profile.payment_compliance.compliance_score >= 70 ? "secondary" : "destructive"
                                        }
                                    >
                                        {profile.payment_compliance.compliance_rating}
                                    </Badge>
                                </div>
                            </CardContent>
                        </Card>
                    </div>
                </TabsContent>

                <TabsContent className="space-y-4" value="behavior">
                    <div className="grid gap-4 lg:grid-cols-2">
                        <Card>
                            <CardHeader>
                                <CardTitle>Payment Patterns</CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="space-y-3">
                                    <div className="flex justify-between text-sm">
                                        <span>Payment Frequency</span>
                                        <span className="font-medium">
                                            {profile.payment_behavior.payment_frequency}
                                        </span>
                                    </div>
                                    <div className="flex justify-between text-sm">
                                        <span>Average Interval</span>
                                        <span className="font-medium">
                                            {profile.payment_behavior.average_payment_interval_days} days
                                        </span>
                                    </div>
                                    <div className="flex justify-between text-sm">
                                        <span>Total Payments Made</span>
                                        <span className="font-medium">
                                            {profile.payment_behavior.total_payments_made}
                                        </span>
                                    </div>
                                    <div className="flex justify-between text-sm">
                                        <span>Peak Payment Month</span>
                                        <span className="font-medium">
                                            {profile.payment_behavior.peak_payment_month}
                                        </span>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader>
                                <CardTitle>Monthly Distribution</CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="space-y-2">
                                    {Object.entries(profile.payment_behavior.monthly_distribution).map(
                                        ([month, count]) => (
                                            <div key={month} className="flex items-center justify-between">
                                                <span className="text-sm">{month}</span>
                                                <div className="flex items-center gap-2">
                                                    <div className="w-20 bg-gray-200 rounded-full h-2">
                                                        <div
                                                            className="bg-blue-600 h-2 rounded-full"
                                                            style={{
                                                                width: `${Math.min(((count as number) / Math.max(...Object.values(profile.payment_behavior.monthly_distribution))) * 100, 100)}%`,
                                                            }}
                                                        />
                                                    </div>
                                                    <span className="text-sm font-medium w-8">{count}</span>
                                                </div>
                                            </div>
                                        ),
                                    )}
                                </div>
                            </CardContent>
                        </Card>
                    </div>
                </TabsContent>

                <TabsContent className="space-y-4" value="risk">
                    <div className="grid gap-4 lg:grid-cols-2">
                        <Card>
                            <CardHeader>
                                <CardTitle>Risk Assessment</CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="text-center">
                                    <RiskBadge risk={profile.risk_assessment.risk_level} size="large" />
                                    <p className="text-sm text-muted-foreground mt-2">
                                        Risk Score: {profile.risk_assessment.risk_score}/100
                                    </p>
                                </div>

                                <div className="space-y-2">
                                    <p className="font-medium text-sm">Risk Factors:</p>
                                    <ul className="space-y-1">
                                        {profile.risk_assessment.risk_factors.map((factor, index) => (
                                            <li
                                                key={index}
                                                className="text-sm text-muted-foreground flex items-center gap-2"
                                            >
                                                <AlertTriangle className="h-3 w-3" />
                                                {factor}
                                            </li>
                                        ))}
                                    </ul>
                                </div>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader>
                                <CardTitle>Recommendation</CardTitle>
                            </CardHeader>
                            <CardContent>
                                <p className="text-sm text-muted-foreground">
                                    {profile.risk_assessment.recommendation}
                                </p>
                            </CardContent>
                        </Card>
                    </div>
                </TabsContent>
            </Tabs>
        </div>
    );
}

interface RiskBadgeProps {
    risk: string;
    size?: "normal" | "large";
}

function RiskBadge({ risk, size = "normal" }: RiskBadgeProps) {
    const getVariant = (risk: string) => {
        switch (risk.toLowerCase()) {
            case "low":
                return "default";
            case "medium":
                return "secondary";
            case "high":
                return "destructive";
            default:
                return "outline";
        }
    };

    return (
        <Badge variant={getVariant(risk)} className={cn(size === "large" && "text-lg px-4 py-2")}>
            {risk} Risk
        </Badge>
    );
}

interface PaymentTimelineProps {
    payments: ProfileType["payment_history"];
    isLoading: boolean;
}

function PaymentTimeline({ payments, isLoading }: PaymentTimelineProps) {
    if (isLoading) {
        return (
            <div className="space-y-3">
                {Array.from({ length: 5 }).map((_, i) => (
                    <Skeleton key={i} className="h-16 w-full" />
                ))}
            </div>
        );
    }

    if (!payments || payments.length === 0) {
        return (
            <div className="text-center py-8">
                <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <p className="text-muted-foreground">No payment history available</p>
            </div>
        );
    }

    return (
        <div className="space-y-3">
            {payments.map((payment) => (
                <div key={payment.payment_id} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center gap-3">
                        <div
                            className={cn(
                                "w-2 h-2 rounded-full",
                                payment.status === "SUCCESS"
                                    ? "bg-green-500"
                                    : payment.status === "PENDING"
                                      ? "bg-yellow-500"
                                      : "bg-red-500",
                            )}
                        />
                        <div>
                            <p className="font-medium">MWK {payment.amount.toLocaleString()}</p>
                            <p className="text-sm text-muted-foreground">
                                {payment.transaction_number} • {payment.payment_method}
                            </p>
                        </div>
                    </div>
                    <div className="text-right">
                        <Badge
                            variant={
                                payment.status === "SUCCESS"
                                    ? "default"
                                payment.status === "PENDING" ? "secondary" : "destructive"
                            }
                        >
                            {payment.status}
                        </Badge>
                        <p className="text-xs text-muted-foreground mt-1">
                            {new Date(payment.date).toLocaleDateString()}
                        </p>
                    </div>
                </div>
            ))}
        </div>
    );
}

function ProfileSkeleton() {
    return (
        <div className="space-y-6">
            <div className="flex items-center justify-between">
                <div>
                    <Skeleton className="h-8 w-[300px] mb-2" />
                    <Skeleton className="h-4 w-[200px]" />
                </div>
                <div className="flex gap-2">
                    <Skeleton className="h-9 w-[120px]" />
                    <Skeleton className="h-9 w-[80px]" />
                </div>
            </div>

            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                {Array.from({ length: 4 }).map((_, i) => (
                    <Card key={i}>
                        <CardHeader className="space-y-0 pb-2">
                            <Skeleton className="h-4 w-[120px]" />
                        </CardHeader>
                        <CardContent>
                            <Skeleton className="h-8 w-[100px] mb-2" />
                            <Skeleton className="h-3 w-[80px]" />
                        </CardContent>
                    </Card>
                ))}
            </div>

            <div className="grid gap-4 lg:grid-cols-2">
                {Array.from({ length: 4 }).map((_, i) => (
                    <Card key={i}>
                        <CardHeader>
                            <Skeleton className="h-6 w-[200px]" />
                        </CardHeader>
                        <CardContent>
                            <Skeleton className="h-[200px] w-full" />
                        </CardContent>
                    </Card>
                ))}
            </div>
        </div>
    );
}
