"use client";

import { useMemo } from "react";
import { <PERSON><PERSON><PERSON>, Pie, Cell, ResponsiveContainer, <PERSON><PERSON><PERSON>, Legend } from "recharts";

import { Badge } from "@/components/ui/badge";

interface PaymentMethodChartProps {
    data?: Record<string, number>;
}

// Color palette for payment methods
const COLORS = [
    "hsl(var(--primary))",
    "hsl(var(--secondary))",
    "#10b981", // emerald-500
    "#f59e0b", // amber-500
    "#ef4444", // red-500
    "#8b5cf6", // violet-500
    "#06b6d4", // cyan-500
    "#84cc16", // lime-500
];

export function PaymentMethodChart({ data = {} }: PaymentMethodChartProps) {
    const chartData = useMemo(() => {
        return Object.entries(data).map(([method, count], index) => ({
            name: method,
            value: count,
            color: COLORS[index % COLORS.length],
            percentage: 0, // Will be calculated below
        }));
    }, [data]);

    const totalPayments = useMemo(() => {
        return chartData.reduce((sum, item) => sum + item.value, 0);
    }, [chartData]);

    const chartDataWithPercentages = useMemo(() => {
        return chartData.map((item) => ({
            ...item,
            percentage: totalPayments > 0 ? (item.value / totalPayments) * 100 : 0,
        }));
    }, [chartData, totalPayments]);

    const CustomTooltip = ({ active, payload }: any) => {
        if (active && payload && payload.length) {
            const data = payload[0].payload;

            return (
                <div className="bg-background border rounded-lg shadow-lg p-3">
                    <p className="font-medium">{data.name}</p>
                    <p className="text-sm text-muted-foreground">
                        Count: <span className="font-medium text-foreground">{data.value}</span>
                    </p>
                    <p className="text-sm text-muted-foreground">
                        Share: <span className="font-medium text-foreground">{data.percentage.toFixed(1)}%</span>
                    </p>
                </div>
            );
        }

        return null;
    };

    const CustomLegend = ({ payload }: any) => {
        return (
            <div className="flex flex-wrap gap-2 justify-center mt-4">
                {payload?.map((entry: any, index: number) => (
                    <div key={index} className="flex items-center gap-2">
                        <div className="w-3 h-3 rounded-full" style={{ backgroundColor: entry.color }} />
                        <span className="text-xs text-muted-foreground">{entry.value}</span>
                    </div>
                ))}
            </div>
        );
    };

    if (!data || Object.keys(data).length === 0) {
        return (
            <div className="h-[300px] flex items-center justify-center text-muted-foreground">
                <div className="text-center">
                    <p className="text-sm">No payment method data available</p>
                    <p className="text-xs mt-1">Data will appear here once payments are processed</p>
                </div>
            </div>
        );
    }

    return (
        <div className="space-y-4">
            {/* Chart */}
            <div className="h-[250px]">
                <ResponsiveContainer height="100%" width="100%">
                    <PieChart>
                        <Pie
                            cx="50%"
                            cy="50%"
                            data={chartDataWithPercentages}
                            dataKey="value"
                            innerRadius={40}
                            outerRadius={80}
                            paddingAngle={2}
                        >
                            {chartDataWithPercentages.map((entry, index) => (
                                <Cell key={`cell-${index}`} fill={entry.color} />
                            ))}
                        </Pie>
                        <Tooltip content={<CustomTooltip />} />
                        <Legend content={<CustomLegend />} />
                    </PieChart>
                </ResponsiveContainer>
            </div>

            {/* Detailed Breakdown */}
            <div className="space-y-2">
                <h4 className="text-sm font-medium">Payment Method Breakdown</h4>
                <div className="space-y-2">
                    {chartDataWithPercentages
                        .sort((a, b) => b.value - a.value)
                        .map((item, index) => (
                            <div key={item.name} className="flex items-center justify-between">
                                <div className="flex items-center gap-2">
                                    <div className="w-3 h-3 rounded-full" style={{ backgroundColor: item.color }} />
                                    <span className="text-sm">{item.name}</span>
                                    {index === 0 && (
                                        <Badge className="text-xs" variant="secondary">
                                            Most Popular
                                        </Badge>
                                    )}
                                </div>
                                <div className="text-right">
                                    <p className="text-sm font-medium">{item.value}</p>
                                    <p className="text-xs text-muted-foreground">{item.percentage.toFixed(1)}%</p>
                                </div>
                            </div>
                        ))}
                </div>
            </div>

            {/* Summary Stats */}
            <div className="grid grid-cols-2 gap-4 pt-4 border-t">
                <div className="text-center">
                    <p className="text-lg font-bold">{Object.keys(data).length}</p>
                    <p className="text-xs text-muted-foreground">Payment Methods</p>
                </div>
                <div className="text-center">
                    <p className="text-lg font-bold">{totalPayments}</p>
                    <p className="text-xs text-muted-foreground">Total Transactions</p>
                </div>
            </div>
        </div>
    );
}
