"use client";

import { useMemo } from "react";
import { <PERSON>sponsiveContainer, Tooltip, XAxis, YAxis, Area, AreaChart } from "recharts";
import { TrendingUp, TrendingDown } from "lucide-react";

interface RevenueChartProps {
    data?: Array<{
        year: number;
        month: number;
        revenue: number;
        payment_count: number;
        period: string;
    }>;
}

export function RevenueChart({ data = [] }: RevenueChartProps) {
    const chartData = useMemo(() => {
        return data.map((item) => ({
            ...item,
            formattedPeriod: new Date(item.year, item.month - 1).toLocaleDateString("en-US", {
                month: "short",
                year: "2-digit",
            }),
            revenueInK: item.revenue / 1000,
        }));
    }, [data]);

    const totalRevenue = useMemo(() => {
        return data.reduce((sum, item) => sum + item.revenue, 0);
    }, [data]);

    const averageRevenue = useMemo(() => {
        return data.length > 0 ? totalRevenue / data.length : 0;
    }, [totalRevenue, data.length]);

    const trend = useMemo(() => {
        if (data.length < 2) return 0;
        const recent = data[data.length - 1]?.revenue || 0;
        const previous = data[data.length - 2]?.revenue || 0;

        return previous > 0 ? ((recent - previous) / previous) * 100 : 0;
    }, [data]);

    const CustomTooltip = ({ active, payload, label }: any) => {
        if (active && payload && payload.length) {
            const data = payload[0].payload;

            return (
                <div className="bg-background border rounded-lg shadow-lg p-3">
                    <p className="font-medium">{label}</p>
                    <p className="text-sm text-muted-foreground">
                        Revenue:{" "}
                        <span className="font-medium text-foreground">MWK {data.revenue.toLocaleString()}</span>
                    </p>
                    <p className="text-sm text-muted-foreground">
                        Payments: <span className="font-medium text-foreground">{data.payment_count}</span>
                    </p>
                </div>
            );
        }

        return null;
    };

    if (!data || data.length === 0) {
        return (
            <div className="h-[300px] flex items-center justify-center text-muted-foreground">
                <div className="text-center">
                    <p className="text-sm">No revenue data available</p>
                    <p className="text-xs mt-1">Data will appear here once payments are processed</p>
                </div>
            </div>
        );
    }

    return (
        <div className="space-y-4">
            {/* Summary Stats */}
            <div className="grid grid-cols-3 gap-4 text-center">
                <div>
                    <p className="text-2xl font-bold">MWK {(totalRevenue / 1000000).toFixed(1)}M</p>
                    <p className="text-xs text-muted-foreground">Total Revenue</p>
                </div>
                <div>
                    <p className="text-2xl font-bold">MWK {(averageRevenue / 1000).toFixed(0)}K</p>
                    <p className="text-xs text-muted-foreground">Monthly Average</p>
                </div>
                <div className="flex items-center justify-center gap-1">
                    <div className="text-center">
                        <div className="flex items-center justify-center gap-1">
                            {trend >= 0 ? (
                                <TrendingUp className="h-4 w-4 text-green-600" />
                            ) : (
                                <TrendingDown className="h-4 w-4 text-red-600" />
                            )}
                            <p className={`text-2xl font-bold ${trend >= 0 ? "text-green-600" : "text-red-600"}`}>
                                {Math.abs(trend).toFixed(1)}%
                            </p>
                        </div>
                        <p className="text-xs text-muted-foreground">Month Trend</p>
                    </div>
                </div>
            </div>

            {/* Chart */}
            <div className="h-[250px]">
                <ResponsiveContainer height="100%" width="100%">
                    <AreaChart data={chartData}>
                        <defs>
                            <linearGradient id="revenueGradient" x1="0" x2="0" y1="0" y2="1">
                                <stop offset="5%" stopColor="hsl(var(--primary))" stopOpacity={0.3} />
                                <stop offset="95%" stopColor="hsl(var(--primary))" stopOpacity={0} />
                            </linearGradient>
                        </defs>
                        <XAxis axisLine={false} className="text-xs" dataKey="formattedPeriod" tickLine={false} />
                        <YAxis
                            axisLine={false}
                            className="text-xs"
                            tickFormatter={(value) => `${value}K`}
                            tickLine={false}
                        />
                        <Tooltip content={<CustomTooltip />} />
                        <Area
                            dataKey="revenueInK"
                            fill="url(#revenueGradient)"
                            stroke="hsl(var(--primary))"
                            strokeWidth={2}
                            type="monotone"
                        />
                    </AreaChart>
                </ResponsiveContainer>
            </div>
        </div>
    );
}
