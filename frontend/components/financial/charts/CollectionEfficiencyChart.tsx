"use client";

import { useMemo } from "react";
import { Line, ResponsiveContainer, Tooltip, XAxis, YAxis, Bar, ComposedChart } from "recharts";
import { useQuery } from "@tanstack/react-query";
import { TrendingUp, TrendingDown, Target, AlertTriangle } from "lucide-react";

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { fetchPaymentAnalytics } from "@/services/FinancialService";

interface CollectionEfficiencyChartProps {
    className?: string;
}

export function CollectionEfficiencyChart({ className }: CollectionEfficiencyChartProps) {
    const { data: analyticsData, isLoading } = useQuery({
        queryKey: ["payment-analytics", "monthly"],
        queryFn: () => fetchPaymentAnalytics("monthly"),
    });

    const analytics = analyticsData?.data;

    // Mock collection efficiency data - in real app this would come from API
    const mockEfficiencyData = useMemo(
        () => [
            { month: "Jan", efficiency: 92.5, target: 95, invoiced: 120000, collected: 111000 },
            { month: "Feb", efficiency: 94.2, target: 95, invoiced: 135000, collected: 127170 },
            { month: "Mar", efficiency: 96.8, target: 95, invoiced: 142000, collected: 137456 },
            { month: "Apr", efficiency: 89.3, target: 95, invoiced: 156000, collected: 139308 },
            { month: "May", efficiency: 93.7, target: 95, invoiced: 148000, collected: 138676 },
            { month: "Jun", efficiency: 97.1, target: 95, invoiced: 162000, collected: 157302 },
        ],
        [],
    );

    const currentEfficiency = useMemo(() => {
        return mockEfficiencyData[mockEfficiencyData.length - 1]?.efficiency || 0;
    }, [mockEfficiencyData]);

    const averageEfficiency = useMemo(() => {
        const sum = mockEfficiencyData.reduce((acc, item) => acc + item.efficiency, 0);

        return sum / mockEfficiencyData.length;
    }, [mockEfficiencyData]);

    const trend = useMemo(() => {
        if (mockEfficiencyData.length < 2) return 0;
        const current = mockEfficiencyData[mockEfficiencyData.length - 1]?.efficiency || 0;
        const previous = mockEfficiencyData[mockEfficiencyData.length - 2]?.efficiency || 0;

        return current - previous;
    }, [mockEfficiencyData]);

    const CustomTooltip = ({ active, payload, label }: any) => {
        if (active && payload && payload.length) {
            const data = payload[0].payload;

            return (
                <div className="bg-background border rounded-lg shadow-lg p-3">
                    <p className="font-medium">{label}</p>
                    <div className="space-y-1">
                        <p className="text-sm text-muted-foreground">
                            Efficiency:{" "}
                            <span className="font-medium text-foreground">{data.efficiency.toFixed(1)}%</span>
                        </p>
                        <p className="text-sm text-muted-foreground">
                            Target: <span className="font-medium text-foreground">{data.target}%</span>
                        </p>
                        <p className="text-sm text-muted-foreground">
                            Invoiced:{" "}
                            <span className="font-medium text-foreground">MWK {data.invoiced.toLocaleString()}</span>
                        </p>
                        <p className="text-sm text-muted-foreground">
                            Collected:{" "}
                            <span className="font-medium text-foreground">MWK {data.collected.toLocaleString()}</span>
                        </p>
                    </div>
                </div>
            );
        }

        return null;
    };

    if (isLoading) {
        return (
            <div className="space-y-4">
                <div className="grid grid-cols-3 gap-4">
                    {Array.from({ length: 3 }).map((_, i) => (
                        <div key={i} className="animate-pulse">
                            <div className="h-4 bg-gray-200 rounded mb-2" />
                            <div className="h-8 bg-gray-200 rounded" />
                        </div>
                    ))}
                </div>
                <div className="h-[300px] bg-gray-100 rounded animate-pulse" />
            </div>
        );
    }

    return (
        <div className={className}>
            <div className="space-y-6">
                {/* Key Metrics */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <Card>
                        <CardContent className="p-4">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm text-muted-foreground">Current Efficiency</p>
                                    <p className="text-2xl font-bold">{currentEfficiency.toFixed(1)}%</p>
                                </div>
                                <div className="flex items-center gap-2">
                                    {currentEfficiency >= 95 ? (
                                        <Target className="h-5 w-5 text-green-600" />
                                    ) : (
                                        <AlertTriangle className="h-5 w-5 text-yellow-600" />
                                    )}
                                    <Badge variant={currentEfficiency >= 95 ? "default" : "secondary"}>
                                        {currentEfficiency >= 95 ? "On Target" : "Below Target"}
                                    </Badge>
                                </div>
                            </div>
                            <div className="mt-2">
                                <Progress className="h-2" value={currentEfficiency} />
                                <p className="text-xs text-muted-foreground mt-1">Target: 95%</p>
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardContent className="p-4">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm text-muted-foreground">6-Month Average</p>
                                    <p className="text-2xl font-bold">{averageEfficiency.toFixed(1)}%</p>
                                </div>
                                <Badge variant="outline">Stable</Badge>
                            </div>
                            <p className="text-xs text-muted-foreground mt-2">Consistent performance across months</p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardContent className="p-4">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm text-muted-foreground">Month Trend</p>
                                    <div className="flex items-center gap-2">
                                        <p className="text-2xl font-bold">
                                            {trend >= 0 ? "+" : ""}
                                            {trend.toFixed(1)}%
                                        </p>
                                        {trend >= 0 ? (
                                            <TrendingUp className="h-4 w-4 text-green-600" />
                                        ) : (
                                            <TrendingDown className="h-4 w-4 text-red-600" />
                                        )}
                                    </div>
                                </div>
                                <Badge variant={trend >= 0 ? "default" : "destructive"}>
                                    {trend >= 0 ? "Improving" : "Declining"}
                                </Badge>
                            </div>
                            <p className="text-xs text-muted-foreground mt-2">vs previous month</p>
                        </CardContent>
                    </Card>
                </div>

                {/* Chart */}
                <Card>
                    <CardHeader>
                        <CardTitle>Collection Efficiency Trend</CardTitle>
                        <CardDescription>
                            Monthly collection efficiency vs target with invoice and collection amounts
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="h-[350px]">
                            <ResponsiveContainer height="100%" width="100%">
                                <ComposedChart data={mockEfficiencyData}>
                                    <XAxis axisLine={false} className="text-xs" dataKey="month" tickLine={false} />
                                    <YAxis
                                        axisLine={false}
                                        className="text-xs"
                                        domain={[80, 100]}
                                        orientation="left"
                                        tickFormatter={(value) => `${value}%`}
                                        tickLine={false}
                                        yAxisId="efficiency"
                                    />
                                    <YAxis
                                        axisLine={false}
                                        className="text-xs"
                                        orientation="right"
                                        tickFormatter={(value) => `${(value / 1000).toFixed(0)}K`}
                                        tickLine={false}
                                        yAxisId="amount"
                                    />
                                    <Tooltip content={<CustomTooltip />} />
                                    <Bar
                                        dataKey="invoiced"
                                        fill="hsl(var(--muted))"
                                        opacity={0.6}
                                        radius={[2, 2, 0, 0]}
                                        yAxisId="amount"
                                    />
                                    <Bar
                                        dataKey="collected"
                                        fill="hsl(var(--primary))"
                                        opacity={0.8}
                                        radius={[2, 2, 0, 0]}
                                        yAxisId="amount"
                                    />
                                    <Line
                                        dataKey="efficiency"
                                        dot={{ fill: "hsl(var(--destructive))", strokeWidth: 2, r: 4 }}
                                        stroke="hsl(var(--destructive))"
                                        strokeWidth={3}
                                        type="monotone"
                                        yAxisId="efficiency"
                                    />
                                    <Line
                                        dataKey="target"
                                        dot={false}
                                        stroke="hsl(var(--muted-foreground))"
                                        strokeDasharray="5 5"
                                        strokeWidth={2}
                                        type="monotone"
                                        yAxisId="efficiency"
                                    />
                                </ComposedChart>
                            </ResponsiveContainer>
                        </div>

                        {/* Legend */}
                        <div className="flex items-center justify-center gap-6 mt-4 text-sm">
                            <div className="flex items-center gap-2">
                                <div className="w-3 h-3 bg-muted rounded" />
                                <span>Invoiced</span>
                            </div>
                            <div className="flex items-center gap-2">
                                <div className="w-3 h-3 bg-primary rounded" />
                                <span>Collected</span>
                            </div>
                            <div className="flex items-center gap-2">
                                <div className="w-4 h-0.5 bg-destructive" />
                                <span>Efficiency</span>
                            </div>
                            <div className="flex items-center gap-2">
                                <div className="w-4 h-0.5 bg-muted-foreground border-dashed border-t" />
                                <span>Target</span>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                {/* Insights */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <Card>
                        <CardHeader>
                            <CardTitle className="text-base">Performance Insights</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-3">
                                <div className="flex items-start gap-3 p-3 rounded-lg bg-green-50">
                                    <div className="w-2 h-2 rounded-full bg-green-600 mt-2" />
                                    <div>
                                        <p className="text-sm font-medium">Strong Performance</p>
                                        <p className="text-sm text-muted-foreground">
                                            Current efficiency above industry average of 89%
                                        </p>
                                    </div>
                                </div>
                                <div className="flex items-start gap-3 p-3 rounded-lg bg-blue-50">
                                    <div className="w-2 h-2 rounded-full bg-blue-600 mt-2" />
                                    <div>
                                        <p className="text-sm font-medium">Consistent Improvement</p>
                                        <p className="text-sm text-muted-foreground">
                                            3 out of last 6 months exceeded target
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader>
                            <CardTitle className="text-base">Recommendations</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-3">
                                <div className="flex items-start gap-3 p-3 rounded-lg bg-yellow-50">
                                    <div className="w-2 h-2 rounded-full bg-yellow-600 mt-2" />
                                    <div>
                                        <p className="text-sm font-medium">Focus on April Performance</p>
                                        <p className="text-sm text-muted-foreground">
                                            Investigate factors causing efficiency drop
                                        </p>
                                    </div>
                                </div>
                                <div className="flex items-start gap-3 p-3 rounded-lg bg-purple-50">
                                    <div className="w-2 h-2 rounded-full bg-purple-600 mt-2" />
                                    <div>
                                        <p className="text-sm font-medium">Implement Auto-Reminders</p>
                                        <p className="text-sm text-muted-foreground">
                                            Reduce collection time with automated follow-ups
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </div>
    );
}
