"use client";

import { useState } from "react";
import { useMutation } from "@tanstack/react-query";
import {
    Download,
    FileText,
    Calendar as CalendarIcon,
    RefreshCw,
    Clock,
    CheckCircle,
    AlertTriangle,
    Bar<PERSON>hart3,
    <PERSON><PERSON>hart,
    TrendingUp,
    DollarSign,
    Users,
    Target,
} from "lucide-react";
import { format } from "date-fns";

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import {
    generateMonthlyRevenueReport,
    generatePaymentReconciliationReport,
    generateOrganizationPaymentHistoryReport,
    generateOutstandingReceivablesReport,
} from "@/services/FinancialService";

export function FinancialReports() {
    const [selectedTab, setSelectedTab] = useState("revenue");
    const [dateRange, setDateRange] = useState<{
        from: Date | undefined;
        to: Date | undefined;
    }>({
        from: new Date(new Date().getFullYear(), new Date().getMonth(), 1),
        to: new Date(),
    });
    const [selectedOrganization, setSelectedOrganization] = useState("");

    // Report generation mutations
    const monthlyRevenueMutation = useMutation({
        mutationFn: ({ month, year }: { month: number; year: number }) => generateMonthlyRevenueReport(month, year),
    });

    const reconciliationMutation = useMutation({
        mutationFn: ({ startDate, endDate, orgId }: { startDate: string; endDate: string; orgId?: string }) =>
            generatePaymentReconciliationReport(startDate, endDate, orgId),
    });

    const paymentHistoryMutation = useMutation({
        mutationFn: ({ orgId, includePending }: { orgId: string; includePending: boolean }) =>
            generateOrganizationPaymentHistoryReport(orgId, includePending),
    });

    const outstandingMutation = useMutation({
        mutationFn: ({ asOfDate }: { asOfDate?: string }) => generateOutstandingReceivablesReport(asOfDate),
    });

    const generateReport = (reportType: string) => {
        const startDate = dateRange.from ? format(dateRange.from, "yyyy-MM-dd") : "";
        const endDate = dateRange.to ? format(dateRange.to, "yyyy-MM-dd") : "";

        switch (reportType) {
            case "monthly-revenue":
                if (dateRange.from) {
                    monthlyRevenueMutation.mutate({
                        month: dateRange.from.getMonth() + 1,
                        year: dateRange.from.getFullYear(),
                    });
                }
                break;
            case "reconciliation":
                if (startDate && endDate) {
                    reconciliationMutation.mutate({
                        startDate,
                        endDate,
                        orgId: selectedOrganization || undefined,
                    });
                }
                break;
            case "payment-history":
                if (selectedOrganization) {
                    paymentHistoryMutation.mutate({
                        orgId: selectedOrganization,
                        includePending: true,
                    });
                }
                break;
            case "outstanding":
                outstandingMutation.mutate({
                    asOfDate: endDate || undefined,
                });
                break;
        }
    };

    // Mock data for report history
    const reportHistory = [
        {
            id: "rep-001",
            name: "Monthly Revenue Report - January 2024",
            type: "Revenue",
            generatedAt: "2024-01-31T23:59:00Z",
            status: "completed",
            size: "2.4 MB",
        },
        {
            id: "rep-002",
            name: "Payment Reconciliation - Q4 2023",
            type: "Reconciliation",
            generatedAt: "2024-01-15T14:30:00Z",
            status: "completed",
            size: "5.1 MB",
        },
        {
            id: "rep-003",
            name: "Outstanding Receivables Report",
            type: "Outstanding",
            generatedAt: "2024-01-30T09:15:00Z",
            status: "generating",
            size: "Pending",
        },
    ];

    return (
        <div className="space-y-6">
            {/* Header */}
            <div className="flex items-center justify-between">
                <div>
                    <h2 className="text-2xl font-bold">Financial Reports</h2>
                    <p className="text-muted-foreground">Generate and manage comprehensive financial reports</p>
                </div>
                <Button variant="outline">
                    <RefreshCw className="h-4 w-4 mr-2" />
                    Refresh
                </Button>
            </div>

            {/* Report Summary Cards */}
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Reports Generated</CardTitle>
                        <FileText className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">47</div>
                        <p className="text-xs text-muted-foreground">This month</p>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Scheduled Reports</CardTitle>
                        <Clock className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">12</div>
                        <p className="text-xs text-muted-foreground">Automated reports</p>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Total Downloads</CardTitle>
                        <Download className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">234</div>
                        <p className="text-xs text-muted-foreground">All time</p>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Storage Used</CardTitle>
                        <BarChart3 className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">1.2 GB</div>
                        <p className="text-xs text-muted-foreground">Report archives</p>
                    </CardContent>
                </Card>
            </div>

            {/* Main Reports Interface */}
            <Tabs className="space-y-4" value={selectedTab} onValueChange={setSelectedTab}>
                <TabsList className="grid w-full grid-cols-4">
                    <TabsTrigger value="revenue">Revenue Reports</TabsTrigger>
                    <TabsTrigger value="reconciliation">Reconciliation</TabsTrigger>
                    <TabsTrigger value="organization">Organization Reports</TabsTrigger>
                    <TabsTrigger value="history">Report History</TabsTrigger>
                </TabsList>

                <TabsContent className="space-y-4" value="revenue">
                    <div className="grid gap-6 lg:grid-cols-2">
                        {/* Monthly Revenue Report */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <DollarSign className="h-5 w-5" />
                                    Monthly Revenue Report
                                </CardTitle>
                                <CardDescription>Comprehensive revenue analysis for a specific month</CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="space-y-2">
                                    <Label>Select Month</Label>
                                    <Popover>
                                        <PopoverTrigger asChild>
                                            <Button className="w-full justify-start" variant="outline">
                                                <CalendarIcon className="mr-2 h-4 w-4" />
                                                {dateRange.from ? format(dateRange.from, "MMMM yyyy") : "Pick a month"}
                                            </Button>
                                        </PopoverTrigger>
                                        <PopoverContent align="start" className="w-auto p-0">
                                            <Calendar
                                                initialFocus
                                                mode="single"
                                                selected={dateRange.from}
                                                onSelect={(date) => setDateRange({ ...dateRange, from: date })}
                                            />
                                        </PopoverContent>
                                    </Popover>
                                </div>

                                <Button
                                    className="w-full"
                                    disabled={monthlyRevenueMutation.isPending || !dateRange.from}
                                    onClick={() => generateReport("monthly-revenue")}
                                >
                                    {monthlyRevenueMutation.isPending ? (
                                        <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                                    ) : (
                                        <Download className="h-4 w-4 mr-2" />
                                    )}
                                    Generate Revenue Report
                                </Button>

                                {monthlyRevenueMutation.isError && (
                                    <div className="p-3 bg-red-50 border border-red-200 rounded text-red-700 text-sm">
                                        <AlertTriangle className="h-4 w-4 inline mr-2" />
                                        Failed to generate report
                                    </div>
                                )}

                                {monthlyRevenueMutation.isSuccess && (
                                    <div className="p-3 bg-green-50 border border-green-200 rounded text-green-700 text-sm">
                                        <CheckCircle className="h-4 w-4 inline mr-2" />
                                        Report generated successfully
                                    </div>
                                )}
                            </CardContent>
                        </Card>

                        {/* Revenue Summary Reports */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <TrendingUp className="h-5 w-5" />
                                    Quick Revenue Reports
                                </CardTitle>
                                <CardDescription>Pre-configured revenue analysis reports</CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-3">
                                <Button className="w-full justify-start" variant="outline">
                                    <PieChart className="h-4 w-4 mr-2" />
                                    Revenue by Application Type
                                </Button>
                                <Button className="w-full justify-start" variant="outline">
                                    <BarChart3 className="h-4 w-4 mr-2" />
                                    Year-over-Year Comparison
                                </Button>
                                <Button className="w-full justify-start" variant="outline">
                                    <Target className="h-4 w-4 mr-2" />
                                    Revenue vs Targets
                                </Button>
                                <Button className="w-full justify-start" variant="outline">
                                    <Users className="h-4 w-4 mr-2" />
                                    Top Revenue Contributors
                                </Button>
                            </CardContent>
                        </Card>
                    </div>
                </TabsContent>

                <TabsContent className="space-y-4" value="reconciliation">
                    <div className="grid gap-6 lg:grid-cols-2">
                        {/* Payment Reconciliation Report */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <CheckCircle className="h-5 w-5" />
                                    Payment Reconciliation Report
                                </CardTitle>
                                <CardDescription>Detailed payment reconciliation analysis</CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="grid grid-cols-2 gap-4">
                                    <div className="space-y-2">
                                        <Label>Start Date</Label>
                                        <Popover>
                                            <PopoverTrigger asChild>
                                                <Button className="w-full justify-start" variant="outline">
                                                    <CalendarIcon className="mr-2 h-4 w-4" />
                                                    {dateRange.from ? format(dateRange.from, "MMM dd") : "Start"}
                                                </Button>
                                            </PopoverTrigger>
                                            <PopoverContent align="start" className="w-auto p-0">
                                                <Calendar
                                                    initialFocus
                                                    mode="single"
                                                    selected={dateRange.from}
                                                    onSelect={(date) => setDateRange({ ...dateRange, from: date })}
                                                />
                                            </PopoverContent>
                                        </Popover>
                                    </div>
                                    <div className="space-y-2">
                                        <Label>End Date</Label>
                                        <Popover>
                                            <PopoverTrigger asChild>
                                                <Button className="w-full justify-start" variant="outline">
                                                    <CalendarIcon className="mr-2 h-4 w-4" />
                                                    {dateRange.to ? format(dateRange.to, "MMM dd") : "End"}
                                                </Button>
                                            </PopoverTrigger>
                                            <PopoverContent align="start" className="w-auto p-0">
                                                <Calendar
                                                    initialFocus
                                                    mode="single"
                                                    selected={dateRange.to}
                                                    onSelect={(date) => setDateRange({ ...dateRange, to: date })}
                                                />
                                            </PopoverContent>
                                        </Popover>
                                    </div>
                                </div>

                                <div className="space-y-2">
                                    <Label>Organization (Optional)</Label>
                                    <Input
                                        placeholder="Enter organization ID"
                                        value={selectedOrganization}
                                        onChange={(e) => setSelectedOrganization(e.target.value)}
                                    />
                                </div>

                                <Button
                                    className="w-full"
                                    disabled={reconciliationMutation.isPending || !dateRange.from || !dateRange.to}
                                    onClick={() => generateReport("reconciliation")}
                                >
                                    {reconciliationMutation.isPending ? (
                                        <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                                    ) : (
                                        <Download className="h-4 w-4 mr-2" />
                                    )}
                                    Generate Reconciliation Report
                                </Button>

                                {reconciliationMutation.isError && (
                                    <div className="p-3 bg-red-50 border border-red-200 rounded text-red-700 text-sm">
                                        <AlertTriangle className="h-4 w-4 inline mr-2" />
                                        Failed to generate report
                                    </div>
                                )}

                                {reconciliationMutation.isSuccess && (
                                    <div className="p-3 bg-green-50 border border-green-200 rounded text-green-700 text-sm">
                                        <CheckCircle className="h-4 w-4 inline mr-2" />
                                        Report generated successfully
                                    </div>
                                )}
                            </CardContent>
                        </Card>

                        {/* Outstanding Receivables */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <AlertTriangle className="h-5 w-5" />
                                    Outstanding Receivables
                                </CardTitle>
                                <CardDescription>Report on unpaid invoices and overdue amounts</CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="space-y-2">
                                    <Label>As of Date</Label>
                                    <Popover>
                                        <PopoverTrigger asChild>
                                            <Button className="w-full justify-start" variant="outline">
                                                <CalendarIcon className="mr-2 h-4 w-4" />
                                                {dateRange.to ? format(dateRange.to, "PPP") : "Select date"}
                                            </Button>
                                        </PopoverTrigger>
                                        <PopoverContent align="start" className="w-auto p-0">
                                            <Calendar
                                                initialFocus
                                                mode="single"
                                                selected={dateRange.to}
                                                onSelect={(date) => setDateRange({ ...dateRange, to: date })}
                                            />
                                        </PopoverContent>
                                    </Popover>
                                </div>

                                <Button
                                    className="w-full"
                                    disabled={outstandingMutation.isPending}
                                    onClick={() => generateReport("outstanding")}
                                >
                                    {outstandingMutation.isPending ? (
                                        <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                                    ) : (
                                        <Download className="h-4 w-4 mr-2" />
                                    )}
                                    Generate Outstanding Report
                                </Button>

                                <div className="text-sm text-muted-foreground space-y-1">
                                    <p>• Aging analysis by 30, 60, 90+ days</p>
                                    <p>• Organization-wise breakdown</p>
                                    <p>• Payment reminders status</p>
                                </div>

                                {outstandingMutation.isError && (
                                    <div className="p-3 bg-red-50 border border-red-200 rounded text-red-700 text-sm">
                                        <AlertTriangle className="h-4 w-4 inline mr-2" />
                                        Failed to generate report
                                    </div>
                                )}

                                {outstandingMutation.isSuccess && (
                                    <div className="p-3 bg-green-50 border border-green-200 rounded text-green-700 text-sm">
                                        <CheckCircle className="h-4 w-4 inline mr-2" />
                                        Report generated successfully
                                    </div>
                                )}
                            </CardContent>
                        </Card>
                    </div>
                </TabsContent>

                <TabsContent className="space-y-4" value="organization">
                    <div className="grid gap-6 lg:grid-cols-2">
                        {/* Organization Payment History */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <Users className="h-5 w-5" />
                                    Organization Payment History
                                </CardTitle>
                                <CardDescription>Detailed payment history for specific organizations</CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="space-y-2">
                                    <Label>Organization ID</Label>
                                    <Input
                                        placeholder="Enter organization ID"
                                        value={selectedOrganization}
                                        onChange={(e) => setSelectedOrganization(e.target.value)}
                                    />
                                </div>

                                <Button
                                    className="w-full"
                                    disabled={paymentHistoryMutation.isPending || !selectedOrganization}
                                    onClick={() => generateReport("payment-history")}
                                >
                                    {paymentHistoryMutation.isPending ? (
                                        <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                                    ) : (
                                        <Download className="h-4 w-4 mr-2" />
                                    )}
                                    Generate Payment History
                                </Button>

                                <div className="text-sm text-muted-foreground space-y-1">
                                    <p>• Complete payment timeline</p>
                                    <p>• Payment methods used</p>
                                    <p>• Compliance analysis</p>
                                    <p>• Outstanding balances</p>
                                </div>

                                {paymentHistoryMutation.isError && (
                                    <div className="p-3 bg-red-50 border border-red-200 rounded text-red-700 text-sm">
                                        <AlertTriangle className="h-4 w-4 inline mr-2" />
                                        Failed to generate report
                                    </div>
                                )}

                                {paymentHistoryMutation.isSuccess && (
                                    <div className="p-3 bg-green-50 border border-green-200 rounded text-green-700 text-sm">
                                        <CheckCircle className="h-4 w-4 inline mr-2" />
                                        Report generated successfully
                                    </div>
                                )}
                            </CardContent>
                        </Card>

                        {/* Bulk Organization Reports */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <BarChart3 className="h-5 w-5" />
                                    Bulk Organization Reports
                                </CardTitle>
                                <CardDescription>Generate reports for multiple organizations</CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-3">
                                <Button className="w-full justify-start" variant="outline">
                                    <Users className="h-4 w-4 mr-2" />
                                    All Organizations Summary
                                </Button>
                                <Button className="w-full justify-start" variant="outline">
                                    <AlertTriangle className="h-4 w-4 mr-2" />
                                    High-Risk Organizations
                                </Button>
                                <Button className="w-full justify-start" variant="outline">
                                    <CheckCircle className="h-4 w-4 mr-2" />
                                    Top Performing Organizations
                                </Button>
                                <Button className="w-full justify-start" variant="outline">
                                    <Clock className="h-4 w-4 mr-2" />
                                    Organizations with Overdue Payments
                                </Button>
                            </CardContent>
                        </Card>
                    </div>
                </TabsContent>

                <TabsContent className="space-y-4" value="history">
                    <Card>
                        <CardHeader>
                            <CardTitle>Report History</CardTitle>
                            <CardDescription>Previously generated reports and their status</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-3">
                                {reportHistory.map((report) => (
                                    <ReportHistoryItem key={report.id} report={report} />
                                ))}
                            </div>
                        </CardContent>
                    </Card>
                </TabsContent>
            </Tabs>
        </div>
    );
}

interface ReportHistoryItemProps {
    report: {
        id: string;
        name: string;
        type: string;
        generatedAt: string;
        status: string;
        size: string;
    };
}

function ReportHistoryItem({ report }: ReportHistoryItemProps) {
    const getStatusBadge = (status: string) => {
        switch (status) {
            case "completed":
                return <Badge className="bg-green-100 text-green-800">Completed</Badge>;
            case "generating":
                return <Badge className="bg-yellow-100 text-yellow-800">Generating</Badge>;
            case "failed":
                return <Badge variant="destructive">Failed</Badge>;
            default:
                return <Badge variant="outline">{status}</Badge>;
        }
    };

    const getStatusIcon = (status: string) => {
        switch (status) {
            case "completed":
                return <CheckCircle className="h-4 w-4 text-green-600" />;
            case "generating":
                return <Clock className="h-4 w-4 text-yellow-600" />;
            case "failed":
                return <AlertTriangle className="h-4 w-4 text-red-600" />;
            default:
                return <FileText className="h-4 w-4 text-muted-foreground" />;
        }
    };

    return (
        <div className="flex items-center justify-between p-4 border rounded-lg">
            <div className="flex items-center gap-3">
                {getStatusIcon(report.status)}
                <div>
                    <p className="font-medium">{report.name}</p>
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                        <span>{report.type}</span>
                        <span>•</span>
                        <span>{new Date(report.generatedAt).toLocaleDateString()}</span>
                        <span>•</span>
                        <span>{report.size}</span>
                    </div>
                </div>
            </div>
            <div className="flex items-center gap-2">
                {getStatusBadge(report.status)}
                {report.status === "completed" && (
                    <Button size="sm" variant="outline">
                        <Download className="h-4 w-4 mr-2" />
                        Download
                    </Button>
                )}
            </div>
        </div>
    );
}
