"use client";

import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useState } from "react";
import {
    Search,
    Download,
    RefreshCw,
    CheckCircle,
    AlertTriangle,
    Calculator,
    FileText,
    TrendingUp,
    Settings,
} from "lucide-react";

import { PaymentDiscrepancies } from "./PaymentDiscrepancies";

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { cn } from "@/lib/utils";
import {
    reconcilePaymentWithFees,
    fetchPaymentReconciliationSummary,
    PaymentReconciliation as PaymentReconciliationType,
} from "@/services/FinancialService";

export function PaymentReconciliation() {
    const [selectedApplicationId, setSelectedApplicationId] = useState("");
    const [searchTerm, setSearchTerm] = useState("");

    const queryClient = useQueryClient();

    const { data: summaryData, isLoading: summaryLoading } = useQuery({
        queryKey: ["payment-reconciliation-summary"],
        queryFn: () => fetchPaymentReconciliationSummary(),
        refetchInterval: 300000,
    });

    const reconcileMutation = useMutation({
        mutationFn: (applicationId: string) => reconcilePaymentWithFees(applicationId),
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ["payment-reconciliation-summary"] });
        },
    });

    const handleReconcile = (applicationId: string) => {
        setSelectedApplicationId(applicationId);
        reconcileMutation.mutate(applicationId);
    };

    return (
        <div className="space-y-6">
            {/* Summary Stats */}
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Total Payments</CardTitle>
                        <Calculator className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">{summaryData?.data?.total_discrepancies || 0}</div>
                        <p className="text-xs text-muted-foreground">Processed this month</p>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Reconciliation Rate</CardTitle>
                        <TrendingUp className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold text-green-600">94.2%</div>
                        <div className="mt-2">
                            <Progress className="h-2" value={94.2} />
                        </div>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Auto-Matched</CardTitle>
                        <CheckCircle className="h-4 w-4 text-green-600" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">847</div>
                        <p className="text-xs text-muted-foreground">No manual intervention</p>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Requires Review</CardTitle>
                        <AlertTriangle className="h-4 w-4 text-yellow-600" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold text-yellow-600">
                            {summaryData?.data?.requires_immediate_attention || 0}
                        </div>
                        <p className="text-xs text-muted-foreground">Manual review needed</p>
                    </CardContent>
                </Card>
            </div>

            {/* Main Reconciliation Interface */}
            <Tabs className="space-y-4" defaultValue="discrepancies">
                <TabsList className="grid w-full grid-cols-4">
                    <TabsTrigger value="discrepancies">Discrepancies</TabsTrigger>
                    <TabsTrigger value="reconcile">Reconcile Payments</TabsTrigger>
                    <TabsTrigger value="reports">Reports</TabsTrigger>
                    <TabsTrigger value="settings">Settings</TabsTrigger>
                </TabsList>

                <TabsContent value="discrepancies">
                    <PaymentDiscrepancies />
                </TabsContent>

                <TabsContent className="space-y-4" value="reconcile">
                    <Card>
                        <CardHeader>
                            <CardTitle>Manual Reconciliation</CardTitle>
                            <CardDescription>Reconcile individual payments with calculated fees</CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="flex items-center gap-4">
                                <div className="relative flex-1">
                                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                                    <Input
                                        className="pl-9"
                                        placeholder="Enter application ID to reconcile..."
                                        value={selectedApplicationId}
                                        onChange={(e) => setSelectedApplicationId(e.target.value)}
                                    />
                                </div>
                                <Button
                                    disabled={!selectedApplicationId || reconcileMutation.isPending}
                                    onClick={() => handleReconcile(selectedApplicationId)}
                                >
                                    {reconcileMutation.isPending ? (
                                        <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                                    ) : (
                                        <Calculator className="h-4 w-4 mr-2" />
                                    )}
                                    Reconcile
                                </Button>
                            </div>

                            {reconcileMutation.data && <ReconciliationResult result={reconcileMutation.data.data} />}

                            {reconcileMutation.isError && (
                                <Card className="border-red-200 bg-red-50">
                                    <CardContent className="p-4">
                                        <div className="flex items-center gap-2">
                                            <AlertTriangle className="h-4 w-4 text-red-600" />
                                            <span className="text-red-600 font-medium">Reconciliation failed</span>
                                        </div>
                                        <p className="text-red-600 text-sm mt-1">
                                            Unable to reconcile payment. Please check the application ID and try again.
                                        </p>
                                    </CardContent>
                                </Card>
                            )}
                        </CardContent>
                    </Card>

                    {/* Bulk Reconciliation */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Bulk Reconciliation</CardTitle>
                            <CardDescription>Process multiple payments at once</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-4">
                                <div className="p-4 border-2 border-dashed border-gray-300 rounded-lg text-center">
                                    <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                                    <p className="text-sm text-muted-foreground mb-2">
                                        Upload CSV file with application IDs
                                    </p>
                                    <Button variant="outline">Choose File</Button>
                                </div>

                                <div className="text-center">
                                    <Button disabled className="w-full">
                                        <Calculator className="h-4 w-4 mr-2" />
                                        Process Bulk Reconciliation
                                    </Button>
                                    <p className="text-xs text-muted-foreground mt-2">
                                        Upload a file to enable bulk processing
                                    </p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                </TabsContent>

                <TabsContent className="space-y-4" value="reports">
                    <div className="grid gap-4 md:grid-cols-2">
                        <Card>
                            <CardHeader>
                                <CardTitle>Reconciliation Reports</CardTitle>
                                <CardDescription>Generate detailed reconciliation reports</CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-3">
                                <Button className="w-full justify-start" variant="outline">
                                    <Download className="h-4 w-4 mr-2" />
                                    Daily Reconciliation Report
                                </Button>
                                <Button className="w-full justify-start" variant="outline">
                                    <Download className="h-4 w-4 mr-2" />
                                    Monthly Discrepancy Report
                                </Button>
                                <Button className="w-full justify-start" variant="outline">
                                    <Download className="h-4 w-4 mr-2" />
                                    Outstanding Payments Report
                                </Button>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader>
                                <CardTitle>Reconciliation Insights</CardTitle>
                                <CardDescription>Key metrics and patterns</CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="space-y-2">
                                    <div className="flex justify-between text-sm">
                                        <span>Average Processing Time</span>
                                        <span className="font-medium">2.3 hours</span>
                                    </div>
                                    <div className="flex justify-between text-sm">
                                        <span>Most Common Discrepancy</span>
                                        <span className="font-medium">Overpayment</span>
                                    </div>
                                    <div className="flex justify-between text-sm">
                                        <span>Peak Processing Hour</span>
                                        <span className="font-medium">2:00 PM</span>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    </div>
                </TabsContent>

                <TabsContent className="space-y-4" value="settings">
                    <Card>
                        <CardHeader>
                            <CardTitle>Reconciliation Settings</CardTitle>
                            <CardDescription>Configure automatic reconciliation rules</CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="space-y-4">
                                <div className="flex items-center justify-between">
                                    <div>
                                        <p className="font-medium">Auto-reconciliation</p>
                                        <p className="text-sm text-muted-foreground">
                                            Automatically reconcile exact matches
                                        </p>
                                    </div>
                                    <Button size="sm" variant="outline">
                                        <Settings className="h-4 w-4 mr-2" />
                                        Configure
                                    </Button>
                                </div>

                                <div className="flex items-center justify-between">
                                    <div>
                                        <p className="font-medium">Tolerance Settings</p>
                                        <p className="text-sm text-muted-foreground">
                                            Set acceptable variance thresholds
                                        </p>
                                    </div>
                                    <Button size="sm" variant="outline">
                                        <Settings className="h-4 w-4 mr-2" />
                                        Configure
                                    </Button>
                                </div>

                                <div className="flex items-center justify-between">
                                    <div>
                                        <p className="font-medium">Notification Rules</p>
                                        <p className="text-sm text-muted-foreground">
                                            Alert settings for discrepancies
                                        </p>
                                    </div>
                                    <Button size="sm" variant="outline">
                                        <Settings className="h-4 w-4 mr-2" />
                                        Configure
                                    </Button>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                </TabsContent>
            </Tabs>
        </div>
    );
}

interface ReconciliationResultProps {
    result: PaymentReconciliationType;
}

function ReconciliationResult({ result }: ReconciliationResultProps) {
    const getStatusColor = (status: string) => {
        switch (status) {
            case "exact":
                return "text-green-600";
            case "overpaid":
                return "text-blue-600";
            case "underpaid":
                return "text-red-600";
            default:
                return "text-gray-600";
        }
    };

    const getStatusBadge = (status: string) => {
        switch (status) {
            case "exact":
                return <Badge className="bg-green-100 text-green-800">Exact Match</Badge>;
            case "overpaid":
                return <Badge className="bg-blue-100 text-blue-800">Overpaid</Badge>;
            case "underpaid":
                return <Badge variant="destructive">Underpaid</Badge>;
            default:
                return <Badge variant="outline">Unknown</Badge>;
        }
    };

    return (
        <Card className="border-green-200 bg-green-50">
            <CardContent className="p-4">
                <div className="flex items-start justify-between mb-4">
                    <div className="flex items-center gap-2">
                        <CheckCircle className="h-5 w-5 text-green-600" />
                        <span className="font-medium text-green-800">Reconciliation Complete</span>
                    </div>
                    {getStatusBadge(result.status)}
                </div>

                <div className="grid grid-cols-3 gap-4 text-sm">
                    <div>
                        <p className="text-muted-foreground">Payment Amount</p>
                        <p className="font-medium">MWK {result.payment_amount.toLocaleString()}</p>
                    </div>
                    <div>
                        <p className="text-muted-foreground">Calculated Fees</p>
                        <p className="font-medium">MWK {result.calculated_fees.toLocaleString()}</p>
                    </div>
                    <div>
                        <p className="text-muted-foreground">Difference</p>
                        <p className={cn("font-medium", getStatusColor(result.status))}>
                            {result.difference >= 0 ? "+" : ""}MWK {result.difference.toLocaleString()}
                        </p>
                    </div>
                </div>

                {result.reconciliation_notes && (
                    <div className="mt-4 p-3 bg-white rounded border">
                        <p className="text-sm font-medium mb-1">Notes:</p>
                        <p className="text-sm text-muted-foreground">{result.reconciliation_notes}</p>
                    </div>
                )}

                {result.requires_action && (
                    <div className="mt-4 flex items-center gap-2 text-yellow-700">
                        <AlertTriangle className="h-4 w-4" />
                        <span className="text-sm font-medium">Manual review required</span>
                    </div>
                )}
            </CardContent>
        </Card>
    );
}
