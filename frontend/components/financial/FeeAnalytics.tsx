"use client";

import { useQuery } from "@tanstack/react-query";
import { useState } from "react";
import {
    BarChart3,
    TrendingUp,
    TrendingDown,
    DollarSign,
    Users,
    Target,
    CheckCircle,
    RefreshCw,
    Download,
    Settings,
    Lightbulb,
    Calculator,
} from "lucide-react";

import { Pie, ResponsiveContainer, Tooltip, BarChart, Bar, XAxis, YAxis, CartesianGrid } from "recharts";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Skeleton } from "@/components/ui/skeleton";
import { cn } from "@/lib/utils";
import {
    fetchFeeEffectivenessAnalysis,
    fetchFeeUtilizationReport 
} from "@/services/FinancialService";


export function FeeAnalytics() {
    const [selectedTab, setSelectedTab] = useState("effectiveness");

    const {
        data: effectivenessData,
        isLoading: effectivenessLoading,
        refetch: refetchEffectiveness,
    } = useQuery({
        queryKey: ["fee-effectiveness-analysis"],
        queryFn: fetchFeeEffectivenessAnalysis,
        refetchInterval: 300000,
    });

    const {
        data: utilizationData,
        isLoading: utilizationLoading,
        refetch: refetchUtilization,
    } = useQuery({
        queryKey: ["fee-utilization-report"],
        queryFn: fetchFeeUtilizationReport,
        refetchInterval: 300000,
    });

    const effectiveness = effectivenessData?.data;
    const utilization = utilizationData?.data;

    const handleRefresh = () => {
        refetchEffectiveness();
        refetchUtilization();
    };

    if (effectivenessLoading || utilizationLoading) {
        return <AnalyticsSkeleton />;
    }

    return (
        <div className="space-y-6">
            {/* Header */}
            <div className="flex items-center justify-between">
                <div>
                    <h2 className="text-2xl font-bold">Fee Analytics & Management</h2>
                    <p className="text-muted-foreground">
                        Analyze fee structure effectiveness and utilization patterns
                    </p>
                </div>
                <div className="flex items-center gap-2">
                    <Button size="sm" variant="outline">
                        <Download className="h-4 w-4 mr-2" />
                        Export
                    </Button>
                    <Button size="sm" variant="outline" onClick={handleRefresh}>
                        <RefreshCw className="h-4 w-4 mr-2" />
                        Refresh
                    </Button>
                </div>
            </div>

            {/* Summary Cards */}
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Total Active Fees</CardTitle>
                        <BarChart3 className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">{utilization?.total_active_fees || 0}</div>
                        <p className="text-xs text-muted-foreground">Fee structures configured</p>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Applications Processed</CardTitle>
                        <Users className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">{utilization?.total_applications_processed || 0}</div>
                        <p className="text-xs text-muted-foreground">Using current fee structure</p>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Revenue Generated</CardTitle>
                        <DollarSign className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">
                            MWK {utilization?.total_revenue_generated?.toLocaleString() || "0"}
                        </div>
                        <p className="text-xs text-muted-foreground">From fee collections</p>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Fee Accuracy</CardTitle>
                        <Target className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold text-green-600">
                            {effectiveness?.fee_accuracy_by_bracket?.accuracy_rate?.toFixed(1) || "0"}%
                        </div>
                        <p className="text-xs text-muted-foreground">Correct fee assignments</p>
                    </CardContent>
                </Card>
            </div>

            {/* Main Analytics Tabs */}
            <Tabs className="space-y-4" value={selectedTab} onValueChange={setSelectedTab}>
                <TabsList className="grid w-full grid-cols-4">
                    <TabsTrigger value="effectiveness">Effectiveness</TabsTrigger>
                    <TabsTrigger value="utilization">Utilization</TabsTrigger>
                    <TabsTrigger value="optimization">Optimization</TabsTrigger>
                    <TabsTrigger value="management">Management</TabsTrigger>
                </TabsList>

                <TabsContent className="space-y-4" value="effectiveness">
                    <div className="grid gap-4 lg:grid-cols-2">
                        {/* Fee Accuracy Analysis */}
                        <Card>
                            <CardHeader>
                                <CardTitle>Fee Accuracy Analysis</CardTitle>
                                <CardDescription>How accurately fees are matched to income brackets</CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                {effectiveness?.fee_accuracy_by_bracket && (
                                    <div className="space-y-4">
                                        <div className="text-center">
                                            <div className="text-3xl font-bold text-green-600 mb-2">
                                                {effectiveness.fee_accuracy_by_bracket.accuracy_rate.toFixed(1)}%
                                            </div>
                                            <Progress
                                                className="h-3"
                                                value={effectiveness.fee_accuracy_by_bracket.accuracy_rate}
                                            />
                                        </div>

                                        <div className="grid grid-cols-2 gap-4 text-sm">
                                            <div className="text-center p-3 bg-green-50 rounded">
                                                <p className="font-bold text-green-600">
                                                    {effectiveness.fee_accuracy_by_bracket.accurate_matches}
                                                </p>
                                                <p className="text-muted-foreground">Accurate Matches</p>
                                            </div>
                                            <div className="text-center p-3 bg-red-50 rounded">
                                                <p className="font-bold text-red-600">
                                                    {effectiveness.fee_accuracy_by_bracket.mismatches_count}
                                                </p>
                                                <p className="text-muted-foreground">Mismatches</p>
                                            </div>
                                        </div>

                                        <div className="text-center">
                                            <p className="text-sm text-muted-foreground">
                                                Total Income-Based Applications:{" "}
                                                {effectiveness.fee_accuracy_by_bracket.total_income_based_applications}
                                            </p>
                                        </div>
                                    </div>
                                )}
                            </CardContent>
                        </Card>

                        {/* Fee Distribution */}
                        <Card>
                            <CardHeader>
                                <CardTitle>Fee Distribution Analysis</CardTitle>
                                <CardDescription>Distribution of fees by type and range</CardDescription>
                            </CardHeader>
                            <CardContent>
                                {effectiveness?.fee_distribution_analysis && (
                                    <div className="space-y-4">
                                        <div className="grid grid-cols-3 gap-2 text-sm">
                                            <div className="text-center p-2 bg-blue-50 rounded">
                                                <p className="font-bold text-blue-600">
                                                    {effectiveness.fee_distribution_analysis.total_active_fees}
                                                </p>
                                                <p className="text-xs text-muted-foreground">Total Fees</p>
                                            </div>
                                            <div className="text-center p-2 bg-purple-50 rounded">
                                                <p className="font-bold text-purple-600">
                                                    {effectiveness.fee_distribution_analysis.income_based_fees}
                                                </p>
                                                <p className="text-xs text-muted-foreground">Income-Based</p>
                                            </div>
                                            <div className="text-center p-2 bg-orange-50 rounded">
                                                <p className="font-bold text-orange-600">
                                                    {effectiveness.fee_distribution_analysis.fixed_fees}
                                                </p>
                                                <p className="text-xs text-muted-foreground">Fixed Fees</p>
                                            </div>
                                        </div>

                                        <div className="space-y-2">
                                            <h4 className="font-medium text-sm">Fee Count by Range:</h4>
                                            {Object.entries(
                                                effectiveness.fee_distribution_analysis.fee_count_by_range,
                                            ).map(([range, count]) => (
                                                <div key={range} className="flex justify-between text-sm">
                                                    <span>{range}</span>
                                                    <span className="font-medium">{count}</span>
                                                </div>
                                            ))}
                                        </div>
                                    </div>
                                )}
                            </CardContent>
                        </Card>
                    </div>

                    {/* Income Bracket Utilization */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Income Bracket Utilization</CardTitle>
                            <CardDescription>How frequently each income bracket is used</CardDescription>
                        </CardHeader>
                        <CardContent>
                            {effectiveness?.income_bracket_utilization && (
                                <div className="space-y-3">
                                    {Object.entries(effectiveness.income_bracket_utilization).map(([bracket, data]) => (
                                        <div
                                            key={bracket}
                                            className="flex items-center justify-between p-3 border rounded"
                                        >
                                            <div className="flex-1">
                                                <div className="flex items-center justify-between mb-2">
                                                    <span className="font-medium">{data.fee_name}</span>
                                                    <Badge variant="outline">{data.usage_count} uses</Badge>
                                                </div>
                                                <div className="flex items-center justify-between text-sm text-muted-foreground">
                                                    <span>
                                                        MWK {data.min_income.toLocaleString()} -{" "}
                                                        {data.max_income
                                                            ? `MWK ${data.max_income.toLocaleString()}`
                                                            : "No limit"}
                                                    </span>
                                                    <span className="font-medium">
                                                        Fee: MWK {data.fee_amount.toLocaleString()}
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            )}
                        </CardContent>
                    </Card>
                </TabsContent>

                <TabsContent className="space-y-4" value="utilization">
                    <div className="grid gap-4 lg:grid-cols-2">
                        {/* Fee Utilization Chart */}
                        <Card>
                            <CardHeader>
                                <CardTitle>Fee Utilization</CardTitle>
                                <CardDescription>Usage frequency of different fees</CardDescription>
                            </CardHeader>
                            <CardContent>
                                {utilization?.fee_utilization && (
                                    <FeeUtilizationChart data={utilization.fee_utilization.slice(0, 10)} />
                                )}
                            </CardContent>
                        </Card>

                        {/* Revenue Concentration */}
                        <Card>
                            <CardHeader>
                                <CardTitle>Revenue Concentration</CardTitle>
                                <CardDescription>How revenue is distributed across fees</CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                {utilization?.revenue_concentration && (
                                    <div className="space-y-4">
                                        <div className="space-y-2">
                                            <div className="flex justify-between text-sm">
                                                <span>Top 3 Fees Generate:</span>
                                                <span className="font-medium">
                                                    {utilization.revenue_concentration.top_3_concentration.toFixed(1)}%
                                                </span>
                                            </div>
                                            <Progress
                                                className="h-2"
                                                value={utilization.revenue_concentration.top_3_concentration}
                                            />
                                        </div>

                                        <div className="space-y-2">
                                            <div className="flex justify-between text-sm">
                                                <span>Top 5 Fees Generate:</span>
                                                <span className="font-medium">
                                                    {utilization.revenue_concentration.top_5_concentration.toFixed(1)}%
                                                </span>
                                            </div>
                                            <Progress
                                                className="h-2"
                                                value={utilization.revenue_concentration.top_5_concentration}
                                            />
                                        </div>

                                        <div className="pt-3 border-t text-sm text-muted-foreground">
                                            <p>
                                                {utilization.revenue_concentration.top_3_concentration > 70
                                                    ? "High concentration - consider diversifying fee structure"
                                                    : "Balanced revenue distribution across fees"}
                                            </p>
                                        </div>
                                    </div>
                                )}

                                {utilization?.most_used_fee && (
                                    <div className="pt-4 border-t">
                                        <h4 className="font-medium text-sm mb-2">Most Used Fee:</h4>
                                        <div className="p-3 bg-blue-50 rounded">
                                            <p className="font-medium">{utilization.most_used_fee.fee_name}</p>
                                            <p className="text-sm text-muted-foreground">
                                                {utilization.most_used_fee.usage_count} uses • MWK{" "}
                                                {utilization.most_used_fee.revenue_generated?.toLocaleString()}
                                            </p>
                                        </div>
                                    </div>
                                )}
                            </CardContent>
                        </Card>
                    </div>

                    {/* Application Type Fee Analysis */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Application Type Fee Analysis</CardTitle>
                            <CardDescription>Fee performance by application type</CardDescription>
                        </CardHeader>
                        <CardContent>
                            {effectiveness?.application_type_fee_analysis && (
                                <div className="space-y-3">
                                    {Object.entries(effectiveness.application_type_fee_analysis).map(([type, data]) => (
                                        <div key={type} className="grid grid-cols-4 gap-4 p-3 border rounded">
                                            <div>
                                                <p className="font-medium">{type}</p>
                                                <p className="text-xs text-muted-foreground">Application Type</p>
                                            </div>
                                            <div className="text-center">
                                                <p className="font-bold">{data.application_count}</p>
                                                <p className="text-xs text-muted-foreground">Applications</p>
                                            </div>
                                            <div className="text-center">
                                                <p className="font-bold">MWK {data.total_revenue.toLocaleString()}</p>
                                                <p className="text-xs text-muted-foreground">Total Revenue</p>
                                            </div>
                                            <div className="text-center">
                                                <p className="font-bold">MWK {data.average_fee.toLocaleString()}</p>
                                                <p className="text-xs text-muted-foreground">Average Fee</p>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            )}
                        </CardContent>
                    </Card>
                </TabsContent>

                <TabsContent className="space-y-4" value="optimization">
                    <div className="grid gap-4 lg:grid-cols-2">
                        {/* Optimization Suggestions */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <Lightbulb className="h-5 w-5" />
                                    Optimization Suggestions
                                </CardTitle>
                                <CardDescription>
                                    AI-powered recommendations for fee structure improvement
                                </CardDescription>
                            </CardHeader>
                            <CardContent>
                                {effectiveness?.optimization_suggestions && (
                                    <div className="space-y-3">
                                        {effectiveness.optimization_suggestions.map((suggestion, index) => (
                                            <div key={index} className="p-3 border rounded">
                                                <div className="flex items-start justify-between mb-2">
                                                    <Badge
                                                        className="text-xs"
                                                        variant={
                                                            suggestion.priority === "high"
                                                                ? "destructive"
                                                                suggestion.priority === "medium" ? "secondary" : "outline"
                                                        }
                                                    >
                                                        {suggestion.priority} priority
                                                    </Badge>
                                                    <Badge className="text-xs" variant="outline">
                                                        {suggestion.type}
                                                    </Badge>
                                                </div>
                                                <p className="text-sm font-medium mb-1">{suggestion.description}</p>
                                                <p className="text-xs text-muted-foreground">
                                                    {suggestion.potential_impact}
                                                </p>
                                            </div>
                                        ))}
                                    </div>
                                )}
                            </CardContent>
                        </Card>

                        {/* Underutilized Brackets */}
                        <Card>
                            <CardHeader>
                                <CardTitle>Underutilized Fee Brackets</CardTitle>
                                <CardDescription>Fee brackets with low usage that may need review</CardDescription>
                            </CardHeader>
                            <CardContent>
                                {effectiveness?.underutilized_brackets && (
                                    <div className="space-y-3">
                                        {effectiveness.underutilized_brackets.map((bracket, index) => (
                                            <div
                                                key={index}
                                                className="p-3 bg-yellow-50 border border-yellow-200 rounded"
                                            >
                                                <div className="flex items-center justify-between mb-2">
                                                    <span className="font-medium">{bracket.bracket}</span>
                                                    <Badge variant="outline">{bracket.usage_count} uses</Badge>
                                                </div>
                                                <p className="text-sm text-muted-foreground mb-2">
                                                    Fee: MWK {bracket.fee_amount.toLocaleString()}
                                                </p>
                                                <p className="text-xs text-yellow-700">{bracket.recommendation}</p>
                                            </div>
                                        ))}
                                    </div>
                                )}
                            </CardContent>
                        </Card>
                    </div>

                    {/* Revenue Impact Analysis */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Revenue Impact Scenarios</CardTitle>
                            <CardDescription>
                                Potential revenue changes from fee structure modifications
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            {effectiveness?.revenue_impact_analysis && (
                                <div className="space-y-4">
                                    <div className="text-center p-4 bg-blue-50 rounded">
                                        <p className="text-sm text-muted-foreground">Current Total Revenue</p>
                                        <p className="text-2xl font-bold">
                                            MWK{" "}
                                            {effectiveness.revenue_impact_analysis.current_total_revenue.toLocaleString()}
                                        </p>
                                    </div>

                                    <div className="grid gap-3">
                                        {Object.entries(effectiveness.revenue_impact_analysis.scenarios).map(
                                            ([scenario, data]) => (
                                                <div key={scenario} className="p-3 border rounded">
                                                    <div className="flex items-center justify-between mb-2">
                                                        <span className="font-medium capitalize">{scenario}</span>
                                                        <div className="flex items-center gap-2">
                                                            {data.revenue_change >= 0 ? (
                                                                <TrendingUp className="h-4 w-4 text-green-600" />
                                                            ) : (
                                                                <TrendingDown className="h-4 w-4 text-red-600" />
                                                            )}
                                                            <span
                                                                className={cn(
                                                                    "font-medium",
                                                                    data.revenue_change >= 0
                                                                        ? "text-green-600"
                                                                        : "text-red-600",
                                                                )}
                                                            >
                                                                {data.revenue_change >= 0 ? "+" : ""}MWK{" "}
                                                                {data.revenue_change.toLocaleString()}
                                                            </span>
                                                        </div>
                                                    </div>
                                                    <p className="text-sm text-muted-foreground">{data.description}</p>
                                                    <p className="text-xs text-muted-foreground mt-1">
                                                        New Total: MWK {data.new_total_revenue.toLocaleString()}
                                                    </p>
                                                </div>
                                            ),
                                        )}
                                    </div>

                                    <div className="pt-4 border-t">
                                        <p className="text-sm font-medium">Recommendation:</p>
                                        <p className="text-sm text-muted-foreground">
                                            {effectiveness.revenue_impact_analysis.recommendation}
                                        </p>
                                    </div>
                                </div>
                            )}
                        </CardContent>
                    </Card>
                </TabsContent>

                <TabsContent className="space-y-4" value="management">
                    <div className="grid gap-4 lg:grid-cols-2">
                        <Card>
                            <CardHeader>
                                <CardTitle>Fee Management Tools</CardTitle>
                                <CardDescription>Configure and manage fee structures</CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-3">
                                <Button className="w-full justify-start">
                                    <Settings className="h-4 w-4 mr-2" />
                                    Configure Fee Brackets
                                </Button>
                                <Button className="w-full justify-start" variant="outline">
                                    <Calculator className="h-4 w-4 mr-2" />
                                    Fee Impact Simulator
                                </Button>
                                <Button className="w-full justify-start" variant="outline">
                                    <Download className="h-4 w-4 mr-2" />
                                    Export Fee Report
                                </Button>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader>
                                <CardTitle>Quick Actions</CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-3">
                                <Button className="w-full justify-start" variant="outline">
                                    Create New Fee Structure
                                </Button>
                                <Button className="w-full justify-start" variant="outline">
                                    Review Underperforming Fees
                                </Button>
                                <Button className="w-full justify-start" variant="outline">
                                    Schedule Fee Review Meeting
                                </Button>
                            </CardContent>
                        </Card>
                    </div>
                </TabsContent>
            </Tabs>
        </div>
    );
}

interface FeeUtilizationChartProps {
    data: Array<{
        fee_name: string;
        usage_count: number;
        revenue_generated: number;
        fee_amount: number;
    }>;
}

function FeeUtilizationChart({ data }: FeeUtilizationChartProps) {
    const chartData = data.map((item) => ({
        name: item.fee_name.length > 20 ? `${item.fee_name.substring(0, 20)}...` : item.fee_name,
        usage: item.usage_count,
        revenue: item.revenue_generated / 1000, // Convert to thousands for display
    }));

    return (
        <div className="h-[300px]">
            <ResponsiveContainer height="100%" width="100%">
                <BarChart data={chartData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis angle={-45} dataKey="name" fontSize={10} height={80} textAnchor="end" />
                    <YAxis fontSize={10} />
                    <Tooltip
                        formatter={(value, name) => [
                            name === "usage" ? value : `MWK ${((value as number) * 1000).toLocaleString()}`,
                            name === "usage" ? "Usage Count" : "Revenue (K)",
                        ]}
                    />
                    <Bar dataKey="usage" fill="hsl(var(--primary))" opacity={0.8} />
                </BarChart>
            </ResponsiveContainer>
        </div>
    );
}

function AnalyticsSkeleton() {
    return (
        <div className="space-y-6">
            <div className="flex items-center justify-between">
                <div>
                    <Skeleton className="h-8 w-[300px] mb-2" />
                    <Skeleton className="h-4 w-[400px]" />
                </div>
                <div className="flex gap-2">
                    <Skeleton className="h-9 w-[100px]" />
                    <Skeleton className="h-9 w-[80px]" />
                </div>
            </div>

            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                {Array.from({ length: 4 }).map((_, i) => (
                    <Card key={i}>
                        <CardHeader className="space-y-0 pb-2">
                            <Skeleton className="h-4 w-[120px]" />
                        </CardHeader>
                        <CardContent>
                            <Skeleton className="h-8 w-[80px] mb-2" />
                            <Skeleton className="h-3 w-[100px]" />
                        </CardContent>
                    </Card>
                ))}
            </div>

            <div className="grid gap-4 lg:grid-cols-2">
                {Array.from({ length: 4 }).map((_, i) => (
                    <Card key={i}>
                        <CardHeader>
                            <Skeleton className="h-6 w-[200px]" />
                            <Skeleton className="h-4 w-[300px]" />
                        </CardHeader>
                        <CardContent>
                            <Skeleton className="h-[200px] w-full" />
                        </CardContent>
                    </Card>
                ))}
            </div>
        </div>
    );
}
