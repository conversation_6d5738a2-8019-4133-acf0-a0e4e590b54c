"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import {
    Search,
    Filter,
    ArrowUpRight,
    DollarSign,
    Clock,
    AlertTriangle,
    CheckCircle,
    Eye,
    Download,
    MoreVertical,
} from "lucide-react";

import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";

// Mock data - in real app this would come from API
const mockOrganizations = [
    {
        id: "org-001",
        name: "Hope Children Foundation",
        totalPaid: 250000,
        outstandingBalance: 0,
        compliance: 98.5,
        riskLevel: "Low",
        lastPayment: "2024-01-15",
        status: "Active",
        paymentCount: 12,
    },
    {
        id: "org-002",
        name: "Rural Development Initiative",
        totalPaid: 180000,
        outstandingBalance: 45000,
        compliance: 76.2,
        riskLevel: "Medium",
        lastPayment: "2024-01-08",
        status: "Active",
        paymentCount: 8,
    },
    {
        id: "org-003",
        name: "Education for All Malawi",
        totalPaid: 320000,
        outstandingBalance: 15000,
        compliance: 92.1,
        riskLevel: "Low",
        lastPayment: "2024-01-12",
        status: "Active",
        paymentCount: 15,
    },
    {
        id: "org-004",
        name: "Community Health Partners",
        totalPaid: 95000,
        outstandingBalance: 75000,
        compliance: 45.8,
        riskLevel: "High",
        lastPayment: "2023-12-20",
        status: "Attention Required",
        paymentCount: 4,
    },
];

interface OrganizationFinancialListProps {
    onSelectOrganization?: (organizationId: string, organizationName: string) => void;
}

export function OrganizationFinancialList({ onSelectOrganization }: OrganizationFinancialListProps) {
    const [searchTerm, setSearchTerm] = useState("");
    const [filterStatus, setFilterStatus] = useState<string>("all");
    const router = useRouter();

    const filteredOrganizations = mockOrganizations.filter((org) => {
        const matchesSearch =
            org.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            org.id.toLowerCase().includes(searchTerm.toLowerCase());
        const matchesFilter =
            filterStatus === "all" ||
            (filterStatus === "attention" && org.status === "Attention Required") ||
            (filterStatus === "active" && org.status === "Active");

        return matchesSearch && matchesFilter;
    });

    const handleViewProfile = (org: (typeof mockOrganizations)[0]) => {
        if (onSelectOrganization) {
            onSelectOrganization(org.id, org.name);
        } else {
            router.push(`/financial/organizations/${org.id}`);
        }
    };

    return (
        <div className="space-y-6">
            {/* Header and Controls */}
            <div className="flex items-center justify-between">
                <div>
                    <h2 className="text-2xl font-bold">Organization Financial Profiles</h2>
                    <p className="text-muted-foreground">Manage and monitor organization financial performance</p>
                </div>
                <Button>
                    <Download className="h-4 w-4 mr-2" />
                    Export All
                </Button>
            </div>

            {/* Search and Filters */}
            <Card>
                <CardContent className="p-4">
                    <div className="flex items-center gap-4">
                        <div className="relative flex-1">
                            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                            <Input
                                className="pl-9"
                                placeholder="Search organizations by name or ID..."
                                value={searchTerm}
                                onChange={(e) => setSearchTerm(e.target.value)}
                            />
                        </div>
                        <select
                            className="text-sm border rounded px-3 py-2"
                            value={filterStatus}
                            onChange={(e) => setFilterStatus(e.target.value)}
                        >
                            <option value="all">All Organizations</option>
                            <option value="active">Active</option>
                            <option value="attention">Needs Attention</option>
                        </select>
                        <Button variant="outline">
                            <Filter className="h-4 w-4 mr-2" />
                            More Filters
                        </Button>
                    </div>
                </CardContent>
            </Card>

            {/* Summary Cards */}
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Total Organizations</CardTitle>
                        <DollarSign className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">{mockOrganizations.length}</div>
                        <p className="text-xs text-muted-foreground">{filteredOrganizations.length} showing</p>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
                        <ArrowUpRight className="h-4 w-4 text-green-600" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">
                            MWK {mockOrganizations.reduce((sum, org) => sum + org.totalPaid, 0).toLocaleString()}
                        </div>
                        <p className="text-xs text-muted-foreground">From all organizations</p>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Outstanding</CardTitle>
                        <Clock className="h-4 w-4 text-yellow-600" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold text-yellow-600">
                            MWK{" "}
                            {mockOrganizations.reduce((sum, org) => sum + org.outstandingBalance, 0).toLocaleString()}
                        </div>
                        <p className="text-xs text-muted-foreground">Pending payments</p>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">High Risk</CardTitle>
                        <AlertTriangle className="h-4 w-4 text-red-600" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold text-red-600">
                            {mockOrganizations.filter((org) => org.riskLevel === "High").length}
                        </div>
                        <p className="text-xs text-muted-foreground">Need attention</p>
                    </CardContent>
                </Card>
            </div>

            {/* Organizations List */}
            <div className="space-y-4">
                {filteredOrganizations.length === 0 ? (
                    <Card>
                        <CardContent className="p-8 text-center">
                            <Search className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                            <h3 className="text-lg font-semibold mb-2">No organizations found</h3>
                            <p className="text-muted-foreground">
                                {searchTerm
                                    ? "Try adjusting your search criteria."
                                    : "No organizations match the selected filters."}
                            </p>
                        </CardContent>
                    </Card>
                ) : (
                    filteredOrganizations.map((org) => (
                        <OrganizationCard
                            key={org.id}
                            organization={org}
                            onViewProfile={() => handleViewProfile(org)}
                        />
                    ))
                )}
            </div>
        </div>
    );
}

interface OrganizationCardProps {
    organization: (typeof mockOrganizations)[0];
    onViewProfile: () => void;
}

function OrganizationCard({ organization, onViewProfile }: OrganizationCardProps) {
    const getRiskColor = (risk: string) => {
        switch (risk) {
            case "Low":
                return "text-green-600 bg-green-50";
            case "Medium":
                return "text-yellow-600 bg-yellow-50";
            case "High":
                return "text-red-600 bg-red-50";
            default:
                return "text-gray-600 bg-gray-50";
        }
    };

    const getStatusColor = (status: string) => {
        switch (status) {
            case "Active":
                return "bg-green-100 text-green-800";
            case "Attention Required":
                return "bg-red-100 text-red-800";
            default:
                return "bg-gray-100 text-gray-800";
        }
    };

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString();
    };

    return (
        <Card className="hover:shadow-md transition-shadow">
            <CardContent className="p-6">
                <div className="flex items-start justify-between">
                    <div className="flex-1">
                        <div className="flex items-start justify-between mb-4">
                            <div>
                                <h3 className="text-lg font-semibold">{organization.name}</h3>
                                <p className="text-sm text-muted-foreground">ID: {organization.id}</p>
                            </div>
                            <div className="flex items-center gap-2">
                                <Badge className={getStatusColor(organization.status)}>{organization.status}</Badge>
                                <Badge className={getRiskColor(organization.riskLevel)}>
                                    {organization.riskLevel} Risk
                                </Badge>
                            </div>
                        </div>

                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                            <div>
                                <p className="text-sm text-muted-foreground">Total Paid</p>
                                <p className="font-semibold">MWK {organization.totalPaid.toLocaleString()}</p>
                                <p className="text-xs text-muted-foreground">{organization.paymentCount} payments</p>
                            </div>
                            <div>
                                <p className="text-sm text-muted-foreground">Outstanding</p>
                                <p
                                    className={cn(
                                        "font-semibold",
                                        organization.outstandingBalance > 0 ? "text-red-600" : "text-green-600",
                                    )}
                                >
                                    MWK {organization.outstandingBalance.toLocaleString()}
                                </p>
                                <p className="text-xs text-muted-foreground">
                                    {organization.outstandingBalance > 0 ? "Pending" : "Paid up"}
                                </p>
                            </div>
                            <div>
                                <p className="text-sm text-muted-foreground">Compliance</p>
                                <div className="flex items-center gap-2">
                                    <p
                                        className={cn(
                                            "font-semibold",
                                            organization.compliance >= 90
                                                ? "text-green-600"
                                                : organization.compliance >= 70
                                                    ? "text-yellow-600"
                                                    : "text-red-600",
                                        )}
                                    >
                                        {organization.compliance.toFixed(1)}%
                                    </p>
                                    {organization.compliance >= 90 ? (
                                        <CheckCircle className="h-3 w-3 text-green-600" />
                                    ) : organization.compliance >= 70 ? (
                                        <Clock className="h-3 w-3 text-yellow-600" />
                                    ) : (
                                        <AlertTriangle className="h-3 w-3 text-red-600" />
                                    )}
                                </div>
                            </div>
                            <div>
                                <p className="text-sm text-muted-foreground">Last Payment</p>
                                <p className="font-semibold">{formatDate(organization.lastPayment)}</p>
                                <p className="text-xs text-muted-foreground">
                                    {Math.ceil(
                                        (Date.now() - new Date(organization.lastPayment).getTime()) /
                                            (1000 * 60 * 60 * 24),
                                    )}{" "}
                                    days ago
                                </p>
                            </div>
                        </div>

                        <div className="flex items-center justify-between pt-4 border-t">
                            <div className="flex items-center gap-4 text-sm text-muted-foreground">
                                <span>Updated 2 hours ago</span>
                            </div>
                            <div className="flex items-center gap-2">
                                <Button size="sm" variant="outline" onClick={onViewProfile}>
                                    <Eye className="h-4 w-4 mr-2" />
                                    View Profile
                                </Button>
                                <Button size="sm" variant="outline">
                                    <MoreVertical className="h-4 w-4" />
                                </Button>
                            </div>
                        </div>
                    </div>
                </div>
            </CardContent>
        </Card>
    );
}
