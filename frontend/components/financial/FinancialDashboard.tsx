"use client";

import { useQuery } from "@tanstack/react-query";
import { useState } from "react";
import {
    DollarSign,
    TrendingUp,
    Receipt,
    CreditCard,
    AlertTriangle,
    CheckCircle,
    Clock,
    ArrowUpRight,
    ArrowDownRight,
    RefreshCw,
    Calendar,
    Filter,
} from "lucide-react";

import { RevenueChart } from "./charts/RevenueChart";
import { PaymentMethodChart } from "./charts/PaymentMethodChart";
import { CollectionEfficiencyChart } from "./charts/CollectionEfficiencyChart";
import { TopPayingOrganizations } from "./TopPayingOrganizations";
import { RecentTransactions } from "./RecentTransactions";
import { PaymentDiscrepancies } from "./PaymentDiscrepancies";

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { cn } from "@/lib/utils";
import { fetchFinancialDashboard } from "@/services/FinancialService";

export function FinancialDashboard() {
    const [dateRange, setDateRange] = useState("30d");

    const {
        data: dashboardData,
        isLoading,
        isError,
        refetch,
    } = useQuery({
        queryKey: ["financial-dashboard", dateRange],
        queryFn: () => fetchFinancialDashboard(dateRange),
        refetchInterval: 300000, // Refresh every 5 minutes
    });

    const stats = dashboardData?.data;

    if (isLoading) {
        return <DashboardSkeleton />;
    }

    if (isError) {
        return (
            <Card className="p-6">
                <div className="text-center">
                    <AlertTriangle className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <h3 className="text-lg font-semibold mb-2">Failed to load dashboard</h3>
                    <p className="text-muted-foreground mb-4">
                        There was an error loading the financial dashboard data.
                    </p>
                    <Button onClick={() => refetch()}>
                        <RefreshCw className="h-4 w-4 mr-2" />
                        Retry
                    </Button>
                </div>
            </Card>
        );
    }

    return (
        <div className="space-y-6">
            {/* Header with Controls */}
            <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                    <div className="flex items-center gap-2">
                        <Calendar className="h-4 w-4 text-muted-foreground" />
                        <select
                            className="text-sm border rounded px-2 py-1"
                            value={dateRange}
                            onChange={(e) => setDateRange(e.target.value)}
                        >
                            <option value="7d">Last 7 days</option>
                            <option value="30d">Last 30 days</option>
                            <option value="90d">Last 3 months</option>
                            <option value="365d">Last year</option>
                        </select>
                    </div>
                    <Button size="sm" variant="outline">
                        <Filter className="h-4 w-4 mr-2" />
                        Filters
                    </Button>
                </div>
                <Button size="sm" variant="outline" onClick={() => refetch()}>
                    <RefreshCw className="h-4 w-4 mr-2" />
                    Refresh
                </Button>
            </div>

            {/* Key Metrics Cards */}
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                <MetricCard
                    change={stats?.revenue_growth ?? 0}
                    icon={DollarSign}
                    title="Total Revenue"
                    trend="up"
                    value={`MWK ${stats?.total_revenue?.toLocaleString() ?? "0"}`}
                />
                <MetricCard
                    change={12.5}
                    icon={CreditCard}
                    title="Total Payments"
                    trend="up"
                    value={stats?.payment_count?.toLocaleString() ?? "0"}
                />
                <MetricCard
                    change={-2.1}
                    icon={CheckCircle}
                    title="Collection Rate"
                    trend="down"
                    value={`${stats?.collection_efficiency?.toFixed(1) ?? "0"}%`}
                />
                <MetricCard
                    change={8.2}
                    icon={Receipt}
                    title="Avg Payment"
                    trend="up"
                    value={`MWK ${stats?.average_payment_amount?.toLocaleString() ?? "0"}`}
                />
            </div>

            {/* Main Dashboard Tabs */}
            <Tabs className="space-y-4" defaultValue="overview">
                <TabsList className="grid w-full grid-cols-4">
                    <TabsTrigger value="overview">Overview</TabsTrigger>
                    <TabsTrigger value="analytics">Analytics</TabsTrigger>
                    <TabsTrigger value="reconciliation">Reconciliation</TabsTrigger>
                    <TabsTrigger value="insights">Insights</TabsTrigger>
                </TabsList>

                <TabsContent className="space-y-4" value="overview">
                    <div className="grid gap-4 lg:grid-cols-2">
                        {/* Revenue Trend Chart */}
                        <Card className="col-span-1">
                            <CardHeader>
                                <CardTitle>Revenue Trend</CardTitle>
                                <CardDescription>Monthly revenue performance over time</CardDescription>
                            </CardHeader>
                            <CardContent>
                                <RevenueChart data={stats?.revenue_by_month} />
                            </CardContent>
                        </Card>

                        {/* Payment Methods Distribution */}
                        <Card className="col-span-1">
                            <CardHeader>
                                <CardTitle>Payment Methods</CardTitle>
                                <CardDescription>Distribution of payment methods used</CardDescription>
                            </CardHeader>
                            <CardContent>
                                <PaymentMethodChart data={stats?.payment_method_breakdown} />
                            </CardContent>
                        </Card>

                        {/* Top Paying Organizations */}
                        <Card className="col-span-1">
                            <CardHeader>
                                <CardTitle>Top Contributors</CardTitle>
                                <CardDescription>Organizations with highest payments</CardDescription>
                            </CardHeader>
                            <CardContent>
                                <TopPayingOrganizations data={stats?.top_paying_organizations} />
                            </CardContent>
                        </Card>

                        {/* Recent Transactions */}
                        <Card className="col-span-1">
                            <CardHeader>
                                <CardTitle>Recent Transactions</CardTitle>
                                <CardDescription>Latest payment activities</CardDescription>
                            </CardHeader>
                            <CardContent>
                                <RecentTransactions />
                            </CardContent>
                        </Card>
                    </div>
                </TabsContent>

                <TabsContent className="space-y-4" value="analytics">
                    <div className="grid gap-4">
                        <Card>
                            <CardHeader>
                                <CardTitle>Collection Efficiency</CardTitle>
                                <CardDescription>
                                    Track payment collection performance across different periods
                                </CardDescription>
                            </CardHeader>
                            <CardContent>
                                <CollectionEfficiencyChart />
                            </CardContent>
                        </Card>

                        <div className="grid gap-4 lg:grid-cols-2">
                            <Card>
                                <CardHeader>
                                    <CardTitle>Revenue by Application Type</CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <div className="space-y-4">
                                        {stats?.revenue_by_application_type &&
                                            Object.entries(stats.revenue_by_application_type).map(([type, amount]) => (
                                                <div key={type} className="flex items-center justify-between">
                                                    <span className="text-sm font-medium">{type}</span>
                                                    <span className="text-sm text-muted-foreground">
                                                        MWK {(amount as number).toLocaleString()}
                                                    </span>
                                                </div>
                                            ))}
                                    </div>
                                </CardContent>
                            </Card>

                            <Card>
                                <CardHeader>
                                    <CardTitle>Payment Status Overview</CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <div className="space-y-4">
                                        <div className="flex items-center justify-between">
                                            <div className="flex items-center gap-2">
                                                <CheckCircle className="h-4 w-4 text-green-500" />
                                                <span className="text-sm">Successful</span>
                                            </div>
                                            <Badge variant="outline">95.2%</Badge>
                                        </div>
                                        <div className="flex items-center justify-between">
                                            <div className="flex items-center gap-2">
                                                <Clock className="h-4 w-4 text-yellow-500" />
                                                <span className="text-sm">Pending</span>
                                            </div>
                                            <Badge variant="outline">3.1%</Badge>
                                        </div>
                                        <div className="flex items-center justify-between">
                                            <div className="flex items-center gap-2">
                                                <AlertTriangle className="h-4 w-4 text-red-500" />
                                                <span className="text-sm">Failed</span>
                                            </div>
                                            <Badge variant="outline">1.7%</Badge>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>
                        </div>
                    </div>
                </TabsContent>

                <TabsContent className="space-y-4" value="reconciliation">
                    <PaymentDiscrepancies />
                </TabsContent>

                <TabsContent className="space-y-4" value="insights">
                    <div className="grid gap-4 lg:grid-cols-2">
                        <Card>
                            <CardHeader>
                                <CardTitle>Key Insights</CardTitle>
                                <CardDescription>AI-powered financial insights and recommendations</CardDescription>
                            </CardHeader>
                            <CardContent>
                                <div className="space-y-4">
                                    <div className="flex items-start gap-3 p-3 rounded-lg bg-blue-50">
                                        <TrendingUp className="h-5 w-5 text-blue-600 mt-0.5" />
                                        <div>
                                            <p className="text-sm font-medium">Revenue Growth</p>
                                            <p className="text-sm text-muted-foreground">
                                                Revenue increased 15% compared to last month, driven by increased
                                                registrations.
                                            </p>
                                        </div>
                                    </div>
                                    <div className="flex items-start gap-3 p-3 rounded-lg bg-green-50">
                                        <CheckCircle className="h-5 w-5 text-green-600 mt-0.5" />
                                        <div>
                                            <p className="text-sm font-medium">Collection Efficiency</p>
                                            <p className="text-sm text-muted-foreground">
                                                Payment collection rate is above target at 95.2%.
                                            </p>
                                        </div>
                                    </div>
                                    <div className="flex items-start gap-3 p-3 rounded-lg bg-yellow-50">
                                        <AlertTriangle className="h-5 w-5 text-yellow-600 mt-0.5" />
                                        <div>
                                            <p className="text-sm font-medium">Payment Delays</p>
                                            <p className="text-sm text-muted-foreground">
                                                Average payment processing time increased by 2 days.
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader>
                                <CardTitle>Recommendations</CardTitle>
                                <CardDescription>Suggested actions to improve financial performance</CardDescription>
                            </CardHeader>
                            <CardContent>
                                <div className="space-y-3">
                                    <div className="flex items-center gap-2 text-sm">
                                        <div className="h-2 w-2 rounded-full bg-blue-500" />
                                        <span>Implement automated payment reminders</span>
                                    </div>
                                    <div className="flex items-center gap-2 text-sm">
                                        <div className="h-2 w-2 rounded-full bg-green-500" />
                                        <span>Review fee structure for income brackets</span>
                                    </div>
                                    <div className="flex items-center gap-2 text-sm">
                                        <div className="h-2 w-2 rounded-full bg-yellow-500" />
                                        <span>Optimize payment verification process</span>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    </div>
                </TabsContent>
            </Tabs>
        </div>
    );
}

interface MetricCardProps {
    title: string;
    value: string;
    change: number;
    icon: React.ElementType;
    trend: "up" | "down";
}

function MetricCard({ title, value, change, icon: Icon, trend }: MetricCardProps) {
    const isPositive = change > 0;
    const TrendIcon = isPositive ? ArrowUpRight : ArrowDownRight;

    return (
        <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">{title}</CardTitle>
                <Icon className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
                <div className="text-2xl font-bold">{value}</div>
                <div className="flex items-center text-xs text-muted-foreground">
                    <TrendIcon className={cn("mr-1 h-3 w-3", isPositive ? "text-green-600" : "text-red-600")} />
                    <span className={cn(isPositive ? "text-green-600" : "text-red-600")}>
                        {Math.abs(change).toFixed(1)}%
                    </span>
                    <span className="ml-1">from last period</span>
                </div>
            </CardContent>
        </Card>
    );
}

function DashboardSkeleton() {
    return (
        <div className="space-y-6">
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                {Array.from({ length: 4 }).map((_, i) => (
                    <Card key={i}>
                        <CardHeader className="space-y-0 pb-2">
                            <Skeleton className="h-4 w-[100px]" />
                        </CardHeader>
                        <CardContent>
                            <Skeleton className="h-8 w-[120px] mb-2" />
                            <Skeleton className="h-3 w-[80px]" />
                        </CardContent>
                    </Card>
                ))}
            </div>

            <div className="grid gap-4 lg:grid-cols-2">
                {Array.from({ length: 4 }).map((_, i) => (
                    <Card key={i}>
                        <CardHeader>
                            <Skeleton className="h-6 w-[200px]" />
                            <Skeleton className="h-4 w-[300px]" />
                        </CardHeader>
                        <CardContent>
                            <Skeleton className="h-[200px] w-full" />
                        </CardContent>
                    </Card>
                ))}
            </div>
        </div>
    );
}
