"use client";

import { useQuery } from "@tanstack/react-query";
import { useRouter } from "next/navigation";
import { <PERSON>tings, Eye, Edit, Trash2, MoreHorizontal, Users, Clock, CheckCircle, XCircle } from "lucide-react";
import { toast } from "sonner";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";
import * as workflowService from "@/services/WorkflowService";
import { TemplateDto } from "@/types";

export function WorkflowTemplatesList() {
    const router = useRouter();

    const {
        data: templates = [],
        isLoading,
        error,
        refetch,
    } = useQuery({
        queryKey: ["workflow-templates"],
        queryFn: async () => {
            const response = await workflowService.fetchTemplates({ page: 1, size: 50 });

            if (response.errors && response.errors.length > 0) {
                //      for (const err of response.errors) {
                // toast.error(err.message);
                //    }

                return [];
            }

            return response.data || [];
        },
        staleTime: 5 * 60 * 1000,
        gcTime: 10 * 60 * 1000,
        retry: 2,
        refetchOnWindowFocus: false,
    });

    const handleViewTemplate = (template: TemplateDto) => {
        router.push(`/settings/workflows/templates/${template.id}`);
    };

    const handleEditTemplate = (template: TemplateDto) => {
        router.push(`/settings/workflows/templates/${template.id}/edit`);
    };

    const handleManageStages = (template: TemplateDto) => {
        router.push(`/settings/workflows/templates/${template.id}/stages`);
    };

    const handleDeleteTemplate = async (template: TemplateDto) => {
        if (!confirm(`Are you sure you want to delete the workflow template "${template.name}"?`)) {
            return;
        }

        try {
            const response = await workflowService.voidTemplate(template.id, "Deleted by user");

            if (response.errors && response.errors.length > 0) {
                // for (const error of response.errors) {
                //   toast.error(error.message);
                // }

                return;
            }

            toast.success("Workflow template deleted successfully");
            refetch();
        } catch (error) {
            toast.error("Failed to delete workflow template");
        }
    };

    const getTemplateIcon = (code: string) => {
        switch (code) {
            case "ORGANIZATION_REGISTRATION":
                return "🏢";
            case "LICENCE_RENEWAL":
                return "📋";
            case "PERMIT_APPLICATION":
                return "📄";
            default:
                return "⚙️";
        }
    };

    const getStatusBadge = (isActive: boolean) => {
        return isActive ? (
            <Badge className="flex items-center space-x-1" variant="default">
                <CheckCircle className="h-3 w-3" />
                <span>Active</span>
            </Badge>
        ) : (
            <Badge className="flex items-center space-x-1" variant="secondary">
                <XCircle className="h-3 w-3" />
                <span>Inactive</span>
            </Badge>
        );
    };

    if (isLoading) {
        return (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {[...Array(6)].map((_, i) => (
                    <Card key={i}>
                        <CardHeader>
                            <Skeleton className="h-6 w-3/4" />
                            <Skeleton className="h-4 w-full" />
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-2">
                                <Skeleton className="h-4 w-1/2" />
                                <Skeleton className="h-8 w-full" />
                            </div>
                        </CardContent>
                    </Card>
                ))}
            </div>
        );
    }

    if (error) {
        return (
            <Alert>
                <XCircle className="h-4 w-4" />
                <AlertDescription>Failed to load workflow templates. Please try again.</AlertDescription>
            </Alert>
        );
    }

    if (templates.length === 0) {
        return (
            <Card>
                <CardContent className="flex flex-col items-center justify-center py-12">
                    <Settings className="h-12 w-12 text-muted-foreground mb-4" />
                    <h3 className="text-lg font-semibold mb-2">No Workflow Templates</h3>
                    <p className="text-muted-foreground text-center mb-4">
                        Get started by creating your first workflow template using the options above.
                    </p>
                </CardContent>
            </Card>
        );
    }

    return (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {templates.map((template) => (
                <Card key={template.id} className="hover:shadow-md transition-shadow">
                    <CardHeader className="pb-3">
                        <div className="flex items-start justify-between">
                            <div className="flex items-center space-x-3">
                                <div className="text-2xl">{getTemplateIcon(template.code)}</div>
                                <div className="flex-1">
                                    <CardTitle className="text-lg line-clamp-1">{template.name}</CardTitle>
                                    <CardDescription className="text-sm line-clamp-2 min-h-[40px]">
                                        {template.description || "No description provided"}
                                    </CardDescription>
                                </div>
                            </div>
                            <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                    <Button size="sm" variant="ghost">
                                        <MoreHorizontal className="h-4 w-4" />
                                    </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end">
                                    <DropdownMenuItem onClick={() => handleViewTemplate(template)}>
                                        <Eye className="h-4 w-4 mr-2" />
                                        View Details
                                    </DropdownMenuItem>
                                    <DropdownMenuItem onClick={() => handleEditTemplate(template)}>
                                        <Edit className="h-4 w-4 mr-2" />
                                        Edit Template
                                    </DropdownMenuItem>
                                    <DropdownMenuItem onClick={() => handleManageStages(template)}>
                                        <Settings className="h-4 w-4 mr-2" />
                                        Manage Stages
                                    </DropdownMenuItem>
                                    <DropdownMenuSeparator />
                                    <DropdownMenuItem
                                        className="text-destructive"
                                        onClick={() => handleDeleteTemplate(template)}
                                    >
                                        <Trash2 className="h-4 w-4 mr-2" />
                                        Delete
                                    </DropdownMenuItem>
                                </DropdownMenuContent>
                            </DropdownMenu>
                        </div>
                    </CardHeader>
                    <CardContent className="pt-0">
                        <div className="space-y-3">
                            <div className="flex items-center justify-between">
                                <Badge className="text-xs" variant="outline">
                                    {template.code}
                                </Badge>
                                {getStatusBadge(template.is_active)}
                            </div>
                            <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                                <div className="flex items-center space-x-1">
                                    <Clock className="h-3 w-3" />
                                    <span>{template.stages.length} stages</span>
                                </div>
                                <div className="flex items-center space-x-1">
                                    <Users className="h-3 w-3" />
                                    <span>
                                        {template.stages.reduce((acc, stage) => acc + stage.roles.length, 0)} roles
                                    </span>
                                </div>
                            </div>

                            <Button
                                className="w-full"
                                size="sm"
                                variant="outline"
                                onClick={() => handleManageStages(template)}
                            >
                                <Settings className="h-3 w-3 mr-2" />
                                Manage Stages
                            </Button>
                        </div>
                    </CardContent>
                </Card>
            ))}
        </div>
    );
}
