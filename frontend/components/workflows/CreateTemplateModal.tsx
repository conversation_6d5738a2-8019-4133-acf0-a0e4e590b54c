"use client";

import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { toast } from "sonner";
import { Loader2 } from "lucide-react";

import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import * as workflowService from "@/services/WorkflowService";
import { WorkflowTemplateCode, TemplateRequest } from "@/types";

const templateSchema = z.object({
    name: z.string().min(5, "Name must be at least 5 characters"),
    code: z.string().min(3, "Code must be at least 3 characters"),
    description: z.string().optional(),
    is_active: z.boolean().default(true),
});

type TemplateFormData = z.infer<typeof templateSchema>;

interface CreateTemplateModalProps {
    isOpen: boolean;
    onClose: () => void;
    templateType: WorkflowTemplateCode | null;
    onTemplateCreated: () => void;
}

const TEMPLATE_DEFAULTS = {
    ORGANIZATION_REGISTRATION: {
        name: "Organization Registration Workflow",
        description: "Approval workflow for new organization registrations",
    },
    LICENCE_RENEWAL: {
        name: "License Renewal Workflow",
        description: "Approval workflow for license renewal applications",
    },
    PERMIT_APPLICATION: {
        name: "Permit Application Workflow",
        description: "Approval workflow for permit applications",
    },
};

export function CreateTemplateModal({ isOpen, onClose, templateType, onTemplateCreated }: CreateTemplateModalProps) {
    const [isSubmitting, setIsSubmitting] = useState(false);

    const form = useForm<TemplateFormData>({
        resolver: zodResolver(templateSchema),
        defaultValues: {
            name: "",
            code: "",
            description: "",
            is_active: true,
        },
    });

    // Update form values when templateType changes
    React.useEffect(() => {
        if (templateType && TEMPLATE_DEFAULTS[templateType]) {
            const defaults = TEMPLATE_DEFAULTS[templateType];

            form.reset({
                name: defaults.name,
                code: templateType,
                description: defaults.description,
                is_active: true,
            });
        }
    }, [templateType, form]);

    const onSubmit = async (data: TemplateFormData) => {
        if (!templateType) return;

        setIsSubmitting(true);

        try {
            const templateData: TemplateRequest = {
                name: data.name,
                code: data.code,
                description: data.description,
                is_active: data.is_active,
                stages: [], // Start with empty stages - user can add them later
            };

            const response = await workflowService.createTemplate(templateData);

            if (response.errors && response.errors.length > 0) {
                for (const err of response.errors) {
                    toast.error(err.message);
                }

                return;
            }

            toast.success("Workflow template created successfully");
            form.reset();
            onTemplateCreated();
        } catch (errors) {
            toast.error("Failed to create workflow template");
        } finally {
            setIsSubmitting(false);
        }
    };

    const handleClose = () => {
        if (!isSubmitting) {
            form.reset();
            onClose();
        }
    };

    return (
        <Dialog open={isOpen} onOpenChange={handleClose}>
            <DialogContent className="sm:max-w-[500px]">
                <DialogHeader>
                    <DialogTitle>Create Workflow Template</DialogTitle>
                    <DialogDescription>
                        Create a new workflow template for {templateType?.replace(/_/g, " ").toLowerCase()}.
                    </DialogDescription>
                </DialogHeader>

                <Form {...form}>
                    <form className="space-y-4" onSubmit={form.handleSubmit(onSubmit)}>
                        <FormField
                            control={form.control}
                            name="name"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>Template Name</FormLabel>
                                    <FormControl>
                                        <Input placeholder="Enter template name" {...field} disabled={isSubmitting} />
                                    </FormControl>
                                    <FormDescription>A descriptive name for this workflow template</FormDescription>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />

                        <FormField
                            control={form.control}
                            name="code"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>Template Code</FormLabel>
                                    <FormControl>
                                        <Input
                                            placeholder="Enter template code"
                                            {...field}
                                            className="uppercase"
                                            disabled={isSubmitting}
                                            onChange={(e) => field.onChange(e.target.value.toUpperCase())}
                                        />
                                    </FormControl>
                                    <FormDescription>A unique code identifier for this template</FormDescription>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />

                        <FormField
                            control={form.control}
                            name="description"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>Description</FormLabel>
                                    <FormControl>
                                        <Textarea
                                            placeholder="Enter template description"
                                            {...field}
                                            disabled={isSubmitting}
                                            rows={3}
                                        />
                                    </FormControl>
                                    <FormDescription>
                                        Optional description of what this workflow template is used for
                                    </FormDescription>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />

                        <FormField
                            control={form.control}
                            name="is_active"
                            render={({ field }) => (
                                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                                    <div className="space-y-0.5">
                                        <FormLabel>Active Template</FormLabel>
                                        <FormDescription>Enable this template for use in workflows</FormDescription>
                                    </div>
                                    <FormControl>
                                        <Switch
                                            checked={field.value}
                                            disabled={isSubmitting}
                                            onCheckedChange={field.onChange}
                                        />
                                    </FormControl>
                                </FormItem>
                            )}
                        />

                        <div className="flex justify-end space-x-2 pt-4">
                            <Button disabled={isSubmitting} type="button" variant="outline" onClick={handleClose}>
                                Cancel
                            </Button>
                            <Button disabled={isSubmitting} type="submit">
                                {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                                Create Template
                            </Button>
                        </div>
                    </form>
                </Form>
            </DialogContent>
        </Dialog>
    );
}
