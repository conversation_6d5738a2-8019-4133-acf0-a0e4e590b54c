"use client";

import { ChevronsUpDown } from "lucide-react";

import { Logo } from "./icons";

import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuShortcut,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { SidebarMenu, SidebarMenuButton, SidebarMenuItem, useSidebar } from "@/components/ui/sidebar";
import { MembershipSummaryDto } from "@/types";

type Props = {
    memberships: MembershipSummaryDto[];
    context: MembershipSummaryDto | null;
    setActiveAccount: (item: MembershipSummaryDto) => void;
};

export function AccountSwitcher({ memberships, context, setActiveAccount }: Props) {
    const { isMobile } = useSidebar();

    if (!context) {
        return <Logo size="sm" />;
    }

    return (
        <SidebarMenu>
            <SidebarMenuItem>
                <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                        <SidebarMenuButton
                            className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
                            size="lg"
                        >
                            <div className="bg-sidebar-primary text-sidebar-primary-foreground flex aspect-square size-8 items-center justify-center rounded-lg">
                                O
                            </div>
                            <div className="grid flex-1 text-left text-sm leading-tight">
                                <span className="truncate font-medium">{context.organization.abbreviation}</span>
                            </div>
                            <ChevronsUpDown className="ml-auto" />
                        </SidebarMenuButton>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent
                        align="start"
                        className="w-(--radix-dropdown-menu-trigger-width) min-w-56 rounded-lg"
                        side={isMobile ? "bottom" : "right"}
                        sideOffset={4}
                    >
                        <DropdownMenuLabel className="text-muted-foreground text-xs">Organizations</DropdownMenuLabel>
                        {memberships.map((item, index) => (
                            <DropdownMenuItem
                                key={item.organization.id}
                                className="gap-2 p-2"
                                onClick={() => setActiveAccount(item)}
                            >
                                <div className="flex size-6 items-center justify-center rounded-md border">O</div>
                                {item.organization.name}
                                <DropdownMenuShortcut>⌘{index + 1}</DropdownMenuShortcut>
                            </DropdownMenuItem>
                        ))}
                    </DropdownMenuContent>
                </DropdownMenu>
            </SidebarMenuItem>
        </SidebarMenu>
    );
}
