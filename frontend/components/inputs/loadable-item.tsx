"use client";

import { Check, ChevronsUpDown, Loader2, Package, Search } from "lucide-react";
import React, { useMemo, useRef, useState } from "react";

import { Button } from "@/components/ui/button";
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from "@/components/ui/command";
import { Label } from "@/components/ui/label";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { cn } from "@/lib/utils";
import { fetchLoadableItems } from "@/services/SettingsService";
import { LoadableItemDto, LoadableItemType } from "@/types";
import { AsyncAutocompleteProps } from "@/types/input.dto";

interface LoadableItemInputProps extends Omit<AsyncAutocompleteProps, "onSelectionChange"> {
    showClear?: boolean;
    type?: LoadableItemType;
    name?: string;
    onSelectionChange?: (value: string | null) => void;
    onItemSelect?: (item: LoadableItemDto | null) => void;
    initialFetchSize?: number;
    searchFetchSize?: number;
}

export default function LoadableItemInput({
    showClear,
    className,
    errorMessage,
    isDisabled,
    isInvalid,
    isRequired,
    label,
    placeholder = "Search items...",
    value,
    name,
    type,
    onSelectionChange,
    onItemSelect,
    initialFetchSize = 10,
    searchFetchSize = 20,
    ...props
}: LoadableItemInputProps) {
    const [open, setOpen] = useState(false);
    const [isLoading, setIsLoading] = useState(false);
    const [loadableItems, setLoadableItems] = useState<LoadableItemDto[]>([]);
    const [searchValue, setSearchValue] = useState("");
    const [hasInitiallyFetched, setHasInitiallyFetched] = useState(false);
    const debounceTimeout = useRef<NodeJS.Timeout>();

    const selectedItem = useMemo(() => {
        if (!value && !name) return null;

        let foundItem: LoadableItemDto | undefined;

        if (value) {
            foundItem = loadableItems.find((item) => item.id.toString() === value || item.display_value === value);
        } else if (name) {
            foundItem = loadableItems.find((item) => item.display_value === name);
        }

        if (foundItem) return foundItem;

        if ((value || name) && loadableItems.length === 0) {
            return {
                id: parseInt(value) || value,
                display_value: `Loading...`,
                description: "",
            } as Partial<LoadableItemDto>;
        }

        return null;
    }, [value, name, loadableItems]);

    const handleInitialFetch = async () => {
        if (hasInitiallyFetched || isLoading) return;

        setIsLoading(true);
        setHasInitiallyFetched(true);

        try {
            const params: any = {
                type: type,
                size: initialFetchSize,
            };

            if (name) {
                params.display_value = name;
            }

            const results = await fetchLoadableItems(params);

            if (results.data) {
                setLoadableItems(results.data);
            }
        } catch (error) {
            console.error("Error fetching initial items:", error);
            setLoadableItems([]);
        } finally {
            setIsLoading(false);
        }
    };

    const handleSearch = async (searchTerm: string) => {
        setSearchValue(searchTerm);

        if (debounceTimeout.current) {
            clearTimeout(debounceTimeout.current);
        }

        if (!searchTerm.trim()) {
            if (!hasInitiallyFetched) {
                await handleInitialFetch();
            }

            return;
        }

        setIsLoading(true);

        debounceTimeout.current = setTimeout(async () => {
            try {
                const results = await fetchLoadableItems({
                    type: type,
                    display_value: searchTerm,
                    size: searchFetchSize,
                });

                if (results.data) {
                    setLoadableItems(results.data);
                }
            } catch (error) {
                console.error("Error fetching loadable items:", error);
                setLoadableItems([]);
            } finally {
                setIsLoading(false);
            }
        }, 300);
    };

    const handleOpenChange = async (newOpen: boolean) => {
        setOpen(newOpen);

        if (newOpen && !hasInitiallyFetched) {
            await handleInitialFetch();
        }
    };

    const loadSelectedItemData = async () => {
        if ((value || name) && loadableItems.length === 0 && !hasInitiallyFetched && !isLoading) {
            await handleInitialFetch();
        }
    };

    React.useEffect(() => {
        loadSelectedItemData();
    }, [value, name]);

    const handleSelect = (item: LoadableItemDto) => {
        setOpen(false);
        if (name && !value) {
            onSelectionChange?.(item.display_value);
        } else {
            onSelectionChange?.(item.id.toString());
        }
        onItemSelect?.(item);
    };

    const handleClear = () => {
        setSearchValue("");
        if (hasInitiallyFetched && !searchValue) {
        } else {
            handleInitialFetch();
        }
        onSelectionChange?.(null);
        onItemSelect?.(null);
    };

    const displayState = useMemo(() => {
        if (isLoading) {
            return {
                showEmptyPrompt: false,
                showNoResults: false,
                showItems: false,
                showLoading: true,
            };
        }

        if (!hasInitiallyFetched) {
            return {
                showEmptyPrompt: true,
                showNoResults: false,
                showItems: false,
                showLoading: false,
            };
        }

        if (searchValue && loadableItems.length === 0) {
            return {
                showEmptyPrompt: false,
                showNoResults: true,
                showItems: false,
                showLoading: false,
            };
        }

        return {
            showEmptyPrompt: false,
            showNoResults: false,
            showItems: loadableItems.length > 0,
            showLoading: false,
        };
    }, [searchValue, isLoading, loadableItems.length, hasInitiallyFetched]);

    const getPlaceholder = () => {
        if (placeholder) return placeholder;

        return type ? `Search ${type.toLowerCase()}...` : "Search items...";
    };

    const getIcon = () => {
        const iconMap: Record<string, typeof Package> = {
            category: Package,
            product: Package,
            service: Package,
        };

        const IconComponent = type ? iconMap[type.toLowerCase()] || Search : Search;

        return IconComponent;
    };

    const IconComponent = getIcon();

    return (
        <div className={cn("w-full, space-y-2", className)}>
            {label && (
                <Label
                    className={cn(
                        "block text-sm font-medium text-left",
                        "text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",
                        isRequired && "after:content-['*'] after:ml-0.5 after:text-destructive",
                    )}
                >
                    {label}
                </Label>
            )}
            <Popover open={open} onOpenChange={handleOpenChange}>
                <PopoverTrigger asChild>
                    <Button
                        aria-expanded={open}
                        className={cn(
                            "w-full justify-between font-normal border button-select",
                            !selectedItem && "text-muted-foreground",
                            isInvalid && "border-destructive focus:ring-destructive",
                            isDisabled && "cursor-not-allowed opacity-50",
                        )}
                        containerClass="w-full"
                        disabled={isDisabled}
                        role="combobox"
                        variant="ghost"
                    >
                        <div className="flex items-center gap-2 flex-1 w-full">
                            {selectedItem ? (
                                <>
                                    <Package className="h-4 w-4 text-primary flex-shrink-0" />
                                    <span className="truncate">{selectedItem.display_value}</span>
                                </>
                            ) : (
                                <>
                                    <IconComponent className="h-4 w-4 text-muted-foreground flex-shrink-0" />
                                    <span className="truncate">{getPlaceholder()}</span>
                                </>
                            )}
                        </div>
                        <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                    </Button>
                </PopoverTrigger>

                <PopoverContent align="start" className="w-full p-0">
                    <Command shouldFilter={false}>
                        <CommandInput
                            className="h-9"
                            placeholder={getPlaceholder()}
                            value={searchValue}
                            onValueChange={handleSearch}
                        />
                        <CommandList>
                            {displayState.showLoading && (
                                <div className="flex items-center justify-center py-6">
                                    <Loader2 className="h-4 w-4 animate-spin" />
                                    <span className="ml-2 text-sm text-muted-foreground">
                                        {searchValue ? "Searching..." : "Loading..."}
                                    </span>
                                </div>
                            )}

                            {displayState.showNoResults && (
                                <CommandEmpty>
                                    <div className="flex flex-col items-center py-6 text-center">
                                        <IconComponent className="h-8 w-8 text-muted-foreground mb-2" />
                                        <p className="text-sm font-medium">No items found</p>
                                        <p className="text-xs text-muted-foreground">
                                            {type
                                                ? `Try a different ${type.toLowerCase()} search`
                                                : "Try a different search term"}
                                        </p>
                                    </div>
                                </CommandEmpty>
                            )}

                            {displayState.showEmptyPrompt && (
                                <div className="flex flex-col items-center py-6 text-center">
                                    <IconComponent className="h-8 w-8 text-muted-foreground mb-2" />
                                    <p className="text-sm text-muted-foreground">
                                        {type ? `Click to load ${type.toLowerCase()}s` : "Click to load items"}
                                    </p>
                                </div>
                            )}

                            {displayState.showItems && (
                                <CommandGroup>
                                    {loadableItems.map((item) => (
                                        <CommandItem
                                            key={item.id}
                                            className="flex items-center gap-3 py-2 cursor-pointer"
                                            value={item.display_value}
                                            onSelect={() => handleSelect(item)}
                                        >
                                            <Package className="h-4 w-4 text-muted-foreground flex-shrink-0" />
                                            <div className="flex-1 min-w-0">
                                                <span className="truncate font-medium">{item.display_value}</span>
                                                {item.description && (
                                                    <p className="text-xs text-muted-foreground truncate mt-0.5">
                                                        {item.description}
                                                    </p>
                                                )}
                                            </div>
                                            <Check
                                                className={cn(
                                                    "ml-auto h-4 w-4 flex-shrink-0",
                                                    selectedItem?.id === item.id ? "opacity-100" : "opacity-0",
                                                )}
                                            />
                                        </CommandItem>
                                    ))}
                                </CommandGroup>
                            )}
                        </CommandList>
                    </Command>
                </PopoverContent>
            </Popover>

            {/* Clear button when item is selected */}
            {selectedItem && !isDisabled && showClear && (
                <Button
                    className="h-auto p-1 text-xs text-muted-foreground hover:text-foreground"
                    size="sm"
                    type="button"
                    variant="ghost"
                    onClick={handleClear}
                >
                    Clear selection
                </Button>
            )}

            {/* Error message */}
            {isInvalid && errorMessage && <p className="text-sm text-destructive">{errorMessage}</p>}
        </div>
    );
}
