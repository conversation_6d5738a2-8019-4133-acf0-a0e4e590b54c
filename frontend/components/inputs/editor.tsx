"use client";

import Link from "@tiptap/extension-link";
import TextAlign from "@tiptap/extension-text-align";
import Underline from "@tiptap/extension-underline";
import { EditorContent, useEditor } from "@tiptap/react";
import StarterKit from "@tiptap/starter-kit";
import { Bold, Italic, List, ListOrdered, LucideIcon, Redo, Underline as UnderlineIcon, Undo } from "lucide-react";
import { memo, useCallback, useMemo } from "react";

import ContentLimit from "./extensions/context-limit";

import { Button } from "@/components/ui/button";
import { Tooltip, TooltipContent, TooltipTrigger } from "@/components/ui/tooltip";

interface TiptapEditorProps {
    content?: string;
    onChange?: (html: string) => void;
    placeholder?: string;
    editable?: boolean;
    className?: string;
    maxLimit?: number;
    limitUnit?: "word" | "char";
    onLimitExceeded?: (currentCount: number) => void;
}

type ToolbarItem =
    | {
          label: string;
          icon: LucideIcon;
          isActive?: boolean;
          isDisabled?: boolean;
          action: () => void;
      }
    | "divider";

export const TiptapEditor: React.FC<TiptapEditorProps> = memo(
    ({
        content = "",
        onChange,
        placeholder = "Start writing...",
        editable = true,
        className = "",
        maxLimit = 250,
        limitUnit = "char",
        onLimitExceeded,
    }) => {
        // Memoize extensions to prevent recreation on every render
        const extensions = useMemo(
            () => [
                StarterKit.configure({
                    bulletList: { keepMarks: true, keepAttributes: false },
                    orderedList: { keepMarks: true, keepAttributes: false },
                }),
                Link.configure({
                    openOnClick: false,
                    HTMLAttributes: { class: "text-primary underline cursor-pointer hover:text-primary/80" },
                }),
                TextAlign.configure({ types: ["heading", "paragraph"] }),
                Underline,
                ContentLimit.configure({
                    limit: maxLimit,
                    unit: limitUnit,
                    onExceedLimit: onLimitExceeded,
                }),
            ],
            [maxLimit, limitUnit, onLimitExceeded],
        );

        const editor = useEditor({
            immediatelyRender: false,
            extensions,
            content,
            editable,
            onUpdate: useCallback(
                ({ editor }) => {
                    onChange?.(editor.getHTML());
                },
                [onChange],
            ),
            editorProps: {
                attributes: {
                    class: "focus:outline-none p-3 text-foreground prose prose-sm max-w-none",
                    placeholder,
                },
            },
        });

        const setLink = useCallback(() => {
            if (!editor) return;
            const previousUrl = editor.getAttributes("link").href;
            const url = window.prompt("URL", previousUrl);

            if (url === null) return;
            if (url === "") {
                editor.chain().focus().extendMarkRange("link").unsetLink().run();

                return;
            }

            editor.chain().focus().extendMarkRange("link").setLink({ href: url }).run();
        }, [editor]);

        // Memoize the toolbar button component to prevent unnecessary re-renders
        const ToolbarButton = memo(
            ({
                label,
                icon: Icon,
                isActive,
                isDisabled,
                onClick,
            }: {
                label: string;
                icon: React.ElementType;
                isActive?: boolean;
                isDisabled?: boolean;
                onClick: () => void;
            }) => (
                <Tooltip>
                    <TooltipTrigger asChild>
                        <Button
                            className={`h-8 w-8 p-0 transition-colors ${
                                isActive
                                    ? "bg-primary text-primary-foreground hover:bg-primary/90"
                                    : "hover:bg-accent hover:text-accent-foreground"
                            }`}
                            disabled={isDisabled}
                            size="sm"
                            variant="ghost"
                            onClick={onClick}
                        >
                            <Icon className="h-4 w-4" />
                        </Button>
                    </TooltipTrigger>
                    <TooltipContent className="bg-background border-border text-foreground">
                        <p>{label}</p>
                    </TooltipContent>
                </Tooltip>
            ),
        );

        // Memoize toolbar items for better performance - must be before early return
        const toolbarItems: ToolbarItem[] = useMemo(() => {
            if (!editor) return [];

            return [
                {
                    label: "Bold",
                    icon: Bold,
                    isActive: editor.isActive("bold"),
                    isDisabled: !editor.can().chain().focus().toggleBold().run(),
                    action: () => editor.chain().focus().toggleBold().run(),
                },
                {
                    label: "Italic",
                    icon: Italic,
                    isActive: editor.isActive("italic"),
                    isDisabled: !editor.can().chain().focus().toggleItalic().run(),
                    action: () => editor.chain().focus().toggleItalic().run(),
                },
                {
                    label: "Underline",
                    icon: UnderlineIcon,
                    isActive: editor.isActive("underline"),
                    isDisabled: !editor.can().chain().focus().toggleUnderline().run(),
                    action: () => editor.chain().focus().toggleUnderline().run(),
                },
                "divider",
                {
                    label: "Bullet List",
                    icon: List,
                    isActive: editor.isActive("bulletList"),
                    action: () => editor.chain().focus().toggleBulletList().run(),
                },
                {
                    label: "Numbered List",
                    icon: ListOrdered,
                    isActive: editor.isActive("orderedList"),
                    action: () => editor.chain().focus().toggleOrderedList().run(),
                },
                "divider",
                {
                    label: "Undo",
                    icon: Undo,
                    isDisabled: !editor.can().chain().focus().undo().run(),
                    action: () => editor.chain().focus().undo().run(),
                },
                {
                    label: "Redo",
                    icon: Redo,
                    isDisabled: !editor.can().chain().focus().redo().run(),
                    action: () => editor.chain().focus().redo().run(),
                },
            ];
        }, [editor, setLink]);

        if (!editor) return null;

        return (
            <div
                className={`rounded-lg border border-border bg-background/50 backdrop-blur shadow-sm transition-colors focus-within:border-primary/50 ${className}`}
            >
                {/* Toolbar */}
                <div className="flex flex-wrap gap-1 border-b border-border bg-background/30 px-3 py-2 rounded-t-lg">
                    {toolbarItems.map((item, i) =>
                        item === "divider" ? (
                            <div key={`divider-${i}`} className="w-px h-6 bg-border mx-1 my-1" />
                        ) : (
                            <ToolbarButton
                                key={item.label}
                                icon={item.icon}
                                isActive={item.isActive}
                                isDisabled={item.isDisabled}
                                label={item.label}
                                onClick={item.action}
                            />
                        ),
                    )}
                </div>

                {/* Editor Content */}
                <EditorContent
                    className={`
                    focus-within:outline-none
                    [&_.ProseMirror]:outline-none
                    [&_.ProseMirror]:text-foreground
                    [&_.ProseMirror]:bg-transparent
                    [&_.ProseMirror_ul]:list-disc
                    [&_.ProseMirror_ul]:ml-6
                    [&_.ProseMirror_ol]:list-decimal
                    [&_.ProseMirror_ol]:ml-6
                    [&_.ProseMirror_li]:my-1
                    [&_.ProseMirror_blockquote]:border-l-4
                    [&_.ProseMirror_blockquote]:border-border
                    [&_.ProseMirror_blockquote]:pl-4
                    [&_.ProseMirror_blockquote]:italic
                    [&_.ProseMirror_blockquote]:text-muted-foreground
                    [&_.ProseMirror_h1]:text-2xl
                    [&_.ProseMirror_h1]:font-bold
                    [&_.ProseMirror_h1]:my-4
                    [&_.ProseMirror_h2]:text-xl
                    [&_.ProseMirror_h2]:font-bold
                    [&_.ProseMirror_h2]:my-3
                    [&_.ProseMirror_h3]:text-lg
                    [&_.ProseMirror_h3]:font-bold
                    [&_.ProseMirror_h3]:my-2
                    [&_.ProseMirror_p]:my-2
                    [&_.ProseMirror_strong]:font-semibold
                    [&_.ProseMirror_em]:italic
                    [&_.ProseMirror]:placeholder-muted-foreground
                `}
                    editor={editor}
                />
            </div>
        );
    },
);
