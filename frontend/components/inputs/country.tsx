"use client";

import { Check, ChevronsUpDown, Globe, Loader2 } from "lucide-react";
import React, { useMemo, useRef, useState } from "react";

import { Button } from "@/components/ui/button";
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from "@/components/ui/command";
import { Label } from "@/components/ui/label";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { cn } from "@/lib/utils";
import { fetchCountries } from "@/services/SettingsService";
import { CountryDto } from "@/types";
import { AsyncAutocompleteProps } from "@/types/input.dto";

interface CountryInputProps extends Omit<AsyncAutocompleteProps, "onSelectionChange"> {
    onSelectionChange?: (value: string | null) => void;
    onCountrySelect?: (country: CountryDto | null) => void;
    name?: string;
    initialFetchSize?: number;
    searchFetchSize?: number;
    showClear?: boolean;
}

export default function CountryInput({
    className,
    errorMessage,
    isDisabled,
    isInvalid,
    isRequired,
    label,
    placeholder = "Select a country...",
    value,
    name,
    onSelectionChange,
    onCountrySelect,
    showClear,
    initialFetchSize = 10,
    searchFetchSize = 20,
    ...props
}: CountryInputProps) {
    const [open, setOpen] = useState(false);
    const [isLoading, setIsLoading] = useState(false);
    const [countries, setCountries] = useState<CountryDto[]>([]);
    const [searchValue, setSearchValue] = useState("");
    const [hasInitiallyFetched, setHasInitiallyFetched] = useState(false);
    const debounceTimeout = useRef<NodeJS.Timeout>();

    const loadSelectedCountryData = async () => {
        if ((value || name) && countries.length === 0 && !hasInitiallyFetched && !isLoading) {
            await handleInitialFetch();
        }
    };

    React.useEffect(() => {
        loadSelectedCountryData();
    }, [value, name]);

    const selectedCountry = useMemo(() => {
        if (!value && !name) return null;

        let foundCountry: CountryDto | undefined;

        if (value) {
            foundCountry = countries.find((c) => c.id.toString() === value || c.name === value);
        } else if (name) {
            foundCountry = countries.find((c) => c.name === name);
        }

        if (foundCountry) return foundCountry;

        if ((value || name) && countries.length === 0) {
            return {
                id: value ? parseInt(value) || value : name || "",
                name: name || `Loading...`,
                flag: "🌍",
                code: "",
            } as unknown as CountryDto;
        }

        return null;
    }, [value, name, countries]);

    const handleInitialFetch = async () => {
        if (hasInitiallyFetched || isLoading) return;

        setIsLoading(true);
        setHasInitiallyFetched(true);

        try {
            const params: any = {
                size: initialFetchSize,
            };

            if (name) {
                params.name = name;
            }

            const results = await fetchCountries(params);

            if (results.data) {
                setCountries(results.data);
            }
        } catch (error) {
            console.error("Error fetching initial countries:", error);
            setCountries([]);
        } finally {
            setIsLoading(false);
        }
    };

    const handleSearch = async (searchTerm: string) => {
        setSearchValue(searchTerm);

        if (debounceTimeout.current) {
            clearTimeout(debounceTimeout.current);
        }

        if (!searchTerm.trim()) {
            if (!hasInitiallyFetched) {
                await handleInitialFetch();
            }

            return;
        }

        setIsLoading(true);

        debounceTimeout.current = setTimeout(async () => {
            try {
                const results = await fetchCountries({
                    name: searchTerm,
                    size: searchFetchSize,
                });

                if (results.data) {
                    setCountries(results.data);
                }
            } catch (error) {
                console.error("Error fetching countries:", error);
                setCountries([]);
            } finally {
                setIsLoading(false);
            }
        }, 300);
    };

    const handleOpenChange = async (newOpen: boolean) => {
        setOpen(newOpen);

        if (newOpen && !hasInitiallyFetched) {
            await handleInitialFetch();
        }
    };

    const handleSelect = (country: CountryDto) => {
        setOpen(false);

        if (name && !value) {
            onSelectionChange?.(country.name);
        } else {
            onSelectionChange?.(country.id.toString());
        }
        onCountrySelect?.(country);
    };

    const handleClear = () => {
        setSearchValue("");

        if (hasInitiallyFetched && !searchValue) {
            // Items are already the initial fetch, no need to change
        } else {
            handleInitialFetch();
        }
        onSelectionChange?.(null);
        onCountrySelect?.(null);
    };

    const displayState = useMemo(() => {
        if (isLoading) {
            return {
                showEmptyPrompt: false,
                showNoResults: false,
                showCountries: false,
                showLoading: true,
            };
        }

        if (!hasInitiallyFetched) {
            return {
                showEmptyPrompt: true,
                showNoResults: false,
                showCountries: false,
                showLoading: false,
            };
        }

        if (searchValue && countries.length === 0) {
            return {
                showEmptyPrompt: false,
                showNoResults: true,
                showCountries: false,
                showLoading: false,
            };
        }

        return {
            showEmptyPrompt: false,
            showNoResults: false,
            showCountries: countries.length > 0,
            showLoading: false,
        };
    }, [searchValue, isLoading, countries.length, hasInitiallyFetched]);

    return (
        <div className={cn("w-full space-y-2", className)}>
            {label && (
                <Label
                    className={cn(
                        "block text-sm font-medium text-left",
                        "peer-disabled:cursor-not-allowed peer-disabled:opacity-70",
                        isRequired && "after:content-['*'] after:ml-0.5 after:text-destructive",
                    )}
                >
                    {label}
                </Label>
            )}
            <Popover open={open} onOpenChange={handleOpenChange}>
                <PopoverTrigger asChild>
                    <Button
                        aria-expanded={open}
                        className={cn(
                            "w-full justify-between font-normal",
                            !selectedCountry && "text-muted-foreground",
                            isInvalid && "border-destructive focus:ring-destructive",
                            isDisabled && "cursor-not-allowed opacity-50",
                        )}
                        containerClass="w-full"
                        disabled={isDisabled}
                        role="combobox"
                        variant="outline"
                    >
                        <div className="flex items-center gap-2 flex-1 min-w-0">
                            {selectedCountry ? (
                                <>
                                    <span className="text-xl flex-shrink-0">{selectedCountry.flag}</span>
                                    <span className="truncate">{selectedCountry.name}</span>
                                </>
                            ) : (
                                <>
                                    <Globe className="h-4 w-4 text-muted-foreground flex-shrink-0" />
                                    <span className="truncate">{placeholder}</span>
                                </>
                            )}
                        </div>
                        <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                    </Button>
                </PopoverTrigger>

                <PopoverContent align="start" className="w-full p-0">
                    <Command shouldFilter={false}>
                        <CommandInput
                            className="h-9"
                            placeholder="Search countries..."
                            value={searchValue}
                            onValueChange={handleSearch}
                        />
                        <CommandList>
                            {displayState.showLoading && (
                                <div className="flex items-center justify-center py-6">
                                    <Loader2 className="h-4 w-4 animate-spin" />
                                    <span className="ml-2 text-sm text-muted-foreground">
                                        {searchValue ? "Searching..." : "Loading..."}
                                    </span>
                                </div>
                            )}

                            {displayState.showNoResults && (
                                <CommandEmpty>
                                    <div className="flex flex-col items-center py-6 text-center">
                                        <Globe className="h-8 w-8 text-muted-foreground mb-2" />
                                        <p className="text-sm font-medium">No countries found</p>
                                        <p className="text-xs text-muted-foreground">Try a different search term</p>
                                    </div>
                                </CommandEmpty>
                            )}

                            {displayState.showEmptyPrompt && (
                                <div className="flex flex-col items-center py-6 text-center">
                                    <Globe className="h-8 w-8 text-muted-foreground mb-2" />
                                    <p className="text-sm text-muted-foreground">Click to load countries</p>
                                </div>
                            )}

                            {displayState.showCountries && (
                                <CommandGroup>
                                    {countries.map((country) => (
                                        <CommandItem
                                            key={country.id}
                                            className="flex items-center gap-3 py-2 cursor-pointer"
                                            value={country.name}
                                            onSelect={() => handleSelect(country)}
                                        >
                                            <span className="text-xl flex-shrink-0">{country.flag}</span>
                                            <span className="flex-1 truncate">{country.name}</span>
                                            <Check
                                                className={cn(
                                                    "ml-auto h-4 w-4",
                                                    selectedCountry?.id === country.id ? "opacity-100" : "opacity-0",
                                                )}
                                            />
                                        </CommandItem>
                                    ))}
                                </CommandGroup>
                            )}
                        </CommandList>
                    </Command>
                </PopoverContent>
            </Popover>

            {/* Clear button when country is selected */}
            {selectedCountry && !isDisabled && showClear && (
                <Button
                    className="h-auto p-1 text-xs text-muted-foreground hover:text-foreground"
                    size="sm"
                    type="button"
                    variant="ghost"
                    onClick={handleClear}
                >
                    Clear selection
                </Button>
            )}

            {/* Error message */}
            {isInvalid && errorMessage && <p className="text-sm text-destructive">{errorMessage}</p>}
        </div>
    );
}
