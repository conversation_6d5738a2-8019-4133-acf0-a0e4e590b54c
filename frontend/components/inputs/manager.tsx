"use client";

import { Building2, Check, ChevronsUpDown, Currency, GroupIcon, Loader2, MapPin, UserCheck2Icon } from "lucide-react";
import React, { useMemo, useRef, useState } from "react";

import { Button } from "@/components/ui/button";
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from "@/components/ui/command";
import { Label } from "@/components/ui/label";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { cn } from "@/lib/utils";
import * as DepartmentService from "@/services/DepartmentService";
import * as organizationService from "@/services/OrganizationService";
import * as RoleService from "@/services/RoleService";
import * as settingsService from "@/services/SettingsService";
import { CurrencyDto, DepartmentDto, DistrictDto, DistrictFilter, RegionDto, RolePermissionDto } from "@/types";
import { OrganizationDto } from "@/types/organization.dto";
import { capitalize } from "@/utils/common";

export type InputType = "role" | "department" | "district" | "currency" | "region" | "organization";

type SelectedItem = DepartmentDto | DistrictDto | RegionDto | CurrencyDto | RolePermissionDto;

export type ManagerInputProps = {
    type: InputType;
    className?: string;
    errorMessage?: string;
    isDisabled?: boolean;
    isInvalid?: boolean;
    isRequired?: boolean;
    label?: string;
    regionId?: string;
    placeholder?: string;
    value?: string;
    name?: string;
    initialFetchSize?: number;
    searchFetchSize?: number;
    onSelectionChange?: (value: string | null) => void;
    onItemSelect?: (item: SelectedItem | null) => void;
    showClear?: boolean;
};

export const ManagerInput = ({
    type,
    className,
    showClear,
    errorMessage,
    isDisabled,
    isInvalid,
    isRequired,
    label,
    placeholder,
    value,
    name,
    regionId,
    onSelectionChange,
    onItemSelect,
    initialFetchSize = 10,
    searchFetchSize = 15,
}: ManagerInputProps) => {
    const [open, setOpen] = useState(false);
    const [isLoading, setIsLoading] = useState(false);
    const [departments, setDepartments] = useState<DepartmentDto[]>([]);
    const [roles, setRoles] = useState<RolePermissionDto[]>([]);
    const [districts, setDistricts] = useState<DistrictDto[]>([]);
    const [regions, setRegions] = useState<RegionDto[]>([]);
    const [currencies, setCurrencies] = useState<CurrencyDto[]>([]);
    const [organizations, setOrganizations] = useState<OrganizationDto[]>([]);
    const [searchValue, setSearchValue] = useState("");
    const [hasInitiallyFetched, setHasInitiallyFetched] = useState(false);
    const debounceTimeout = useRef<NodeJS.Timeout>();

    const loadSelectedItemData = async () => {
        if ((value || name) && items.length === 0 && !hasInitiallyFetched && !isLoading) {
            await handleInitialFetch();
        }
    };

    React.useEffect(() => {
        loadSelectedItemData();
    }, [value, name]);

    const itemsData = {
        department: departments,
        role: roles,
        district: districts,
        region: regions,
        currency: currencies,
        organization: organizations,
    };

    const items = itemsData[type];

    const handleInitialFetch = async () => {
        if (hasInitiallyFetched || isLoading) return;

        setIsLoading(true);
        setHasInitiallyFetched(true);

        try {
            if (type === "department") {
                const params: any = {
                    page: 1,
                    size: initialFetchSize,
                };

                if (name) {
                    params.name = name;
                }

                const results = await DepartmentService.fetchAll(params);

                if (results.data) setDepartments(results.data);
            }

            if (type === "role") {
                const params: any = {
                    page: 1,
                    size: initialFetchSize,
                };

                if (name) {
                    params.name = name;
                }

                const results = await RoleService.fetchAll(params);

                if (results.data) setRoles(results.data);
            }

            if (type === "district") {
                const filter: Partial<DistrictFilter> = { page: 1, size: initialFetchSize };

                if (regionId) Object.assign(filter, { region_id: regionId });

                if (name) Object.assign(filter, { name: name });

                const response = await settingsService.fetchDistricts(filter);

                if (response.data) setDistricts(response.data);
            }

            if (type === "region") {
                const params: any = { page: 1, size: initialFetchSize };

                if (name) {
                    params.name = name;
                }

                const response = await settingsService.fetchRegions(params);

                if (response.data) setRegions(response.data);
            }

            if (type === "currency") {
                const params: any = { page: 1, size: initialFetchSize };

                if (name) {
                    params.name = name;
                }

                const response = await settingsService.fetchCurrencies(params);

                if (response.data) setCurrencies(response.data);
            }

            if (type === "organization") {
                const params: any = { page: 1, size: initialFetchSize };

                if (name) {
                    params.name = name;
                }

                const response = await organizationService.fetchOrganizations(params);

                if (response.data) setOrganizations(response.data);
            }
        } catch (error) {
            console.error(`Error fetching initial ${type}s:`, error);
        } finally {
            setIsLoading(false);
        }
    };

    const selectedItem = useMemo(() => {
        if (!value && !name) return null;

        let foundItem: SelectedItem | undefined;

        if (value) {
            foundItem = items.find((item) => item.id.toString() === value || item.name === value);
        } else if (name) {
            foundItem = items.find((item) => item.name === name);
        }

        if (foundItem) return foundItem;

        if ((value || name) && items.length === 0) {
            return {
                id: value ? parseInt(value) || value : name || "",
                name: name || `Loading...`,
            } as SelectedItem;
        }

        return null;
    }, [value, name, items]);

    const handleSearch = async (searchTerm: string) => {
        setSearchValue(searchTerm);

        if (debounceTimeout.current) {
            clearTimeout(debounceTimeout.current);
        }

        if (!searchTerm.trim()) {
            if (!hasInitiallyFetched) {
                await handleInitialFetch();
            }

            return;
        }

        setIsLoading(true);

        debounceTimeout.current = setTimeout(async () => {
            try {
                if (type === "department") {
                    const results = await DepartmentService.fetchAll({
                        name: searchTerm,
                        page: 1,
                        size: searchFetchSize,
                    });

                    if (results.data) {
                        setDepartments(results.data);
                    }
                }

                if (type === "role") {
                    const results = await RoleService.fetchAll({
                        name: searchTerm,
                        page: 1,
                        size: searchFetchSize,
                    });

                    if (results.data) {
                        setRoles(results.data);
                    }
                }

                if (type === "district") {
                    const filter: Partial<DistrictFilter> = { page: 1, size: searchFetchSize, name: searchTerm };

                    if (regionId) {
                        Object.assign(filter, { region_id: regionId });
                    }

                    const response = await settingsService.fetchDistricts(filter);

                    if (response.data) {
                        setDistricts(response.data);
                    }
                }

                if (type === "region") {
                    const response = await settingsService.fetchRegions({
                        page: 1,
                        size: searchFetchSize,
                        name: searchTerm,
                    });

                    if (response.data) {
                        setRegions(response.data);
                    }
                }

                if (type === "currency") {
                    const response = await settingsService.fetchCurrencies({
                        page: 1,
                        size: searchFetchSize,
                        name: searchTerm,
                    });

                    if (response.data) {
                        setCurrencies(response.data);
                    }
                }

                if (type === "organization") {
                    const params: any = {
                        page: 1,
                        size: searchFetchSize,
                        name: searchTerm,
                    };

                    const response = await organizationService.fetchOrganizations(params);

                    if (response.data) {
                        setOrganizations(response.data);
                    }
                }
            } catch (error) {
                console.error(`Error fetching ${type}s:`, error);
                if (type === "department") {
                    setDepartments([]);
                } else {
                    setRoles([]);
                }
            } finally {
                setIsLoading(false);
            }
        }, 300);
    };

    const handleSelect = (item: SelectedItem) => {
        setOpen(false);

        if (name && !value) {
            onSelectionChange?.(item.name);
        } else {
            onSelectionChange?.(item.id.toString());
        }
        onItemSelect?.(item);
    };

    const handleClear = () => {
        setSearchValue("");
        setDepartments([]);
        setRoles([]);
        setDistricts([]);
        setDistricts([]);
        setCurrencies([]);
        setOrganizations([]);
        onSelectionChange?.(null);
        onItemSelect?.(null);
    };

    const displayState = useMemo(() => {
        if (isLoading) {
            return {
                showEmptyPrompt: false,
                showNoResults: false,
                showItems: false,
                showLoading: true,
            };
        }

        if (!hasInitiallyFetched) {
            return {
                showEmptyPrompt: true,
                showNoResults: false,
                showItems: false,
                showLoading: false,
            };
        }

        if (searchValue && items.length === 0) {
            return {
                showEmptyPrompt: false,
                showNoResults: true,
                showItems: false,
                showLoading: false,
            };
        }

        return {
            showEmptyPrompt: false,
            showNoResults: false,
            showItems: items.length > 0,
            showLoading: false,
        };
    }, [searchValue, isLoading, items.length, hasInitiallyFetched]);

    const getPlaceholder = () => {
        if (placeholder) return placeholder;

        return `Search ${type}s...`;
    };

    const getIcon = () => {
        const icons = {
            department: GroupIcon,
            role: UserCheck2Icon,
            district: MapPin,
            region: MapPin,
            currency: Currency,
            organization: Building2,
        };

        return icons[type];
    };

    const getLabel = () => {
        if (label) return label;

        return capitalize(type);
    };

    const IconComponent = getIcon();

    return (
        <div className={cn("w-full space-y-2", className)}>
            {(label !== undefined || !label) && (
                <Label
                    className={cn(
                        "block text-sm font-medium text-left",
                        "text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",
                    )}
                >
                    {getLabel() + (isRequired ? " *" : "")}
                </Label>
            )}
            <Popover
                open={open}
                onOpenChange={async (newOpen) => {
                    setOpen(newOpen);
                    if (newOpen && !hasInitiallyFetched) {
                        await handleInitialFetch();
                    }
                }}
            >
                <PopoverTrigger asChild>
                    <Button
                        aria-expanded={open}
                        className={cn(
                            "w-full justify-between font-normal border button-select",
                            !selectedItem && "text-muted-foreground",
                            isInvalid && "border-destructive focus:ring-destructive",
                            isDisabled && "cursor-not-allowed opacity-50",
                        )}
                        containerClass="w-full"
                        disabled={isDisabled}
                        role="combobox"
                        variant="ghost"
                    >
                        <div className="flex items-center gap-2 flex-1 min-w-0">
                            {selectedItem ? (
                                <>
                                    <IconComponent className="h-4 w-4 text-primary flex-shrink-0" />
                                    <span className="truncate">{selectedItem.name}</span>
                                </>
                            ) : (
                                <>
                                    <IconComponent className="h-4 w-4 text-muted-foreground flex-shrink-0" />
                                    <span className="truncate">{getPlaceholder()}</span>
                                </>
                            )}
                        </div>
                        <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                    </Button>
                </PopoverTrigger>

                <PopoverContent align="start" className="w-full p-0">
                    <Command shouldFilter={false}>
                        <CommandInput
                            className="h-9"
                            placeholder={getPlaceholder()}
                            value={searchValue}
                            onValueChange={handleSearch}
                        />
                        <CommandList>
                            {displayState.showLoading && (
                                <div className="flex items-center justify-center py-6">
                                    <Loader2 className="h-4 w-4 animate-spin" />
                                    <span className="ml-2 text-sm text-muted-foreground">
                                        {searchValue ? "Searching..." : "Loading..."}
                                    </span>
                                </div>
                            )}
                            {displayState.showNoResults && (
                                <CommandEmpty>
                                    <div className="flex flex-col items-center py-6 text-center">
                                        <IconComponent className="h-8 w-8 text-muted-foreground mb-2" />
                                        <p className="text-sm font-medium">No {type}s found</p>
                                        <p className="text-xs text-muted-foreground">Try a different {type} search</p>
                                    </div>
                                </CommandEmpty>
                            )}
                            {displayState.showEmptyPrompt && (
                                <div className="flex flex-col items-center py-6 text-center">
                                    <IconComponent className="h-8 w-8 text-muted-foreground mb-2" />
                                    <p className="text-sm text-muted-foreground">Click to load {type}s</p>
                                </div>
                            )}

                            {displayState.showItems && (
                                <CommandGroup>
                                    {items.map((item) => (
                                        <CommandItem
                                            key={item.id}
                                            className="flex items-center gap-3 py-2 cursor-pointer"
                                            value={item.name}
                                            onSelect={() => handleSelect(item)}
                                        >
                                            <IconComponent className="h-4 w-4 text-muted-foreground flex-shrink-0" />
                                            <div className="flex-1 min-w-0">
                                                <span className="truncate font-medium">{item.name}</span>
                                                {/* Add description if available in your DTOs */}
                                                {/* {item.description && (
                                                    <p className="text-xs text-muted-foreground truncate mt-0.5">
                                                        {item.description}
                                                    </p>
                                                )} */}
                                            </div>
                                            <Check
                                                className={cn(
                                                    "ml-auto h-4 w-4 flex-shrink-0",
                                                    selectedItem?.id === item.id ? "opacity-100" : "opacity-0",
                                                )}
                                            />
                                        </CommandItem>
                                    ))}
                                </CommandGroup>
                            )}
                        </CommandList>
                    </Command>
                </PopoverContent>
            </Popover>

            {/* Clear button when item is selected */}
            {selectedItem && !isDisabled && showClear && (
                <Button
                    className="h-auto p-1 text-xs text-muted-foreground hover:text-foreground"
                    size="sm"
                    type="button"
                    variant="ghost"
                    onClick={handleClear}
                >
                    Clear selection
                </Button>
            )}

            {/* Error message */}
            {isInvalid && errorMessage && <p className="text-sm text-destructive">{errorMessage}</p>}
        </div>
    );
};
