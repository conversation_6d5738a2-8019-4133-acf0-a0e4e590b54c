"use client";
import React from "react";

import { ApplicationPipelineChart } from "@/components/dashboard/ApplicationPipeline";
import { QuickActions } from "@/components/dashboard/QuickActions";
import { QuickStatsOverview } from "@/components/dashboard/QuickStatsOverview";
import { RecentActivityFeed } from "@/components/dashboard/RecentActivity";
import { ApplicationPipeline, DashboardStats, RecentActivity, User } from "@/types/dashboard";

interface DashboardPageContentProps {
    loading?: boolean;
    user: User;
}

export const DashboardPageContent: React.FC<DashboardPageContentProps> = ({ loading = false, user }) => {
    // Mock data - replace with real API calls
    const dashboardStats: DashboardStats = {
        totalUsers: 1247,
        totalOrganizations: 342,
        pendingApplications: 28,
        approvedApplications: 156,
        totalRevenue: 84500,
        activeWorkflows: 15,
        monthlyGrowth: {
            users: 12.5,
            organizations: 8.3,
            applications: -5.2,
            revenue: 15.7,
        },
    };

    const applicationPipeline: ApplicationPipeline[] = [
        { stage: "Submitted", count: 45, percentage: 35, color: "blue" },
        { stage: "Under Review", count: 28, percentage: 22, color: "yellow" },
        {
            stage: "Pending Approval",
            count: 18,
            percentage: 14,
            color: "yellow",
        },
        { stage: "Approved", count: 37, percentage: 29, color: "green" },
    ];

    const recentActivities: RecentActivity[] = [
        {
            id: "1",
            type: "application",
            title: "New application submitted",
            description: "Green Earth Foundation submitted registration application",
            timestamp: new Date(Date.now() - 1000 * 60 * 15).toISOString(),
            status: "pending",
            user: "System",
        },
        {
            id: "2",
            type: "organization",
            title: "Organization approved",
            description: "Hope Children Center registration approved",
            timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2).toISOString(),
            status: "approved",
            user: "Jane Smith",
        },
        {
            id: "3",
            type: "user",
            title: "New user created",
            description: "John Doe added as Organization Admin",
            timestamp: new Date(Date.now() - 1000 * 60 * 60 * 4).toISOString(),
            status: "completed",
            user: "Admin",
        },
    ];

    const currentTime = new Date().toLocaleDateString("en-US", {
        weekday: "long",
        year: "numeric",
        month: "long",
        day: "numeric",
    });

    return (
        <div className="min-h-screen">
            <div className="space-y-8 p-6">
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                    {/* Perfect Size Welcome Section */}
                    <div className="lg:col-span-2 space-y-8 relative overflow-hidden rounded-2xl bg-gradient-to-br from-orange-600 via-purple-700 to-indigo-800">
                        {/* Background Elements */}
                        <div className="absolute inset-0">
                            <div className="absolute inset-0 bg-gradient-to-br from-blue-500/20 via-purple-500/10 to-indigo-600/20" />
                            <div className="absolute top-0 right-0 w-80 h-80 bg-gradient-to-br from-purple-400/15 to-pink-400/15 rounded-full blur-3xl" />
                            <div className='absolute inset-0 bg-[url(`data:image/svg+xml,%3Csvg width=\"60\" height=\"60\" viewBox=\"0 0 60 60\" xmlns=\"http://www.w3.org/2000/svg\"%3E%3Cg fill=\"none\" fill-rule=\"evenodd\"%3E%3Cg fill=\"%23ffffff\" fill-opacity=\"0.05\"%3E%3Ccircle cx=\"30\" cy=\"30\" r=\"2\"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E`)] opacity-40' />
                        </div>

                        {/* Content */}
                        <div className="relative px-6 py-8 sm:px-8" style={{ marginTop: "0px" }}>
                            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 items-center">
                                {/* Left - Main Greeting */}
                                <div className="lg:col-span-2 space-y-4">
                                    {/* Time and Date Row */}
                                    <div className="flex items-center space-x-4 text-blue-100">
                                        <div className="flex items-center space-x-2">
                                            <svg
                                                className="h-4 w-4"
                                                fill="none"
                                                stroke="currentColor"
                                                viewBox="0 0 24 24"
                                            >
                                                <path
                                                    d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"
                                                    strokeLinecap="round"
                                                    strokeLinejoin="round"
                                                    strokeWidth={2}
                                                />
                                            </svg>
                                            <span className="text-sm font-medium">
                                                {new Date().toLocaleTimeString("en-US", {
                                                    hour: "2-digit",
                                                    minute: "2-digit",
                                                    hour12: true,
                                                })}
                                            </span>
                                        </div>
                                        <div className="h-3 w-px bg-blue-300/30" />
                                        <span className="text-sm">{currentTime}</span>
                                    </div>

                                    {/* Main Greeting */}
                                    <div className="space-y-2">
                                        <div className="flex items-center space-x-3">
                                            <h1 className="text-3xl sm:text-4xl font-bold text-white tracking-tight">
                                                {(() => {
                                                    const hour = new Date().getHours();

                                                    if (hour < 12) return "Good morning";
                                                    else if (hour < 17) return "Good afternoon";
                                                    else return "Good evening";
                                                })()}
                                                , {user.name.split(" ")[0]}!
                                            </h1>
                                            <div className="flex items-center space-x-1">
                                                <svg
                                                    className="h-6 w-6 text-yellow-400 animate-pulse"
                                                    fill="none"
                                                    stroke="currentColor"
                                                    viewBox="0 0 24 24"
                                                >
                                                    <path
                                                        d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"
                                                        strokeLinecap="round"
                                                        strokeLinejoin="round"
                                                        strokeWidth={2}
                                                    />
                                                </svg>
                                            </div>
                                        </div>
                                        <p className="text-lg text-blue-100 font-medium">
                                            Ready to manage your NGO platform today
                                        </p>
                                    </div>

                                    {/* User Info & Quick Stats */}
                                    <div className="flex flex-wrap items-center gap-4">
                                        <div className="inline-flex items-center space-x-2 px-3 py-1.5 rounded-full bg-white/10 backdrop-blur-sm border border-white/20">
                                            <svg
                                                className="h-4 w-4 text-blue-300"
                                                fill="none"
                                                stroke="currentColor"
                                                viewBox="0 0 24 24"
                                            >
                                                <path
                                                    d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"
                                                    strokeLinecap="round"
                                                    strokeLinejoin="round"
                                                    strokeWidth={2}
                                                />
                                            </svg>
                                            <span className="text-sm font-medium text-white">{user.role}</span>
                                        </div>

                                        {user.lastLogin && (
                                            <div className="inline-flex items-center space-x-2 px-3 py-1.5 rounded-full bg-white/10 backdrop-blur-sm border border-white/20">
                                                <svg
                                                    className="h-4 w-4 text-green-300"
                                                    fill="none"
                                                    stroke="currentColor"
                                                    viewBox="0 0 24 24"
                                                >
                                                    <path
                                                        d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                                                        strokeLinecap="round"
                                                        strokeLinejoin="round"
                                                        strokeWidth={2}
                                                    />
                                                </svg>
                                                <span className="text-sm text-blue-200">Last: {user.lastLogin}</span>
                                            </div>
                                        )}

                                        <div className="flex items-center space-x-6 text-white">
                                            <div className="text-center">
                                                <p className="text-xl font-bold">28</p>
                                                <p className="text-xs text-blue-200">Pending</p>
                                            </div>
                                            <div className="text-center">
                                                <p className="text-xl font-bold text-green-400">342</p>
                                                <p className="text-xs text-blue-200">Organizations</p>
                                            </div>
                                            <div className="text-center">
                                                <p className="text-xl font-bold text-purple-400">1,247</p>
                                                <p className="text-xs text-blue-200">Users</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            {/* Bottom Stats Bar */}
                            <div className="mt-6 pt-4 border-t border-white/10 flex items-center justify-between text-blue-200">
                                <div className="flex items-center space-x-4 text-sm">
                                    <div className="flex items-center space-x-2">
                                        <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path
                                                d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"
                                                strokeLinecap="round"
                                                strokeLinejoin="round"
                                                strokeWidth={2}
                                            />
                                        </svg>
                                        <span>Platform growing 12.5% this month</span>
                                    </div>
                                    <div className="hidden sm:flex items-center space-x-2">
                                        <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path
                                                d="M15 17h5l-5 5v-5z"
                                                strokeLinecap="round"
                                                strokeLinejoin="round"
                                                strokeWidth={2}
                                            />
                                        </svg>
                                        <span>3 notifications pending</span>
                                    </div>
                                </div>
                                <div className="inline-flex items-center space-x-2 px-3 py-1 rounded-full bg-green-500/20 border border-green-400/30">
                                    <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse" />
                                    <span className="text-sm font-medium text-green-200">Online</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div className="space-y-8">
                        {/* Quick Actions */}
                        <QuickActions />
                    </div>
                </div>
                {/* Quick Stats Overview */}
                <div className="space-y-6">
                    <div className="flex items-center justify-between">
                        <div>
                            <h2 className="text-2xl font-bold tracking-tight">Platform Overview</h2>
                            <p className="text-gray-500 font-medium">Key performance metrics and insights</p>
                        </div>
                    </div>
                    <QuickStatsOverview loading={loading} stats={dashboardStats} />
                </div>

                {/* Main Content Grid */}
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                    {/* Left Column - 2/3 width */}
                    <div className="lg:col-span-2 space-y-8">
                        {/* Application Pipeline */}
                        <ApplicationPipelineChart loading={loading} pipeline={applicationPipeline} />
                    </div>

                    {/* Right Column - 1/3 width */}
                    <div className="space-y-8">
                        {/* Recent Activity */}
                        <RecentActivityFeed activities={recentActivities} loading={loading} />
                    </div>
                </div>
            </div>
        </div>
    );
};
