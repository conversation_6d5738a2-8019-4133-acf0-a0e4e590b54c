"use client";

import { useRouter } from "next/navigation";
import { useEffect } from "react";

import { setRouterInstance } from "@/config/http";

export function RouterProvider({ children }: { children: React.ReactNode }) {
    const router = useRouter();

    useEffect(() => {
        setRouterInstance(router);

        return () => {
            setRouterInstance(null);
        };
    }, [router]);

    return <>{children}</>;
}
