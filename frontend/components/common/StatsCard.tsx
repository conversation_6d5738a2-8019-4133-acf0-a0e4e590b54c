"use client";
import { ArrowUpRightIcon, TrendingDownIcon, TrendingUpIcon } from "lucide-react";
import React from "react";

interface ChangeData {
    value: number;
    type: "increase" | "decrease" | "neutral";
    period: string;
}

interface StatsCardProps {
    title: string;
    value: string | number;
    change?: ChangeData;
    icon?: React.ComponentType<{ className?: string }>;
    color?: "blue" | "green" | "purple" | "orange" | "red";
    trend?: number[];
    loading?: boolean;
    onClick?: () => void;
    className?: string;
}

export const StatsCard: React.FC<StatsCardProps> = ({
    title,
    value,
    change,
    icon: Icon,
    color = "blue",
    trend,
    loading = false,
    onClick,
    className = "",
}) => {
    const colorSchemes = {
        blue: {
            bg: "from-blue-500 to-blue-600",
            light: "from-blue-50 to-blue-100",
            text: "text-blue-600",
            ring: "ring-blue-500/20",
            shadow: "shadow-blue-500/25",
        },
        green: {
            bg: "from-green-500 to-emerald-600",
            light: "from-green-50 to-emerald-100",
            text: "text-green-600",
            ring: "ring-green-500/20",
            shadow: "shadow-green-500/25",
        },
        purple: {
            bg: "from-purple-500 to-violet-600",
            light: "from-purple-50 to-violet-100",
            text: "text-purple-600",
            ring: "ring-purple-500/20",
            shadow: "shadow-purple-500/25",
        },
        orange: {
            bg: "from-orange-500 to-amber-600",
            light: "from-orange-50 to-amber-100",
            text: "text-orange-600",
            ring: "ring-orange-500/20",
            shadow: "shadow-orange-500/25",
        },
        red: {
            bg: "from-red-500 to-rose-600",
            light: "from-red-50 to-rose-100",
            text: "text-red-600",
            ring: "ring-red-500/20",
            shadow: "shadow-red-500/25",
        },
    };

    const scheme = colorSchemes[color];

    if (loading) {
        return (
            <div
                className={`relative overflow-hidden rounded-xl bg-white border border-gray-200/60 shadow-lg shadow-gray-900/5 p-6 animate-pulse ${className}`}
            >
                <div className="space-y-4">
                    <div className="flex items-center justify-between">
                        <div className="h-10 w-10 bg-gray-200 rounded-lg" />
                        <div className="h-4 w-16 bg-gray-200 rounded" />
                    </div>
                    <div className="space-y-2">
                        <div className="h-4 bg-gray-200 rounded w-2/3" />
                        <div className="h-8 bg-gray-200 rounded w-1/2" />
                        <div className="h-3 bg-gray-200 rounded w-1/3" />
                    </div>
                </div>
            </div>
        );
    }

    return (
        <div
            className={`group relative overflow-hidden rounded-xl bg-white border border-gray-200/60 shadow-lg shadow-gray-900/5 transition-all duration-300 hover:shadow-xl hover:${scheme.shadow} hover:-translate-y-1 ${onClick ? "cursor-pointer" : ""} ${className}`}
            // onClick={onClick}
        >
            {/* Background Pattern */}
            <div className="absolute inset-0 bg-gradient-to-br from-gray-50/50 to-transparent opacity-60" />

            {/* Content */}
            <div className="relative p-6">
                <div className="flex items-center justify-between mb-4">
                    {Icon && (
                        <div className={`p-3 rounded-xl bg-gradient-to-br ${scheme.light} ring-1 ${scheme.ring}`}>
                            <Icon className={`h-6 w-6 ${scheme.text}`} />
                        </div>
                    )}
                    {onClick && (
                        <ArrowUpRightIcon className="h-5 w-5 text-gray-400 group-hover:text-gray-600 transition-colors" />
                    )}
                </div>

                <div className="space-y-2">
                    <p className="text-sm font-medium text-gray-600 tracking-wide uppercase">{title}</p>
                    <p className="text-3xl font-bold text-gray-900 tracking-tight">
                        {typeof value === "number" ? value.toLocaleString() : value}
                    </p>
                    {change && (
                        <div className="flex items-center space-x-2">
                            <div
                                className={`flex items-center space-x-1 px-2.5 py-1 rounded-full text-xs font-semibold ${
                                    change.type === "increase"
                                        ? "text-green-700 bg-green-100"
                                        : change.type === "decrease"
                                            ? "text-red-700 bg-red-100"
                                            : "text-gray-700 bg-gray-100"
                                }`}
                            >
                                {change.type === "increase" ? (
                                    <TrendingUpIcon className="h-3 w-3" />
                                ) : change.type === "decrease" ? (
                                    <TrendingDownIcon className="h-3 w-3" />
                                ) : null}
                                <span>
                                    {change.type === "increase" ? "+" : change.type === "decrease" ? "-" : ""}
                                    {Math.abs(change.value)}%
                                </span>
                            </div>
                            <span className="text-xs text-gray-500 font-medium">vs {change.period}</span>
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
};
