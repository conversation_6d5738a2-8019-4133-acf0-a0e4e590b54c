import { useEffect, useState } from "react";

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>er, Card<PERSON>itle } from "../ui/card";
import { Button } from "../ui/button";
import { Checkbox } from "../ui/checkbox";

import { useError } from "@/context/ErrorContext";
import { usePermissions } from "@/app/management/roles/roleHooks";
import { LoadableItemDto, RolePermissionDto } from "@/types";
import { useLoading } from "@/context/LoadingContext";

interface PermissionFormInterface {
    role: Partial<RolePermissionDto>;
    onClose: () => void;
    onSave: (role: Partial<RolePermissionDto>) => void;
    loading: boolean;
}

export default function PermissionsForm({ role, onClose, onSave, loading }: PermissionFormInterface) {
    const { data: permissions, isError, isLoading } = usePermissions(role.id || "");
    const { showError, hideError } = useError();
    const { showLoading, hideLoading } = useLoading();

    const [rolePermissions, setRolePermissions] = useState<LoadableItemDto[] | undefined>(role.role_permissions);

    const handleSave = () => {
        onSave({
            ...role,
            role_permissions: rolePermissions,
        });
    };

    const pages: Record<string, string[]> = {
        Dashboard: ["dashboard"],
        "NGO Registration": ["organization", "application"],
        Reporting: ["reporting"],
        TEP: ["tep"],
        "NGO Card": ["ngo_card"],
        "Case Management": ["case_management"],
        Activities: ["activities"],
        Finance: ["invoice", "payment"],
        Projects: ["projects"],
        Reports: ["reports"],
        Settings: ["config"],
        Admin: ["user", "role", "permissions", "notification"],
    };

    useEffect(() => {
        if (isError) {
            showError("Failed to load role permissions. Please try again later.");

            return;
        }
        hideError();
        if (isLoading) {
            showLoading("Loading role permissions...");

            return;
        }
        hideLoading();
    }, [isError, isLoading]);

    const sectionPermissions = (group: string[]) => {
        return (
            permissions?.filter((p) => group.some((c) => p.code.toLowerCase().includes(c.toString().toLowerCase()))) ||
            []
        );
    };

    const handleUpdateGroupPermissions = (title: string, checked: boolean) => {
        const titlepermissions = sectionPermissions(pages[title]);

        if (checked) {
            setRolePermissions((prev) => [...(prev || []), ...titlepermissions]);

            return;
        }
        setRolePermissions((prev) => prev?.filter((p) => !titlepermissions.some((sp) => sp.id === p.id)));
    };

    if (isLoading || isError) {
        return null;
    }

    return (
        <>
            {permissions &&
                Object.entries(pages).map(([title, group]) => {
                    const sp = sectionPermissions(group);
                    const checked = sp.some((p) => rolePermissions?.some((rp) => rp.id === p.id));

                    return (
                        <Card key={title}>
                            <div>
                                <CardHeader>
                                    <CardTitle>
                                        <div className="flex justify-between items-center gap-2 text-lg font-semibold mb-4">
                                            <h2>{title}</h2>
                                            <Checkbox
                                                checked={checked}
                                                className="text-lg font-semibold"
                                                label="Select All"
                                                onCheckedChange={(e) =>
                                                    handleUpdateGroupPermissions(title, e.valueOf() as boolean)
                                                }
                                            />
                                        </div>
                                    </CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                                        {sectionPermissions(group).map((permission) => (
                                            <Checkbox
                                                key={permission.id}
                                                checked={rolePermissions?.some((p) => p.id === permission.id)}
                                                label={permission.display_value}
                                                onCheckedChange={(e) => {
                                                    if (e.valueOf() === true) {
                                                        setRolePermissions((prev) => [...(prev || []), permission]);
                                                    } else {
                                                        setRolePermissions((prev) =>
                                                            prev?.filter((p) => p.id !== permission.id),
                                                        );
                                                    }
                                                }}
                                            />
                                        ))}
                                    </div>
                                </CardContent>
                            </div>
                        </Card>
                    );
                })}
            <div className="mt-4 flex justify-end space-x-2">
                <Button loadingIcon loading={loading} type="button" variant="secondary" onClick={onClose}>
                    Cancel
                </Button>
                <Button loadingIcon loading={loading} variant={"default"} onClick={handleSave}>
                    Save
                </Button>
            </div>
        </>
    );
}
