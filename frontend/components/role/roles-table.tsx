import { ColumnDef } from "@tanstack/react-table";
import { MoreHorizontal, <PERSON>cil, Plus, Trash2 } from "lucide-react";
import { useState } from "react";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { DataTable } from "@/components/ui/data-table";
import { Input } from "@/components/ui/input";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { RolePermissionDto } from "@/types/role.type";

interface RolesTableProps {
    roles: RolePermissionDto[];
    loading: boolean;
    onEdit: (role: RolePermissionDto) => void;
    onDelete: (role: RolePermissionDto) => void;
    onCreate: () => void;
    onEditPermissions: (role: RolePermissionDto) => void;
}

export function RolesTable({ roles, loading, onEdit, onEditPermissions, onDelete, onCreate }: RolesTableProps) {
    const [search, setSearch] = useState("");

    const filteredRoles = roles.filter(
        (role) =>
            role.name.toLowerCase().includes(search.toLowerCase()) ||
            role.code.toLowerCase().includes(search.toLowerCase()),
    );

    const columns: ColumnDef<RolePermissionDto>[] = [
        {
            accessorKey: "name",
            header: "Role",
            cell: ({ row }) => (
                <div>
                    <div className="font-medium text-foreground">{row.original.name}</div>
                    <div className="text-muted-foreground text-xs font-mono">{row.original.code}</div>
                </div>
            ),
        },
        {
            accessorKey: "description",
            header: "Description",
            cell: ({ row }) => (
                <div className="max-w-[300px] truncate" title={row.original.description}>
                    {row.original.description || "—"}
                </div>
            ),
        },
        {
            accessorKey: "created_at",
            header: "Created",
            cell: ({ row }) => (
                <div className="text-sm text-muted-foreground">
                    {row.original.created_at ? new Date(row.original.created_at).toLocaleDateString() : "—"}
                </div>
            ),
        },
        {
            id: "actions",
            header: "Actions",
            cell: ({ row }) => (
                <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                        <Button className="h-8 w-8 p-0" size="icon-sm" variant="ghost">
                            <span className="sr-only">Open menu</span>
                            <MoreHorizontal className="h-4 w-4" />
                        </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => onEditPermissions(row.original)}>
                            <Pencil className="mr-2 h-4 w-4" /> Permissions
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => onEdit(row.original)}>
                            <Pencil className="mr-2 h-4 w-4" /> Edit
                        </DropdownMenuItem>
                        <DropdownMenuItem className="text-red-600" onClick={() => onDelete(row.original)}>
                            <Trash2 className="mr-2 h-4 w-4" /> Delete
                        </DropdownMenuItem>
                    </DropdownMenuContent>
                </DropdownMenu>
            ),
        },
    ];

    return (
        <>
            <div className="flex items-center justify-between py-6">
                <div>
                    <CardTitle className="text-2xl">Roles</CardTitle>
                    <p className="text-muted-foreground">Manage user roles and their associated permissions</p>
                </div>
                <Button onClick={onCreate}>
                    <Plus className="mr-2 h-4 w-4" /> Create Role
                </Button>
            </div>
            <Card>
                <CardHeader />
                <CardContent>
                    <Input
                        className="mb-4 max-w-sm"
                        placeholder="Search roles..."
                        value={search}
                        onChange={(e) => setSearch(e.target.value)}
                    />
                    <DataTable
                        columns={columns}
                        data={filteredRoles}
                        enableFiltering={false}
                        enablePagination={true}
                        enableSorting={true}
                        pageSize={10}
                    />
                </CardContent>
            </Card>
        </>
    );
}
