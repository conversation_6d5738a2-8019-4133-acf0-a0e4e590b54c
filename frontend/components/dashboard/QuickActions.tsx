"use client";
import { Building2Icon, ChartBarIcon, FileTextIcon, PlusIcon, SparklesIcon, UsersIcon } from "lucide-react";
import { useRouter } from "next/navigation";
import React from "react";

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";

interface QuickAction {
    title: string;
    description: string;
    icon: React.ComponentType<{ className?: string }>;
    href: string;
    color: "blue" | "green" | "orange" | "purple";
    featured?: boolean;
}

export const QuickActions: React.FC = () => {
    const router = useRouter();
    const actions: QuickAction[] = [
        {
            title: "Register Organization",
            description: "",
            icon: Building2Icon,
            href: "/organizations/create",
            color: "blue",
            featured: true,
        },
        {
            title: "Create User",
            description: "",
            icon: UsersIcon,
            href: "/users/create",
            color: "green",
        },
        {
            title: "Process Application",
            description: "",
            icon: FileTextIcon,
            href: "/applications/pending",
            color: "orange",
        },
        {
            title: "Generate Report",
            description: "",
            icon: ChartBarIcon,
            href: "/management/reports",
            color: "purple",
        },
    ];

    const colorSchemes = {
        blue: {
            border: "border-blue-200 hover:border-blue-400",
            bg: "hover:bg-foreground/10",
            icon: "bg-blue-100 text-blue-600",
            gradient: "from-blue-500 to-blue-600",
        },
        green: {
            border: "border-green-200 hover:border-green-400",
            bg: "hover:bg-foreground/10",
            icon: "bg-green-100 text-green-600",
            gradient: "from-green-500 to-emerald-600",
        },
        orange: {
            border: "border-orange-200 hover:border-orange-400",
            bg: "hover:bg-foreground/10",
            icon: "bg-orange-100 text-orange-600",
            gradient: "from-orange-500 to-amber-600",
        },
        purple: {
            border: "border-purple-200 hover:border-purple-400",
            bg: "hover:bg-foreground/10",
            icon: "bg-purple-100 text-purple-600",
            gradient: "from-purple-500 to-violet-600",
        },
    };

    return (
        <Card className="bg-background/20">
            <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
                <CardDescription>Common tasks and workflow shortcuts</CardDescription>
            </CardHeader>
            <CardContent>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    {actions.map((action, index) => {
                        const scheme = colorSchemes[action.color];

                        return (
                            <button
                                key={index}
                                className={`group relative p-5 rounded-xl border-2 border-dashed transition-all duration-300 hover:border-solid ${scheme.border} ${scheme.bg}`}
                                onClick={() => router.push(action.href)}
                            >
                                {action.featured && (
                                    <div className="absolute -top-2 -right-2">
                                        <div
                                            className={`p-1.5 rounded-full bg-gradient-to-r ${scheme.gradient} shadow-lg`}
                                        >
                                            <SparklesIcon className="h-3 w-3 text-white" />
                                        </div>
                                    </div>
                                )}

                                <div className="flex items-start space-x-4">
                                    <div
                                        className={`p-3 rounded-xl ${scheme.icon} group-hover:scale-110 transition-transform duration-200`}
                                    >
                                        <action.icon className="h-8 w-8" />
                                    </div>
                                    <div className="flex-1 text-left">
                                        <h4 className="text-sm font-semibold transition-colors">{action.title}</h4>
                                        <p className="text-xs mt-1 leading-relaxed">{action.description}</p>
                                    </div>
                                    <PlusIcon className="h-4 w-4 transition-colors" />
                                </div>
                            </button>
                        );
                    })}
                </div>
            </CardContent>
        </Card>
    );
};
