"use client";
import { BanknoteIcon, Building2Icon, ChartBarIcon, Clock10Icon, FileTextIcon, Users2Icon } from "lucide-react";
import React from "react";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardTitle } from "@/components/ui/card";
import { RecentActivity } from "@/types/dashboard";

interface RecentActivityProps {
    activities: RecentActivity[];
    loading?: boolean;
}

export const RecentActivityFeed: React.FC<RecentActivityProps> = ({ activities, loading = false }) => {
    const getActivityIcon = (type: string) => {
        switch (type) {
            case "application":
                return FileTextIcon;
            case "organization":
                return Building2Icon;
            case "user":
                return Users2Icon;
            case "payment":
                return BanknoteIcon;
            case "workflow":
                return ChartBarIcon;
            default:
                return FileTextIcon;
        }
    };

    const getStatusColor = (status?: string): "gray" | "blue" | "green" | "yellow" | "red" => {
        switch (status) {
            case "approved":
                return "green";
            case "rejected":
                return "red";
            case "pending":
                return "yellow";
            case "completed":
                return "blue";
            default:
                return "gray";
        }
    };

    const formatTimeAgo = (timestamp: string): string => {
        const date = new Date(timestamp);
        const now = new Date();
        const diff = now.getTime() - date.getTime();
        const minutes = Math.floor(diff / 60000);
        const hours = Math.floor(diff / 3600000);
        const days = Math.floor(diff / ********);

        if (days > 0) return `${days}d ago`;
        if (hours > 0) return `${hours}h ago`;
        if (minutes > 0) return `${minutes}m ago`;

        return "Just now";
    };

    if (loading) {
        return (
            <Card className="animate-pulse" title="Recent Activity">
                <div className="space-y-4">
                    {[1, 2, 3, 4, 5].map((i) => (
                        <div key={i} className="flex items-start space-x-3">
                            <div className="w-8 h-8 bg-gray-200 rounded-full" />
                            <div className="flex-1 space-y-2">
                                <div className="h-4 bg-gray-200 rounded w-3/4" />
                                <div className="h-3 bg-gray-200 rounded w-1/2" />
                            </div>
                        </div>
                    ))}
                </div>
            </Card>
        );
    }

    return (
        <Card className="h-full" title="">
            <CardTitle>
                <h1>Recent Activity</h1>
                <span className="text-muted-foreground">Latest system updates and actions</span>
            </CardTitle>
            <CardContent>
                <div className="space-y-4 max-h-96 overflow-y-auto">
                    {activities.map((activity) => {
                        const Icon = getActivityIcon(activity.type);

                        return (
                            <div
                                key={activity.id}
                                className="flex items-start space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-colors"
                            >
                                <div
                                    className={`flex-shrink-0 p-2 rounded-lg ${
                                        activity.type === "application"
                                            ? "bg-blue-100 text-blue-600"
                                            : activity.type === "organization"
                                              ? "bg-green-100 text-green-600"
                                              : activity.type === "user"
                                                ? "bg-purple-100 text-purple-600"
                                                : activity.type === "payment"
                                                  ? "bg-yellow-100 text-yellow-600"
                                                  : "bg-gray-100 text-gray-600"
                                    }`}
                                >
                                    <Icon className="h-4 w-4" />
                                </div>
                                <div className="flex-1 min-w-0">
                                    <div className="flex items-start justify-between">
                                        <div className="flex-1">
                                            <p className="text-sm font-medium text-gray-900">{activity.title}</p>
                                            <p className="text-xs text-gray-500 mt-1">{activity.description}</p>
                                            <div className="flex items-center mt-2 space-x-2">
                                                {activity.user && (
                                                    <span className="text-xs text-gray-400">by {activity.user}</span>
                                                )}
                                                <span className="text-xs text-gray-400">
                                                    {formatTimeAgo(activity.timestamp)}
                                                </span>
                                            </div>
                                        </div>
                                        {activity.status && (
                                            <Badge className={`bg-${getStatusColor(activity.status)}-600`}>
                                                {activity.status}
                                            </Badge>
                                        )}
                                    </div>
                                </div>
                            </div>
                        );
                    })}
                </div>

                <div className="pt-4 border-t border-gray-200">
                    <Button size="sm" variant="outline">
                        <Clock10Icon className="h-4 w-4 mr-2" />
                        View All Activity
                    </Button>
                </div>
            </CardContent>
        </Card>
    );
};
