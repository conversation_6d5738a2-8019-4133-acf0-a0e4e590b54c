"use client";
import React from "react";
import { BanknoteIcon, Building2Icon, FileTextIcon, UsersIcon } from "lucide-react";

import { StatsCard } from "@/components/common/StatsCard";
import { DashboardStats } from "@/types/dashboard";

interface QuickStatsOverviewProps {
    stats: DashboardStats;
    loading?: boolean;
}

export const QuickStatsOverview: React.FC<QuickStatsOverviewProps> = ({ stats, loading = false }) => {
    const statsCards = [
        {
            title: "Total Users",
            value: stats.totalUsers,
            change: {
                value: stats.monthlyGrowth.users,
                type: "increase" as const,
                period: "this month",
            },
            icon: UsersIcon,
            color: "blue" as const,
            trend: [65, 75, 80, 88, 92, 95, 100],
            href: "/users",
        },
        {
            title: "Organizations",
            value: stats.totalOrganizations,
            change: {
                value: stats.monthlyGrowth.organizations,
                type: "increase" as const,
                period: "this month",
            },
            icon: Building2Icon,
            color: "green" as const,
            trend: [45, 52, 58, 65, 70, 75, 80],
            href: "/organizations",
        },
        {
            title: "Pending Applications",
            value: stats.pendingApplications,
            change: {
                value: Math.abs(stats.monthlyGrowth.applications),
                type: "decrease" as const,
                period: "this week",
            },
            icon: FileTextIcon,
            color: "orange" as const,
            trend: [40, 35, 32, 28, 25, 22, 20],
            href: "/applications",
        },
        {
            title: "Monthly Revenue",
            value: `$${(stats.totalRevenue / 1000).toFixed(1)}K`,
            change: {
                value: stats.monthlyGrowth.revenue,
                type: "increase" as const,
                period: "this month",
            },
            icon: BanknoteIcon,
            color: "purple" as const,
            trend: [60, 65, 70, 75, 82, 88, 95],
            href: "/payments",
        },
    ];

    return (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {statsCards.map((stat, index) => (
                <StatsCard
                    key={index}
                    change={stat.change}
                    color={stat.color}
                    icon={stat.icon}
                    loading={loading}
                    title={stat.title}
                    trend={stat.trend}
                    value={stat.value}
                    onClick={() => (window.location.href = stat.href)}
                />
            ))}
        </div>
    );
};
