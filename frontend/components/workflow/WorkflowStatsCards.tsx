"use client";

import { Act<PERSON>, Bar<PERSON>hart3, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, XCircle } from "lucide-react";

import { Badge } from "@/components/ui/badge";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";

interface WorkflowStats {
    total: number;
    in_review: number;
    completed: number;
    rejected: number;
    this_month: number;
    trend_percentage: string;
    average_time_days: number;
}

interface WorkflowStatsCardsProps {
    stats?: WorkflowStats;
    isLoading: boolean;
}

const StatCardSkeleton = () => (
    <Card>
        <CardHeader className="pb-2">
            <Skeleton className="h-4 w-20" />
        </CardHeader>
        <CardContent>
            <div className="flex items-center justify-between">
                <Skeleton className="h-8 w-12" />
                <Skeleton className="h-4 w-4" />
            </div>
            <Skeleton className="h-3 w-16 mt-1" />
        </CardContent>
    </Card>
);

export function WorkflowStatsCards({ stats, isLoading }: WorkflowStatsCardsProps) {
    if (isLoading) {
        return (
            <>
                <div className="xl:col-span-2">
                    <StatCardSkeleton />
                </div>
                {Array.from({ length: 5 }).map((_, i) => (
                    <StatCardSkeleton key={i} />
                ))}
            </>
        );
    }

    return (
        <>
            <Card className="xl:col-span-2 border-border bg-background/50 backdrop-blur">
                <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium text-muted-foreground">Total Applications</CardTitle>
                </CardHeader>
                <CardContent>
                    <div className="flex items-center justify-between">
                        <div className="text-2xl font-bold text-foreground">{stats?.total || 0}</div>
                        <Activity className="h-4 w-4 text-primary" />
                    </div>
                    <p className="text-xs text-muted-foreground mt-1">
                        <span className="text-emerald-600 font-medium">{stats?.trend_percentage || "0%"}</span> from
                        last month
                    </p>
                </CardContent>
            </Card>

            <Card>
                <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium text-blue-600">In Review</CardTitle>
                </CardHeader>
                <CardContent>
                    <div className="flex items-center justify-between">
                        <div className="text-2xl font-bold">{stats?.in_review || 0}</div>
                        <Clock className="h-4 w-4 text-blue-600" />
                    </div>
                </CardContent>
            </Card>

            <Card>
                <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium text-green-600">Completed</CardTitle>
                </CardHeader>
                <CardContent>
                    <div className="flex items-center justify-between">
                        <div className="text-2xl font-bold">{stats?.completed || 0}</div>
                        <CheckCircle className="h-4 w-4 text-green-600" />
                    </div>
                </CardContent>
            </Card>

            <Card>
                <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium text-red-600">Rejected</CardTitle>
                </CardHeader>
                <CardContent>
                    <div className="flex items-center justify-between">
                        <div className="text-2xl font-bold">{stats?.rejected || 0}</div>
                        <XCircle className="h-4 w-4 text-red-600" />
                    </div>
                </CardContent>
            </Card>

            <Card>
                <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium text-gray-600">Avg. Time</CardTitle>
                </CardHeader>
                <CardContent>
                    <div className="flex items-center justify-between">
                        <div className="text-2xl font-bold">
                            {stats?.average_time_days ? `${stats.average_time_days.toFixed(1)}d` : "0d"}
                        </div>
                        <BarChart3 className="h-4 w-4 text-gray-400" />
                    </div>
                </CardContent>
            </Card>
        </>
    );
}

export function WorkflowStatsHeader({ stats, isLoading }: WorkflowStatsCardsProps) {
    return (
        <div className="flex items-center gap-4">
            {isLoading ? (
                <Skeleton className="h-6 w-32" />
            ) : (
                <Badge className="px-3 py-1 bg-primary/10 text-primary border-primary/20" variant="outline">
                    {stats?.this_month || 0} applications this month
                </Badge>
            )}
        </div>
    );
}
