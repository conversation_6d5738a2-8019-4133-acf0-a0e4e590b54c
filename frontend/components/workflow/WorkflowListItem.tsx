"use client";

import { ArrowR<PERSON>, Building2, CheckCir<PERSON>, Clock, XCircle } from "lucide-react";
import { useRouter } from "next/navigation";

import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { WorkflowListDto } from "@/types";

interface WorkflowListItemProps {
    workflow: WorkflowListDto;
}

const getStatusConfig = (status: string) => {
    const configs = {
        APPROVED: { color: "text-green-600", bg: "bg-green-100", icon: CheckCircle },
        COMPLETED: { color: "text-green-600", bg: "bg-green-100", icon: CheckCircle },
        IN_REVIEW: { color: "text-blue-600", bg: "bg-blue-100", icon: Clock },
        REJECTED: { color: "text-red-600", bg: "bg-red-100", icon: XCircle },
    };

    return configs[status as keyof typeof configs] || configs["IN_REVIEW"];
};

const getPriorityColor = (priority: string) => {
    const colors = {
        HIGH: "bg-red-100 text-red-800",
        MEDIUM: "bg-yellow-100 text-yellow-800",
        LOW: "bg-green-100 text-green-800",
    };

    return colors[priority as keyof typeof colors] || colors["MEDIUM"];
};

export function WorkflowListItem({ workflow }: WorkflowListItemProps) {
    const router = useRouter();
    const statusConfig = getStatusConfig(workflow.current_stage.status);
    const StatusIcon = statusConfig.icon;

    return (
        <div
            className="p-4 hover:bg-accent/30 transition-colors cursor-pointer group border-l-4 border-l-transparent hover:border-l-primary/50"
            role="button"
            tabIndex={0}
            onClick={() => router.push("/workflows/" + workflow.id)}
            onKeyDown={(e) => {
                if (e.key === "Enter" || e.key === " ") {
                    e.preventDefault();
                    router.push("/workflows/" + workflow.id);
                }
            }}
        >
            <div className="flex items-center justify-between">
                <div className="flex items-start gap-4 flex-1">
                    {/* Status Icon */}
                    <div className={`p-2 rounded-full ${statusConfig.bg}`}>
                        <StatusIcon className={`h-4 w-4 ${statusConfig.color}`} />
                    </div>

                    {/* Main Content */}
                    <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2 mb-1">
                            <h3 className="font-semibold truncate text-foreground">
                                {workflow.application.organization.name}
                            </h3>
                            <Badge className="text-xs border-border text-foreground" variant="outline">
                                {workflow.application.code}
                            </Badge>
                            {workflow.assigned_to_me && (
                                <Badge
                                    className="text-xs bg-primary/10 text-primary border-primary/20"
                                    variant="secondary"
                                >
                                    Assigned to Me
                                </Badge>
                            )}
                        </div>

                        <p className="text-sm text-muted-foreground mb-2">
                            {workflow.template.name} • Stage {workflow.current_stage.position}/{workflow.total_stages}:{" "}
                            {workflow.current_stage.name}
                        </p>

                        <div className="flex items-center gap-4 text-xs text-muted-foreground">
                            <span className="flex items-center gap-1">
                                <Clock className="h-3 w-3" />
                                {workflow.days_active} days active
                            </span>
                            <span className="flex items-center gap-1">
                                <Building2 className="h-3 w-3" />
                                {workflow.application.type.replace("_", " ") || "Unknown Type"}
                            </span>
                        </div>
                    </div>
                </div>

                {/* Right Side */}
                <div className="flex items-center gap-3">
                    <Badge className={`${getPriorityColor(workflow.priority)} border-0`} variant="secondary">
                        {workflow.priority}
                    </Badge>

                    <div className="text-right">
                        <div className={`text-sm font-medium ${statusConfig.color}`}>
                            {workflow.current_stage.status.replace("_", " ")}
                        </div>
                    </div>

                    <ArrowRight className="h-4 w-4 text-muted-foreground group-hover:text-foreground transition-colors" />
                </div>
            </div>
        </div>
    );
}

export function WorkflowListItemSkeleton() {
    return (
        <div className="p-4">
            <div className="flex items-center justify-between">
                <div className="flex items-start gap-4 flex-1">
                    <Skeleton className="h-10 w-10 rounded-full" />
                    <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2 mb-1">
                            <Skeleton className="h-5 w-32" />
                            <Skeleton className="h-4 w-20" />
                            <Skeleton className="h-4 w-24" />
                        </div>
                        <Skeleton className="h-4 w-48 mb-2" />
                        <div className="flex items-center gap-4">
                            <Skeleton className="h-3 w-16" />
                            <Skeleton className="h-3 w-20" />
                            <Skeleton className="h-3 w-24" />
                        </div>
                    </div>
                </div>
                <div className="flex items-center gap-3">
                    <Skeleton className="h-5 w-12" />
                    <div className="text-right">
                        <Skeleton className="h-4 w-16 mb-1" />
                        <Skeleton className="h-3 w-12" />
                    </div>
                    <Skeleton className="h-4 w-4" />
                </div>
            </div>
        </div>
    );
}
