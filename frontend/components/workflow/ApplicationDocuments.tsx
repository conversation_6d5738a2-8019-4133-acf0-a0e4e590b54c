"use client";

import { FileText } from "lucide-react";
import { useState } from "react";

import { DocumentViewer } from "@/components/document/DocumentViewer";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import api from "@/config/api.config";
import { ApplicationDocumentDto } from "@/types";
import { getFileIconAndColor } from "@/utils/common";

const formatFileSize = (sizeStr: string): string => {
    const size = parseInt(sizeStr);

    if (isNaN(size) || size === 0) {
        return "Unknown size";
    }

    const units = ["B", "KB", "MB", "GB", "TB"];
    let unitIndex = 0;
    let fileSize = size;

    while (fileSize >= 1024 && unitIndex < units.length - 1) {
        fileSize /= 1024;
        unitIndex++;
    }

    const formattedSize = unitIndex === 0 ? fileSize.toString() : fileSize.toFixed(1);

    return `${formattedSize} ${units[unitIndex]}`;
};

interface ApplicationDocumentsProps {
    documents?: ApplicationDocumentDto[];
    trigger?: React.ReactNode;
    title?: string;
    description?: string;
}

export function ApplicationDocuments({
    documents = [],
    trigger,
    title = "Application Documents",
    description = "View all documents submitted with this application",
}: ApplicationDocumentsProps) {
    const [documentsDialog, setDocumentsDialog] = useState(false);
    const [selectedDocument, setSelectedDocument] = useState<ApplicationDocumentDto | null>(null);
    const [viewerDialog, setViewerDialog] = useState(false);

    const handleViewDocuments = () => {
        setDocumentsDialog(true);
    };

    const handleViewDocument = (document: ApplicationDocumentDto) => {
        setSelectedDocument(document);
        setViewerDialog(true);
    };

    const closeDocumentsDialog = () => {
        setDocumentsDialog(false);
    };

    const closeViewerDialog = () => {
        setViewerDialog(false);
        setSelectedDocument(null);
    };

    const defaultTrigger = (
        <Button className="gap-2" variant="outline" onClick={handleViewDocuments}>
            <FileText className="h-4 w-4" />
            View Documents
        </Button>
    );

    return (
        <>
            {trigger ? (
                <div role="button" tabIndex={0} onClick={handleViewDocuments} onKeyDown={() => ({})}>
                    {trigger}
                </div>
            ) : (
                defaultTrigger
            )}

            {/* Documents List Dialog */}
            <Dialog open={documentsDialog} onOpenChange={closeDocumentsDialog}>
                <DialogContent className="sm:max-w-5xl bg-background/95 backdrop-blur-sm border-border/50">
                    <DialogHeader>
                        <DialogTitle className="text-foreground">{title}</DialogTitle>
                        <DialogDescription className="text-muted-foreground">{description}</DialogDescription>
                    </DialogHeader>

                    <div className="space-y-4 max-h-svh overflow-y-auto">
                        {!documents || documents.length === 0 ? (
                            <div className="text-center py-8">
                                <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-2" />
                                <p className="text-muted-foreground">No documents found</p>
                            </div>
                        ) : (
                            documents.map((document) => {
                                const filename = document.document?.original_name || "Document";
                                const { icon: IconComponent, color, bgColor } = getFileIconAndColor(filename);

                                return (
                                    <Card
                                        key={document.id}
                                        className="p-4 hover:bg-muted/50 cursor-pointer transition-colors"
                                        onClick={() => handleViewDocument(document)}
                                    >
                                        <div className="flex items-start justify-between">
                                            <div className="flex items-start gap-3">
                                                <div className={`p-2 rounded-lg ${bgColor}`}>
                                                    <IconComponent className={`h-5 w-5 ${color}`} />
                                                </div>
                                                <div>
                                                    <h4 className="font-medium text-foreground">
                                                        {document.document_type?.display_value || "Unknown Type"}
                                                    </h4>
                                                    <p className="text-sm text-muted-foreground">{filename}</p>
                                                    <p className="text-xs text-muted-foreground mt-1">
                                                        Size: {formatFileSize(document.document?.size || "0")} •{" "}
                                                        {new Date(document.created_at).toLocaleDateString()}
                                                    </p>
                                                </div>
                                            </div>
                                            <Button size="sm" variant="ghost">
                                                View
                                            </Button>
                                        </div>
                                    </Card>
                                );
                            })
                        )}
                    </div>
                </DialogContent>
            </Dialog>

            {/* Document Viewer Dialog */}
            <Dialog open={viewerDialog} onOpenChange={closeViewerDialog}>
                <DialogContent className="sm:max-w-4xl max-h-[90vh] bg-background/95 backdrop-blur-sm border-border/50">
                    <DialogHeader>
                        <DialogTitle className="text-foreground">
                            {selectedDocument?.document_type?.display_value || "Document"}
                        </DialogTitle>
                        <DialogDescription className="text-muted-foreground">
                            {selectedDocument?.document?.original_name || "Document"}
                        </DialogDescription>
                    </DialogHeader>

                    <div className="h-[70vh]">
                        {selectedDocument?.document?.location && (
                            <DocumentViewer
                                height="100%"
                                url={api.bucket(selectedDocument.document.location)}
                                width="100%"
                            />
                        )}
                    </div>
                </DialogContent>
            </Dialog>
        </>
    );
}
