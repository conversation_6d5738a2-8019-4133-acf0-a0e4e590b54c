"use client";

import { format } from "date-fns";
import { Calendar, ChevronDown, Clock, RotateCcw, Save, Search, Settings2, Target, Trash2, X } from "lucide-react";
import { useState } from "react";

import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Calendar as CalendarComponent } from "@/components/ui/calendar";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
} from "@/components/ui/dialog";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Select } from "@/components/ui/select";
import { Slider } from "@/components/ui/slider";
import { useWorkflowStore } from "@/stores/useWorkflowStore";
import { AdvancedWorkflowFilter, DateRange } from "@/types";

interface AdvancedWorkflowFiltersProps {
    onFiltersChange?: (filters: any) => void;
    className?: string;
}

export function AdvancedWorkflowFilters({ onFiltersChange, className = "" }: AdvancedWorkflowFiltersProps) {
    const { filters, setFilters, clearFilters, savedFilters, saveFilter, loadFilter, deleteFilter } =
        useWorkflowStore();

    const [showAdvanced, setShowAdvanced] = useState(false);
    const [dateRange, setDateRange] = useState<{ start?: Date; end?: Date }>({});
    const [stageDurationRange, setStageDurationRange] = useState([0, 30]);
    const [saveFilterDialog, setSaveFilterDialog] = useState({ open: false, name: "" });

    const handleSearchChange = (value: string) => {
        const newFilters = { ...filters, search: value || undefined };

        setFilters(newFilters);

        onFiltersChange?.(newFilters);
    };

    const handleFilterChange = (key: string, value: any) => {
        const newFilters = {
            ...filters,
            [key]: value === "all" || value === "" ? undefined : value,
        };

        setFilters(newFilters);

        onFiltersChange?.(newFilters);
    };

    const handleDateRangeChange = (range: DateRange) => {
        setDateRange(range);
        const newFilters: Partial<AdvancedWorkflowFilter> = {
            ...filters,
        };

        if (range.start && range.end) {
            newFilters.dateRange = range;
        }

        setFilters(newFilters);
        onFiltersChange?.(newFilters);
    };

    const handleStageDurationChange = (range: number[]) => {
        setStageDurationRange(range);
        const newFilters = {
            ...filters,
            stageDuration: { min: range[0], max: range[1] },
        };

        setFilters(newFilters);

        onFiltersChange?.(newFilters);
    };

    const clearAllFilters = () => {
        clearFilters();
        setDateRange({});
        setStageDurationRange([0, 30]);
        onFiltersChange?.({});
    };

    const getActiveFiltersCount = () => {
        return Object.keys(filters).filter(
            (key) => filters[key as keyof typeof filters] !== undefined && filters[key as keyof typeof filters] !== "",
        ).length;
    };

    const handleSaveFilter = () => {
        if (saveFilterDialog.name.trim()) {
            saveFilter(saveFilterDialog.name, filters);
            setSaveFilterDialog({ open: false, name: "" });
        }
    };

    return (
        <>
            <Card
                className={`border-border bg-background/50 backdrop-blur supports-[backdrop-filter]:bg-background/80 ${className}`}
            >
                <CardHeader className="pb-4">
                    <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                            <Settings2 className="h-5 w-5 text-primary" />
                            <CardTitle className="text-lg font-semibold text-foreground">Filters & Search</CardTitle>
                            {getActiveFiltersCount() > 0 && (
                                <Badge className="bg-primary/10 text-primary border-primary/20" variant="secondary">
                                    {getActiveFiltersCount()} active
                                </Badge>
                            )}
                        </div>
                        <div className="flex items-center gap-2">
                            <Button
                                className="text-muted-foreground hover:text-foreground"
                                size="sm"
                                variant="ghost"
                                onClick={() => setShowAdvanced(!showAdvanced)}
                            >
                                Advanced
                                <ChevronDown
                                    className={`h-4 w-4 ml-1 transition-transform ${showAdvanced ? "rotate-180" : ""}`}
                                />
                            </Button>

                            {getActiveFiltersCount() > 0 && (
                                <Button
                                    className="text-muted-foreground hover:text-foreground"
                                    size="sm"
                                    variant="ghost"
                                    onClick={clearAllFilters}
                                >
                                    <RotateCcw className="h-4 w-4 mr-1" />
                                    Clear
                                </Button>
                            )}
                        </div>
                    </div>
                </CardHeader>

                <CardContent className="space-y-6">
                    {/* Basic Filters */}
                    <div className="grid grid-cols-1 lg:grid-cols-6 gap-4">
                        {/* Search */}
                        <div className="lg:col-span-2 relative">
                            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                            <Input
                                className="pl-10 bg-background border-border focus:border-primary/50"
                                placeholder="Search organizations, codes..."
                                value={filters.search || ""}
                                onChange={(e) => handleSearchChange(e.target.value)}
                            />
                        </div>

                        {/* Status Filter */}
                        <Select
                            options={[
                                { label: "All", value: "all" },
                                // { label: "Pending", value: "PENDING" },
                                { label: "In Review", value: "IN_REVIEW" },
                                { label: "Completed", value: "APPROVED" },
                                { label: "Rejected", value: "REJECTED" },
                            ]}
                            value={filters.status || "all"}
                            onValueChange={(value) => handleFilterChange("status", value)}
                        />

                        {/* Priority Filter */}
                        <Select
                            options={[
                                { label: "All", value: "all" },
                                { label: "High", value: "high" },
                                { label: "medium", value: "Medium" },
                                { label: "Low", value: "Low" },
                            ]}
                            value={filters.priority || "all"}
                            onValueChange={(value) => handleFilterChange("priority", value)}
                        />

                        {/* Assignment Filter */}
                        <Select
                            options={[
                                { label: "All", value: "All Applications" },
                                { label: "mine", value: "Assigned to Me" },
                            ]}
                            value={filters.assigned_to_me ? "mine" : "all"}
                            onValueChange={(value) => handleFilterChange("assigned_to_me", value === "mine")}
                        />

                        {/* Saved Filters */}
                        <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                                <Button className="bg-background border-border hover:bg-accent" variant="outline">
                                    <Save className="h-4 w-4 mr-2" />
                                    Saved
                                </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end" className="bg-background border-border min-w-[200px]">
                                {savedFilters.length > 0 ? (
                                    <>
                                        {savedFilters.map((savedFilter) => (
                                            <div
                                                key={savedFilter.id}
                                                className="flex items-center justify-between px-2 py-1 hover:bg-accent rounded"
                                            >
                                                <DropdownMenuItem
                                                    className="flex-1 p-0 hover:bg-transparent text-foreground"
                                                    onClick={() => loadFilter(savedFilter.id)}
                                                >
                                                    {savedFilter.name}
                                                </DropdownMenuItem>
                                                <Button
                                                    className="h-6 w-6 p-0 text-muted-foreground hover:text-destructive"
                                                    size="sm"
                                                    variant="ghost"
                                                    onClick={(e) => {
                                                        e.stopPropagation();
                                                        deleteFilter(savedFilter.id);
                                                    }}
                                                >
                                                    <Trash2 className="h-3 w-3" />
                                                </Button>
                                            </div>
                                        ))}
                                        <DropdownMenuSeparator className="bg-border" />
                                    </>
                                ) : (
                                    <DropdownMenuItem disabled className="text-muted-foreground">
                                        No saved filters
                                    </DropdownMenuItem>
                                )}
                                <DropdownMenuItem
                                    className="text-primary hover:bg-primary/10"
                                    onClick={() => setSaveFilterDialog({ open: true, name: "" })}
                                >
                                    <Save className="h-4 w-4 mr-2" />
                                    Save Current Filters
                                </DropdownMenuItem>
                            </DropdownMenuContent>
                        </DropdownMenu>
                    </div>

                    {/* Advanced Filters */}
                    {showAdvanced && (
                        <div className="space-y-6 pt-6 border-t border-border">
                            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                {/* Date Range */}
                                <div className="space-y-3">
                                    <Label className="text-sm font-medium text-foreground flex items-center gap-2">
                                        <Calendar className="h-4 w-4 text-primary" />
                                        Date Range
                                    </Label>
                                    <div className="flex gap-2">
                                        <Popover>
                                            <PopoverTrigger asChild>
                                                <Button
                                                    className="flex-1 justify-start bg-background border-border hover:bg-accent text-left font-normal"
                                                    variant="outline"
                                                >
                                                    {dateRange.start ? (
                                                        format(dateRange.start, "MMM dd, yyyy")
                                                    ) : (
                                                        <span className="text-muted-foreground">Start date</span>
                                                    )}
                                                </Button>
                                            </PopoverTrigger>
                                            <PopoverContent
                                                align="start"
                                                className="w-auto p-0 bg-background border-border"
                                            >
                                                <CalendarComponent
                                                    mode="single"
                                                    selected={dateRange.start}
                                                    onSelect={(date) =>
                                                        handleDateRangeChange({ ...dateRange, start: date })
                                                    }
                                                />
                                            </PopoverContent>
                                        </Popover>

                                        <Popover>
                                            <PopoverTrigger asChild>
                                                <Button
                                                    className="flex-1 justify-start bg-background border-border hover:bg-accent text-left font-normal"
                                                    variant="outline"
                                                >
                                                    {dateRange.end ? (
                                                        format(dateRange.end, "MMM dd, yyyy")
                                                    ) : (
                                                        <span className="text-muted-foreground">End date</span>
                                                    )}
                                                </Button>
                                            </PopoverTrigger>
                                            <PopoverContent
                                                align="start"
                                                className="w-auto p-0 bg-background border-border"
                                            >
                                                <CalendarComponent
                                                    mode="single"
                                                    selected={dateRange.end}
                                                    onSelect={(date) =>
                                                        handleDateRangeChange({ ...dateRange, end: date })
                                                    }
                                                />
                                            </PopoverContent>
                                        </Popover>
                                    </div>
                                    {(dateRange.start || dateRange.end) && (
                                        <Button
                                            className="text-muted-foreground hover:text-foreground"
                                            size="sm"
                                            variant="ghost"
                                            onClick={() => handleDateRangeChange({})}
                                        >
                                            <X className="h-3 w-3 mr-1" />
                                            Clear dates
                                        </Button>
                                    )}
                                </div>

                                {/* Stage Duration */}
                                <div className="space-y-3">
                                    <Label className="text-sm font-medium text-foreground flex items-center gap-2">
                                        <Clock className="h-4 w-4 text-primary" />
                                        Stage Duration (days)
                                    </Label>
                                    <div className="px-3">
                                        <Slider
                                            className="w-full"
                                            max={90}
                                            min={0}
                                            step={1}
                                            value={stageDurationRange}
                                            onValueChange={handleStageDurationChange}
                                        />
                                        <div className="flex justify-between text-xs text-muted-foreground mt-2">
                                            <span>{stageDurationRange[0]} days</span>
                                            <span>{stageDurationRange[1]} days</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            {/* SLA Status */}
                            <div className="space-y-3">
                                <Label className="text-sm font-medium text-foreground flex items-center gap-2">
                                    <Target className="h-4 w-4 text-primary" />
                                    SLA Status
                                </Label>
                                <div className="flex gap-2">
                                    {[
                                        {
                                            value: "on_track",
                                            label: "On Track",
                                            color: "bg-emerald-100 text-emerald-700 border-emerald-200",
                                        },
                                        {
                                            value: "at_risk",
                                            label: "At Risk",
                                            color: "bg-amber-100 text-amber-700 border-amber-200",
                                        },
                                        {
                                            value: "overdue",
                                            label: "Overdue",
                                            color: "bg-red-100 text-red-700 border-red-200",
                                        },
                                    ].map((sla) => (
                                        <Button
                                            key={sla.value}
                                            className={
                                                filters.slaStatus === sla.value
                                                    ? sla.color
                                                    : "bg-background border-border hover:bg-accent"
                                            }
                                            size="sm"
                                            variant={filters.slaStatus === sla.value ? "default" : "outline"}
                                            onClick={() =>
                                                handleFilterChange(
                                                    "slaStatus",
                                                    filters.slaStatus === sla.value ? undefined : sla.value,
                                                )
                                            }
                                        >
                                            {sla.label}
                                        </Button>
                                    ))}
                                </div>
                            </div>
                        </div>
                    )}
                </CardContent>
            </Card>

            {/* Save Filter Dialog */}
            <Dialog open={saveFilterDialog.open} onOpenChange={(open) => setSaveFilterDialog({ open, name: "" })}>
                <DialogContent className="bg-background border-border">
                    <DialogHeader>
                        <DialogTitle className="text-foreground">Save Filter Set</DialogTitle>
                        <DialogDescription className="text-muted-foreground">
                            Give your current filter configuration a name to save it for future use.
                        </DialogDescription>
                    </DialogHeader>

                    <div className="space-y-4">
                        <div>
                            <Label className="text-sm font-medium text-foreground">Filter Name</Label>
                            <Input
                                className="mt-2 bg-background border-border focus:border-primary/50"
                                placeholder="Enter filter name..."
                                value={saveFilterDialog.name}
                                onChange={(e) => setSaveFilterDialog({ ...saveFilterDialog, name: e.target.value })}
                            />
                        </div>

                        <div className="p-3 bg-muted/50 rounded-lg border border-border">
                            <p className="text-sm text-muted-foreground mb-2">Current filters:</p>
                            <div className="flex flex-wrap gap-1">
                                {Object.entries(filters).map(
                                    ([key, value]) =>
                                        value !== undefined &&
                                        value !== "" && (
                                            <Badge
                                                key={key}
                                                className="text-xs bg-primary/10 text-primary border-primary/20"
                                                variant="secondary"
                                            >
                                                {key}: {String(value)}
                                            </Badge>
                                        ),
                                )}
                            </div>
                        </div>
                    </div>

                    <DialogFooter>
                        <Button
                            className="border-border text-foreground hover:bg-accent"
                            variant="outline"
                            onClick={() => setSaveFilterDialog({ open: false, name: "" })}
                        >
                            Cancel
                        </Button>
                        <Button
                            className="bg-primary hover:bg-primary/90 text-primary-foreground"
                            disabled={!saveFilterDialog.name.trim()}
                            onClick={handleSaveFilter}
                        >
                            Save Filter
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        </>
    );
}
