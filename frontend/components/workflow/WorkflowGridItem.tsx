"use client";

import { Check<PERSON>ir<PERSON>, Clock, XCircle } from "lucide-react";
import { useRouter } from "next/navigation";

import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { WorkflowListDto } from "@/types";

interface WorkflowGridItemProps {
    workflow: WorkflowListDto;
}

const getStatusConfig = (status: string) => {
    const configs = {
        APPROVED: { color: "text-green-600", bg: "bg-green-100", icon: CheckCircle },
        COMPLETED: { color: "text-green-600", bg: "bg-green-100", icon: CheckCircle },
        IN_REVIEW: { color: "text-blue-600", bg: "bg-blue-100", icon: Clock },
        REJECTED: { color: "text-red-600", bg: "bg-red-100", icon: XCircle },
    };

    return configs[status as keyof typeof configs] || configs["IN_REVIEW"];
};

const getPriorityColor = (priority: string) => {
    const colors = {
        HIGH: "bg-red-100 text-red-800",
        MEDIUM: "bg-yellow-100 text-yellow-800",
        LOW: "bg-green-100 text-green-800",
    };

    return colors[priority as keyof typeof colors] || colors["MEDIUM"];
};

export function WorkflowGridItem({ workflow }: WorkflowGridItemProps) {
    const router = useRouter();
    const statusConfig = getStatusConfig(workflow.current_stage.status);
    const StatusIcon = statusConfig.icon;

    return (
        <Card
            className="hover:shadow-lg transition-all cursor-pointer group border-l-4 border-border bg-background/50 backdrop-blur"
            style={{
                borderLeftColor: statusConfig.color.replace("text-", "").replace("-600", ""),
            }}
            onClick={() => router.push("/workflows/" + workflow.id)}
        >
            <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                    <Badge className="text-xs border-border text-foreground" variant="outline">
                        {workflow.application.code}
                    </Badge>
                    <Badge className={`${getPriorityColor(workflow.priority)} border-0`} variant="secondary">
                        {workflow.priority}
                    </Badge>
                </div>
                <CardTitle className="text-lg leading-tight text-foreground">
                    {workflow.application.organization.name}
                </CardTitle>
                <CardDescription className="text-muted-foreground">{workflow.template.name}</CardDescription>
            </CardHeader>

            <CardContent>
                <div className="space-y-3">
                    {/* Progress */}
                    <div>
                        <div className="flex justify-between text-sm mb-1">
                            <span className="text-foreground">Progress</span>
                            <span className="text-muted-foreground">
                                {workflow.current_stage.position}/{workflow.total_stages}
                            </span>
                        </div>
                        <div className="w-full bg-muted rounded-full h-2">
                            <div
                                className="bg-primary h-2 rounded-full transition-all"
                                style={{
                                    width: `${(workflow.current_stage.position / workflow.total_stages) * 100}%`,
                                }}
                            />
                        </div>
                    </div>

                    {/* Current Stage */}
                    <div className="flex items-center gap-2">
                        <StatusIcon className={`h-4 w-4 ${statusConfig.color}`} />
                        <span className="text-sm font-medium">{workflow.current_stage.name}</span>
                    </div>

                    {/* Meta info */}
                    <div className="flex items-center justify-between text-xs text-muted-foreground">
                        <span>{workflow.days_active} days active</span>
                        <span>{workflow.application.type.replace("_", " ") || "Unknown"}</span>
                    </div>

                    {workflow.assigned_to_me && (
                        <Badge
                            className="w-full justify-center bg-primary/10 text-primary border-primary/20"
                            variant="secondary"
                        >
                            Assigned to Me
                        </Badge>
                    )}
                </div>
            </CardContent>
        </Card>
    );
}

export function WorkflowGridItemSkeleton() {
    return (
        <Card className="border-l-4">
            <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                    <Skeleton className="h-4 w-20" />
                    <Skeleton className="h-4 w-12" />
                </div>
                <Skeleton className="h-6 w-32" />
                <Skeleton className="h-4 w-24" />
            </CardHeader>
            <CardContent>
                <div className="space-y-3">
                    <div>
                        <div className="flex justify-between text-sm mb-1">
                            <Skeleton className="h-4 w-16" />
                            <Skeleton className="h-4 w-8" />
                        </div>
                        <Skeleton className="h-2 w-full rounded-full" />
                    </div>
                    <div className="flex items-center gap-2">
                        <Skeleton className="h-4 w-4" />
                        <Skeleton className="h-4 w-20" />
                    </div>
                    <div className="flex items-center justify-between">
                        <Skeleton className="h-3 w-16" />
                        <Skeleton className="h-3 w-20" />
                    </div>
                    <Skeleton className="h-6 w-full" />
                </div>
            </CardContent>
        </Card>
    );
}
