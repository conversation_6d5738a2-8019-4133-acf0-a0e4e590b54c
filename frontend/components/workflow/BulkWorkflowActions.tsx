"use client";

import { useState } from "react";
import { Check, X, Users, Download, ChevronDown, Loader2, CheckSquare, Square } from "lucide-react";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
} from "@/components/ui/dialog";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Label } from "@/components/ui/label";
import { TiptapEditor } from "@/components/inputs/editor";
import { useWorkflowStore } from "@/stores/useWorkflowStore";
import { WorkflowListDto } from "@/types";

interface BulkWorkflowActionsProps {
    workflows: WorkflowListDto[];
    selectedCount: number;
    totalCount: number;
    onSelectAll: () => void;
    onClearSelection: () => void;
    className?: string;
}

export function BulkWorkflowActions({
    workflows,
    selectedCount,
    totalCount,
    onSelectAll,
    onClearSelection,
    className = "",
}: BulkWorkflowActionsProps) {
    const { selectedWorkflows, bulkApprove, bulkReject, isBulkOperationLoading, clearSelection } = useWorkflowStore();

    const [bulkDialog, setBulkDialog] = useState<{
        open: boolean;
        action: "approve" | "reject" | "assign" | null;
    }>({
        open: false,
        action: null,
    });

    const [bulkComment, setBulkComment] = useState("");
    const [bulkRejectionReason, setBulkRejectionReason] = useState("");

    const isAllSelected = selectedCount > 0 && selectedCount === totalCount;
    const isPartiallySelected = selectedCount > 0 && selectedCount < totalCount;

    const handleBulkAction = (action: "approve" | "reject" | "assign") => {
        setBulkDialog({ open: true, action });
    };

    const executeBulkAction = async () => {
        if (!bulkDialog.action) return;

        try {
            switch (bulkDialog.action) {
                case "approve":
                    await bulkApprove(selectedWorkflows, bulkComment);
                    break;
                case "reject":
                    if (!bulkRejectionReason.trim()) {
                        return;
                    }
                    await bulkReject(selectedWorkflows, bulkRejectionReason, bulkComment);
                    break;
                case "assign":
                    // Implementation for bulk assign
                    break;
            }
        } finally {
            setBulkDialog({ open: false, action: null });
            setBulkComment("");
            setBulkRejectionReason("");
        }
    };

    const exportSelectedWorkflows = () => {
        const selectedWorkflowData = workflows.filter((w) => selectedWorkflows.includes(w.id));
        const csvContent = [
            ["Organization", "Application Code", "Status", "Priority", "Days Active"].join(","),
            ...selectedWorkflowData.map((w) =>
                [
                    w.application.organization.name,
                    w.application.code,
                    w.current_stage.status,
                    w.priority,
                    w.days_active.toString(),
                ].join(","),
            ),
        ].join("\\n");

        const blob = new Blob([csvContent], { type: "text/csv" });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement("a");

        a.href = url;
        a.download = `workflows_${new Date().toISOString().split("T")[0]}.csv`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url);
    };

    if (selectedCount === 0) return null;

    return (
        <>
            <Card
                className={`sticky top-4 z-10 border-primary/20 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 ${className}`}
            >
                <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                        <div className="flex items-center gap-4">
                            <div className="flex items-center gap-2">
                                <Button
                                    className="h-8 w-8 p-0 text-muted-foreground hover:text-foreground"
                                    size="sm"
                                    variant="ghost"
                                    onClick={isAllSelected ? onClearSelection : onSelectAll}
                                >
                                    {isAllSelected ? (
                                        <CheckSquare className="h-4 w-4" />
                                    ) : isPartiallySelected ? (
                                        <Square className="h-4 w-4 opacity-50" />
                                    ) : (
                                        <Square className="h-4 w-4" />
                                    )}
                                </Button>
                                <div className="flex flex-col">
                                    <div className="flex items-center gap-2">
                                        <span className="text-sm font-medium text-foreground">
                                            {selectedCount} selected
                                        </span>
                                        <Badge
                                            className="bg-primary/10 text-primary border-primary/20"
                                            variant="secondary"
                                        >
                                            {totalCount} total
                                        </Badge>
                                    </div>
                                    {selectedCount > 0 && (
                                        <Button
                                            className="h-auto p-0 text-xs text-muted-foreground hover:text-foreground justify-start"
                                            size="sm"
                                            variant="ghost"
                                            onClick={onClearSelection}
                                        >
                                            Clear selection
                                        </Button>
                                    )}
                                </div>
                            </div>
                        </div>

                        <div className="flex items-center gap-2">
                            <Button
                                className="bg-emerald-600 hover:bg-emerald-700 text-white border-emerald-600"
                                disabled={isBulkOperationLoading}
                                size="sm"
                                variant="default"
                                onClick={() => handleBulkAction("approve")}
                            >
                                {isBulkOperationLoading ? (
                                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                                ) : (
                                    <Check className="h-4 w-4 mr-2" />
                                )}
                                Approve ({selectedCount})
                            </Button>

                            <Button
                                className="bg-red-600 hover:bg-red-700 text-white border-red-600"
                                disabled={isBulkOperationLoading}
                                size="sm"
                                variant="destructive"
                                onClick={() => handleBulkAction("reject")}
                            >
                                <X className="h-4 w-4 mr-2" />
                                Reject ({selectedCount})
                            </Button>

                            <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                    <Button
                                        className="border-border bg-background hover:bg-accent"
                                        size="sm"
                                        variant="outline"
                                    >
                                        More Actions
                                        <ChevronDown className="h-4 w-4 ml-2" />
                                    </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end" className="bg-background border-border">
                                    <DropdownMenuItem
                                        className="text-foreground hover:bg-accent"
                                        onClick={() => handleBulkAction("assign")}
                                    >
                                        <Users className="h-4 w-4 mr-2" />
                                        Bulk Assign
                                    </DropdownMenuItem>
                                    <DropdownMenuSeparator className="bg-border" />
                                    <DropdownMenuItem
                                        className="text-foreground hover:bg-accent"
                                        onClick={exportSelectedWorkflows}
                                    >
                                        <Download className="h-4 w-4 mr-2" />
                                        Export Selected
                                    </DropdownMenuItem>
                                </DropdownMenuContent>
                            </DropdownMenu>
                        </div>
                    </div>
                </CardContent>
            </Card>

            {/* Bulk Action Dialog */}
            <Dialog open={bulkDialog.open} onOpenChange={(open) => setBulkDialog({ open, action: null })}>
                <DialogContent className="bg-background border-border">
                    <DialogHeader>
                        <DialogTitle className="text-foreground">
                            {bulkDialog.action === "approve" && `Bulk Approve ${selectedCount} Workflows`}
                            {bulkDialog.action === "reject" && `Bulk Reject ${selectedCount} Workflows`}
                            {bulkDialog.action === "assign" && `Bulk Assign ${selectedCount} Workflows`}
                        </DialogTitle>
                        <DialogDescription className="text-muted-foreground">
                            {bulkDialog.action === "approve" &&
                                "This will approve the current stage for all selected workflows. This action cannot be undone."}
                            {bulkDialog.action === "reject" &&
                                "This will reject the current stage for all selected workflows and notify the applicants."}
                            {bulkDialog.action === "assign" && "Assign all selected workflows to a user or department."}
                        </DialogDescription>
                    </DialogHeader>

                    <div className="space-y-4">
                        {bulkDialog.action === "reject" && (
                            <div>
                                <Label className="text-sm font-medium text-destructive">Rejection Reason *</Label>
                                <TiptapEditor
                                    className="mt-2 border-destructive/20 focus-within:border-destructive/50"
                                    content={bulkRejectionReason}
                                    limitUnit="char"
                                    maxLimit={500}
                                    placeholder="Provide a detailed reason for rejecting these workflows..."
                                    onChange={setBulkRejectionReason}
                                />
                            </div>
                        )}

                        <div>
                            <Label className="text-sm font-medium text-foreground">
                                {bulkDialog.action === "approve"
                                    ? "Comments (Optional)"
                                    : "Additional Comments (Optional)"}
                            </Label>
                            <TiptapEditor
                                className="mt-2 border-border focus-within:border-primary/50"
                                content={bulkComment}
                                limitUnit="char"
                                maxLimit={300}
                                placeholder={
                                    bulkDialog.action === "approve"
                                        ? "Add any additional comments or instructions..."
                                        : "Add any additional context or information..."
                                }
                                onChange={setBulkComment}
                            />
                        </div>
                    </div>

                    <DialogFooter>
                        <Button
                            className="border-border text-foreground hover:bg-accent"
                            variant="outline"
                            onClick={() => setBulkDialog({ open: false, action: null })}
                        >
                            Cancel
                        </Button>
                        <Button
                            className={
                                bulkDialog.action === "approve"
                                    ? "bg-emerald-600 hover:bg-emerald-700 text-white"
                                    : bulkDialog.action === "reject"
                                        ? "bg-red-600 hover:bg-red-700 text-white"
                                        : "bg-primary hover:bg-primary/90 text-primary-foreground"
                            }
                            disabled={
                                isBulkOperationLoading ||
                                (bulkDialog.action === "reject" && !bulkRejectionReason.trim())
                            }
                            onClick={executeBulkAction}
                        >
                            {isBulkOperationLoading && <Loader2 className="h-4 w-4 animate-spin mr-2" />}
                            {bulkDialog.action === "approve" && "Approve All"}
                            {bulkDialog.action === "reject" && "Reject All"}
                            {bulkDialog.action === "assign" && "Assign All"}
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        </>
    );
}
