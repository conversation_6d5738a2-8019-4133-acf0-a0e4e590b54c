"use client";

import { Grid3X3, List } from "lucide-react";

import { <PERSON><PERSON> } from "@/components/ui/button";

interface WorkflowViewToggleProps {
    viewMode: "list" | "grid";
    onViewModeChange: (mode: "list" | "grid") => void;
}

export function WorkflowViewToggle({ viewMode, onViewModeChange }: WorkflowViewToggleProps) {
    return (
        <div className="flex items-center gap-1 p-1 bg-background border border-border rounded-lg">
            <Button
                className="h-8 px-3"
                size="sm"
                variant={viewMode === "list" ? "default" : "ghost"}
                onClick={() => onViewModeChange("list")}
            >
                <List className="h-4 w-4 mr-1" />
                List
            </Button>
            <Button
                className="h-8 px-3"
                size="sm"
                variant={viewMode === "grid" ? "default" : "ghost"}
                onClick={() => onViewModeChange("grid")}
            >
                <Grid3X3 className="h-4 w-4 mr-1" />
                Grid
            </Button>
        </div>
    );
}
