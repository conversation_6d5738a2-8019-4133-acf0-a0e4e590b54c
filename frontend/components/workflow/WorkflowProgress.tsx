"use client";

import { differenceInDays, format } from "date-fns";
import { <PERSON><PERSON><PERSON><PERSON>gle, CheckCircle2, ChevronRight, Clock, Timer, TrendingUp, XCircle, Zap } from "lucide-react";
import { useMemo, useState } from "react";

import { But<PERSON> } from "../ui/button";

import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Separator } from "@/components/ui/separator";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { TemplateStageDto, WorkflowStageDetailDto } from "@/types/workflow.dto";

interface WorkflowProgressProps {
    stages: WorkflowStageDetailDto[];
    templateStages: TemplateStageDto[];
    workflowCreatedAt: string;
    className?: string;
    onApprove?: (stageId: string, stageName: string) => void;
    onReject?: (stageId: string, stageName: string) => void;
    canActOnStage?: (stage: any) => boolean;
    isApproving?: boolean;
    isRejecting?: boolean;
}

interface StageConfig {
    icon: any;
    iconColor: string;
    bgColor: string;
    borderColor: string;
    progressColor: string;
}

interface SLAConfig {
    color: string;
    bg: string;
    label: string;
}

interface Analytics {
    progressPercentage: number;
    completedStages: number;
    totalStages: number;
    workflowAge: number;
    slaStatus: "on_track" | "at_risk" | "overdue";
    activeStage: any;
    activeStageAge: number;
    estimatedCompletion: number;
}

interface AllStage {
    id: string;
    name: string;
    description?: string | null;
    position: number;
    roles: any[];
    workflowStage: any;
    status: string;
    duration: number;
    isActive: boolean;
    isCompleted: boolean;
    isRejected: boolean;
}

interface ProgressHeaderProps {
    analytics: Analytics;
}

interface OverallProgressProps {
    analytics: Analytics;
}

interface StageTimelineProps {
    allStages: AllStage[];
    selectedStage: string | null;
    setSelectedStage: (stageId: string | null) => void;
    analytics: Analytics;
    onApprove?: (stageId: string, stageName: string) => void;
    onReject?: (stageId: string, stageName: string) => void;
    canActOnStage?: (stage: any) => boolean;
    isApproving?: boolean;
    isRejecting?: boolean;
}

interface StageItemProps {
    stage: AllStage;
    index: number;
    isLast: boolean;
    selectedStage: string | null;
    setSelectedStage: (stageId: string | null) => void;
    analytics: Analytics;
    onApprove?: (stageId: string, stageName: string) => void;
    onReject?: (stageId: string, stageName: string) => void;
    canActOnStage?: (stage: any) => boolean;
    isApproving?: boolean;
    isRejecting?: boolean;
}

interface StageDetailsProps {
    stage: AllStage;
    onApprove?: (stageId: string, stageName: string) => void;
    onReject?: (stageId: string, stageName: string) => void;
    canActOnStage?: (stage: any) => boolean;
    isApproving?: boolean;
    isRejecting?: boolean;
}

interface AnalyticsSummaryProps {
    analytics: Analytics;
}

// Utility Functions
function getDurationInDays(startDate: string, endDate?: string | null): number {
    const start = new Date(startDate);
    const end = endDate ? new Date(endDate) : new Date();

    return differenceInDays(end, start);
}

function getStageConfig(stage: AllStage): StageConfig {
    if (stage.isRejected) {
        return {
            icon: XCircle,
            iconColor: "text-red-500",
            bgColor: "bg-red-100 dark:bg-red-900/30",
            borderColor: "border-red-200 dark:border-red-800",
            progressColor: "bg-red-500",
        };
    }
    if (stage.isCompleted) {
        return {
            icon: CheckCircle2,
            iconColor: "text-emerald-500",
            bgColor: "bg-emerald-100 dark:bg-emerald-900/30",
            borderColor: "border-emerald-200 dark:border-emerald-800",
            progressColor: "bg-emerald-500",
        };
    }
    if (stage.isActive) {
        return {
            icon: Zap,
            iconColor: "text-blue-500",
            bgColor: "bg-blue-100 dark:bg-blue-900/30",
            borderColor: "border-blue-200 dark:border-blue-800",
            progressColor: "bg-blue-500",
        };
    }

    return {
        icon: Timer,
        iconColor: "text-muted-foreground",
        bgColor: "bg-muted/30",
        borderColor: "border-muted",
        progressColor: "bg-muted-foreground/30",
    };
}

function getSLAStatusConfig(status: string): SLAConfig {
    switch (status) {
        case "on_track":
            return { color: "text-emerald-600", bg: "bg-emerald-100", label: "On Track" };
        case "at_risk":
            return { color: "text-amber-600", bg: "bg-amber-100", label: "At Risk" };
        case "overdue":
            return { color: "text-red-600", bg: "bg-red-100", label: "Overdue" };
        default:
            return { color: "text-muted-foreground", bg: "bg-muted", label: "Unknown" };
    }
}

// Progress Header Component
function ProgressHeader({ analytics }: ProgressHeaderProps) {
    return (
        <CardHeader className="pb-4">
            <div className="flex items-center justify-between">
                <CardTitle className="text-lg font-semibold text-foreground flex items-center gap-2">
                    <TrendingUp className="h-5 w-5 text-primary" />
                    Workflow Progress & Analytics
                </CardTitle>

                <div className="flex items-center gap-2">
                    <Badge
                        className={`${getSLAStatusConfig(analytics.slaStatus).bg} ${getSLAStatusConfig(analytics.slaStatus).color} border-0`}
                        variant="secondary"
                    >
                        {getSLAStatusConfig(analytics.slaStatus).label}
                    </Badge>
                    <Badge className="border-border text-foreground" variant="outline">
                        {analytics.completedStages} / {analytics.totalStages} stages
                    </Badge>
                </div>
            </div>
        </CardHeader>
    );
}

// Overall Progress Component
function OverallProgress({ analytics }: OverallProgressProps) {
    return (
        <div className="space-y-3">
            <div className="flex justify-between items-center text-sm">
                <span className="font-medium text-foreground">Overall Progress</span>
                <span className="text-muted-foreground">{Math.round(analytics.progressPercentage)}%</span>
            </div>
            <Progress className="h-2 bg-muted" value={analytics.progressPercentage} />
            <div className="flex justify-between text-xs text-muted-foreground">
                <span>{analytics.workflowAge} days active</span>
                <span>Est. {analytics.estimatedCompletion - analytics.workflowAge} days remaining</span>
            </div>
        </div>
    );
}

// Stage Details Component
function StageDetails({ stage, onApprove, onReject, canActOnStage, isApproving, isRejecting }: StageDetailsProps) {
    if (!stage.workflowStage) return null;

    return (
        <div className="mt-4 p-3 bg-background/50 rounded-lg border border-border space-y-3">
            <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                    <span className="text-muted-foreground">Started:</span>
                    <p className="font-medium text-foreground">
                        {format(new Date(stage.workflowStage.created_at), "MMM dd, yyyy HH:mm")}
                    </p>
                </div>
                {stage.workflowStage.updated_at && (
                    <div>
                        <span className="text-muted-foreground">Last Updated:</span>
                        <p className="font-medium text-foreground">
                            {format(new Date(stage.workflowStage.updated_at), "MMM dd, yyyy HH:mm")}
                        </p>
                    </div>
                )}
                <div>
                    <span className="text-muted-foreground">Duration:</span>
                    <p className="font-medium text-foreground">{stage.duration} days</p>
                </div>
                {stage.workflowStage.approved_by_name && (
                    <div>
                        <span className="text-muted-foreground">Approved by:</span>
                        <p className="font-medium text-foreground">{stage.workflowStage.approved_by_name}</p>
                    </div>
                )}
            </div>

            {stage.roles && stage.roles.length > 0 && (
                <div>
                    <span className="text-sm text-muted-foreground">Assigned Roles:</span>
                    <div className="flex flex-wrap gap-1 mt-1">
                        {stage.roles.map((roleData, roleIndex) => (
                            <Badge key={roleIndex} className="text-xs border-border text-foreground" variant="outline">
                                {roleData.role?.name}
                            </Badge>
                        ))}
                    </div>
                </div>
            )}

            {canActOnStage && onApprove && onReject && canActOnStage(stage.workflowStage) && !stage.isCompleted && (
                <div className="pt-3 border-t border-border flex gap-2">
                    <Button
                        className="bg-emerald-600 hover:bg-emerald-700 text-white"
                        disabled={isApproving}
                        size="sm"
                        onClick={(e) => {
                            e.stopPropagation();
                            onApprove(stage.workflowStage?.id!, stage.name);
                        }}
                    >
                        {isApproving ? (
                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                        ) : (
                            <CheckCircle2 className="h-4 w-4 mr-2" />
                        )}
                        Approve
                    </Button>
                    <Button
                        disabled={isRejecting}
                        size="sm"
                        variant="destructive"
                        onClick={(e) => {
                            e.stopPropagation();
                            onReject(stage.workflowStage?.id!, stage.name);
                        }}
                    >
                        {isRejecting ? (
                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                        ) : (
                            <XCircle className="h-4 w-4 mr-2" />
                        )}
                        Reject
                    </Button>
                </div>
            )}
        </div>
    );
}

// Stage Item Component
function StageItem({
    stage,
    index,
    isLast,
    selectedStage,
    setSelectedStage,
    analytics,
    onApprove,
    onReject,
    canActOnStage,
    isApproving,
    isRejecting,
}: StageItemProps) {
    const config = getStageConfig(stage);
    const Icon = config.icon;

    return (
        <div
            key={stage.id}
            className={`relative cursor-pointer transition-all duration-200 ${
                selectedStage === stage.id ? "scale-[1.02]" : ""
            }`}
            role="button"
            tabIndex={0}
            onClick={() => setSelectedStage(selectedStage === stage.id ? null : stage.id)}
            onKeyDown={() => ({})}
        >
            {stage.isCompleted && !isLast && <div className="absolute left-8 top-12 w-0.5 h-8 bg-emerald-500" />}
            <div
                className={`flex items-start gap-4 p-4 rounded-lg border transition-colors ${config.borderColor} ${config.bgColor} hover:shadow-md`}
            >
                <div className="relative z-10">
                    <div
                        className={`w-8 h-8 rounded-full border-2 bg-background flex items-center justify-center ${config.borderColor}`}
                    >
                        <Icon className={`h-4 w-4 ${config.iconColor}`} />
                    </div>
                    {stage.isActive && (
                        <div className="absolute -inset-1 rounded-full border-2 border-blue-300 animate-pulse" />
                    )}
                </div>

                <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between">
                        <div>
                            <h5 className="font-medium text-foreground">{stage.name}</h5>
                            {stage.description && (
                                <p className="text-sm text-muted-foreground mt-1">{stage.description}</p>
                            )}
                        </div>
                        <div className="flex items-center gap-2">
                            <Badge
                                className={`text-xs ${config.bgColor} ${config.iconColor} border-0`}
                                variant="secondary"
                            >
                                {stage.status.replace("_", " ")}
                            </Badge>
                            {stage.isActive && (
                                <Tooltip>
                                    <TooltipTrigger asChild>
                                        <div className="flex items-center gap-1 text-xs text-muted-foreground">
                                            <Clock className="h-3 w-3" />
                                            {analytics.activeStageAge}d
                                        </div>
                                    </TooltipTrigger>
                                    <TooltipContent className="bg-background border-border text-foreground">
                                        <p>Active for {analytics.activeStageAge} days</p>
                                    </TooltipContent>
                                </Tooltip>
                            )}
                            <ChevronRight
                                className={`h-4 w-4 text-muted-foreground transition-transform ${
                                    selectedStage === stage.id ? "rotate-90" : ""
                                }`}
                            />
                        </div>
                    </div>

                    {selectedStage === stage.id && (
                        <StageDetails
                            canActOnStage={canActOnStage}
                            isApproving={isApproving}
                            isRejecting={isRejecting}
                            stage={stage}
                            onApprove={onApprove}
                            onReject={onReject}
                        />
                    )}
                </div>
            </div>
        </div>
    );
}

// Stage Timeline Component
function StageTimeline({
    allStages,
    selectedStage,
    setSelectedStage,
    analytics,
    onApprove,
    onReject,
    canActOnStage,
    isApproving,
    isRejecting,
}: StageTimelineProps) {
    return (
        <div className="space-y-4">
            <h4 className="font-medium text-foreground">Stage Timeline</h4>

            <div className="relative">
                <div className="absolute left-8 top-0 bottom-0 w-0.5 bg-muted-foreground/20" />

                <div className="space-y-4">
                    {allStages.map((stage, index) => {
                        const isLast = index === allStages.length - 1;

                        return (
                            <StageItem
                                key={stage.id}
                                analytics={analytics}
                                canActOnStage={canActOnStage}
                                index={index}
                                isApproving={isApproving}
                                isLast={isLast}
                                isRejecting={isRejecting}
                                selectedStage={selectedStage}
                                setSelectedStage={setSelectedStage}
                                stage={stage}
                                onApprove={onApprove}
                                onReject={onReject}
                            />
                        );
                    })}
                </div>
            </div>
        </div>
    );
}

// Analytics Summary Component
function AnalyticsSummary({ analytics }: AnalyticsSummaryProps) {
    return (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card className="border-border bg-background/30">
                <CardContent className="p-4">
                    <div className="flex items-center gap-2 mb-2">
                        <Clock className="h-4 w-4 text-blue-500" />
                        <span className="text-sm font-medium text-foreground">Active Stage</span>
                    </div>
                    <p className="text-sm text-muted-foreground">
                        {analytics.activeStage ? analytics.activeStage.name : "No active stage"}
                    </p>
                    {analytics.activeStage && (
                        <p className="text-xs text-muted-foreground mt-1">
                            Running for {analytics.activeStageAge} days
                        </p>
                    )}
                </CardContent>
            </Card>

            <Card className="border-border bg-background/30">
                <CardContent className="p-4">
                    <div className="flex items-center gap-2 mb-2">
                        <TrendingUp className="h-4 w-4 text-emerald-500" />
                        <span className="text-sm font-medium text-foreground">Progress Rate</span>
                    </div>
                    <p className="text-sm text-muted-foreground">
                        {analytics.workflowAge > 0
                            ? `${(analytics.completedStages / analytics.workflowAge).toFixed(1)} stages/day`
                            : "Just started"}
                    </p>
                </CardContent>
            </Card>

            <Card className="border-border bg-background/30">
                <CardContent className="p-4">
                    <div className="flex items-center gap-2 mb-2">
                        <AlertTriangle className="h-4 w-4 text-amber-500" />
                        <span className="text-sm font-medium text-foreground">SLA Status</span>
                    </div>
                    <p className={`text-sm font-medium ${getSLAStatusConfig(analytics.slaStatus).color}`}>
                        {getSLAStatusConfig(analytics.slaStatus).label}
                    </p>
                </CardContent>
            </Card>
        </div>
    );
}

// Main Component
export function WorkflowProgress({
    stages,
    templateStages,
    workflowCreatedAt,
    className = "",
    onApprove,
    onReject,
    canActOnStage,
    isApproving = false,
    isRejecting = false,
}: WorkflowProgressProps) {
    const [selectedStage, setSelectedStage] = useState<string | null>(null);

    const allStages = useMemo(() => {
        return templateStages
            .sort((a, b) => a.position - b.position)
            .map((templateStage) => {
                const workflowStage = stages.find(
                    (ws) => ws.template_stage_name === templateStage.name && ws.position === templateStage.position,
                );

                return {
                    ...templateStage,
                    workflowStage: workflowStage || null,
                    status: workflowStage?.status || "PENDING",
                    duration: workflowStage ? getDurationInDays(workflowStage.created_at, workflowStage.updated_at) : 0,
                    isActive: workflowStage?.status === "IN_REVIEW",
                    isCompleted: workflowStage?.status === "COMPLETED" || workflowStage?.status === "APPROVED",
                    isRejected: workflowStage?.status === "REJECTED",
                };
            });
    }, [stages, templateStages]);

    const analytics = useMemo(() => {
        const completedStages = allStages.filter((s) => s.isCompleted).length;
        const totalStages = allStages.length;
        const progressPercentage = (completedStages / totalStages) * 100;

        const workflowAge = differenceInDays(new Date(), new Date(workflowCreatedAt));
        const estimatedCompletion = totalStages * 3; // Assume 3 days per stage average

        const slaStatus: "on_track" | "at_risk" | "overdue" =
            workflowAge < estimatedCompletion * 0.7
                ? "on_track"
                : workflowAge < estimatedCompletion
                  ? "at_risk"
                  : "overdue";

        const activeStage = allStages.find((s) => s.isActive);
        const activeStageAge = activeStage?.workflowStage
            ? differenceInDays(new Date(), new Date(activeStage.workflowStage.created_at))
            : 0;

        return {
            progressPercentage,
            completedStages,
            totalStages,
            workflowAge,
            slaStatus,
            activeStage,
            activeStageAge,
            estimatedCompletion,
        };
    }, [allStages, workflowCreatedAt]);

    return (
        <TooltipProvider>
            <Card
                className={`border-border bg-background/50 backdrop-blur supports-[backdrop-filter]:bg-background/80 ${className}`}
            >
                <ProgressHeader analytics={analytics} />

                <CardContent className="space-y-6">
                    <OverallProgress analytics={analytics} />

                    <Separator className="bg-border" />

                    <StageTimeline
                        allStages={allStages}
                        analytics={analytics}
                        canActOnStage={canActOnStage}
                        isApproving={isApproving}
                        isRejecting={isRejecting}
                        selectedStage={selectedStage}
                        setSelectedStage={setSelectedStage}
                        onApprove={onApprove}
                        onReject={onReject}
                    />

                    <Separator className="bg-border" />

                    <AnalyticsSummary analytics={analytics} />
                </CardContent>
            </Card>
        </TooltipProvider>
    );
}
