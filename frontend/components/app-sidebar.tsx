"use client";

import {
    AwardI<PERSON>,
    Bot,
    BriefcaseIcon,
    Building2Icon,
    CalendarIcon,
    ChartLineIcon,
    CogIcon,
    CompassIcon,
    CreditCardIcon,
    DatabaseIcon,
    FileTextIcon,
    GroupIcon,
    HomeIcon,
    List,
    LucideIcon,
    ScaleIcon,
    SearchIcon,
    Settings2,
    TicketCheckIcon,
    Users2Icon,
} from "lucide-react";
import * as React from "react";

import { AccountSwitcher } from "./account-switcher";
import { NavAdmin } from "./nav-admin";

import { NavMain } from "@/components/nav-main";
import { NavUser } from "@/components/nav-user";
import {
    Sidebar,
    SidebarContent,
    SidebarFooter,
    SidebarHeader,
    SidebarMenu,
    SidebarMenuItem,
    SidebarRail,
} from "@/components/ui/sidebar";
import { useAuth } from "@/composables/useStore";

type SidebarNavItem = {
    title: string;
    url: string;
    items?: Array<{ title: string; url: string; icon?: LucideIcon }>;
    icon?: LucideIcon;
};

type AdminLink = {
    name: string;
    url: string;
    icon: LucideIcon;
    items?: Array<{ name: string; url: string; icon: LucideIcon }>;
};

type SidebarProps = {
    navMain: SidebarNavItem[];
    adminLinks: AdminLink[];
};

const data: SidebarProps = {
    navMain: [
        {
            title: "Dashboard",
            url: "/dashboard",
            icon: HomeIcon,
        },
        {
            title: "Organizations",
            url: "",
            icon: Bot,
            items: [
                {
                    title: "Overview",
                    icon: List,
                    url: "/organizations",
                },
                {
                    title: "Registration",
                    icon: FileTextIcon,
                    url: "/organizations/create",
                },
                {
                    title: "Approvals",
                    icon: TicketCheckIcon,
                    url: "/workflows?type=registration",
                },
                {
                    title: "Certificates",
                    icon: AwardIcon,
                    url: "/documents?type=certificate",
                },
            ],
        },
        {
            title: "Reporting",
            url: "#",
            icon: ChartLineIcon,
            items: [
                {
                    title: "Report Submission",
                    icon: FileTextIcon,
                    url: "/reporting",
                },
                {
                    title: "Reporting Approvals",
                    icon: TicketCheckIcon,
                    url: "/workflows?type=renewal",
                },
                {
                    title: "Licences",
                    icon: AwardIcon,
                    url: "/documents?type=licence",
                },
            ],
        },
        {
            title: "TEP",
            url: "#",
            icon: FileTextIcon,
            items: [
                {
                    title: "TEP Requests",
                    icon: FileTextIcon,
                    url: "/permits",
                },
                {
                    title: "New TEP Request",
                    icon: FileTextIcon,
                    url: "/permits/create",
                },
                {
                    title: "TEP Approvals",
                    icon: TicketCheckIcon,
                    url: "/workflows?type=permit",
                },
                {
                    title: "Permits",
                    icon: AwardIcon,
                    url: "/permits/documents",
                },
            ],
        },
        {
            title: "NGO Card",
            url: "#",
            icon: CreditCardIcon,
            items: [
                {
                    title: "Search",
                    icon: SearchIcon,
                    url: "/ngo_card",
                },
            ],
        },
        {
            title: "Case Management",
            url: "#",
            icon: ScaleIcon,
            items: [
                {
                    title: "Overview",
                    icon: FileTextIcon,
                    url: "/complaints",
                },
                {
                    title: "Lodge Complaint",
                    icon: FileTextIcon,
                    url: "/complaints/submit",
                },
            ],
        },
        {
            title: "Activities",
            url: "#",
            icon: CalendarIcon,
            items: [
                {
                    title: "Activities",
                    icon: CalendarIcon,
                    url: "/management/activities",
                },
            ],
        },
        {
            title: "Finance",
            url: "#",
            icon: BriefcaseIcon,
            items: [
                {
                    title: "Overview",
                    icon: ChartLineIcon,
                    url: "/financials",
                },
                {
                    title: "Payments",
                    icon: BriefcaseIcon,
                    url: "/financials/payments",
                },
                {
                    title: "Invoices",
                    icon: FileTextIcon,
                    url: "/financials/invoices",
                },
            ],
        },
        {
            title: "Projects",
            url: "#",
            icon: CompassIcon,
            items: [
                {
                    title: "Project Creation",
                    icon: CompassIcon,
                    url: "/project",
                },
                {
                    title: "Data Capture",
                    icon: FileTextIcon,
                    url: "/project_data",
                },
            ],
        },
        {
            title: "Reports",
            url: "/report",
            icon: FileTextIcon,
            items: [],
        },
        {
            title: "Settings",
            url: "",
            icon: Settings2,
            items: [
                {
                    title: "General",
                    url: "/settings",
                },
                {
                    title: "Fees",
                    url: "/finance/fees",
                },
                {
                    title: "Payment Modes",
                    url: "/finance/payment-modes",
                },
            ],
        },
    ],
    adminLinks: [
        {
            name: "Administration",
            url: "#",
            icon: Users2Icon,
            items: [
                {
                    name: "Workflow Templates",
                    icon: CogIcon,
                    url: "/settings/workflows",
                },
                {
                    name: "Users",
                    url: "/management/users",
                    icon: Users2Icon,
                },
                {
                    name: "Departments",
                    url: "/management/departments",
                    icon: GroupIcon,
                },
                {
                    name: "Roles",
                    url: "/management/roles",
                    icon: GroupIcon,
                },
                {
                    name: "Notices",
                    url: "/management/notices",
                    icon: FileTextIcon,
                },
                {
                    name: "Audit Trail",
                    url: "/management/audit_trail",
                    icon: FileTextIcon,
                },
            ],
        },
        {
            name: "Configurations",
            url: "#",
            icon: CogIcon,
            items: [
                {
                    name: "Districts",
                    url: "/management/districts",
                    icon: DatabaseIcon,
                },
                {
                    name: "Zones",
                    url: "/management/zones",
                    icon: DatabaseIcon,
                },
                {
                    name: "Regions",
                    url: "/management/regions",
                    icon: DatabaseIcon,
                },
                {
                    name: "System Settings",
                    url: "/management/system",
                    icon: CogIcon,
                },
                {
                    name: "Security",
                    url: "/management/security",
                    icon: Settings2,
                },
                {
                    name: "NGO Settings",
                    url: "/management/ngo-settings",
                    icon: Building2Icon,
                },
                // {
                //     name: "Complaint Categories",
                //     url: "/management/complaint_category",
                //     icon: ScaleIcon,
                // }, -> loadable items
                {
                    name: "NGO Card Sharing",
                    url: "/management/ngo_card_sharing",
                    icon: CreditCardIcon,
                },
                // {
                //     name: "Activity Categories",
                //     url: "/management/activity_category",
                //     icon: CalendarIcon,
                // }, -> loadable items
                {
                    name: "Data Sharing",
                    url: "/management/data_sharing",
                    icon: DatabaseIcon,
                },
                {
                    name: "Templates",
                    url: "/management/template",
                    icon: FileTextIcon,
                },
                {
                    name: "Loadable Items",
                    url: "/management/loadable-items",
                    icon: FileTextIcon,
                },
            ],
        },
    ],
};

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
    const { session, context, setContext } = useAuth();

    return (
        <Sidebar collapsible="icon" {...props}>
            <SidebarHeader>
                <AccountSwitcher
                    context={context}
                    memberships={session?.memberships || []}
                    setActiveAccount={setContext}
                />
            </SidebarHeader>
            <SidebarContent>
                <NavMain items={data.navMain} />
                <NavAdmin links={data.adminLinks} />
            </SidebarContent>
            {session ? (
                <SidebarFooter>
                    <SidebarMenu>
                        <SidebarMenuItem>
                            <NavUser position="bottom" session={session} />
                        </SidebarMenuItem>
                    </SidebarMenu>
                </SidebarFooter>
            ) : (
                ""
            )}
            <SidebarRail />
        </Sidebar>
    );
}
