import { ColumnDef } from "@tanstack/react-table";
import { MoreH<PERSON><PERSON>tal, <PERSON>cil, Plus, Trash2 } from "lucide-react";
import { useState } from "react";

import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { DataTable } from "@/components/ui/data-table";
import { Input } from "@/components/ui/input";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { LoadableItemDto } from "@/types";

interface ZonesTableProps {
    zones: LoadableItemDto[];
    loading: boolean;
    onEdit: (zone: LoadableItemDto) => void;
    onDelete: (zone: LoadableItemDto) => void;
    onCreate: () => void;
}

export function ZonesTable({ zones, loading, onEdit, onDelete, onCreate }: ZonesTableProps) {
    const [search, setSearch] = useState("");

    const filteredZones = zones.filter(
        (zone) =>
            zone.display_value.toLowerCase().includes(search.toLowerCase()) ||
            zone.code.toLowerCase().includes(search.toLowerCase()),
    );

    const columns: ColumnDef<LoadableItemDto>[] = [
        {
            accessorKey: "display_value",
            header: "Zone",
            cell: ({ row }) => (
                <div>
                    <div className="font-medium text-foreground">{row.original.display_value}</div>
                    <div className="text-muted-foreground text-xs font-mono">{row.original.code}</div>
                </div>
            ),
        },
        {
            accessorKey: "description",
            header: "Description",
            cell: ({ row }) => (
                <div className="max-w-[300px] truncate" title={row.original.description}>
                    {row.original.description || "—"}
                </div>
            ),
        },
        {
            accessorKey: "created_at",
            header: "Created",
            cell: ({ row }) => (
                <div className="text-sm text-muted-foreground">
                    {row.original.created_at ? new Date(row.original.created_at).toLocaleDateString() : "—"}
                </div>
            ),
        },
        {
            id: "actions",
            header: "Actions",
            cell: ({ row }) => (
                <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                        <Button className="h-8 w-8 p-0" size="icon-sm" variant="ghost">
                            <span className="sr-only">Open menu</span>
                            <MoreHorizontal className="h-4 w-4" />
                        </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => onEdit(row.original)}>
                            <Pencil className="mr-2 h-4 w-4" /> Edit
                        </DropdownMenuItem>
                        <DropdownMenuItem className="text-red-600" onClick={() => onDelete(row.original)}>
                            <Trash2 className="mr-2 h-4 w-4" /> Delete
                        </DropdownMenuItem>
                    </DropdownMenuContent>
                </DropdownMenu>
            ),
        },
    ];

    return (
        <>
            <div className="flex items-center justify-between py-6">
                <div>
                    <CardTitle className="text-2xl">Zones</CardTitle>
                    <p className="text-muted-foreground">Manage geographical zones and their descriptions</p>
                </div>
                <Button onClick={onCreate}>
                    <Plus className="mr-2 h-4 w-4" /> Create Zone
                </Button>
            </div>
            <Card>
                <CardHeader>
                    <Input
                        className="mb-4 max-w-sm"
                        placeholder="Search zones..."
                        value={search}
                        onChange={(e) => setSearch(e.target.value)}
                    />
                </CardHeader>
                <CardContent>
                    <DataTable
                        columns={columns}
                        data={filteredZones}
                        enableFiltering={false}
                        enablePagination={true}
                        enableSorting={true}
                        pageSize={10}
                    />
                </CardContent>
            </Card>
        </>
    );
}
