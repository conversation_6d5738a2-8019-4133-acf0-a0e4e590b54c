"use client";

import React from "react";
import { ColumnDef } from "@tanstack/react-table";
import { Edit, Trash2, MoreH<PERSON><PERSON><PERSON> } from "lucide-react";
// import { format } from "date-fns";

import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { DataTable } from "@/components/ui/data-table";
import { LoadableItemDto } from "@/types";

interface LoadableItemsTableProps {
    data: LoadableItemDto[];
    loading?: boolean;
    onEdit: (item: LoadableItemDto) => void;
    onDelete: (item: LoadableItemDto) => void;
}

export const LoadableItemsTable: React.FC<LoadableItemsTableProps> = ({ data, loading = false, onEdit, onDelete }) => {
    const columns: ColumnDef<LoadableItemDto>[] = [
        {
            accessorKey: "type",
            header: "Type",
            cell: ({ row }) => (
                <Badge className="font-medium" variant="secondary">
                    {row.getValue("type")}
                </Badge>
            ),
        },
        {
            accessorKey: "code",
            header: "Code",
            cell: ({ row }) => (
                <span className="font-mono text-sm bg-gray-100 dark:bg-gray-800 px-2 py-1 rounded">
                    {row.getValue("code")}
                </span>
            ),
        },
        {
            accessorKey: "display_value",
            header: "Display Value",
            cell: ({ row }) => <span className="font-medium">{row.getValue("display_value")}</span>,
        },
        {
            accessorKey: "description",
            header: "Description",
            cell: ({ row }) => {
                const description = row.getValue("description") as string;

                return <span className="text-muted-foreground max-w-xs truncate block">{description || "—"}</span>;
            },
        },
        {
            accessorKey: "created_at",
            header: "Created",
            cell: ({ row }) => {
                const date = row.getValue("created_at") as Date;

                return <span className="text-sm text-muted-foreground">{new Date(date).toLocaleDateString()}</span>;
            },
        },
        {
            id: "actions",
            header: "Actions",
            cell: ({ row }) => {
                const item = row.original;

                return (
                    <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                            <Button className="h-8 w-8 p-0" variant="ghost">
                                <span className="sr-only">Open menu</span>
                                <MoreHorizontal className="h-4 w-4" />
                            </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                            <DropdownMenuItem className="cursor-pointer" onClick={() => onEdit(item)}>
                                <Edit className="mr-2 h-4 w-4" />
                                Edit
                            </DropdownMenuItem>
                            <DropdownMenuItem
                                className="cursor-pointer text-red-600 focus:text-red-600"
                                onClick={() => onDelete(item)}
                            >
                                <Trash2 className="mr-2 h-4 w-4" />
                                Delete
                            </DropdownMenuItem>
                        </DropdownMenuContent>
                    </DropdownMenu>
                );
            },
        },
    ];

    if (loading) {
        return (
            <div className="space-y-4">
                <div className="h-10 bg-gray-200 dark:bg-gray-700 rounded animate-pulse" />
                <div className="space-y-2">
                    {[...Array(5)].map((_, i) => (
                        <div key={i} className="h-16 bg-gray-100 dark:bg-gray-800 rounded animate-pulse" />
                    ))}
                </div>
            </div>
        );
    }

    return (
        <DataTable
            columns={columns}
            data={data}
            enableFiltering={true}
            enablePagination={true}
            enableSorting={true}
            pageSize={10}
            searchKey="display_value"
            searchPlaceholder="Search loadable items..."
        />
    );
};
