# Staging Environment Configuration

# Database Configuration
POSTGRES_DB=ngora_staging
POSTGRES_USER=postgres
POSTGRES_PASSWORD=staging_postgres_password
POSTGRES_HOST=postgres
POSTGRES_PORT=5432

# Redis Configuration
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=staging_redis_password

# MinIO/S3 Storage Configuration
MINIO_ROOT_USER=staging_minio_admin
MINIO_ROOT_PASSWORD=staging_minio_password
STORAGE_ENDPOINT=minio:9000
STORAGE_ACCESS_KEY=staging_minio_admin
STORAGE_SECRET_KEY=staging_minio_password
STORAGE_BUCKET=ngora-documents-staging

# Application Security
SECRET_KEY=staging_secret_key_at_least_32_characters_long
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=60

# Email Configuration (Mailjet - staging keys)
MAILJET_API_KEY=your_staging_mailjet_api_key
MAILJET_API_SECRET=your_staging_mailjet_api_secret
FROM_EMAIL=<EMAIL>

# External URLs
FRONTEND_URL=https://staging.ngora.mw
NEXT_PUBLIC_API_BASE_URL=https://staging.ngora.mw/api
NEXT_PUBLIC_STORAGE_URL=https://staging.ngora.mw/storage

# Environment
ENVIRONMENT=staging
NODE_ENV=production

# SSL Configuration
SSL_CERT_PATH=/etc/nginx/ssl/cert.pem
SSL_KEY_PATH=/etc/nginx/ssl/key.pem

# Backup Configuration
BACKUP_RETENTION_DAYS=7
BACKUP_STORAGE_PATH=/opt/backups

# Monitoring
ENABLE_MONITORING=true
LOG_LEVEL=debug

# Rate Limiting (more lenient for testing)
API_RATE_LIMIT=200
AUTH_RATE_LIMIT=10