# Production Environment Configuration

# Database Configuration
POSTGRES_DB=ngora_prod
POSTGRES_USER=postgres
POSTGRES_PASSWORD=your_secure_postgres_password_here
POSTGRES_HOST=postgres
POSTGRES_PORT=5432

# Redis Configuration
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=your_secure_redis_password_here

# MinIO/S3 Storage Configuration
MINIO_ROOT_USER=your_minio_admin_user
MINIO_ROOT_PASSWORD=your_secure_minio_password_here
STORAGE_ENDPOINT=minio:9000
STORAGE_ACCESS_KEY=your_minio_admin_user
STORAGE_SECRET_KEY=your_secure_minio_password_here
STORAGE_BUCKET=ngora-documents

# Application Security
SECRET_KEY=your_very_long_secret_key_at_least_32_characters_long
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Email Configuration (Mailjet)
MAILJET_API_KEY=your_mailjet_api_key
MAILJET_API_SECRET=your_mailjet_api_secret
FROM_EMAIL=<EMAIL>

# External URLs
FRONTEND_URL=https://ngora.mw
NEXT_PUBLIC_API_BASE_URL=https://ngora.mw/api
NEXT_PUBLIC_STORAGE_URL=https://ngora.mw/storage

# Environment
ENVIRONMENT=production
NODE_ENV=production

# SSL Configuration (if using custom certificates)
SSL_CERT_PATH=/etc/nginx/ssl/cert.pem
SSL_KEY_PATH=/etc/nginx/ssl/key.pem

# Backup Configuration
BACKUP_RETENTION_DAYS=30
BACKUP_STORAGE_PATH=/opt/backups

# Monitoring
ENABLE_MONITORING=true
LOG_LEVEL=info

# Rate Limiting
API_RATE_LIMIT=100
AUTH_RATE_LIMIT=5