#!/bin/bash

# Hetzner Cloud Deployment Script
# This script deploys the NGORA application to a Hetzner Cloud server

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "${SCRIPT_DIR}/../.." && pwd)"

# Default values
ENVIRONMENT=${1:-production}
SERVER_IP=${2:-""}
SSH_USER=${3:-root}
SSH_KEY_PATH=${4:-~/.ssh/id_rsa}

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check if required tools are installed
    command -v ssh >/dev/null 2>&1 || { log_error "ssh is required but not installed."; exit 1; }
    command -v scp >/dev/null 2>&1 || { log_error "scp is required but not installed."; exit 1; }
    
    # Check if SSH key exists
    if [[ ! -f "${SSH_KEY_PATH}" ]]; then
        log_error "SSH key not found at ${SSH_KEY_PATH}"
        exit 1
    fi
    
    # Check if server IP is provided
    if [[ -z "${SERVER_IP}" ]]; then
        log_error "Server IP address is required"
        echo "Usage: $0 <environment> <server_ip> [ssh_user] [ssh_key_path]"
        exit 1
    fi
    
    log_success "Prerequisites check passed"
}

# Test SSH connection
test_ssh_connection() {
    log_info "Testing SSH connection to ${SERVER_IP}..."
    
    if ssh -i "${SSH_KEY_PATH}" -o ConnectTimeout=10 -o BatchMode=yes "${SSH_USER}@${SERVER_IP}" "echo 'SSH connection successful'" >/dev/null 2>&1; then
        log_success "SSH connection successful"
    else
        log_error "Failed to connect to ${SERVER_IP} via SSH"
        exit 1
    fi
}

# Install Docker and Docker Compose on the server
install_docker() {
    log_info "Installing Docker and Docker Compose on the server..."
    
    ssh -i "${SSH_KEY_PATH}" "${SSH_USER}@${SERVER_IP}" << 'EOF'
        # Update system
        apt-get update
        apt-get upgrade -y
        
        # Install required packages
        apt-get install -y \
            apt-transport-https \
            ca-certificates \
            curl \
            gnupg \
            lsb-release \
            ufw \
            fail2ban
        
        # Add Docker GPG key
        curl -fsSL https://download.docker.com/linux/ubuntu/gpg | gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg
        
        # Add Docker repository
        echo "deb [arch=amd64 signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | tee /etc/apt/sources.list.d/docker.list > /dev/null
        
        # Install Docker
        apt-get update
        apt-get install -y docker-ce docker-ce-cli containerd.io
        
        # Install Docker Compose
        curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
        chmod +x /usr/local/bin/docker-compose
        
        # Start and enable Docker
        systemctl start docker
        systemctl enable docker
        
        # Add user to docker group (if not root)
        if [ "$USER" != "root" ]; then
            usermod -aG docker $USER
        fi
        
        # Verify installation
        docker --version
        docker compose --version
EOF
    
    log_success "Docker and Docker Compose installed successfully"
}

# Setup firewall
setup_firewall() {
    log_info "Setting up firewall..."
    
    ssh -i "${SSH_KEY_PATH}" "${SSH_USER}@${SERVER_IP}" << 'EOF'
        # Reset UFW to defaults
        ufw --force reset
        
        # Set default policies
        ufw default deny incoming
        ufw default allow outgoing
        
        # Allow SSH
        ufw allow ssh
        
        # Allow HTTP and HTTPS
        ufw allow 80/tcp
        ufw allow 443/tcp
        
        # Enable UFW
        ufw --force enable
        
        # Configure fail2ban
        systemctl enable fail2ban
        systemctl start fail2ban
        
        # Show status
        ufw status verbose
EOF
    
    log_success "Firewall configured successfully"
}

# Create application directory and copy files
setup_application() {
    log_info "Setting up application on the server..."
    
    # Create application directory
    ssh -i "${SSH_KEY_PATH}" "${SSH_USER}@${SERVER_IP}" "mkdir -p /opt/ngora-${ENVIRONMENT}"
    
    # Copy deployment files
    scp -i "${SSH_KEY_PATH}" -r "${PROJECT_ROOT}/infra/docker" "${SSH_USER}@${SERVER_IP}:/opt/ngora-${ENVIRONMENT}/"
    scp -i "${SSH_KEY_PATH}" -r "${PROJECT_ROOT}/infra/scripts" "${SSH_USER}@${SERVER_IP}:/opt/ngora-${ENVIRONMENT}/"
    
    # Copy environment-specific docker compose file
    if [[ "${ENVIRONMENT}" == "production" ]]; then
        scp -i "${SSH_KEY_PATH}" "${PROJECT_ROOT}/infra/docker/docker-compose.prod.yml" "${SSH_USER}@${SERVER_IP}:/opt/ngora-${ENVIRONMENT}/docker-compose.yml"
    else
        scp -i "${SSH_KEY_PATH}" "${PROJECT_ROOT}/infra/docker/docker-compose.staging.yml" "${SSH_USER}@${SERVER_IP}:/opt/ngora-${ENVIRONMENT}/docker-compose.yml"
    fi
    
    log_success "Application files copied successfully"
}

# Setup SSL certificates (Let's Encrypt)
setup_ssl() {
    log_info "Setting up SSL certificates..."
    
    ssh -i "${SSH_KEY_PATH}" "${SSH_USER}@${SERVER_IP}" << 'EOF'
        # Install certbot
        apt-get update
        apt-get install -y certbot python3-certbot-nginx
        
        # Create SSL directory
        mkdir -p /opt/ngora-production/docker/ssl
        
        # Generate self-signed certificate for initial setup
        openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
            -keyout /opt/ngora-production/docker/ssl/key.pem \
            -out /opt/ngora-production/docker/ssl/cert.pem \
            -subj "/C=MW/ST=Central/L=Lilongwe/O=NGORA/CN=ngora.mw"
        
        echo "SSL certificates generated. Replace with Let's Encrypt certificates in production."
EOF
    
    log_success "SSL certificates setup completed"
}

# Setup monitoring and logging
setup_monitoring() {
    log_info "Setting up monitoring and logging..."
    
    ssh -i "${SSH_KEY_PATH}" "${SSH_USER}@${SERVER_IP}" << 'EOF'
        # Create log directories
        mkdir -p /var/log/ngora
        
        # Setup log rotation for Docker
        cat > /etc/logrotate.d/docker << 'LOGROTATE'
/var/lib/docker/containers/*/*.log {
    rotate 7
    daily
    compress
    size=50M
    missingok
    delaycompress
    copytruncate
}
LOGROTATE
        
        # Setup system monitoring script
        cat > /usr/local/bin/system-monitor.sh << 'MONITOR'
#!/bin/bash
LOGFILE="/var/log/ngora/system-monitor.log"
echo "$(date): System monitoring check" >> $LOGFILE

# Check disk space
df -h | grep -E '9[0-9]%|100%' >> $LOGFILE 2>&1

# Check memory usage
free -h >> $LOGFILE

# Check Docker containers
docker ps --format "table {{.Names}}\t{{.Status}}" >> $LOGFILE

# Check application health
curl -f http://localhost/health >> $LOGFILE 2>&1 || echo "$(date): Health check failed" >> $LOGFILE
MONITOR

        chmod +x /usr/local/bin/system-monitor.sh

        # Add cron job for monitoring
        (crontab -l 2>/dev/null; echo "*/5 * * * * /usr/local/bin/system-monitor.sh") | crontab -
EOF

    log_success "Monitoring and logging setup completed"
}

# Deploy application
deploy_application() {
    log_info "Deploying application..."

    ssh -i "${SSH_KEY_PATH}" "${SSH_USER}@${SERVER_IP}" << EOF
        cd /opt/ngora-${ENVIRONMENT}

        # Load environment variables
        if [[ -f .env.${ENVIRONMENT} ]]; then
            source .env.${ENVIRONMENT}
        fi

        # Pull latest images
        docker compose pull

        # Start services
        docker compose up -d

        # Wait for services to start
        sleep 30

        # Check if services are running
        docker compose ps

        # Health check
        if curl -f http://localhost/health; then
            echo "Application deployed successfully!"
        else
            echo "Application health check failed!"
            docker compose logs
            exit 1
        fi
EOF

    log_success "Application deployed successfully"
}

# Create backup script
create_backup_script() {
    log_info "Creating backup script..."

    ssh -i "${SSH_KEY_PATH}" "${SSH_USER}@${SERVER_IP}" << 'EOF'
        cat > /usr/local/bin/ngora-backup.sh << 'BACKUP'
#!/bin/bash
BACKUP_DIR="/opt/backups"
DATE=$(date +%Y%m%d_%H%M%S)
ENVIRONMENT="production"

mkdir -p $BACKUP_DIR

# Backup database
docker exec ngora-${ENVIRONMENT}_postgres_1 pg_dumpall -U postgres > $BACKUP_DIR/database_backup_$DATE.sql

# Backup uploaded files (MinIO data)
tar -czf $BACKUP_DIR/files_backup_$DATE.tar.gz -C /var/lib/docker/volumes ngora-${ENVIRONMENT}_minio_data

# Keep only last 7 days of backups
find $BACKUP_DIR -name "*.sql" -mtime +7 -delete
find $BACKUP_DIR -name "*.tar.gz" -mtime +7 -delete

echo "Backup completed: $DATE"
BACKUP

        chmod +x /usr/local/bin/ngora-backup.sh

        # Add daily backup cron job
        (crontab -l 2>/dev/null; echo "0 2 * * * /usr/local/bin/ngora-backup.sh") | crontab -
EOF

    log_success "Backup script created"
}

# Main deployment function
main() {
    log_info "Starting deployment to Hetzner Cloud server..."
    log_info "Environment: ${ENVIRONMENT}"
    log_info "Server IP: ${SERVER_IP}"
    log_info "SSH User: ${SSH_USER}"

    check_prerequisites
    test_ssh_connection
    install_docker
    setup_firewall
    setup_application
    setup_ssl
    setup_monitoring
    deploy_application
    create_backup_script

    log_success "Deployment completed successfully!"
    log_info "Application should be available at: https://${SERVER_IP}"
    log_warning "Don't forget to:"
    log_warning "1. Configure your domain DNS to point to ${SERVER_IP}"
    log_warning "2. Replace self-signed SSL certificates with Let's Encrypt certificates"
    log_warning "3. Set up proper environment variables in .env.${ENVIRONMENT}"
    log_warning "4. Configure email settings for notifications"
}

# Run main function
main "$@"