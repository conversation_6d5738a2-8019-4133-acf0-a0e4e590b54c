#!/bin/bash

# Setup Secrets for NGORA Deployment
# This script helps generate secure passwords and configure environment variables

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Generate a secure random password
generate_password() {
    local length=${1:-32}
    openssl rand -base64 $length | tr -d "=+/" | cut -c1-$length
}

# Generate a secure secret key
generate_secret_key() {
    openssl rand -hex 32
}

# Setup environment for specified environment
setup_environment() {
    local env_name=$1
    local template_file="infra/docker/.env.${env_name}.template"
    local output_file="infra/docker/.env.${env_name}"

    log_info "Setting up ${env_name} environment..."

    if [[ ! -f "$template_file" ]]; then
        log_error "Template file $template_file not found"
        return 1
    fi

    # Copy template to actual env file
    cp "$template_file" "$output_file"

    # Generate passwords
    local postgres_password=$(generate_password 24)
    local redis_password=$(generate_password 24)
    local minio_password=$(generate_password 24)
    local secret_key=$(generate_secret_key)

    # Replace placeholders in the env file
    if [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS
        sed -i '' "s/your_secure_postgres_password_here/$postgres_password/g" "$output_file"
        sed -i '' "s/staging_postgres_password/$postgres_password/g" "$output_file"
        sed -i '' "s/your_secure_redis_password_here/$redis_password/g" "$output_file"
        sed -i '' "s/staging_redis_password/$redis_password/g" "$output_file"
        sed -i '' "s/your_secure_minio_password_here/$minio_password/g" "$output_file"
        sed -i '' "s/staging_minio_password/$minio_password/g" "$output_file"
        sed -i '' "s/your_very_long_secret_key_at_least_32_characters_long/$secret_key/g" "$output_file"
        sed -i '' "s/staging_secret_key_at_least_32_characters_long/$secret_key/g" "$output_file"
    else
        # Linux
        sed -i "s/your_secure_postgres_password_here/$postgres_password/g" "$output_file"
        sed -i "s/staging_postgres_password/$postgres_password/g" "$output_file"
        sed -i "s/your_secure_redis_password_here/$redis_password/g" "$output_file"
        sed -i "s/staging_redis_password/$redis_password/g" "$output_file"
        sed -i "s/your_secure_minio_password_here/$minio_password/g" "$output_file"
        sed -i "s/staging_minio_password/$minio_password/g" "$output_file"
        sed -i "s/your_very_long_secret_key_at_least_32_characters_long/$secret_key/g" "$output_file"
        sed -i "s/staging_secret_key_at_least_32_characters_long/$secret_key/g" "$output_file"
    fi

    log_success "Environment file created: $output_file"
    log_warning "Please review and update the following values manually:"
    echo "  - MAILJET_API_KEY"
    echo "  - MAILJET_API_SECRET"
    echo "  - MINIO_ROOT_USER (if needed)"
    echo "  - FROM_EMAIL"
    echo "  - Domain URLs"

    # Set appropriate permissions
    chmod 600 "$output_file"

    log_info "Generated passwords:"
    echo "  PostgreSQL: $postgres_password"
    echo "  Redis: $redis_password"
    echo "  MinIO: $minio_password"
    echo "  Secret Key: $secret_key"
    log_warning "Store these passwords securely!"
}

# Generate GitHub Secrets format
generate_github_secrets() {
    local env_name=$1
    local env_file="infra/docker/.env.${env_name}"

    if [[ ! -f "$env_file" ]]; then
        log_error "Environment file $env_file not found. Run setup first."
        return 1
    fi

    log_info "GitHub Secrets for ${env_name} environment:"
    echo "================================"

    # Extract key-value pairs and format for GitHub
    while IFS='=' read -r key value; do
        # Skip comments and empty lines
        if [[ $key == \#* ]] || [[ -z $key ]]; then
            continue
        fi

        # Clean up the value (remove quotes if present)
        value=$(echo "$value" | sed 's/^"//;s/"$//')

        # Convert to uppercase for GitHub secrets
        secret_name=$(echo "${env_name}_${key}" | tr '[:lower:]' '[:upper:]')

        echo "$secret_name: $value"
    done < "$env_file"
}

# Validate environment file
validate_environment() {
    local env_file=$1

    log_info "Validating environment file: $env_file"

    # Check for placeholder values that need to be replaced
    local placeholders=(
        "your_mailjet_api_key"
        "your_staging_mailjet_api_key"
        "your_mailjet_api_secret"
        "your_staging_mailjet_api_secret"
    )

    local has_placeholders=false
    for placeholder in "${placeholders[@]}"; do
        if grep -q "$placeholder" "$env_file"; then
            log_warning "Found placeholder: $placeholder"
            has_placeholders=true
        fi
    done

    if [[ "$has_placeholders" == "true" ]]; then
        log_warning "Please replace all placeholder values before deployment"
        return 1
    fi

    log_success "Environment file validation passed"
    return 0
}

# Main script logic
main() {
    local command=${1:-help}

    case $command in
        "production")
            setup_environment "production"
            ;;
        "staging")
            setup_environment "staging"
            ;;
        "github-secrets")
            local env_name=${2:-production}
            generate_github_secrets "$env_name"
            ;;
        "validate")
            local env_file=${2:-infra/docker/.env.production}
            validate_environment "$env_file"
            ;;
        "help"|*)
            echo "Usage: $0 <command> [options]"
            echo ""
            echo "Commands:"
            echo "  production       Setup production environment"
            echo "  staging          Setup staging environment"
            echo "  github-secrets   Generate GitHub secrets format"
            echo "  validate         Validate environment file"
            echo "  help            Show this help message"
            echo ""
            echo "Examples:"
            echo "  $0 production"
            echo "  $0 staging"
            echo "  $0 github-secrets production"
            echo "  $0 validate infra/docker/.env.production"
            ;;
    esac
}

# Check prerequisites
if ! command -v openssl &> /dev/null; then
    log_error "openssl is required but not installed"
    exit 1
fi

# Run main function
main "$@"