# NGORA Deployment Checklist

Use this checklist to ensure a successful deployment to Hetzner Cloud.

## 🔧 Pre-Deployment

### 1. Environment Setup
- [ ] Generate environment configuration using `./infra/scripts/setup-secrets.sh`
- [ ] Review and update `.env.production` with actual values:
  - [ ] Mailjet API credentials
  - [ ] Domain names (ngora.mw)
  - [ ] Email addresses
  - [ ] Any custom configurations
- [ ] Validate environment file: `./infra/scripts/setup-secrets.sh validate`

### 2. GitHub Repository Setup
- [ ] Repository secrets configured:
  - [ ] `PRODUCTION_SSH_PRIVATE_KEY`
  - [ ] `PRODUCTION_USER`
  - [ ] `PRODUCTION_HOST`
  - [ ] `STAGING_SSH_PRIVATE_KEY` (if using staging)
  - [ ] `STAGING_USER` (if using staging)
  - [ ] `STAGING_HOST` (if using staging)
- [ ] GitHub Actions workflows are enabled
- [ ] Container registry permissions configured

### 3. Hetzner Cloud Server
- [ ] Server created with minimum specifications:
  - [ ] 4 vCPUs, 8 GB RAM, 80 GB SSD (production)
  - [ ] 2 vCPUs, 4 GB RAM, 40 GB SSD (staging)
- [ ] Ubuntu 22.04 LTS installed
- [ ] SSH key access configured
- [ ] Server IP address noted
- [ ] Firewall allows SSH access

### 4. Domain & DNS
- [ ] Domain registered and configured
- [ ] DNS A records created:
  - [ ] `ngora.mw` → Production IP
  - [ ] `www.ngora.mw` → Production IP
  - [ ] `staging.ngora.mw` → Staging IP (if applicable)
- [ ] DNS propagation verified

## 🚀 Deployment Process

### 1. Automated Deployment (Recommended)
- [ ] Code pushed to `main` branch for production
- [ ] GitHub Actions workflow completed successfully
- [ ] Container images built and pushed to registry
- [ ] Services deployed and healthy

### 2. Manual Deployment (If needed)
- [ ] Run deployment script: `./infra/scripts/deploy-hetzner.sh production <server-ip>`
- [ ] Script completed without errors
- [ ] All services started successfully

## 🔒 Post-Deployment Security

### 1. SSL Certificates
- [ ] Stop Nginx: `docker-compose down nginx`
- [ ] Install Let's Encrypt certificates:
  ```bash
  certbot certonly --standalone -d ngora.mw -d www.ngora.mw
  ```
- [ ] Copy certificates to application directory
- [ ] Start Nginx: `docker-compose up -d nginx`
- [ ] Verify HTTPS access

### 2. Security Configuration
- [ ] Firewall rules active (UFW enabled)
- [ ] Fail2ban configured and running
- [ ] SSH key-only authentication
- [ ] Strong passwords generated and stored securely
- [ ] Rate limiting configured in Nginx

### 3. Database Security
- [ ] Database password is strong and unique
- [ ] Database not accessible from external networks
- [ ] Regular backup schedule configured

## ✅ System Verification

### 1. Service Health Checks
- [ ] All Docker containers running: `docker-compose ps`
- [ ] Application health endpoint: `curl https://ngora.mw/health`
- [ ] Database connectivity verified
- [ ] Redis connectivity verified
- [ ] MinIO storage accessible

### 2. Application Testing
- [ ] Homepage loads correctly
- [ ] User registration works
- [ ] User login works
- [ ] File upload functionality works
- [ ] Email notifications work
- [ ] API endpoints respond correctly

### 3. Performance Verification
- [ ] Page load times acceptable (<3 seconds)
- [ ] API response times acceptable (<1 second)
- [ ] No memory leaks detected
- [ ] CPU usage within normal range

## 📊 Monitoring Setup

### 1. Backup Verification
- [ ] Backup script created and executable
- [ ] Backup cron job scheduled
- [ ] Test backup restoration process
- [ ] Backup storage location verified

### 2. Log Management
- [ ] Application logs being written
- [ ] Nginx logs being written
- [ ] Log rotation configured
- [ ] Log monitoring alerts configured (if applicable)

### 3. System Monitoring
- [ ] System monitoring script running
- [ ] Disk space monitoring active
- [ ] Memory usage monitoring active
- [ ] Service uptime monitoring active

## 🔄 Final Steps

### 1. Documentation
- [ ] Update deployment documentation with any changes
- [ ] Document any custom configurations
- [ ] Update runbook with server-specific details
- [ ] Share access credentials securely with team

### 2. Team Notification
- [ ] Notify team of successful deployment
- [ ] Share application URLs
- [ ] Provide access to monitoring tools
- [ ] Schedule post-deployment review

### 3. Maintenance Schedule
- [ ] Schedule regular security updates
- [ ] Plan backup testing schedule
- [ ] Schedule SSL certificate renewal
- [ ] Plan application updates and maintenance windows

## 🚨 Rollback Plan

### If Deployment Fails
- [ ] Identify the failure point
- [ ] Check application logs
- [ ] Check service status
- [ ] If necessary, rollback to previous version:
  ```bash
  docker pull ghcr.io/tumbati/myngo-monorepo/backend:<previous-tag>
  docker pull ghcr.io/tumbati/myngo-monorepo/frontend:<previous-tag>
  docker-compose down && docker-compose up -d
  ```

### Emergency Contacts
- [ ] Development team contact information available
- [ ] Hetzner Cloud support information available
- [ ] Domain registrar support information available

## 📋 Checklist Summary

- [ ] Pre-deployment setup completed
- [ ] Deployment successful
- [ ] Security configured
- [ ] System verified
- [ ] Monitoring active
- [ ] Team notified
- [ ] Documentation updated

---

**Deployment Date:** _______________  
**Deployed By:** _______________  
**Server IP:** _______________  
**Version/Tag:** _______________