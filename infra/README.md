# NGORA Infrastructure & Deployment

This directory contains all infrastructure and deployment configurations for the NGORA (Non-Governmental Organizations Regulatory Authority) platform.

## 📁 Directory Structure

```
infra/
├── docker/                     # Docker configurations
│   ├── backend.Dockerfile      # Production backend Dockerfile
│   ├── frontend.Dockerfile     # Production frontend Dockerfile
│   ├── docker-compose.prod.yml # Production compose file
│   ├── docker-compose.staging.yml # Staging compose file
│   ├── nginx/                  # Nginx configuration
│   │   ├── nginx.conf          # Main nginx config
│   │   └── conf.d/default.conf # Virtual host config
│   ├── .env.production.template # Production env template
│   └── .env.staging.template   # Staging env template
├── scripts/                    # Deployment scripts
│   ├── deploy-hetzner.sh      # Hetzner Cloud deployment
│   └── setup-secrets.sh       # Secrets management
└── README.md                   # This file
```

## 🚀 Quick Start

### 1. Environment Setup

Generate environment configuration:

```bash
# Setup production environment
./infra/scripts/setup-secrets.sh production

# Setup staging environment
./infra/scripts/setup-secrets.sh staging
```

### 2. Manual Configuration

Edit the generated environment files and update:
- Mailjet API credentials
- Domain names and URLs
- SSL certificate paths
- Any other service-specific configurations

### 3. Deploy to Hetzner Cloud

```bash
# Deploy to production
./infra/scripts/deploy-hetzner.sh production <server-ip>

# Deploy to staging
./infra/scripts/deploy-hetzner.sh staging <server-ip>
```

## 🐳 Docker Configuration

### Services

The application stack includes:

- **PostgreSQL 15**: Primary database
- **Redis 7**: Caching and session storage
- **MinIO**: Object storage (S3-compatible)
- **FastAPI Backend**: Python/FastAPI application
- **Next.js Frontend**: React/Next.js application
- **Nginx**: Reverse proxy and load balancer

### Environment Files

Environment files contain sensitive configuration. Copy templates and fill in actual values:

```bash
cp infra/docker/.env.production.template infra/docker/.env.production
cp infra/docker/.env.staging.template infra/docker/.env.staging
```

## 🔄 CI/CD Pipeline

### GitHub Actions Workflows

1. **ci-cd.yml**: Main CI/CD pipeline
   - Runs tests for backend and frontend
   - Builds and pushes Docker images
   - Deploys to staging/production

2. **security.yml**: Security scanning
   - Dependency vulnerability scanning
   - Container image scanning
   - Secret scanning
   - SAST analysis

### Required GitHub Secrets

For staging deployment:
```
STAGING_SSH_PRIVATE_KEY
STAGING_USER
STAGING_HOST
```

For production deployment:
```
PRODUCTION_SSH_PRIVATE_KEY
PRODUCTION_USER
PRODUCTION_HOST
```

Additional secrets (auto-generated or manual):
```
POSTGRES_PASSWORD
REDIS_PASSWORD
MINIO_ROOT_PASSWORD
SECRET_KEY
MAILJET_API_KEY
MAILJET_API_SECRET
```

## 🖥️ Hetzner Cloud Deployment

### Server Requirements

**Minimum Requirements:**
- 2 vCPUs
- 4 GB RAM
- 40 GB SSD
- Ubuntu 20.04 or 22.04

**Recommended for Production:**
- 4 vCPUs
- 8 GB RAM
- 80 GB SSD
- Ubuntu 22.04 LTS

### Deployment Process

The deployment script automates:

1. **System Setup**
   - Docker and Docker Compose installation
   - Firewall configuration (UFW)
   - Fail2ban setup

2. **Application Deployment**
   - File transfer and configuration
   - SSL certificate setup
   - Service orchestration

3. **Security & Monitoring**
   - Automated backups
   - System monitoring
   - Log rotation

### Manual Steps After Deployment

1. **DNS Configuration**
   ```bash
   # Point your domain to the server IP
   # Example DNS records:
   A    ngora.mw           -> <server-ip>
   A    www.ngora.mw      -> <server-ip>
   A    staging.ngora.mw  -> <staging-server-ip>
   ```

2. **SSL Certificates (Let's Encrypt)**
   ```bash
   # SSH into the server
   ssh root@<server-ip>
   
   # Stop nginx temporarily
   docker-compose down nginx
   
   # Get Let's Encrypt certificates
   certbot certonly --standalone -d ngora.mw -d www.ngora.mw
   
   # Copy certificates to the correct location
   cp /etc/letsencrypt/live/ngora.mw/fullchain.pem /opt/ngora-production/docker/ssl/cert.pem
   cp /etc/letsencrypt/live/ngora.mw/privkey.pem /opt/ngora-production/docker/ssl/key.pem
   
   # Restart nginx
   docker-compose up -d nginx
   ```

3. **Database Initialization**
   ```bash
   # SSH into the server
   ssh root@<server-ip>
   cd /opt/ngora-production
   
   # Initialize database
   docker-compose exec backend python -m src.cli db init
   ```

## 🔧 Local Development

For local development with production-like environment:

```bash
# Copy production docker-compose
cp infra/docker/docker-compose.prod.yml docker-compose.override.yml

# Modify for local development (remove image references, add build contexts)
# Then run:
docker-compose up --build
```

## 📊 Monitoring & Maintenance

### Health Checks

All services include health checks:
- Database connectivity
- Redis availability
- MinIO storage access
- Application health endpoints

### Backup Strategy

Automated daily backups include:
- PostgreSQL database dump
- MinIO file storage
- Configuration files

Backup retention: 30 days (production), 7 days (staging)

### Log Management

Logs are stored in:
- `/var/log/nginx/` - Nginx access and error logs
- `/var/log/ngora/` - Application logs
- Docker logs via `docker-compose logs`

Log rotation is configured automatically.

### Monitoring Script

System monitoring runs every 5 minutes:
```bash
# View monitoring logs
tail -f /var/log/ngora/system-monitor.log

# Manual system check
/usr/local/bin/system-monitor.sh
```

## 🔒 Security

### Firewall Rules

- SSH (port 22): Allowed
- HTTP (port 80): Allowed (redirects to HTTPS)
- HTTPS (port 443): Allowed
- All other ports: Denied

### Rate Limiting

Nginx implements rate limiting:
- API endpoints: 10 requests/second
- Authentication: 5 requests/minute

### SSL/TLS

- TLS 1.2 and 1.3 only
- Strong cipher suites
- HSTS headers
- Secure headers (XSS protection, etc.)

## 🚨 Troubleshooting

### Common Issues

1. **Services not starting**
   ```bash
   # Check service status
   docker-compose ps
   
   # Check logs
   docker-compose logs <service-name>
   
   # Restart services
   docker-compose restart
   ```

2. **Database connection errors**
   ```bash
   # Check PostgreSQL logs
   docker-compose logs postgres
   
   # Test database connection
   docker-compose exec postgres psql -U postgres -d ngora_prod
   ```

3. **SSL certificate issues**
   ```bash
   # Check certificate validity
   openssl x509 -in /opt/ngora-production/docker/ssl/cert.pem -text -noout
   
   # Renew Let's Encrypt certificates
   certbot renew
   ```

### Performance Tuning

1. **Database Optimization**
   ```sql
   -- Monitor slow queries
   SELECT query, mean_time, calls 
   FROM pg_stat_statements 
   ORDER BY mean_time DESC LIMIT 10;
   ```

2. **Application Scaling**
   ```yaml
   # Increase backend workers in docker-compose
   command: ["uvicorn", "main:app", "--workers", "8"]
   ```

3. **Nginx Optimization**
   ```nginx
   # Enable gzip compression
   gzip on;
   gzip_comp_level 6;
   
   # Enable caching
   location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
       expires 1y;
       add_header Cache-Control "public, immutable";
   }
   ```

## 📞 Support

For deployment issues:
1. Check this documentation
2. Review GitHub Actions logs
3. Check server logs
4. Contact the development team

## 🔄 Updates & Releases

### Automated Deployments

- **Staging**: Automatically deploys on push to `development` branch
- **Production**: Automatically deploys on push to `main` branch

### Manual Deployments

```bash
# Manual deployment to specific server
./infra/scripts/deploy-hetzner.sh production <server-ip> <ssh-user> <ssh-key-path>
```

### Rollback Procedure

```bash
# SSH into server
ssh root@<server-ip>
cd /opt/ngora-production

# Pull previous image version
docker pull ghcr.io/tumbati/myngo-monorepo/backend:<previous-tag>
docker pull ghcr.io/tumbati/myngo-monorepo/frontend:<previous-tag>

# Update docker-compose to use previous tags and restart
docker-compose down
docker-compose up -d
```