services:
  db:
    container_name: ngora_db
    image: postgres:17
    env_file:
      - .env
    environment:
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_DB: ${POSTGRES_DB}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5455:5432"
    networks:
      - ngora_pipeline
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER} -d ${POSTGRES_DB}"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s

  redis:
    container_name: ngora_redis
    image: redis:8-alpine
    env_file:
      - .env
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - ngora_pipeline
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 10s
    command: redis-server --appendonly yes

  minio:
    container_name: ngora_minio
    image: minio/minio:latest
    env_file:
      - .env
    command: server /data --console-address ":9001"
    environment:
      - MINIO_BROWSER_REDIRECT_URL=http://localhost:9021
    ports:
      - "9020:9000"
      - "9021:9001"
    volumes:
      - minio_data:/data
    networks:
      - ngora_pipeline
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 20s

  api:
    container_name: ngora_api
    build:
      context: .
      dockerfile: Dockerfile.Development
    env_file:
      - .env
    restart: unless-stopped
    command: "python -m debugpy --listen 0.0.0.0:5678 -m uvicorn main:app --host 0.0.0.0 --port 8000 --reload --reload-exclude *.log"
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
      minio:
        condition: service_healthy
    ports:
      - "8000:8000"
      - "5678:5678" # for debugging
    volumes:
      - .:/app
      - /app/.venv
    networks:
      - ngora_pipeline

volumes:
  postgres_data:
  redis_data:
  minio_data:

networks:
  ngora_pipeline:
    driver: bridge
