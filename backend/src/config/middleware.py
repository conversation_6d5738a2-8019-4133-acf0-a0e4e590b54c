from typing import Callable
from fastapi import Request
from src.core.shared_schema import BaseResponse, CurrentUser
from src.core.services.audit_service import set_request_context
from src.modules.auth.auth_controller import get_current_user
from src.core.context.auth_context import current_user_context
from starlette.middleware.base import BaseHTTPMiddleware


class CurrentUserMiddleware(BaseHTTPMiddleware):
	async def dispatch(self, request: Request, call_next: Callable):
		try:
			user: BaseResponse[CurrentUser] = await get_current_user(request)
			current_user_context.set(user.data)
		except Exception:
			current_user_context.set(None)

		response = await call_next(request)
		return response


class AuditMiddleware(BaseHTTPMiddleware):
	async def dispatch(self, request: Request, call_next):
		client_ip = request.client.host
		endpoint = request.url.path

		# Set context for SQLAlchemy events
		set_request_context(ip_address=client_ip, endpoint=endpoint)

		response = await call_next(request)
		return response
