<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Activity Invitation - {{ app_name }}</title>
    <style>
        :root {
            --color-primary: hsl(22, 100%, 52%);
            --color-secondary: hsl(262, 84%, 58%);
            --color-text: #0f172a;
            --color-text-light: #64748b;
            --color-bg: #f8fafc;
            --gradient-primary: linear-gradient(135deg, hsl(22, 100%, 52%) 0%, hsl(262, 84%, 58%) 100%);
        }

        body {
            font-family: 'Inter UI', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: var(--color-text);
            background-color: var(--color-bg);
            margin: 0;
            padding: 0;
        }

        .container {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: var(--gradient-primary);
            color: white;
            padding: 30px 20px;
            text-align: center;
        }

        .header h1 {
            margin: 0 0 12px 0;
            font-size: 28px;
            font-weight: 700;
        }

        .content {
            padding: 40px 30px;
        }

        .content p {
            margin: 0 0 16px 0;
        }

        .invitation-card {
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            border-radius: 12px;
            padding: 24px;
            margin: 24px 0;
        }

        .invitation-card h2 {
            margin: 0 0 12px 0;
            font-size: 20px;
            font-weight: 600;
        }

        .invitation-card p {
            margin: 0 0 16px 0;
        }

        .invitation-card .cta-button {
            display: inline-block;
            background: var(--gradient-primary);
            color: white;
            text-decoration: none;
            padding: 16px 32px;
            border-radius: 12px;
            font-weight: 600;
            text-align: center;
            margin: 24px 0;
            box-shadow: 0 8px 25px rgba(251, 146, 60, 0.3);
            transition: all 0.3s ease;
        }

        .invitation-card .cta-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 35px rgba(251, 146, 60, 0.4);
        }

        .footer {
            background: var(--color-bg);
            padding: 24px 30px;
            text-align: center;
            font-size: 14px;
            color: var(--color-text-light);
        }

        .footer a {
            color: var(--color-primary);
            text-decoration: none;
        }

        .footer a:hover {
            text-decoration: underline;
        }

        @media (max-width: 600px) {
            .container {
                padding: 20px;
            }

            .header h1 {
                font-size: 24px;
            }

            .content {
                padding: 30px 20px;
            }

            .invitation-card {
                padding: 20px;
            }
        }

        @import url('https://fonts.googleapis.com/css2?family=Inter+UI:wght@300;400;500;600;700&display=swap');
    </style>
</head>

<body>
    <div class="container">
        <div class="header">
            <h1>Activity Invitation</h1>
            <p>Join us for an upcoming activity</p>
        </div>
        <div class="content">
            <p>Hello,</p>
            <p>You have been invited to join the {{ activity_title }} on {{ activity_date }}.</p>
            <div class="invitation-card">
                <table class="details-table">
                    <tr>
                        <td>Activity:</td>
                        <td>{{ activity_title }}</td>
                    </tr>
                    <tr>
                        <td>Organizer:</td>
                        <td>{{ organizer_name }}</td>
                    </tr>
                    <tr>
                        <td>Date:</td>
                        <td>{{ activity_date }}</td>
                    </tr>
                    <tr>
                        <td>Time:</td>
                        <td>{{ activity_start_time }} - {{ activity_end_time }}</td>
                    </tr>
                    <tr>
                        <td>Venue:</td>
                        <td>{{ activity_venue }}</td>
                    </tr>
                    <tr>
                        <td>Summary:</td>
                        <td>{{ activity_summary }}</td>
                    </tr>
                    <tr>
                        <td>Link:</td>
                        <td><a href="{{ activity_link }}" class="cta-button" style="color: black;">View Activity</a>
                        </td>
                    </tr>
                </table>
            </div>
            <p>If the button above doesn't work, you can copy and paste this link into your browser:</p>
            <p style="word-break: break-all; color: #4f46e5; font-size: 14px;">{{ invitation_link }}</p>
            <p>Thank you,</p>
            <p>The {{ app_name }} Team</p>
            <p style="color: #64748b; font-size: 14px;">
                Need help? Contact our support team or visit our help center.
            </p>
        </div>
        <div class="footer">
            <p>© {{ year }} {{ app_name }}. All rights reserved.</p>
            <p>
                <a href="{{ app_url }}">Visit {{ app_name }}</a> |
                <a href="{{ support_url }}">Support</a>
            </p>
        </div>
    </div>
</body>

</html>