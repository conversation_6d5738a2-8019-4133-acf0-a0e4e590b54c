<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Two-Factor Authentication Code - NGORA</title>
    <style>
      @import url("https://fonts.googleapis.com/css2?family=Inter+UI:wght@400;500;600;700&display=swap");

      :root {
        --color-primary: hsl(22, 100%, 52%);
        --color-secondary: hsl(262, 84%, 58%);
        --color-text: #0f172a;
        --color-text-light: #64748b;
        --color-bg: #f8fafc;
        --color-success: #22c55e;
        --gradient-primary: linear-gradient(135deg, hsl(22, 100%, 52%) 0%, hsl(262, 84%, 58%) 100%);
        --shadow-lg: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
      }

      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: "Inter UI", "Inter", Arial, sans-serif;
        line-height: 1.6;
        color: var(--color-text);
        background: var(--color-bg);
        padding: 20px;
        margin: 0;
      }

      .email-container {
        max-width: 600px;
        margin: 0 auto;
        background: #ffffff;
        border-radius: 16px;
        box-shadow: var(--shadow-lg);
        overflow: hidden;
      }

      .header {
        background: var(--gradient-primary);
        padding: 40px 30px;
        text-align: center;
        position: relative;
        overflow: hidden;
        color: white;
      }

      .header::before {
        content: "";
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 1px, transparent 1px);
        background-size: 30px 30px;
        animation: float 25s infinite linear;
      }

      @keyframes float {
        0% { transform: translate(0, 0) rotate(0deg); }
        100% { transform: translate(-30px, -30px) rotate(360deg); }
      }

      .header-content {
        position: relative;
        z-index: 2;
      }

      .header-icon {
        width: 64px;
        height: 64px;
        margin: 0 auto 20px;
        background: rgba(255, 255, 255, 0.15);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        backdrop-filter: blur(10px);
      }

      .header h1 {
        font-size: 24px;
        font-weight: 700;
        margin: 0 0 8px;
      }

      .header p {
        font-size: 16px;
        opacity: 0.9;
        margin: 0;
      }

      .content {
        padding: 40px 30px;
      }

      .security-message {
        background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
        border-radius: 12px;
        padding: 24px;
        margin: 24px 0;
        text-align: center;
        border: 1px solid #0ea5e9;
      }

      .code-container {
        background: var(--gradient-primary);
        border-radius: 16px;
        padding: 32px;
        text-align: center;
        margin: 32px 0;
        box-shadow: 0 8px 25px rgba(251, 146, 60, 0.2);
      }

      .verification-code {
        font-size: 36px;
        font-weight: 700;
        letter-spacing: 8px;
        color: white;
        font-family: 'Courier New', monospace;
        margin: 12px 0;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }

      .code-label {
        color: rgba(255, 255, 255, 0.9);
        font-size: 14px;
        text-transform: uppercase;
        letter-spacing: 1px;
        margin-bottom: 8px;
      }

      .code-description {
        color: rgba(255, 255, 255, 0.8);
        font-size: 14px;
        margin-top: 12px;
      }

      .important-note {
        background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
        border: 1px solid #fca5a5;
        border-radius: 12px;
        padding: 20px;
        margin: 24px 0;
        display: flex;
        align-items: flex-start;
        gap: 12px;
      }

      .note-icon {
        width: 20px;
        height: 20px;
        flex-shrink: 0;
        margin-top: 2px;
      }

      .note-content {
        flex: 1;
      }

      .note-title {
        font-weight: 600;
        color: #dc2626;
        margin-bottom: 4px;
      }

      .note-text {
        color: #991b1b;
        font-size: 14px;
        line-height: 1.5;
      }

      .instructions {
        background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
        border-radius: 12px;
        padding: 24px;
        margin: 24px 0;
      }

      .instructions h3 {
        color: var(--color-text);
        font-size: 18px;
        font-weight: 600;
        margin-bottom: 16px;
      }

      .instruction-list {
        list-style: none;
        padding: 0;
      }

      .instruction-list li {
        display: flex;
        align-items: flex-start;
        margin-bottom: 12px;
        padding: 12px;
        background: white;
        border-radius: 8px;
        border-left: 3px solid var(--color-primary);
      }

      .step-number {
        background: var(--color-primary);
        color: white;
        width: 24px;
        height: 24px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        font-weight: 600;
        margin-right: 12px;
        flex-shrink: 0;
      }

      .step-text {
        color: var(--color-text);
        font-size: 14px;
        line-height: 1.5;
      }

      .footer {
        background: var(--color-bg);
        padding: 24px 30px;
        text-align: center;
        font-size: 14px;
        color: var(--color-text-light);
        border-top: 1px solid #e2e8f0;
      }

      .footer p {
        margin: 0 0 8px 0;
      }

      .footer a {
        color: var(--color-primary);
        text-decoration: none;
        margin: 0 8px;
      }

      .footer a:hover {
        text-decoration: underline;
      }

      @media screen and (max-width: 600px) {
        .content {
          padding: 24px 20px;
        }

        .header {
          padding: 32px 20px;
        }

        .footer {
          padding: 20px;
        }

        .verification-code {
          font-size: 28px;
          letter-spacing: 6px;
        }

        .code-container {
          padding: 24px;
        }
      }
    </style>
  </head>
  <body>
    <div class="email-container">
      <div class="header">
        <div class="header-content">
          <div class="header-icon">
            <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <rect width="18" height="11" x="3" y="11" rx="2" ry="2"/>
              <path d="M7 11V7C7 5.89543 7.89543 5 9 5H15C16.1046 5 17 5.89543 17 7V11"/>
            </svg>
          </div>
          <h1>Security Verification Code</h1>
          <p>Your two-factor authentication code</p>
        </div>
      </div>

      <div class="content">
        <p>Hello {{ user_name }},</p>

        <div class="security-message">
          <p><strong>A login attempt requires verification</strong></p>
          <p>Someone is trying to sign in to your NGORA account. If this was you, please use the verification code below to complete your login.</p>
        </div>

        <div class="code-container">
          <div class="code-label">Your Verification Code</div>
          <div class="verification-code">{{ code }}</div>
          <div class="code-description">This code expires in 10 minutes</div>
        </div>

        <div class="instructions">
          <h3>How to use this code:</h3>
          <ul class="instruction-list">
            <li>
              <div class="step-number">1</div>
              <div class="step-text">Return to the NGORA login page where you were prompted for the code</div>
            </li>
            <li>
              <div class="step-number">2</div>
              <div class="step-text">Enter the 6-digit verification code exactly as shown above</div>
            </li>
            <li>
              <div class="step-number">3</div>
              <div class="step-text">Click "Verify" to complete your secure login</div>
            </li>
          </ul>
        </div>

        <div class="important-note">
          <svg class="note-icon" viewBox="0 0 24 24" fill="none" stroke="#dc2626" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <circle cx="12" cy="12" r="10"/>
            <line x1="12" y1="8" x2="12" y2="12"/>
            <line x1="12" y1="16" x2="12.01" y2="16"/>
          </svg>
          <div class="note-content">
            <div class="note-title">Important Security Notice</div>
            <div class="note-text">
              If you didn't request this code, someone may be trying to access your account. Please change your password immediately and contact our support team. Never share this code with anyone.
            </div>
          </div>
        </div>

        <p style="color: var(--color-text-light); font-size: 14px; text-align: center; margin-top: 32px;">
          This is an automated security message from NGORA. For support, contact us at <a href="mailto:<EMAIL>" style="color: var(--color-primary);"><EMAIL></a>
        </p>
      </div>

      <div class="footer">
        <p><strong>NGO Regulatory Authority (NGORA)</strong></p>
        <p>Government of Malawi | Ministry of Gender, Community Development and Social Welfare</p>
        <p>
          <a href="https://ngora.mw">Visit Website</a> |
          <a href="https://ngora.mw/contact">Contact Support</a> |
          <a href="https://ngora.mw/security">Security Center</a>
        </p>
        <p style="margin-top: 12px; font-size: 12px;">
          &copy; {{ year }} NGO Regulatory Authority. All rights reserved.
        </p>
      </div>
    </div>
  </body>
</html>