<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Organization Invitation - {{ app_name }}</title>
    <style>
        :root {
            --color-primary: hsl(22, 100%, 52%);
            --color-secondary: hsl(262, 84%, 58%);
            --color-text: #0f172a;
            --color-text-light: #64748b;
            --color-bg: #f8fafc;
            --gradient-primary: linear-gradient(135deg, hsl(22, 100%, 52%) 0%, hsl(262, 84%, 58%) 100%);
        }
        
        body { 
            font-family: 'Inter UI', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
            line-height: 1.6; 
            color: var(--color-text); 
            background-color: var(--color-bg);
            margin: 0;
            padding: 0;
        }
        .container { 
            max-width: 600px; 
            margin: 40px auto; 
            background: white;
            border-radius: 16px;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            overflow: hidden;
        }
        .header {
            background: var(--gradient-primary);
            color: white;
            padding: 40px 30px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }
        .header::before {
            content: "";
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 1px, transparent 1px);
            background-size: 30px 30px;
            animation: float 25s infinite linear;
        }
        @keyframes float {
            0% { transform: translate(0, 0) rotate(0deg); }
            100% { transform: translate(-30px, -30px) rotate(360deg); }
        }
        .header-content {
            position: relative;
            z-index: 2;
        }
        .header h1 {
            margin: 0 0 12px 0;
            font-size: 28px;
            font-weight: 700;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 12px;
        }
        .header-icon {
            width: 32px;
            height: 32px;
        }
        .content { 
            padding: 40px 30px;
        }
        .invitation-card {
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            border-radius: 12px;
            padding: 24px;
            margin: 24px 0;
            border-left: 4px solid var(--color-primary);
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        }
        .org-name {
            font-size: 20px;
            font-weight: 600;
            color: var(--color-text);
            margin-bottom: 8px;
        }
        .role-badge {
            display: inline-block;
            background: linear-gradient(135deg, rgba(251, 146, 60, 0.1) 0%, rgba(147, 51, 234, 0.1) 100%);
            color: var(--color-primary);
            padding: 6px 16px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            border: 1px solid rgba(251, 146, 60, 0.2);
        }
        .cta-button {
            display: inline-block;
            background: var(--gradient-primary);
            color: white;
            text-decoration: none;
            padding: 16px 32px;
            border-radius: 12px;
            font-weight: 600;
            text-align: center;
            margin: 24px 0;
            box-shadow: 0 8px 25px rgba(251, 146, 60, 0.3);
            transition: all 0.3s ease;
        }
        .cta-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 35px rgba(251, 146, 60, 0.4);
        }
        .invitation-details {
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
            border: 1px solid #f59e0b;
            border-radius: 12px;
            padding: 20px;
            margin: 24px 0;
        }
        .detail-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
        }
        .detail-row:last-child {
            margin-bottom: 0;
        }
        .detail-label {
            font-weight: 600;
            color: #92400e;
        }
        .detail-value {
            color: #451a03;
        }
        .footer { 
            background: var(--color-bg); 
            padding: 24px 30px; 
            text-align: center; 
            font-size: 14px; 
            color: var(--color-text-light);
        }
        .footer a {
            color: var(--color-primary);
            text-decoration: none;
        }
        .security-note {
            background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
            border: 1px solid #fca5a5;
            border-radius: 12px;
            padding: 20px;
            margin: 24px 0;
            font-size: 14px;
            display: flex;
            align-items: flex-start;
            gap: 12px;
        }
        .security-note strong {
            color: #dc2626;
        }
        .security-icon {
            width: 20px;
            height: 20px;
            flex-shrink: 0;
            margin-top: 2px;
        }
        @media (max-width: 600px) {
            .container {
                margin: 20px;
                border-radius: 8px;
            }
            .header, .content {
                padding: 24px 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="header-content">
                <h1>
                    <svg class="header-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M8 2V5"/>
                        <path d="M16 2V5"/>
                        <rect width="18" height="18" x="3" y="4" rx="2"/>
                        <path d="M3 10H21"/>
                        <path d="M8 14H10"/>
                        <path d="M14 14H16"/>
                    </svg>
                    You're Invited!
                </h1>
                <p style="margin: 16px 0 0 0; opacity: 0.9;">Join us on {{ app_name }}</p>
            </div>
        </div>
        
        <div class="content">
            <p>Hello,</p>
            
            <p><strong>{{ inviter_name }}</strong> has invited you to join their organization on {{ app_name }}.</p>
            
            <div class="invitation-card">
                <div class="org-name">{{ organization_name }}</div>
                <div class="role-badge">{{ role }}</div>
                {% if organization_description %}
                <p style="margin: 16px 0 0 0; color: #64748b; font-size: 14px;">{{ organization_description }}</p>
                {% endif %}
            </div>
            
            <div class="invitation-details">
                <div class="detail-row">
                    <span class="detail-label">Invited by:</span>
                    <span class="detail-value">{{ inviter_name }} ({{ inviter_email }})</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Role:</span>
                    <span class="detail-value">{{ role }}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Expires:</span>
                    <span class="detail-value">{{ expires_at }}</span>
                </div>
            </div>
            
            <div style="text-align: center;">
                <a href="{{ invitation_link }}" class="cta-button">
                    Accept Invitation
                </a>
            </div>
            
            <div class="security-note">
                <svg class="security-icon" viewBox="0 0 24 24" fill="none" stroke="#dc2626" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <rect width="18" height="11" x="3" y="11" rx="2" ry="2"/>
                    <path d="M7 11V7C7 5.89543 7.89543 5 9 5H15C16.1046 5 17 5.89543 17 7V11"/>
                </svg>
                <div>
                    <strong>Security Notice:</strong> This invitation link contains a secure token and will expire on {{ expires_at }}. Do not share this link with others. If you didn't expect this invitation, you can safely ignore this email.
                </div>
            </div>
            
            <p>If the button above doesn't work, you can copy and paste this link into your browser:</p>
            <p style="word-break: break-all; color: #4f46e5; font-size: 14px;">{{ invitation_link }}</p>
            
            <hr style="border: none; border-top: 1px solid #e2e8f0; margin: 32px 0;">
            
            <p style="color: #64748b; font-size: 14px;">
                Need help? Contact our support team or visit our help center.
            </p>
        </div>
        
        <div class="footer">
            <p>© {{ year }} {{ app_name }}. All rights reserved.</p>
            <p>
                <a href="{{ app_url }}">Visit {{ app_name }}</a> | 
                <a href="{{ support_url }}">Support</a>
            </p>
        </div>
    </div>
</body>
</html>