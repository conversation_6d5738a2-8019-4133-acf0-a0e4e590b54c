<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Account Setup - {{ app_name }}</title>
    <style>
      body {
        font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
        background-color: #f4f4f4;
        margin: 0;
        padding: 0;
      }
      .container {
        max-width: 600px;
        margin: 40px auto;
        background-color: #ffffff;
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }
      .header {
        background-color: #007bff;
        color: white;
        padding: 30px 20px;
        text-align: center;
      }
      .header h1 {
        margin: 0;
        font-size: 24px;
      }
      .content {
        padding: 30px 20px;
        color: #333;
      }
      .info-box {
        background-color: #f1f9fe;
        border-left: 4px solid #007bff;
        padding: 15px;
        margin: 20px 0;
      }
      .code-box {
        font-size: 32px;
        font-weight: bold;
        text-align: center;
        letter-spacing: 8px;
        background-color: #f1f1f1;
        padding: 20px;
        border-radius: 8px;
        margin: 20px 0;
      }
      .button {
        display: block;
        width: fit-content;
        margin: 0 auto 20px;
        background-color: #28a745;
        color: white;
        padding: 12px 24px;
        text-decoration: none;
        border-radius: 5px;
        font-weight: bold;
      }
      .fallback-link {
        font-size: 14px;
        color: #007bff;
        word-break: break-all;
        text-align: center;
      }
      .footer {
        text-align: center;
        font-size: 12px;
        color: #888;
        padding: 20px;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="header">
        <h1>Your {{ app_name }} Account</h1>
      </div>
      <div class="content">
        <h2>Hello {{ user_name }},</h2>
        <p>
          Your internal account has been created. Please find your account details below:
        </p>

        <div class="info-box">
          <p><strong>Department:</strong> {{ department }}</p>
          <p><strong>Role:</strong> {{ role }}</p>
        </div>

        <p>To complete your account setup, please click the button below to verify your account:</p>

        <a href="{{ verification_url }}" class="button">Verify My Account</a>

        <p>
          If the button doesn't work, please copy and paste this link into your browser:
        </p>
        <div class="fallback-link">{{ verification_url }}</div>

        <p>
          <strong>Important:</strong> This verification link will expire in 24 hours.
        </p>
        <p>If you believe this account was created in error, please contact IT Support immediately.</p>
        <p>Welcome to the team,<br />The {{ department }} Leadership Team</p>
      </div>
      <div class="footer">
        &copy; {{ year }} {{ app_name }}. For internal use only.
      </div>
    </div>
  </body>
</html>
