<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title>Organization Application Review</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <style>
      @import url("https://fonts.googleapis.com/css2?family=Inter+UI:wght@400;500;600;700&display=swap");

      :root {
        --color-primary: hsl(22, 100%, 52%);
        --color-secondary: hsl(262, 84%, 58%);
        --color-success: #22c55e;
        --color-danger: #ef4444;
        --color-text: #0f172a;
        --color-text-light: #64748b;
        --color-bg: #f8fafc;
        --gradient-primary: linear-gradient(135deg, hsl(22, 100%, 52%) 0%, hsl(262, 84%, 58%) 100%);
        --shadow-lg: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
      }

      body {
        font-family: "Inter UI", "Inter", sans-serif;
        background-color: var(--color-bg);
        margin: 0;
        padding: 20px;
        color: var(--color-text);
      }

      .email-container {
        max-width: 600px;
        margin: 0 auto;
        background: #ffffff;
        border-radius: 16px;
        box-shadow: var(--shadow-lg);
        overflow: hidden;
      }

      .header {
        background: var(--gradient-primary);
        padding: 40px 30px;
        text-align: center;
        color: #ffffff;
        position: relative;
        overflow: hidden;
      }

      .header::before {
        content: "";
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 1px, transparent 1px);
        background-size: 30px 30px;
        animation: float 25s infinite linear;
      }

      @keyframes float {
        0% { transform: translate(0, 0) rotate(0deg); }
        100% { transform: translate(-30px, -30px) rotate(360deg); }
      }

      .header-content {
        position: relative;
        z-index: 2;
      }

      .header .icon {
        width: 48px;
        height: 48px;
        margin: 0 auto 16px;
      }

      .header h1 {
        font-size: 24px;
        margin: 0 0 12px;
        font-weight: 700;
      }

      .header p {
        margin: 0;
        font-size: 16px;
        opacity: 0.9;
      }

      .content {
        padding: 40px 30px;
      }

      .content h2 {
        font-size: 20px;
        margin-bottom: 20px;
        color: var(--color-text);
        font-weight: 600;
      }

      .application-details {
        background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
        border-radius: 12px;
        padding: 24px;
        margin: 24px 0;
        border-left: 4px solid var(--color-primary);
      }

      .detail-row {
        margin-bottom: 16px;
        display: flex;
        justify-content: space-between;
        align-items: start;
      }

      .detail-row:last-child {
        margin-bottom: 0;
      }

      .detail-label {
        font-weight: 600;
        color: var(--color-text);
        min-width: 140px;
      }

      .detail-value {
        color: var(--color-text-light);
        text-align: right;
        flex: 1;
      }

      .actions {
        margin: 30px 0;
        text-align: center;
      }

      .btn {
        display: inline-block;
        padding: 16px 32px;
        font-size: 16px;
        font-weight: 600;
        text-decoration: none;
        border-radius: 12px;
        margin: 0 12px 12px;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
      }

      .btn-approve {
        background: linear-gradient(135deg, var(--color-success), #16a34a);
        color: white;
        border: none;
      }

      .btn-approve:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(34, 197, 94, 0.3);
      }

      .btn-reject {
        background: linear-gradient(135deg, var(--color-danger), #dc2626);
        color: white;
        border: none;
      }

      .btn-reject:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(239, 68, 68, 0.3);
      }

      .footer {
        background: var(--color-bg);
        padding: 24px;
        text-align: center;
        font-size: 14px;
        color: var(--color-text-light);
        border-top: 1px solid #e2e8f0;
      }

      .footer a {
        color: var(--color-primary);
        text-decoration: none;
        margin: 0 12px;
        font-weight: 500;
      }

      .footer a:hover {
        text-decoration: underline;
      }

      @media (max-width: 600px) {
        .btn {
          width: 100%;
          margin-bottom: 12px;
        }
      }
    </style>
  </head>
  <body>
    <div class="email-container">
      <div class="header">
        <div class="header-content">
          <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M9 11L12 14L22 4"/>
            <path d="M21 12V19C21 20.1046 20.1046 21 19 21H5C3.89543 21 3 20.1046 3 19V5C3 3.89543 3.89543 3 5 3H16"/>
          </svg>
          <h1>Organization Application Review Required</h1>
          <p>Please review and take action on the pending application.</p>
        </div>
      </div>

      <div class="content">
        <p>
          A new organization application has been submitted and is pending your review.
          Please review the details below and take the appropriate action.
        </p>

        <div class="application-details">
          <div class="detail-row">
            <strong class="detail-label">Organization Name:</strong>
            <div class="detail-value">{{ organization_name }}</div>
          </div>
          <div class="detail-row">
            <strong class="detail-label">Application Code:</strong>
            <div class="detail-value">{{ application_code }}</div>
          </div>
          <div class="detail-row">
            <strong class="detail-label">Submitted By:</strong>
            <div class="detail-value">{{ submitted_by }}</div>
          </div>
          <div class="detail-row">
            <strong class="detail-label">Submitted On:</strong>
            <div class="detail-value">{{ submitted_date }}</div>
          </div>
        </div>

        <div class="actions">
          <a href="{{ approve_url }}" target="_blank" class="btn btn-approve">Approve Application</a>
        </div>
      </div>

      <div class="footer">
        <p>&copy; {{year}} {{app_name}}. All rights reserved.</p>
        <a href="#">Terms</a> |
        <a href="#">Privacy</a> |
        <a href="#">Contact</a>
      </div>
    </div>
  </body>
</html>
