<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Welcome to NGORA - NGO Regulatory Authority</title>
    <style>
      :root {
        --color-primary: hsl(22, 100%, 52%); /* Orange from frontend */
        --color-secondary: hsl(262, 84%, 58%); /* Purple from frontend */
        --color-accent: #4a90b8;
        --color-success: #22c55e;
        --color-warning: #f59e0b;
        --color-danger: #ef4444;
        --color-text: #0f172a;
        --color-text-light: #64748b;
        --color-bg: #f8fafc;
        --color-bg-dark: #f1f5f9;
        --shadow-lg: 0 20px 25px -5px rgba(0, 0, 0, 0.1),
          0 10px 10px -5px rgba(0, 0, 0, 0.04);
        --shadow-xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        --gradient-primary: linear-gradient(135deg, hsl(22, 100%, 52%) 0%, hsl(262, 84%, 58%) 100%);
        --gradient-bg: linear-gradient(135deg, hsl(22, 100%, 52%) 0%, hsl(262, 84%, 58%) 100%);
      }

      * {
        box-sizing: border-box;
      }

      body {
        margin: 0;
        padding: 0;
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
          "Helvetica Neue", Arial, sans-serif;
        line-height: 1.6;
        background: var(--color-bg);
        color: var(--color-text);
      }

      .email-container {
        max-width: 680px;
        margin: 0 auto;
        background: white;
        box-shadow: var(--shadow-lg);
      }

      /* Header Section */
      .header {
        background: var(--gradient-primary);
        padding: 40px 32px;
        text-align: center;
        position: relative;
        overflow: hidden;
      }

      .header::before {
        content: "";
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(
          circle,
          rgba(255, 255, 255, 0.1) 1px,
          transparent 1px
        );
        background-size: 30px 30px;
        animation: float 25s infinite linear;
      }

      @keyframes float {
        0% {
          transform: translate(0, 0) rotate(0deg);
        }
        100% {
          transform: translate(-30px, -30px) rotate(360deg);
        }
      }

      .logo-section {
        position: relative;
        z-index: 2;
        margin-bottom: 24px;
      }

      .logo {
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 20px;
      }

      .logo-icon {
        width: 80px;
        height: 80px;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 20px;
        backdrop-filter: blur(10px);
        border: 2px solid rgba(255, 255, 255, 0.3);
      }

      .logo-text {
        color: white;
        font-size: 32px;
        font-weight: 800;
        letter-spacing: -1px;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
      }

      .tagline {
        color: rgba(255, 255, 255, 0.9);
        font-size: 16px;
        font-weight: 500;
        margin-bottom: 8px;
        position: relative;
        z-index: 2;
      }

      .gov-badge {
        background: rgba(255, 255, 255, 0.15);
        color: white;
        padding: 8px 20px;
        border-radius: 25px;
        font-size: 13px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 1px;
        border: 1px solid rgba(255, 255, 255, 0.25);
        display: inline-block;
        position: relative;
        z-index: 2;
      }

      /* Content Section */
      .content {
        padding: 48px 40px;
      }

      .welcome-title {
        font-size: 28px;
        font-weight: 700;
        color: var(--color-primary);
        margin-bottom: 16px;
        text-align: center;
        letter-spacing: -0.5px;
      }

      .welcome-subtitle {
        font-size: 18px;
        color: var(--color-text-light);
        text-align: center;
        margin-bottom: 40px;
        line-height: 1.5;
      }

      .user-greeting {
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        border-radius: 16px;
        padding: 24px;
        margin-bottom: 32px;
        border-left: 4px solid var(--color-secondary);
        text-align: center;
      }

      .user-greeting h3 {
        margin: 0 0 8px 0;
        color: var(--color-primary);
        font-size: 20px;
        font-weight: 600;
      }

      .user-greeting p {
        margin: 0;
        color: var(--color-text);
        font-size: 16px;
      }

      .welcome-message {
        font-size: 16px;
        line-height: 1.7;
        margin-bottom: 32px;
        color: var(--color-text);
      }

      .features-section {
        margin: 40px 0;
      }

      .features-title {
        font-size: 22px;
        font-weight: 600;
        color: var(--color-primary);
        margin-bottom: 24px;
        text-align: center;
      }

      .features-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 24px;
        margin-bottom: 40px;
      }

      .feature-card {
        background: white;
        border: 1px solid #e2e8f0;
        border-radius: 12px;
        padding: 24px;
        text-align: center;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
      }

      .feature-card::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: var(--gradient-primary);
        transform: scaleX(0);
        transition: transform 0.3s ease;
      }

      .feature-card:hover::before {
        transform: scaleX(1);
      }

      .feature-card:hover {
        transform: translateY(-4px);
        box-shadow: var(--shadow-lg);
      }

      .feature-icon {
        width: 60px;
        height: 60px;
        background: var(--gradient-primary);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 16px;
        font-size: 24px;
      }

      .feature-title {
        font-size: 18px;
        font-weight: 600;
        color: var(--color-primary);
        margin-bottom: 8px;
      }

      .feature-desc {
        font-size: 14px;
        color: var(--color-text-light);
        line-height: 1.5;
      }

      .next-steps {
        background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
        border-radius: 16px;
        padding: 32px;
        margin: 40px 0;
        border: 1px solid rgba(74, 144, 184, 0.2);
      }

      .next-steps h3 {
        color: var(--color-accent);
        font-size: 20px;
        font-weight: 600;
        margin-bottom: 20px;
        text-align: center;
      }

      .steps-list {
        list-style: none;
        padding: 0;
        margin: 0;
      }

      .steps-list li {
        display: flex;
        align-items: flex-start;
        margin-bottom: 16px;
        padding: 12px;
        background: white;
        border-radius: 8px;
        border-left: 3px solid var(--color-accent);
      }

      .step-number {
        background: var(--color-accent);
        color: white;
        width: 24px;
        height: 24px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        font-weight: 600;
        margin-right: 12px;
        flex-shrink: 0;
      }

      .step-text {
        color: var(--color-text);
        font-size: 14px;
        line-height: 1.5;
      }

      .cta-section {
        text-align: center;
        margin: 40px 0;
      }

      .cta-button {
        display: inline-flex;
        align-items: center;
        background: var(--gradient-primary);
        color: white;
        text-decoration: none;
        padding: 16px 32px;
        border-radius: 12px;
        font-weight: 600;
        font-size: 16px;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(96, 21, 166, 0.3);
        margin: 0 8px 16px;
      }

      .cta-button:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(96, 21, 166, 0.4);
      }

      .cta-button.secondary {
        background: white;
        color: var(--color-primary);
        border: 2px solid var(--color-primary);
        box-shadow: none;
      }

      .cta-button.secondary:hover {
        background: var(--color-primary);
        color: white;
      }

      .support-section {
        background: #f8f9fa;
        border-radius: 12px;
        padding: 24px;
        margin: 32px 0;
        text-align: center;
      }

      .support-title {
        color: var(--color-primary);
        font-size: 18px;
        font-weight: 600;
        margin-bottom: 16px;
      }

      .contact-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 16px;
        margin-top: 20px;
      }

      .contact-item {
        background: white;
        padding: 16px;
        border-radius: 8px;
        border: 1px solid #e2e8f0;
      }

      .contact-item strong {
        color: var(--color-primary);
        display: block;
        margin-bottom: 4px;
      }

      .contact-item span {
        color: var(--color-text-light);
        font-size: 14px;
      }

      /* Footer */
      .footer {
        background: #2d3748;
        color: #a0aec0;
        padding: 32px 40px;
        text-align: center;
      }

      .footer-content {
        margin-bottom: 20px;
      }

      .footer-content p {
        margin: 0 0 8px 0;
        font-size: 14px;
        line-height: 1.5;
      }

      .footer-links {
        margin: 16px 0;
      }

      .footer-links a {
        color: #4a90b8;
        text-decoration: none;
        margin: 0 12px;
        font-size: 14px;
      }

      .footer-links a:hover {
        color: #6bb6ff;
      }

      .footer-bottom {
        border-top: 1px solid #4a5568;
        padding-top: 16px;
        font-size: 12px;
        color: #718096;
      }

      /* Responsive Design */
      @media screen and (max-width: 640px) {
        .content {
          padding: 32px 24px;
        }

        .header {
          padding: 32px 24px;
        }

        .footer {
          padding: 24px 20px;
        }

        .logo {
          flex-direction: column;
          text-align: center;
        }

        .logo-icon {
          margin-right: 0;
          margin-bottom: 16px;
        }

        .logo-text {
          font-size: 28px;
        }

        .features-grid {
          grid-template-columns: 1fr;
        }

        .cta-button {
          display: block;
          margin: 8px 0;
        }

        .contact-grid {
          grid-template-columns: 1fr;
        }
      }

      /* Print Styles */
      @media print {
        .header::before {
          display: none;
        }

        .cta-button {
          border: 2px solid var(--color-primary) !important;
          color: var(--color-primary) !important;
          background: white !important;
        }
      }
    </style>
  </head>
  <body>
    <div class="email-container">
      <!-- Header -->
      <div class="header">
        <div class="logo-section">
          <div class="logo">
            <div class="logo-icon">
              <svg
                width="40"
                height="40"
                viewBox="0 0 40 40"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M20 4C12.268 4 6 10.268 6 18C6 25.732 12.268 32 20 32C27.732 32 34 25.732 34 18C34 10.268 27.732 4 20 4ZM20 6C26.627 6 32 11.373 32 18C32 24.627 26.627 30 20 30C13.373 30 8 24.627 8 18C8 11.373 13.373 6 20 6ZM20 10C16.686 10 14 12.686 14 16C14 19.314 16.686 22 20 22C23.314 22 26 19.314 26 16C26 12.686 23.314 10 20 10Z"
                  fill="white"
                />
              </svg>
            </div>
            <div class="logo-text">NGORA</div>
          </div>
          <div class="tagline">NGO Regulatory Authority</div>
          <div class="gov-badge">Government of Malawi</div>
        </div>
      </div>

      <!-- Main Content -->
      <div class="content">
        <h1 class="welcome-title">Welcome to NGORA</h1>
        <p class="welcome-subtitle">
          Your gateway to NGO registration and compliance in Malawi
        </p>

        <div class="user-greeting">
          <h3>Hello, {{ user_name }}!</h3>
          <p>Welcome to the NGO Regulatory Authority platform</p>
        </div>

        <div class="welcome-message">
          <p>
            We're excited to have you join the NGORA digital platform! As
            Malawi's official statutory body for NGO regulation, we're here to
            support your organization's journey toward legal compliance and
            sustainable development impact.
          </p>

          <p>
            Our mission is
            <strong
              >"Delivering sustainable development through NGO sector
              regulation"</strong
            >, and we're committed to helping your organization thrive while
            maintaining the highest standards of accountability and
            transparency.
          </p>
        </div>

        <!-- Features Section -->
        <div class="features-section">
          <h2 class="features-title">What You Can Do on Our Platform</h2>
          <div class="features-grid">
            <div class="feature-card">
              <div class="feature-icon">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M9 11H15"/>
                  <path d="M9 15H12"/>
                  <path d="M16 3H5C4.44772 3 4 3.44772 4 4V20C4 20.5523 4.44772 21 5 21H19C19.5523 21 20 20.5523 20 20V7L16 3Z"/>
                  <path d="M16 3V7H20"/>
                </svg>
              </div>
              <div class="feature-title">NGO Registration</div>
              <div class="feature-desc">
                Complete your organization's registration process online through
                our secure myNGO platform
              </div>
            </div>
            <div class="feature-card">
              <div class="feature-icon">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M14 2H6C5.44772 2 5 2.44772 5 3V21C5 21.5523 5.44772 22 6 22H18C18.5523 22 19 21.5523 19 21V7L14 2Z"/>
                  <path d="M14 2V7H19"/>
                  <path d="M16 13H8"/>
                  <path d="M16 17H8"/>
                  <path d="M10 9H9H8"/>
                </svg>
              </div>
              <div class="feature-title">License Management</div>
              <div class="feature-desc">
                Apply for, renew, and manage your NGO operating licenses with
                ease
              </div>
            </div>
            <div class="feature-card">
              <div class="feature-icon">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M3 3V21H21"/>
                  <path d="M9 9L12 6L16 10L21 5"/>
                  <path d="M21 12V5H14"/>
                </svg>
              </div>
              <div class="feature-title">Compliance Reporting</div>
              <div class="feature-desc">
                Submit annual reports and maintain compliance with regulatory
                requirements
              </div>
            </div>
            <div class="feature-card">
              <div class="feature-icon">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M20 6L9 17L4 12"/>
                  <path d="M8 12L12 16L16 12"/>
                </svg>
              </div>
              <div class="feature-title">Activity Management</div>
              <div class="feature-desc">
                Track and report your organization's activities and projects
              </div>
            </div>
            <div class="feature-card">
              <div class="feature-icon">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <rect width="20" height="14" x="2" y="5" rx="2"/>
                  <line x1="2" x2="22" y1="10" y2="10"/>
                </svg>
              </div>
              <div class="feature-title">Online Payments</div>
              <div class="feature-desc">
                Pay registration fees and other charges securely online
              </div>
            </div>
            <div class="feature-card">
              <div class="feature-icon">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M22 16.92V19C22 20.1046 21.1046 21 20 21C10.0589 21 2 12.9411 2 3C2 1.89543 2.89543 1 4 1H7.08C7.55499 1 7.97196 1.32713 8.06921 1.79384L8.92 6.79C9.03658 7.38604 8.67799 7.98277 8.07846 8.10206L5.5 8.5C6.92 11.36 8.64 13.08 11.5 14.5L11.8979 11.9215C12.0172 11.322 12.614 10.9634 13.21 11.08L18.2062 11.9308C18.6729 12.028 19 12.445 19 12.92Z"/>
                </svg>
              </div>
              <div class="feature-title">Support Services</div>
              <div class="feature-desc">
                Access help desk support and complaint handling services
              </div>
            </div>
          </div>
        </div>

        <!-- Next Steps -->
        <div class="next-steps">
          <h3>Getting Started - Your Next Steps</h3>
          <ul class="steps-list">
            <li>
              <div class="step-number">1</div>
              <div class="step-text">
                <strong>Complete Your Profile:</strong> Log in to your account
                and fill out your organization's complete profile information
              </div>
            </li>
            <li>
              <div class="step-number">2</div>
              <div class="step-text">
                <strong>Prepare Documents:</strong> Gather required documents
                including constitution, board resolutions, and registration
                certificates
              </div>
            </li>
            <li>
              <div class="step-number">3</div>
              <div class="step-text">
                <strong>Submit Application:</strong> Complete your NGO
                registration or license renewal application online
              </div>
            </li>
            <li>
              <div class="step-number">4</div>
              <div class="step-text">
                <strong>Track Progress:</strong> Monitor your application status
                and respond to any additional requirements
              </div>
            </li>
            <li>
              <div class="step-number">5</div>
              <div class="step-text">
                <strong>Stay Compliant:</strong> Maintain regular reporting and
                keep your information up to date
              </div>
            </li>
          </ul>
        </div>

        <!-- CTA Section -->
        <div class="cta-section">
          <a href="{{ platform_url }}" class="cta-button"
            >Access myNGO Platform</a
          >
          <a href="{{ resources_url }}" class="cta-button secondary"
            >Download Resources</a
          >
        </div>

        <!-- Support Section -->
        <div class="support-section">
          <h3 class="support-title">Need Help? We're Here to Support You</h3>
          <p>
            Our team is ready to assist you with any questions or technical
            support you may need.
          </p>

          <div class="contact-grid">
            <div class="contact-item">
              <strong>Email Support</strong>
              <span><EMAIL></span>
            </div>
            <div class="contact-item">
              <strong>Phone Support</strong>
              <span>+265 884 462 808</span>
            </div>
            <div class="contact-item">
              <strong>Office Hours</strong>
              <span>Mon-Fri: 8:00 AM - 5:00 PM</span>
            </div>
            <div class="contact-item">
              <strong>General Inquiries</strong>
              <span><EMAIL></span>
            </div>
          </div>
        </div>
      </div>

      <!-- Footer -->
      <div class="footer">
        <div class="footer-content">
          <p><strong>NGO Regulatory Authority (NGORA)</strong></p>
          <p>
            A statutory corporation under the Ministry of Gender, Community
            Development and Social Welfare
          </p>
          <p>
            Committed to professionalism, independence, accountability, and
            being people-centric
          </p>
        </div>

        <div class="footer-links">
          <a href="https://ngora.mw">Visit Website</a>
          <a href="https://ngora.mw/faq">FAQ</a>
          <a href="https://ngora.mw/contact-us">Contact Us</a>
          <a href="https://ngora.mw/downloads">Resources</a>
        </div>

        <div class="footer-bottom">
          <p>
            &copy; {{ year }} NGO Regulatory Authority. All rights reserved.
          </p>
          <p>
            This is an automated message from the NGORA system. Please do not
            reply to this email.
          </p>
        </div>
      </div>
    </div>
  </body>
</html>
