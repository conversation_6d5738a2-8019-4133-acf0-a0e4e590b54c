<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Reset Your Password - NGORA</title>
    <style>
      @import url("https://fonts.googleapis.com/css2?family=Inter+UI:wght@400;500;600;700&display=swap");

      :root {
        --color-primary: hsl(22, 100%, 52%);
        --color-secondary: hsl(262, 84%, 58%);
        --color-text: #0f172a;
        --color-text-light: #64748b;
        --color-bg: #f8fafc;
        --color-warning: #f59e0b;
        --gradient-primary: linear-gradient(135deg, hsl(22, 100%, 52%) 0%, hsl(262, 84%, 58%) 100%);
        --shadow-lg: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
      }

      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: "Inter UI", "Inter", Arial, sans-serif;
        line-height: 1.6;
        color: var(--color-text);
        background: var(--color-bg);
        padding: 20px;
        margin: 0;
      }

      .email-container {
        max-width: 600px;
        margin: 0 auto;
        background: #ffffff;
        border-radius: 16px;
        box-shadow: var(--shadow-lg);
        overflow: hidden;
      }

      .header {
        background: var(--gradient-primary);
        padding: 40px 30px;
        text-align: center;
        position: relative;
        overflow: hidden;
        color: white;
      }

      .header::before {
        content: "";
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 1px, transparent 1px);
        background-size: 30px 30px;
        animation: float 25s infinite linear;
      }

      @keyframes float {
        0% { transform: translate(0, 0) rotate(0deg); }
        100% { transform: translate(-30px, -30px) rotate(360deg); }
      }

      .header-content {
        position: relative;
        z-index: 2;
      }

      .header-icon {
        width: 64px;
        height: 64px;
        margin: 0 auto 20px;
        background: rgba(255, 255, 255, 0.15);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        backdrop-filter: blur(10px);
      }

      .header h1 {
        font-size: 24px;
        font-weight: 700;
        margin: 0 0 8px;
      }

      .header p {
        font-size: 16px;
        opacity: 0.9;
        margin: 0;
      }

      .content {
        padding: 40px 30px;
      }

      .alert-message {
        background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
        border: 1px solid var(--color-warning);
        border-radius: 12px;
        padding: 20px;
        margin: 24px 0;
        display: flex;
        align-items: flex-start;
        gap: 12px;
      }

      .alert-icon {
        width: 20px;
        height: 20px;
        flex-shrink: 0;
        margin-top: 2px;
      }

      .alert-content {
        flex: 1;
      }

      .alert-title {
        font-weight: 600;
        color: #92400e;
        margin-bottom: 4px;
      }

      .alert-text {
        color: #451a03;
        font-size: 14px;
        line-height: 1.5;
      }

      .reset-instructions {
        background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
        border-radius: 12px;
        padding: 24px;
        margin: 24px 0;
      }

      .reset-instructions h3 {
        color: var(--color-text);
        font-size: 18px;
        font-weight: 600;
        margin-bottom: 16px;
      }

      .cta-section {
        text-align: center;
        margin: 32px 0;
      }

      .cta-button {
        display: inline-block;
        background: var(--gradient-primary);
        color: white;
        text-decoration: none;
        padding: 16px 32px;
        border-radius: 12px;
        font-weight: 600;
        font-size: 16px;
        transition: all 0.3s ease;
        box-shadow: 0 8px 25px rgba(251, 146, 60, 0.3);
        margin-bottom: 16px;
      }

      .cta-button:hover {
        transform: translateY(-2px);
        box-shadow: 0 12px 35px rgba(251, 146, 60, 0.4);
      }

      .link-info {
        background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
        border: 1px solid #0ea5e9;
        border-radius: 12px;
        padding: 20px;
        margin: 24px 0;
        text-align: center;
      }

      .link-expiry {
        color: #0c4a6e;
        font-size: 14px;
        font-weight: 500;
        margin-bottom: 8px;
      }

      .alternative-link {
        word-break: break-all;
        color: var(--color-primary);
        font-size: 12px;
        background: white;
        padding: 12px;
        border-radius: 8px;
        border: 1px solid #e2e8f0;
        margin-top: 8px;
      }

      .security-tips {
        background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
        border: 1px solid #22c55e;
        border-radius: 12px;
        padding: 24px;
        margin: 24px 0;
      }

      .security-tips h4 {
        color: #166534;
        font-size: 16px;
        font-weight: 600;
        margin-bottom: 12px;
        display: flex;
        align-items: center;
        gap: 8px;
      }

      .tips-list {
        list-style: none;
        padding: 0;
        margin: 0;
      }

      .tips-list li {
        color: #15803d;
        font-size: 14px;
        margin-bottom: 8px;
        display: flex;
        align-items: flex-start;
        gap: 8px;
      }

      .tips-list li:last-child {
        margin-bottom: 0;
      }

      .check-icon {
        width: 16px;
        height: 16px;
        flex-shrink: 0;
        margin-top: 2px;
      }

      .footer {
        background: var(--color-bg);
        padding: 24px 30px;
        text-align: center;
        font-size: 14px;
        color: var(--color-text-light);
        border-top: 1px solid #e2e8f0;
      }

      .footer p {
        margin: 0 0 8px 0;
      }

      .footer a {
        color: var(--color-primary);
        text-decoration: none;
        margin: 0 8px;
      }

      .footer a:hover {
        text-decoration: underline;
      }

      @media screen and (max-width: 600px) {
        .content {
          padding: 24px 20px;
        }

        .header {
          padding: 32px 20px;
        }

        .footer {
          padding: 20px;
        }

        .cta-button {
          display: block;
          margin: 16px 0;
        }
      }
    </style>
  </head>
  <body>
    <div class="email-container">
      <div class="header">
        <div class="header-content">
          <div class="header-icon">
            <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <circle cx="12" cy="12" r="4"/>
              <path d="M16 8V6C16 4.89543 15.1046 4 14 4H10C8.89543 4 8 4.89543 8 6V8"/>
              <rect width="20" height="12" x="2" y="8" rx="2" ry="2"/>
            </svg>
          </div>
          <h1>Reset Your Password</h1>
          <p>Secure your NGORA account</p>
        </div>
      </div>

      <div class="content">
        <p>Hello {{ user_name }},</p>

        <div class="alert-message">
          <svg class="alert-icon" viewBox="0 0 24 24" fill="none" stroke="#f59e0b" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <circle cx="12" cy="12" r="10"/>
            <line x1="12" y1="8" x2="12" y2="12"/>
            <line x1="12" y1="16" x2="12.01" y2="16"/>
          </svg>
          <div class="alert-content">
            <div class="alert-title">Password Reset Request</div>
            <div class="alert-text">
              We received a request to reset the password for your NGORA account. If you made this request, click the button below to create a new password.
            </div>
          </div>
        </div>

        <div class="cta-section">
          <a href="{{ reset_url }}" class="cta-button">Reset My Password</a>
        </div>

        <div class="link-info">
          <div class="link-expiry">⏰ This reset link expires in 1 hour for security</div>
          <p style="font-size: 14px; color: #475569; margin: 8px 0;">
            If the button doesn't work, copy and paste this link into your browser:
          </p>
          <div class="alternative-link">{{ reset_url }}</div>
        </div>

        <div class="reset-instructions">
          <h3>What happens next:</h3>
          <ol style="padding-left: 20px; color: var(--color-text-light);">
            <li style="margin-bottom: 8px;">Click the reset button or link above</li>
            <li style="margin-bottom: 8px;">You'll be taken to a secure page to create your new password</li>
            <li style="margin-bottom: 8px;">Choose a strong, unique password</li>
            <li>Log in with your new password</li>
          </ol>
        </div>

        <div class="security-tips">
          <h4>
            <svg class="check-icon" viewBox="0 0 24 24" fill="none" stroke="#22c55e" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M9 12L11 14L15 10"/>
              <circle cx="12" cy="12" r="10"/>
            </svg>
            Security Tips
          </h4>
          <ul class="tips-list">
            <li>
              <svg class="check-icon" viewBox="0 0 24 24" fill="none" stroke="#22c55e" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <polyline points="20 6 9 17 4 12"/>
              </svg>
              Use a password that's at least 8 characters long
            </li>
            <li>
              <svg class="check-icon" viewBox="0 0 24 24" fill="none" stroke="#22c55e" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <polyline points="20 6 9 17 4 12"/>
              </svg>
              Include uppercase and lowercase letters, numbers, and symbols
            </li>
            <li>
              <svg class="check-icon" viewBox="0 0 24 24" fill="none" stroke="#22c55e" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <polyline points="20 6 9 17 4 12"/>
              </svg>
              Don't reuse passwords from other accounts
            </li>
            <li>
              <svg class="check-icon" viewBox="0 0 24 24" fill="none" stroke="#22c55e" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <polyline points="20 6 9 17 4 12"/>
              </svg>
              Consider using a password manager
            </li>
          </ul>
        </div>

        <div style="background: #fef2f2; border: 1px solid #fca5a5; border-radius: 12px; padding: 20px; margin: 24px 0;">
          <p style="color: #dc2626; font-weight: 600; margin-bottom: 8px;">
            🔒 Didn't request this reset?
          </p>
          <p style="color: #991b1b; font-size: 14px; margin: 0;">
            If you didn't ask to reset your password, you can safely ignore this email. Your password won't be changed. However, you may want to review your account security settings.
          </p>
        </div>

        <p style="color: var(--color-text-light); font-size: 14px; text-align: center; margin-top: 32px;">
          Need help? Contact our support team at <a href="mailto:<EMAIL>" style="color: var(--color-primary);"><EMAIL></a>
        </p>
      </div>

      <div class="footer">
        <p><strong>NGO Regulatory Authority (NGORA)</strong></p>
        <p>Government of Malawi | Ministry of Gender, Community Development and Social Welfare</p>
        <p>
          <a href="https://ngora.mw">Visit Website</a> |
          <a href="https://ngora.mw/contact">Contact Support</a> |
          <a href="https://ngora.mw/security">Security Center</a>
        </p>
        <p style="margin-top: 12px; font-size: 12px;">
          &copy; {{ year }} NGO Regulatory Authority. All rights reserved.
        </p>
      </div>
    </div>
  </body>
</html>