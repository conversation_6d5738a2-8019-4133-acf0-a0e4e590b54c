"""added nationality document id

Revision ID: 7ae2e779d12f
Revises: f6debd32317f
Create Date: 2025-09-08 08:06:12.161165

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '7ae2e779d12f'
down_revision: Union[str, None] = 'f6debd32317f'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('permit_applicants', sa.Column('letter_document_id', sa.UUID(), nullable=True))
    op.add_column('permit_applicants', sa.Column('passport_document_id', sa.UUID(), nullable=True))
    op.create_foreign_key(None, 'permit_applicants', 'documents', ['passport_document_id'], ['id'])
    op.create_foreign_key(None, 'permit_applicants', 'documents', ['letter_document_id'], ['id'])
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'permit_applicants', type_='foreignkey')
    op.drop_constraint(None, 'permit_applicants', type_='foreignkey')
    op.drop_column('permit_applicants', 'passport_document_id')
    op.drop_column('permit_applicants', 'letter_document_id')
    # ### end Alembic commands ###
