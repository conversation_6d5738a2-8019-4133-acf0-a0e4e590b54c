"""added currency to organization donor

Revision ID: 1984ed7eca9b
Revises: 4096ec565409
Create Date: 2025-08-18 12:27:14.973582

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "1984ed7eca9b"
down_revision: Union[str, None] = "4096ec565409"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
	"""Upgrade schema."""
	# ### commands auto generated by Alembic - please adjust! ###
	op.create_unique_constraint(None, "licence_renewals", ["id"])
	op.add_column("organization_donors", sa.Column("currency_id", sa.UUID(), nullable=True))
	op.create_foreign_key(None, "organization_donors", "currencies", ["currency_id"], ["id"])
	# ### end Alembic commands ###


def downgrade() -> None:
	"""Downgrade schema."""
	# ### commands auto generated by Alembic - please adjust! ###
	op.drop_constraint(None, "organization_donors", type_="foreignkey")
	op.drop_column("organization_donors", "currency_id")
	op.drop_constraint(None, "licence_renewals", type_="unique")
	# ### end Alembic commands ###
