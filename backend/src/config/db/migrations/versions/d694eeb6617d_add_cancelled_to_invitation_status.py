"""add cancelled to invitation status

Revision ID: d694eeb6617d
Revises: c83b33f83174
Create Date: 2025-08-30 14:12:22.294094

"""

from typing import Sequence, Union

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "d694eeb6617d"
down_revision: Union[str, None] = "c83b33f83174"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
	"""Upgrade schema."""
	# ### commands auto generated by Alembic - please adjust! ###
	op.create_unique_constraint(None, "amendments", ["id"])
	op.execute("ALTER TYPE invitationstatus ADD VALUE 'CANCELLED'")
	# ### end Alembic commands ###


def downgrade() -> None:
	"""Downgrade schema."""
	# ### commands auto generated by Alembic - please adjust! ###
	op.drop_constraint(None, "amendments", type_="unique")
	# ### end Alembic commands ###
