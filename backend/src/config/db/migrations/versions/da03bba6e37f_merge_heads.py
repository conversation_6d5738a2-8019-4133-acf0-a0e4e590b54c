"""merge heads

Revision ID: da03bba6e37f
Revises: 581e0d521b70, 927f44062cc1
Create Date: 2025-08-13 18:52:47.237474

"""

from typing import Sequence, Union


# revision identifiers, used by Alembic.
revision: str = "da03bba6e37f"
down_revision: Union[str, None] = ("581e0d521b70", "927f44062cc1")
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
	"""Upgrade schema."""
	pass


def downgrade() -> None:
	"""Downgrade schema."""
	pass
