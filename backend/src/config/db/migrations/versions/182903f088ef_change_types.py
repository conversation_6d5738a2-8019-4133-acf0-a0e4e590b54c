"""change types

Revision ID: 182903f088ef
Revises: 2f0ff79d4587
Create Date: 2025-08-09 10:23:49.530140

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "182903f088ef"
down_revision: Union[str, None] = "2f0ff79d4587"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
	"""Upgrade schema."""
	# ### commands auto generated by Alembic - please adjust! ###
	op.add_column("notification_recipients", sa.Column("read_at", sa.DateTime(), nullable=True))
	op.create_unique_constraint(None, "workflow_stage_comments", ["id"])
	# ### end Alembic commands ###


def downgrade() -> None:
	"""Downgrade schema."""
	# ### commands auto generated by Alembic - please adjust! ###
	op.drop_constraint(None, "workflow_stage_comments", type_="unique")
	op.drop_column("notification_recipients", "read_at")
	# ### end Alembic commands ###
