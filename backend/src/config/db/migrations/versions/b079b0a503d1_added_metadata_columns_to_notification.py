"""added metadata columns to notification

Revision ID: b079b0a503d1
Revises: b2acfab29d86
Create Date: 2025-08-30 02:43:53.221227

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = "b079b0a503d1"
down_revision: Union[str, None] = "b2acfab29d86"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
	"""Upgrade schema."""
	# ### commands auto generated by Alembic - please adjust! ###
	op.add_column("notifications", sa.Column("context", postgresql.JSONB(astext_type=sa.Text()), nullable=True))
	op.add_column("notifications", sa.Column("action_type", sa.String(length=50), nullable=True))
	# ### end Alembic commands ###


def downgrade() -> None:
	"""Downgrade schema."""
	# ### commands auto generated by Alembic - please adjust! ###
	op.drop_column("notifications", "action_type")
	op.drop_column("notifications", "context")
	# ### end Alembic commands ###
