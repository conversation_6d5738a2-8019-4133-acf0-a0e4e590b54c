"""change types on application

Revision ID: 155c65693ac7
Revises: 182903f088ef
Create Date: 2025-08-09 11:30:27.996343

"""

from typing import Sequence, Union

from alembic import op


# revision identifiers, used by Alembic.
revision: str = "155c65693ac7"
down_revision: Union[str, None] = "182903f088ef"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
	"""Upgrade schema."""
	# ### commands auto generated by Alembic - please adjust! ###
	op.execute("ALTER TYPE applicationstatus RENAME VALUE 'REVIEW' TO 'IN_REVIEW'")
	# ### end Alembic commands ###


def downgrade() -> None:
	"""Downgrade schema."""
	# ### commands auto generated by Alembic - please adjust! ###
	op.execute("ALTER TYPE applicationstatus RENAME VALUE 'IN_REVIEW' TO 'REVIEW'")
	# ### end Alembic commands ###
