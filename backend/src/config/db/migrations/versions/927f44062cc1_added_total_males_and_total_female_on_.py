"""added total males and total female on organization staff

Revision ID: 927f44062cc1
Revises: 0d10652569d0
Create Date: 2025-08-12 15:39:47.534359

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "927f44062cc1"
down_revision: Union[str, None] = "0d10652569d0"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
	"""Upgrade schema."""
	# ### commands auto generated by Alembic - please adjust! ###
	op.add_column(
		"organization_staffs", sa.Column("total_male", sa.Integer(), nullable=False, server_default=sa.text("0"))
	)
	op.add_column(
		"organization_staffs", sa.Column("total_female", sa.Integer(), nullable=False, server_default=sa.text("0"))
	)
	# ### end Alembic commands ###


def downgrade() -> None:
	"""Downgrade schema."""
	# ### commands auto generated by Alembic - please adjust! ###
	op.drop_column("organization_staffs", "total_female")
	op.drop_column("organization_staffs", "total_male")
	# ### end Alembic commands ###
