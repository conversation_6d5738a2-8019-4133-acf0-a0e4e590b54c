"""added columns to complaints

Revision ID: 6b5b028971da
Revises: 043df52fb1f6
Create Date: 2025-08-22 14:12:59.297895

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "6b5b028971da"
down_revision: Union[str, None] = "043df52fb1f6"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
	"""Upgrade schema."""
	# ### commands auto generated by Alembic - please adjust! ###
	op.add_column("complaints", sa.Column("tracking_code", sa.String(), nullable=False))
	op.add_column("complaints", sa.Column("complainant_name", sa.String(), nullable=True))
	op.add_column("complaints", sa.Column("complainant_email", sa.String(), nullable=True))
	op.add_column("complaints", sa.Column("complainant_phone", sa.String(), nullable=True))
	op.alter_column("complaints", "complainant_id", existing_type=sa.UUID(), nullable=True)
	op.create_unique_constraint(None, "complaints", ["tracking_code"])

	# Add PENDING to PaymentStatus enum
	op.execute("ALTER TYPE paymentstatus ADD VALUE 'PENDING'")
	# ### end Alembic commands ###


def downgrade() -> None:
	"""Downgrade schema."""
	# ### commands auto generated by Alembic - please adjust! ###

	# Remove PENDING from PaymentStatus enum by recreating the type
	op.execute("ALTER TYPE paymentstatus RENAME TO paymentstatus_old")
	op.execute("CREATE TYPE paymentstatus AS ENUM ('SUCCESS', 'FAILED')")
	op.execute("ALTER TABLE payments ALTER COLUMN status TYPE paymentstatus USING status::text::paymentstatus")
	op.execute("DROP TYPE paymentstatus_old")

	op.drop_constraint(None, "complaints", type_="unique")
	op.alter_column("complaints", "complainant_id", existing_type=sa.UUID(), nullable=False)
	op.drop_column("complaints", "complainant_phone")
	op.drop_column("complaints", "complainant_email")
	op.drop_column("complaints", "complainant_name")
	op.drop_column("complaints", "tracking_code")
	# ### end Alembic commands ###
