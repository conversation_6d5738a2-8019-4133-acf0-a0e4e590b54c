"""change registered status to approved

Revision ID: 0d10652569d0
Revises: e1b2d131209e
Create Date: 2025-08-09 14:16:02.233548

"""

from typing import Sequence, Union

from alembic import op


# revision identifiers, used by Alembic.
revision: str = "0d10652569d0"
down_revision: Union[str, None] = "e1b2d131209e"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
	"""Upgrade schema."""
	# ### commands auto generated by Alembic - please adjust! ###
	op.execute("ALTER TYPE applicationstatus RENAME VALUE 'REGISTERED' TO 'APPROVED'")
	# ### end Alembic commands ###


def downgrade() -> None:
	"""Downgrade schema."""
	# ### commands auto generated by Alembic - please adjust! ###
	op.execute("ALTER TYPE applicationstatus RENAME VALUE 'APPROVED' TO 'REGISTERED'")
	# ### end Alembic commands ###
