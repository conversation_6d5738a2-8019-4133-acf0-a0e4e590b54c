"""added comments tables

Revision ID: 2f0ff79d4587
Revises: 29b553d3354c
Create Date: 2025-08-08 11:44:07.342636

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "2f0ff79d4587"
down_revision: Union[str, None] = "29b553d3354c"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
	"""Upgrade schema."""
	# ### commands auto generated by Alembic - please adjust! ###
	op.create_table(
		"workflow_stage_comments",
		sa.Column("id", sa.UUID(), nullable=False, comment="Primary key UUID for this table"),
		sa.Column("text", sa.Text(), nullable=True),
		sa.Column("workflow_stage_id", sa.UUID(), nullable=False),
		sa.Column("created_at", sa.TIMESTAMP(timezone=True), server_default=sa.text("now()"), nullable=False),
		sa.Column("updated_at", sa.TIMESTAMP(timezone=True), server_default=sa.text("now()"), nullable=False),
		sa.Column("created_by", sa.UUID(), nullable=True),
		sa.Column("updated_by", sa.UUID(), nullable=True),
		sa.Column("voided", sa.Boolean(), nullable=False),
		sa.Column("voided_by", sa.UUID(), nullable=True),
		sa.Column("void_reason", sa.Text(), nullable=True),
		sa.ForeignKeyConstraint(
			["created_by"],
			["users.id"],
		),
		sa.ForeignKeyConstraint(
			["updated_by"],
			["users.id"],
		),
		sa.ForeignKeyConstraint(
			["voided_by"],
			["users.id"],
		),
		sa.ForeignKeyConstraint(
			["workflow_stage_id"],
			["workflow_stages.id"],
		),
		sa.PrimaryKeyConstraint("id"),
		sa.UniqueConstraint("id"),
	)
	op.create_unique_constraint(None, "account_verifications", ["id"])
	# ### end Alembic commands ###


def downgrade() -> None:
	"""Downgrade schema."""
	# ### commands auto generated by Alembic - please adjust! ###
	op.drop_constraint(None, "account_verifications", type_="unique")
	op.drop_table("workflow_stage_comments")
	# ### end Alembic commands ###
