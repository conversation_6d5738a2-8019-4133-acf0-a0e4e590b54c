"""added permit applicants table

Revision ID: 8dcb2d9eeee8
Revises: d694eeb6617d
Create Date: 2025-09-08 07:12:19.423451

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '8dcb2d9eeee8'
down_revision: Union[str, None] = 'd694eeb6617d'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('permit_applicants',
    sa.Column('id', sa.UUID(), nullable=False, comment='Primary key UUID for this table'),
    sa.Column('application_id', sa.UUID(), nullable=False),
    sa.Column('full_name', sa.String(length=255), nullable=False),
    sa.Column('nationality', sa.String(length=100), nullable=False),
    sa.Column('passport_number', sa.String(length=50), nullable=False),
    sa.Column('position', sa.String(length=255), nullable=True),
    sa.Column('department', sa.String(length=255), nullable=True),
    sa.Column('employment_start_date', sa.Date(), nullable=True),
    sa.Column('employment_end_date', sa.Date(), nullable=True),
    sa.Column('application_letter_document_id', sa.UUID(), nullable=True),
    sa.Column('passport_picture_document_id', sa.UUID(), nullable=True),
    sa.Column('created_at', sa.TIMESTAMP(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('updated_at', sa.TIMESTAMP(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('created_by', sa.UUID(), nullable=True),
    sa.Column('updated_by', sa.UUID(), nullable=True),
    sa.Column('voided', sa.Boolean(), nullable=False),
    sa.Column('voided_by', sa.UUID(), nullable=True),
    sa.Column('void_reason', sa.Text(), nullable=True),
    sa.ForeignKeyConstraint(['application_id'], ['applications.id'], ),
    sa.ForeignKeyConstraint(['application_letter_document_id'], ['documents.id'], ),
    sa.ForeignKeyConstraint(['created_by'], ['users.id'], ),
    sa.ForeignKeyConstraint(['passport_picture_document_id'], ['documents.id'], ),
    sa.ForeignKeyConstraint(['updated_by'], ['users.id'], ),
    sa.ForeignKeyConstraint(['voided_by'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('id')
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('permit_applicants')
    # ### end Alembic commands ###
