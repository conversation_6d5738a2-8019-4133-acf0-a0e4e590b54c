"""update activity invitation columns

Revision ID: 282cdbc90f22
Revises: 7ae2e779d12f
Create Date: 2025-09-09 14:28:24.015145

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '282cdbc90f22'
down_revision: Union[str, None] = '7ae2e779d12f'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('activity_invitations', sa.Column('email', sa.String(length=100), nullable=True))
    op.add_column('activity_invitations', sa.Column('name', sa.String(length=100), nullable=True))
    op.add_column('activity_invitations', sa.Column('account_type', sa.Enum('ORG', 'USER', name='accounttype'), nullable=False))
    op.drop_column('activity_invitations', 'external_name')
    op.drop_column('activity_invitations', 'external_email')
    op.drop_column('activity_invitations', 'external_type')
    op.alter_column('licences', 'expires_at',
               existing_type=sa.DATE(),
               nullable=True)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('licences', 'expires_at',
               existing_type=sa.DATE(),
               nullable=False)
    op.add_column('activity_invitations', sa.Column('external_type', postgresql.ENUM('ORG', 'USER', name='accounttype'), autoincrement=False, nullable=False))
    op.add_column('activity_invitations', sa.Column('external_email', sa.VARCHAR(length=100), autoincrement=False, nullable=True))
    op.add_column('activity_invitations', sa.Column('external_name', sa.VARCHAR(length=100), autoincrement=False, nullable=True))
    op.drop_column('activity_invitations', 'account_type')
    op.drop_column('activity_invitations', 'name')
    op.drop_column('activity_invitations', 'email')
    # ### end Alembic commands ###
