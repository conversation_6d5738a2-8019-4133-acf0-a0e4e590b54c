"""added document to licence

Revision ID: 043df52fb1f6
Revises: bc38c3525547
Create Date: 2025-08-21 17:22:24.548252

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = "043df52fb1f6"
down_revision: Union[str, None] = "bc38c3525547"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
	"""Upgrade schema."""
	# ### commands auto generated by Alembic - please adjust! ###
	op.drop_table("printed_licences")
	op.drop_table("printed_permits")
	op.drop_index("idx_account_verifications_user_id", table_name="account_verifications")
	op.drop_index("idx_account_verifications_verified", table_name="account_verifications")
	op.drop_index("idx_applications_created_at", table_name="applications")
	op.drop_index("idx_applications_created_by", table_name="applications")
	op.drop_index("idx_applications_org_status", table_name="applications")
	op.drop_index("idx_applications_organization_id", table_name="applications")
	op.drop_index("idx_applications_status", table_name="applications")
	op.drop_index("idx_applications_status_type", table_name="applications")
	op.drop_index("idx_applications_type", table_name="applications")
	op.drop_index("idx_applications_voided", table_name="applications")
	op.drop_index("idx_audit_logs_created_at", table_name="audit_logs")
	op.drop_index("idx_audit_logs_table_created", table_name="audit_logs")
	op.drop_index("idx_audit_logs_table_record", table_name="audit_logs")
	op.drop_index("idx_audit_logs_user_id", table_name="audit_logs")
	op.drop_index("idx_directors_organization_id", table_name="directors")
	op.drop_index("idx_invoices_created_at", table_name="invoices")
	op.drop_index("idx_invoices_organization_id", table_name="invoices")
	op.drop_index("idx_invoices_status", table_name="invoices")
	op.add_column("licences", sa.Column("document_id", sa.UUID(), nullable=False))
	op.create_foreign_key(None, "licences", "documents", ["document_id"], ["id"])
	op.drop_index("idx_members_is_active", table_name="members")
	op.drop_index("idx_members_organization_id", table_name="members")
	op.drop_index("idx_members_user_id", table_name="members")
	op.drop_index("idx_notification_recipients_account_id", table_name="notification_recipients")
	op.drop_index("idx_notification_recipients_account_read", table_name="notification_recipients")
	op.drop_index("idx_notification_recipients_is_read", table_name="notification_recipients")
	op.drop_index("idx_notification_recipients_notification_id", table_name="notification_recipients")
	op.drop_index("idx_notifications_activity_id", table_name="notifications")
	op.drop_index("idx_notifications_created_at", table_name="notifications")
	op.drop_index("idx_notifications_priority", table_name="notifications")
	op.drop_index("idx_notifications_sender_id", table_name="notifications")
	op.drop_index("idx_notifications_type", table_name="notifications")
	op.drop_index("idx_organization_donors_organization_id", table_name="organization_donors")
	op.drop_index("idx_organization_sectors_organization_id", table_name="organization_sectors")
	op.drop_index("idx_organization_staffs_organization_id", table_name="organization_staffs")
	op.drop_index("idx_organizations_account_id", table_name="organizations")
	op.drop_index("idx_organizations_created_at", table_name="organizations")
	op.drop_index("idx_organizations_created_by", table_name="organizations")
	op.drop_index("idx_organizations_district_id", table_name="organizations")
	op.drop_index("idx_organizations_district_status", table_name="organizations")
	op.drop_index("idx_organizations_organization_type_id", table_name="organizations")
	op.drop_index("idx_organizations_registration_type_id", table_name="organizations")
	op.drop_index("idx_organizations_status", table_name="organizations")
	op.drop_index("idx_organizations_voided", table_name="organizations")
	op.drop_index("idx_payments_created_at", table_name="payments")
	op.drop_index("idx_payments_invoice_id", table_name="payments")
	op.drop_index("idx_payments_organization_id", table_name="payments")
	op.drop_index("idx_payments_status", table_name="payments")
	op.add_column("permits", sa.Column("document_id", sa.UUID(), nullable=False))
	op.create_foreign_key(None, "permits", "documents", ["document_id"], ["id"])
	op.add_column("print_histories", sa.Column("document_id", sa.UUID(), nullable=False))
	op.add_column("print_histories", sa.Column("ip_address", sa.String(length=15), nullable=True))
	op.create_foreign_key(None, "print_histories", "documents", ["document_id"], ["id"])
	op.drop_index("idx_sessions_is_active", table_name="sessions")
	op.drop_index("idx_sessions_user_id", table_name="sessions")
	op.drop_index("idx_user_departments_department_id", table_name="user_departments")
	op.drop_index("idx_user_departments_user_id", table_name="user_departments")
	op.drop_index("idx_user_roles_role_id", table_name="user_roles")
	op.drop_index("idx_user_roles_user_id", table_name="user_roles")
	op.drop_index("idx_users_account_id", table_name="users")
	op.drop_index("idx_users_created_at", table_name="users")
	op.drop_index("idx_users_created_by", table_name="users")
	op.drop_index("idx_users_is_external", table_name="users")
	op.drop_index("idx_users_verified", table_name="users")
	op.drop_index("idx_users_voided", table_name="users")
	op.drop_index("idx_workflow_stages_approved_by", table_name="workflow_stages")
	op.drop_index("idx_workflow_stages_created_at", table_name="workflow_stages")
	op.drop_index("idx_workflow_stages_status", table_name="workflow_stages")
	op.drop_index("idx_workflow_stages_template_stage_id", table_name="workflow_stages")
	op.drop_index("idx_workflow_stages_workflow_id", table_name="workflow_stages")
	op.drop_index("idx_workflow_stages_workflow_status", table_name="workflow_stages")
	op.drop_index("idx_workflows_application_id", table_name="workflows")
	op.drop_index("idx_workflows_created_at", table_name="workflows")
	op.drop_index("idx_workflows_created_by", table_name="workflows")
	op.drop_index("idx_workflows_template_id", table_name="workflows")
	# ### end Alembic commands ###


def downgrade() -> None:
	"""Downgrade schema."""
	# ### commands auto generated by Alembic - please adjust! ###
	op.create_index("idx_workflows_template_id", "workflows", ["template_id"], unique=False)
	op.create_index("idx_workflows_created_by", "workflows", ["created_by"], unique=False)
	op.create_index("idx_workflows_created_at", "workflows", ["created_at"], unique=False)
	op.create_index("idx_workflows_application_id", "workflows", ["application_id"], unique=False)
	op.create_index("idx_workflow_stages_workflow_status", "workflow_stages", ["workflow_id", "status"], unique=False)
	op.create_index("idx_workflow_stages_workflow_id", "workflow_stages", ["workflow_id"], unique=False)
	op.create_index("idx_workflow_stages_template_stage_id", "workflow_stages", ["template_stage_id"], unique=False)
	op.create_index("idx_workflow_stages_status", "workflow_stages", ["status"], unique=False)
	op.create_index("idx_workflow_stages_created_at", "workflow_stages", ["created_at"], unique=False)
	op.create_index("idx_workflow_stages_approved_by", "workflow_stages", ["approved_by"], unique=False)
	op.create_index("idx_users_voided", "users", ["voided"], unique=False)
	op.create_index("idx_users_verified", "users", ["verified"], unique=False)
	op.create_index("idx_users_is_external", "users", ["is_external"], unique=False)
	op.create_index("idx_users_created_by", "users", ["created_by"], unique=False)
	op.create_index("idx_users_created_at", "users", ["created_at"], unique=False)
	op.create_index("idx_users_account_id", "users", ["account_id"], unique=False)
	op.create_index("idx_user_roles_user_id", "user_roles", ["user_id"], unique=False)
	op.create_index("idx_user_roles_role_id", "user_roles", ["role_id"], unique=False)
	op.create_index("idx_user_departments_user_id", "user_departments", ["user_id"], unique=False)
	op.create_index("idx_user_departments_department_id", "user_departments", ["department_id"], unique=False)
	op.create_index("idx_sessions_user_id", "sessions", ["user_id"], unique=False)
	op.create_index("idx_sessions_is_active", "sessions", ["is_active"], unique=False)
	op.drop_constraint(None, "print_histories", type_="foreignkey")
	op.drop_column("print_histories", "ip_address")
	op.drop_column("print_histories", "document_id")
	op.drop_constraint(None, "permits", type_="foreignkey")
	op.drop_column("permits", "document_id")
	op.create_index("idx_payments_status", "payments", ["status"], unique=False)
	op.create_index("idx_payments_organization_id", "payments", ["organization_id"], unique=False)
	op.create_index("idx_payments_invoice_id", "payments", ["invoice_id"], unique=False)
	op.create_index("idx_payments_created_at", "payments", ["created_at"], unique=False)
	op.create_index("idx_organizations_voided", "organizations", ["voided"], unique=False)
	op.create_index("idx_organizations_status", "organizations", ["status"], unique=False)
	op.create_index("idx_organizations_registration_type_id", "organizations", ["registration_type_id"], unique=False)
	op.create_index("idx_organizations_organization_type_id", "organizations", ["organization_type_id"], unique=False)
	op.create_index("idx_organizations_district_status", "organizations", ["district_id", "status"], unique=False)
	op.create_index("idx_organizations_district_id", "organizations", ["district_id"], unique=False)
	op.create_index("idx_organizations_created_by", "organizations", ["created_by"], unique=False)
	op.create_index("idx_organizations_created_at", "organizations", ["created_at"], unique=False)
	op.create_index("idx_organizations_account_id", "organizations", ["account_id"], unique=False)
	op.create_index("idx_organization_staffs_organization_id", "organization_staffs", ["organization_id"], unique=False)
	op.create_index(
		"idx_organization_sectors_organization_id", "organization_sectors", ["organization_id"], unique=False
	)
	op.create_index("idx_organization_donors_organization_id", "organization_donors", ["organization_id"], unique=False)
	op.create_index("idx_notifications_type", "notifications", ["type"], unique=False)
	op.create_index("idx_notifications_sender_id", "notifications", ["sender_id"], unique=False)
	op.create_index("idx_notifications_priority", "notifications", ["priority"], unique=False)
	op.create_index("idx_notifications_created_at", "notifications", ["created_at"], unique=False)
	op.create_index("idx_notifications_activity_id", "notifications", ["activity_id"], unique=False)
	op.create_index(
		"idx_notification_recipients_notification_id", "notification_recipients", ["notification_id"], unique=False
	)
	op.create_index("idx_notification_recipients_is_read", "notification_recipients", ["is_read"], unique=False)
	op.create_index(
		"idx_notification_recipients_account_read", "notification_recipients", ["account_id", "is_read"], unique=False
	)
	op.create_index("idx_notification_recipients_account_id", "notification_recipients", ["account_id"], unique=False)
	op.create_index("idx_members_user_id", "members", ["user_id"], unique=False)
	op.create_index("idx_members_organization_id", "members", ["organization_id"], unique=False)
	op.create_index("idx_members_is_active", "members", ["is_active"], unique=False)
	op.drop_constraint(None, "licences", type_="foreignkey")
	op.drop_column("licences", "document_id")
	op.create_index("idx_invoices_status", "invoices", ["status"], unique=False)
	op.create_index("idx_invoices_organization_id", "invoices", ["organization_id"], unique=False)
	op.create_index("idx_invoices_created_at", "invoices", ["created_at"], unique=False)
	op.create_index("idx_directors_organization_id", "directors", ["organization_id"], unique=False)
	op.create_index("idx_audit_logs_user_id", "audit_logs", ["user_id"], unique=False)
	op.create_index("idx_audit_logs_table_record", "audit_logs", ["table_name", "record_id"], unique=False)
	op.create_index("idx_audit_logs_table_created", "audit_logs", ["table_name", "created_at"], unique=False)
	op.create_index("idx_audit_logs_created_at", "audit_logs", ["created_at"], unique=False)
	op.create_index("idx_applications_voided", "applications", ["voided"], unique=False)
	op.create_index("idx_applications_type", "applications", ["type"], unique=False)
	op.create_index("idx_applications_status_type", "applications", ["status", "type"], unique=False)
	op.create_index("idx_applications_status", "applications", ["status"], unique=False)
	op.create_index("idx_applications_organization_id", "applications", ["organization_id"], unique=False)
	op.create_index("idx_applications_org_status", "applications", ["organization_id", "status"], unique=False)
	op.create_index("idx_applications_created_by", "applications", ["created_by"], unique=False)
	op.create_index("idx_applications_created_at", "applications", ["created_at"], unique=False)
	op.create_index("idx_account_verifications_verified", "account_verifications", ["verified"], unique=False)
	op.create_index("idx_account_verifications_user_id", "account_verifications", ["user_id"], unique=False)
	op.create_table(
		"printed_permits",
		sa.Column("id", sa.UUID(), autoincrement=False, nullable=False, comment="Primary key UUID for this table"),
		sa.Column("print_history_id", sa.UUID(), autoincrement=False, nullable=False),
		sa.Column("permit_id", sa.UUID(), autoincrement=False, nullable=False),
		sa.Column("created_by", sa.UUID(), autoincrement=False, nullable=True),
		sa.Column("updated_by", sa.UUID(), autoincrement=False, nullable=True),
		sa.Column("voided", sa.BOOLEAN(), autoincrement=False, nullable=False),
		sa.Column("voided_by", sa.UUID(), autoincrement=False, nullable=True),
		sa.Column("void_reason", sa.TEXT(), autoincrement=False, nullable=True),
		sa.Column(
			"created_at",
			postgresql.TIMESTAMP(timezone=True),
			server_default=sa.text("now()"),
			autoincrement=False,
			nullable=False,
		),
		sa.Column(
			"updated_at",
			postgresql.TIMESTAMP(timezone=True),
			server_default=sa.text("now()"),
			autoincrement=False,
			nullable=False,
		),
		sa.ForeignKeyConstraint(["created_by"], ["users.id"], name="printed_permits_created_by_fkey"),
		sa.ForeignKeyConstraint(["permit_id"], ["permits.id"], name="printed_permits_permit_id_fkey"),
		sa.ForeignKeyConstraint(
			["print_history_id"], ["print_histories.id"], name="printed_permits_print_history_id_fkey"
		),
		sa.ForeignKeyConstraint(["updated_by"], ["users.id"], name="printed_permits_updated_by_fkey"),
		sa.ForeignKeyConstraint(["voided_by"], ["users.id"], name="printed_permits_voided_by_fkey"),
		sa.PrimaryKeyConstraint("id", name="printed_permits_pkey"),
		sa.UniqueConstraint("id", name="printed_permits_id_key"),
	)
	op.create_table(
		"printed_licences",
		sa.Column("id", sa.UUID(), autoincrement=False, nullable=False, comment="Primary key UUID for this table"),
		sa.Column("print_history_id", sa.UUID(), autoincrement=False, nullable=False),
		sa.Column("licence_id", sa.UUID(), autoincrement=False, nullable=False),
		sa.Column("created_by", sa.UUID(), autoincrement=False, nullable=True),
		sa.Column("updated_by", sa.UUID(), autoincrement=False, nullable=True),
		sa.Column("voided", sa.BOOLEAN(), autoincrement=False, nullable=False),
		sa.Column("voided_by", sa.UUID(), autoincrement=False, nullable=True),
		sa.Column("void_reason", sa.TEXT(), autoincrement=False, nullable=True),
		sa.Column(
			"created_at",
			postgresql.TIMESTAMP(timezone=True),
			server_default=sa.text("now()"),
			autoincrement=False,
			nullable=False,
		),
		sa.Column(
			"updated_at",
			postgresql.TIMESTAMP(timezone=True),
			server_default=sa.text("now()"),
			autoincrement=False,
			nullable=False,
		),
		sa.ForeignKeyConstraint(["created_by"], ["users.id"], name="printed_licences_created_by_fkey"),
		sa.ForeignKeyConstraint(["licence_id"], ["licences.id"], name="printed_licences_licence_id_fkey"),
		sa.ForeignKeyConstraint(
			["print_history_id"], ["print_histories.id"], name="printed_licences_print_history_id_fkey"
		),
		sa.ForeignKeyConstraint(["updated_by"], ["users.id"], name="printed_licences_updated_by_fkey"),
		sa.ForeignKeyConstraint(["voided_by"], ["users.id"], name="printed_licences_voided_by_fkey"),
		sa.PrimaryKeyConstraint("id", name="printed_licences_pkey"),
		sa.UniqueConstraint("id", name="printed_licences_id_key"),
	)
	# ### end Alembic commands ###
