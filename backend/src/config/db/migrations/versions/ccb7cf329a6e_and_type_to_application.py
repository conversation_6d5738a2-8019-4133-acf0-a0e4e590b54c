"""and type to application

Revision ID: ccb7cf329a6e
Revises: b079b0a503d1
Create Date: 2025-08-30 02:57:31.377236

"""

from typing import Sequence, Union

from alembic import op


# revision identifiers, used by Alembic.
revision: str = "ccb7cf329a6e"
down_revision: Union[str, None] = "b079b0a503d1"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
	"""Upgrade schema."""
	# Add AMENDMENT_APPLICATION to the applicationtype enum
	op.execute("ALTER TYPE applicationtype ADD VALUE 'AMENDMENT_APPLICATION'")


def downgrade() -> None:
	"""Downgrade schema."""
	# Note: PostgreSQL does not support removing enum values directly
	# This would require recreating the enum type and updating all references
	pass
