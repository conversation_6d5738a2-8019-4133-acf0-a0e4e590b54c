"""added amendments table

Revision ID: c83b33f83174
Revises: ccb7cf329a6e
Create Date: 2025-08-30 13:32:00.924207

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = "c83b33f83174"
down_revision: Union[str, None] = "ccb7cf329a6e"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
	"""Upgrade schema."""
	# ### commands auto generated by Alembic - please adjust! ###
	op.create_table(
		"amendments",
		sa.Column("id", sa.UUID(), nullable=False, comment="Primary key UUID for this table"),
		sa.Column("organization_id", sa.UUID(), nullable=False),
		sa.Column("application_id", sa.UUID(), nullable=False),
		sa.Column("form_data", postgresql.JSONB(astext_type=sa.Text()), nullable=False),
		sa.Column("status", sa.Enum("DRAFT", "SUBMITTED", "PROCESSED", name="amendmentstatus"), nullable=False),
		sa.Column("remarks", sa.Text(), nullable=True),
		sa.Column("created_at", sa.TIMESTAMP(timezone=True), server_default=sa.text("now()"), nullable=False),
		sa.Column("updated_at", sa.TIMESTAMP(timezone=True), server_default=sa.text("now()"), nullable=False),
		sa.Column("created_by", sa.UUID(), nullable=True),
		sa.Column("updated_by", sa.UUID(), nullable=True),
		sa.Column("voided", sa.Boolean(), nullable=False),
		sa.Column("voided_by", sa.UUID(), nullable=True),
		sa.Column("void_reason", sa.Text(), nullable=True),
		sa.ForeignKeyConstraint(
			["application_id"],
			["applications.id"],
		),
		sa.ForeignKeyConstraint(
			["created_by"],
			["users.id"],
		),
		sa.ForeignKeyConstraint(
			["organization_id"],
			["organizations.id"],
		),
		sa.ForeignKeyConstraint(
			["updated_by"],
			["users.id"],
		),
		sa.ForeignKeyConstraint(
			["voided_by"],
			["users.id"],
		),
		sa.PrimaryKeyConstraint("id"),
		sa.UniqueConstraint("id"),
	)
	# ### end Alembic commands ###


def downgrade() -> None:
	"""Downgrade schema."""
	# ### commands auto generated by Alembic - please adjust! ###
	op.drop_table("amendments")
	# ### end Alembic commands ###
