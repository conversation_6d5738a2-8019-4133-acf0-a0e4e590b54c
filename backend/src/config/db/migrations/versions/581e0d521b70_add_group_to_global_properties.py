"""add group in global properties

Revision ID: 581e0d521b70
Revises: 0d10652569d0
Create Date: 2025-08-12 07:04:50.860216

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "581e0d521b70"
down_revision: Union[str, None] = "0d10652569d0"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
	"""Upgrade schema."""
	op.add_column("global_properties", sa.Column("group", sa.String(), nullable=False))


def downgrade() -> None:
	"""Downgrade schema."""
	op.drop_column("global_properties", "group")
