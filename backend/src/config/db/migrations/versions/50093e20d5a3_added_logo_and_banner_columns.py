"""added logo and banner columns

Revision ID: 50093e20d5a3
Revises: da03bba6e37f
Create Date: 2025-08-13 18:53:18.584628

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "50093e20d5a3"
down_revision: Union[str, None] = "da03bba6e37f"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
	"""Upgrade schema."""
	# ### commands auto generated by Alembic - please adjust! ###
	op.add_column("organizations", sa.Column("logo", sa.String(length=300), nullable=True))
	op.add_column("organizations", sa.Column("banner", sa.String(length=300), nullable=True))
	# ### end Alembic commands ###


def downgrade() -> None:
	"""Downgrade schema."""
	# ### commands auto generated by Alembic - please adjust! ###
	op.drop_column("organizations", "banner")
	op.drop_column("organizations", "logo")
	# ### end Alembic commands ###
