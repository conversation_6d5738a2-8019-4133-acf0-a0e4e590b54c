"""Add licence renewal staging and organization status enums

Revision ID: 4096ec565409
Revises: 50093e20d5a3
Create Date: 2025-08-16 14:30:33.911905

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = "4096ec565409"
down_revision: Union[str, None] = "50093e20d5a3"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
	"""Upgrade schema."""
	# Add new enum values to organizationstatus
	op.execute("ALTER TYPE organizationstatus ADD VALUE 'RENEWAL_DRAFT'")
	op.execute("ALTER TYPE organizationstatus ADD VALUE 'RENEWAL_IN_REVIEW'")
	op.execute("ALTER TYPE organizationstatus ADD VALUE 'RENEWAL_REJECTED'")

	# ### commands auto generated by Alembic - please adjust! ###
	op.create_table(
		"licence_renewals",
		sa.Column("id", sa.UUID(), nullable=False, comment="Primary key UUID for this table"),
		sa.Column("organization_id", sa.UUID(), nullable=False),
		sa.Column("application_id", sa.UUID(), nullable=False),
		sa.Column("form_data", postgresql.JSONB(astext_type=sa.Text()), nullable=False),
		sa.Column("status", sa.Enum("DRAFT", "SUBMITTED", "PROCESSED", name="licencerenewalstatus"), nullable=True),
		sa.Column("created_at", sa.TIMESTAMP(timezone=True), server_default=sa.text("now()"), nullable=False),
		sa.Column("updated_at", sa.TIMESTAMP(timezone=True), server_default=sa.text("now()"), nullable=False),
		sa.Column("created_by", sa.UUID(), nullable=True),
		sa.Column("updated_by", sa.UUID(), nullable=True),
		sa.Column("voided", sa.Boolean(), nullable=False),
		sa.Column("voided_by", sa.UUID(), nullable=True),
		sa.Column("void_reason", sa.Text(), nullable=True),
		sa.ForeignKeyConstraint(
			["application_id"],
			["applications.id"],
		),
		sa.ForeignKeyConstraint(
			["created_by"],
			["users.id"],
		),
		sa.ForeignKeyConstraint(
			["organization_id"],
			["organizations.id"],
		),
		sa.ForeignKeyConstraint(
			["updated_by"],
			["users.id"],
		),
		sa.ForeignKeyConstraint(
			["voided_by"],
			["users.id"],
		),
		sa.PrimaryKeyConstraint("id"),
		sa.UniqueConstraint("id"),
	)
	# ### end Alembic commands ###


def downgrade() -> None:
	"""Downgrade schema."""
	# ### commands auto generated by Alembic - please adjust! ###
	op.drop_table("licence_renewals")
	# ### end Alembic commands ###

	# Note: PostgreSQL doesn't support removing enum values directly
	# The new enum values will remain after downgrade
