"""added avatar column to user

Revision ID: b2acfab29d86
Revises: 6b5b028971da
Create Date: 2025-08-30 01:36:10.926621

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "b2acfab29d86"
down_revision: Union[str, None] = "6b5b028971da"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
	"""Upgrade schema."""
	# ### commands auto generated by Alembic - please adjust! ###
	op.add_column("users", sa.Column("avatar", sa.String(length=350), nullable=True))
	# ### end Alembic commands ###


def downgrade() -> None:
	"""Downgrade schema."""
	# ### commands auto generated by Alembic - please adjust! ###
	op.drop_column("users", "avatar")
	# ### end Alembic commands ###
