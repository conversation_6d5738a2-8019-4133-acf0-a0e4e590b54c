"""removed workflow stage role table

Revision ID: e1b2d131209e
Revises: 155c65693ac7
Create Date: 2025-08-09 13:43:23.521464

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = "e1b2d131209e"
down_revision: Union[str, None] = "155c65693ac7"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
	"""Upgrade schema."""
	# ### commands auto generated by Alembic - please adjust! ###
	op.drop_table("workflow_stage_roles")
	# ### end Alembic commands ###


def downgrade() -> None:
	"""Downgrade schema."""
	# ### commands auto generated by Alembic - please adjust! ###
	op.create_table(
		"workflow_stage_roles",
		sa.Column("id", sa.UUID(), autoincrement=False, nullable=False, comment="Primary key UUID for this table"),
		sa.Column("workflow_stage_id", sa.UUID(), autoincrement=False, nullable=False),
		sa.Column("role_id", sa.UUID(), autoincrement=False, nullable=False),
		sa.Column(
			"created_at",
			postgresql.TIMESTAMP(timezone=True),
			server_default=sa.text("now()"),
			autoincrement=False,
			nullable=False,
		),
		sa.Column(
			"updated_at",
			postgresql.TIMESTAMP(timezone=True),
			server_default=sa.text("now()"),
			autoincrement=False,
			nullable=False,
		),
		sa.Column("created_by", sa.UUID(), autoincrement=False, nullable=True),
		sa.Column("updated_by", sa.UUID(), autoincrement=False, nullable=True),
		sa.Column("voided", sa.BOOLEAN(), autoincrement=False, nullable=False),
		sa.Column("voided_by", sa.UUID(), autoincrement=False, nullable=True),
		sa.Column("void_reason", sa.TEXT(), autoincrement=False, nullable=True),
		sa.ForeignKeyConstraint(["created_by"], ["users.id"], name="workflow_stage_roles_created_by_fkey"),
		sa.ForeignKeyConstraint(["role_id"], ["roles.id"], name="workflow_stage_roles_role_id_fkey"),
		sa.ForeignKeyConstraint(["updated_by"], ["users.id"], name="workflow_stage_roles_updated_by_fkey"),
		sa.ForeignKeyConstraint(["voided_by"], ["users.id"], name="workflow_stage_roles_voided_by_fkey"),
		sa.ForeignKeyConstraint(
			["workflow_stage_id"], ["workflow_stages.id"], name="workflow_stage_roles_workflow_stage_id_fkey"
		),
		sa.PrimaryKeyConstraint("id", name="workflow_stage_roles_pkey"),
		sa.UniqueConstraint("id", name="workflow_stage_roles_id_key"),
	)
	# ### end Alembic commands ###
