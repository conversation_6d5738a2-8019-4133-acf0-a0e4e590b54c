"""added nationality id

Revision ID: f6debd32317f
Revises: 8dcb2d9eeee8
Create Date: 2025-09-08 07:23:22.506332

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'f6debd32317f'
down_revision: Union[str, None] = '8dcb2d9eeee8'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('permit_applicants', sa.Column('nationality_id', sa.UUID(), nullable=False))
    op.create_unique_constraint(None, 'permit_applicants', ['id'])
    op.drop_constraint('permit_applicants_passport_picture_document_id_fkey', 'permit_applicants', type_='foreignkey')
    op.drop_constraint('permit_applicants_application_letter_document_id_fkey', 'permit_applicants', type_='foreignkey')
    op.create_foreign_key(None, 'permit_applicants', 'countries', ['nationality_id'], ['id'])
    op.drop_column('permit_applicants', 'nationality')
    op.drop_column('permit_applicants', 'passport_picture_document_id')
    op.drop_column('permit_applicants', 'application_letter_document_id')
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('permit_applicants', sa.Column('application_letter_document_id', sa.UUID(), autoincrement=False, nullable=True))
    op.add_column('permit_applicants', sa.Column('passport_picture_document_id', sa.UUID(), autoincrement=False, nullable=True))
    op.add_column('permit_applicants', sa.Column('nationality', sa.VARCHAR(length=100), autoincrement=False, nullable=False))
    op.drop_constraint(None, 'permit_applicants', type_='foreignkey')
    op.create_foreign_key('permit_applicants_application_letter_document_id_fkey', 'permit_applicants', 'documents', ['application_letter_document_id'], ['id'])
    op.create_foreign_key('permit_applicants_passport_picture_document_id_fkey', 'permit_applicants', 'documents', ['passport_picture_document_id'], ['id'])
    op.drop_constraint(None, 'permit_applicants', type_='unique')
    op.drop_column('permit_applicants', 'nationality_id')
    # ### end Alembic commands ###
