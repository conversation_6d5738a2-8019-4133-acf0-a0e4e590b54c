"""add_performance_indexes

Revision ID: bc38c3525547
Revises: 1984ed7eca9b
Create Date: 2025-08-19 12:25:28.955495

"""

from typing import Sequence, Union

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "bc38c3525547"
down_revision: Union[str, None] = "1984ed7eca9b"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
	"""Add performance indexes for database optimization."""

	# HIGH PRIORITY INDEXES - Foreign Keys & Status Columns

	# Applications table indexes
	op.create_index("idx_applications_organization_id", "applications", ["organization_id"])
	op.create_index("idx_applications_status", "applications", ["status"])
	op.create_index("idx_applications_type", "applications", ["type"])
	op.create_index("idx_applications_status_type", "applications", ["status", "type"])
	op.create_index("idx_applications_created_at", "applications", ["created_at"])
	op.create_index("idx_applications_created_by", "applications", ["created_by"])
	op.create_index("idx_applications_voided", "applications", ["voided"])

	# Organizations table indexes
	op.create_index("idx_organizations_account_id", "organizations", ["account_id"])
	op.create_index("idx_organizations_status", "organizations", ["status"])
	op.create_index("idx_organizations_district_id", "organizations", ["district_id"])
	op.create_index("idx_organizations_organization_type_id", "organizations", ["organization_type_id"])
	op.create_index("idx_organizations_registration_type_id", "organizations", ["registration_type_id"])
	op.create_index("idx_organizations_created_at", "organizations", ["created_at"])
	op.create_index("idx_organizations_created_by", "organizations", ["created_by"])
	op.create_index("idx_organizations_voided", "organizations", ["voided"])

	# Users table indexes
	op.create_index("idx_users_account_id", "users", ["account_id"])
	op.create_index("idx_users_is_external", "users", ["is_external"])
	op.create_index("idx_users_verified", "users", ["verified"])
	op.create_index("idx_users_created_at", "users", ["created_at"])
	op.create_index("idx_users_created_by", "users", ["created_by"])
	op.create_index("idx_users_voided", "users", ["voided"])

	# Workflows table indexes
	op.create_index("idx_workflows_application_id", "workflows", ["application_id"])
	op.create_index("idx_workflows_template_id", "workflows", ["template_id"])
	op.create_index("idx_workflows_created_at", "workflows", ["created_at"])
	op.create_index("idx_workflows_created_by", "workflows", ["created_by"])

	# Workflow stages table indexes
	op.create_index("idx_workflow_stages_workflow_id", "workflow_stages", ["workflow_id"])
	op.create_index("idx_workflow_stages_status", "workflow_stages", ["status"])
	op.create_index("idx_workflow_stages_approved_by", "workflow_stages", ["approved_by"])
	op.create_index("idx_workflow_stages_template_stage_id", "workflow_stages", ["template_stage_id"])
	op.create_index("idx_workflow_stages_workflow_status", "workflow_stages", ["workflow_id", "status"])
	op.create_index("idx_workflow_stages_created_at", "workflow_stages", ["created_at"])

	# MEDIUM PRIORITY INDEXES - Audit & Tracking

	# Audit logs table indexes
	op.create_index("idx_audit_logs_table_record", "audit_logs", ["table_name", "record_id"])
	op.create_index("idx_audit_logs_user_id", "audit_logs", ["user_id"])
	op.create_index("idx_audit_logs_created_at", "audit_logs", ["created_at"])
	op.create_index("idx_audit_logs_table_created", "audit_logs", ["table_name", "created_at"])

	# Notifications table indexes
	op.create_index("idx_notifications_sender_id", "notifications", ["sender_id"])
	op.create_index("idx_notifications_type", "notifications", ["type"])
	op.create_index("idx_notifications_priority", "notifications", ["priority"])
	op.create_index("idx_notifications_created_at", "notifications", ["created_at"])
	op.create_index("idx_notifications_activity_id", "notifications", ["activity_id"])

	# Notification recipients indexes
	op.create_index("idx_notification_recipients_notification_id", "notification_recipients", ["notification_id"])
	op.create_index("idx_notification_recipients_account_id", "notification_recipients", ["account_id"])
	op.create_index("idx_notification_recipients_is_read", "notification_recipients", ["is_read"])
	op.create_index("idx_notification_recipients_account_read", "notification_recipients", ["account_id", "is_read"])

	# LOW PRIORITY INDEXES - Relationships

	# Organization relationship indexes
	op.create_index("idx_members_organization_id", "members", ["organization_id"])
	op.create_index("idx_members_user_id", "members", ["user_id"])
	op.create_index("idx_members_is_active", "members", ["is_active"])

	op.create_index("idx_directors_organization_id", "directors", ["organization_id"])
	op.create_index("idx_organization_staffs_organization_id", "organization_staffs", ["organization_id"])
	op.create_index("idx_organization_donors_organization_id", "organization_donors", ["organization_id"])
	op.create_index("idx_organization_sectors_organization_id", "organization_sectors", ["organization_id"])

	# User relationship indexes
	op.create_index("idx_user_departments_user_id", "user_departments", ["user_id"])
	op.create_index("idx_user_departments_department_id", "user_departments", ["department_id"])
	op.create_index("idx_user_roles_user_id", "user_roles", ["user_id"])
	op.create_index("idx_user_roles_role_id", "user_roles", ["role_id"])

	# Financial indexes
	op.create_index("idx_invoices_organization_id", "invoices", ["organization_id"])
	op.create_index("idx_invoices_status", "invoices", ["status"])
	op.create_index("idx_invoices_created_at", "invoices", ["created_at"])

	op.create_index("idx_payments_organization_id", "payments", ["organization_id"])
	op.create_index("idx_payments_invoice_id", "payments", ["invoice_id"])
	op.create_index("idx_payments_status", "payments", ["status"])
	op.create_index("idx_payments_created_at", "payments", ["created_at"])

	# Session management
	op.create_index("idx_sessions_user_id", "sessions", ["user_id"])
	op.create_index("idx_sessions_is_active", "sessions", ["is_active"])

	# Account verifications
	op.create_index("idx_account_verifications_user_id", "account_verifications", ["user_id"])
	op.create_index("idx_account_verifications_verified", "account_verifications", ["verified"])

	# COMPOSITE INDEXES for complex queries
	op.create_index("idx_applications_org_status", "applications", ["organization_id", "status"])
	op.create_index("idx_organizations_district_status", "organizations", ["district_id", "status"])


def downgrade() -> None:
	"""Remove performance indexes."""

	# Drop composite indexes
	op.drop_index("idx_organizations_district_status", "organizations")
	op.drop_index("idx_applications_org_status", "applications")

	# Drop account verifications indexes
	op.drop_index("idx_account_verifications_verified", "account_verifications")
	op.drop_index("idx_account_verifications_user_id", "account_verifications")

	# Drop session indexes
	op.drop_index("idx_sessions_is_active", "sessions")
	op.drop_index("idx_sessions_user_id", "sessions")

	# Drop financial indexes
	op.drop_index("idx_payments_created_at", "payments")
	op.drop_index("idx_payments_status", "payments")
	op.drop_index("idx_payments_invoice_id", "payments")
	op.drop_index("idx_payments_organization_id", "payments")

	op.drop_index("idx_invoices_created_at", "invoices")
	op.drop_index("idx_invoices_status", "invoices")
	op.drop_index("idx_invoices_organization_id", "invoices")

	# Drop user relationship indexes
	op.drop_index("idx_user_roles_role_id", "user_roles")
	op.drop_index("idx_user_roles_user_id", "user_roles")
	op.drop_index("idx_user_departments_department_id", "user_departments")
	op.drop_index("idx_user_departments_user_id", "user_departments")

	# Drop organization relationship indexes
	op.drop_index("idx_organization_sectors_organization_id", "organization_sectors")
	op.drop_index("idx_organization_donors_organization_id", "organization_donors")
	op.drop_index("idx_organization_staffs_organization_id", "organization_staffs")
	op.drop_index("idx_directors_organization_id", "directors")

	op.drop_index("idx_members_is_active", "members")
	op.drop_index("idx_members_user_id", "members")
	op.drop_index("idx_members_organization_id", "members")

	# Drop notification recipient indexes
	op.drop_index("idx_notification_recipients_account_read", "notification_recipients")
	op.drop_index("idx_notification_recipients_is_read", "notification_recipients")
	op.drop_index("idx_notification_recipients_account_id", "notification_recipients")
	op.drop_index("idx_notification_recipients_notification_id", "notification_recipients")

	# Drop notification indexes
	op.drop_index("idx_notifications_activity_id", "notifications")
	op.drop_index("idx_notifications_created_at", "notifications")
	op.drop_index("idx_notifications_priority", "notifications")
	op.drop_index("idx_notifications_type", "notifications")
	op.drop_index("idx_notifications_sender_id", "notifications")

	# Drop audit log indexes
	op.drop_index("idx_audit_logs_table_created", "audit_logs")
	op.drop_index("idx_audit_logs_created_at", "audit_logs")
	op.drop_index("idx_audit_logs_user_id", "audit_logs")
	op.drop_index("idx_audit_logs_table_record", "audit_logs")

	# Drop workflow stage indexes
	op.drop_index("idx_workflow_stages_created_at", "workflow_stages")
	op.drop_index("idx_workflow_stages_workflow_status", "workflow_stages")
	op.drop_index("idx_workflow_stages_template_stage_id", "workflow_stages")
	op.drop_index("idx_workflow_stages_approved_by", "workflow_stages")
	op.drop_index("idx_workflow_stages_status", "workflow_stages")
	op.drop_index("idx_workflow_stages_workflow_id", "workflow_stages")

	# Drop workflow indexes
	op.drop_index("idx_workflows_created_by", "workflows")
	op.drop_index("idx_workflows_created_at", "workflows")
	op.drop_index("idx_workflows_template_id", "workflows")
	op.drop_index("idx_workflows_application_id", "workflows")

	# Drop user indexes
	op.drop_index("idx_users_voided", "users")
	op.drop_index("idx_users_created_by", "users")
	op.drop_index("idx_users_created_at", "users")
	op.drop_index("idx_users_verified", "users")
	op.drop_index("idx_users_is_external", "users")
	op.drop_index("idx_users_account_id", "users")

	# Drop organization indexes
	op.drop_index("idx_organizations_voided", "organizations")
	op.drop_index("idx_organizations_created_by", "organizations")
	op.drop_index("idx_organizations_created_at", "organizations")
	op.drop_index("idx_organizations_registration_type_id", "organizations")
	op.drop_index("idx_organizations_organization_type_id", "organizations")
	op.drop_index("idx_organizations_district_id", "organizations")
	op.drop_index("idx_organizations_status", "organizations")
	op.drop_index("idx_organizations_account_id", "organizations")

	# Drop application indexes
	op.drop_index("idx_applications_voided", "applications")
	op.drop_index("idx_applications_created_by", "applications")
	op.drop_index("idx_applications_created_at", "applications")
	op.drop_index("idx_applications_status_type", "applications")
	op.drop_index("idx_applications_type", "applications")
	op.drop_index("idx_applications_status", "applications")
	op.drop_index("idx_applications_organization_id", "applications")
