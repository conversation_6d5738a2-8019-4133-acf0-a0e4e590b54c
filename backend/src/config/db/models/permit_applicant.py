from sqlalchemy import Column, Date, String, Text
from sqlalchemy.orm import relationship

from src.config.db import tables
from src.config.db.models.base import AuditMixin, BaseModel, foreign_key, primary_key


class PermitApplicant(BaseModel, AuditMixin):
	"""Model for permit applicants (including TEP and other permits)."""

	__tablename__ = tables.permit_applicants

	id = primary_key()
	application_id = foreign_key(f"{tables.applications}.id")
	full_name = Column(String(255), nullable=False)
	nationality_id = foreign_key(f"{tables.countries}.id")
	passport_number = Column(String(50), nullable=False)
	position = Column(String(255), nullable=True)
	department = Column(String(255), nullable=True)
	employment_start_date = Column(Date, nullable=True)
	employment_end_date = Column(Date, nullable=True)
	letter_document_id = foreign_key(f"{tables.documents}.id", nullable=True)
	passport_document_id = foreign_key(f"{tables.documents}.id", nullable=True)

	# Relationships
	application = relationship(
		"Application",
		back_populates="permit_applicants",
		uselist=False,
		foreign_keys=[application_id],
	)

	letter_document = relationship(
		"Document",
		foreign_keys=[letter_document_id],
		uselist=False,
	)

	passport_document = relationship(
		"Document",
		foreign_keys=[passport_document_id],
		uselist=False,
	)

	def __repr__(self):
		return f"<PermitApplicant(id={self.id}, full_name={self.full_name}, nationality={self.nationality})>"
