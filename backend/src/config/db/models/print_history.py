from sqlalchemy import Column, Integer, String
from sqlalchemy.orm import relationship

from src.config.db import tables
from src.config.db.models.base import AuditMixin, BaseModel, foreign_key, primary_key


class PrintHistory(BaseModel, AuditMixin):
	"""Model for print history of documents."""

	__tablename__ = tables.print_histories

	id = primary_key()
	count = Column(Integer, nullable=True)
	document_id = foreign_key(f"{tables.documents}.id")
	ip_address = Column(String(15), nullable=True)

	# Relationships
	document = relationship(
		"Document",
		back_populates="print_histories",
	)

	def __repr__(self):
		return f"<PrintHistory(id={self.id}, count={self.count})>"
