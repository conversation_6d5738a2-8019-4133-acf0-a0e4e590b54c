from enum import Enum

from sqlalchemy import Column
from sqlalchemy import Enum as SQLEnum
from sqlalchemy import String
from sqlalchemy.orm import relationship

from src.config.db import tables
from src.config.db.models.account import AccountType
from src.config.db.models.base import (
	AuditMixin,
	BaseModel,
	InvitationStatus,
	foreign_key,
	primary_key,
)


class InviteeType(str, Enum):
	INTERNAL = "INTERNAL"
	EXTERNAL = "EXTERNAL"


class ActivityInvitation(BaseModel, AuditMixin):
	__tablename__ = tables.activity_invitations

	id = primary_key()
	activity_id = foreign_key(f"{tables.activities}.id")
	status = Column(SQLEnum(InvitationStatus), nullable=False, default=InvitationStatus.PENDING)
	type = Column(SQLEnum(InviteeType), nullable=False)
	account_id = foreign_key(f"{tables.accounts}.id", nullable=True)
	email = Column(String(100))
	name = Column(String(100))
	account_type = Column(SQLEnum(AccountType), nullable=False)

	# Relationships
	attendances = relationship(
		"Attendance",
		back_populates="invitation",
		uselist=True,
		foreign_keys="Attendance.invitation_id",
		cascade="all, delete-orphan",
	)
	activity = relationship(
		"Activity",
		back_populates="invitations",
		uselist=False,
		foreign_keys=[activity_id],
	)
	account = relationship(
		"Account",
		back_populates="activity_invitations",
		uselist=False,
		foreign_keys=[account_id],
	)

	def __repr__(self):
		return f"<ActivityInvitation(id={self.id}, status={self.status})>"
