import enum

from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON>n, Enum, Float, String
from sqlalchemy.orm import relationship

from src.config.db import tables
from src.config.db.models.base import AuditMixin, BaseModel, primary_key


class CurrencyStatus(enum.Enum):
	ACTIVE = "ACTIVE"
	INACTIVE = "INACTIVE"


class Currency(BaseModel, AuditMixin):
	__tablename__ = tables.currencies

	id = primary_key()
	name = Column(String(50), unique=True, nullable=False)
	code = Column(String(5), nullable=False)
	exchange_rate = Column(Float, nullable=False)
	is_default = Column(Boolean, nullable=False, default=False)
	status = Column(Enum(CurrencyStatus), default=CurrencyStatus.ACTIVE)

	# Relationships
	funding_sources = relationship(
		"FundingSource",
		back_populates="currency",
		cascade="all, delete-orphan",
		foreign_keys="FundingSource.currency_id",
	)
	fees = relationship(
		"Fee",
		back_populates="currency",
		foreign_keys="Fee.currency_id",
		cascade="all, delete-orphan",
	)
	organization_donors = relationship(
		"OrganizationDonor",
		back_populates="currency",
		foreign_keys="OrganizationDonor.currency_id",
		cascade="all, delete-orphan",
	)

	def __repr__(self):
		return f"<Currency(id={self.id}, name={self.name}, code={self.code})>"
