from sqlalchemy import Column, String
from sqlalchemy.orm import relationship

from src.config.db.tables import tables

from .base import AuditMixin, BaseModel, primary_key


class Document(BaseModel, AuditMixin):
	__tablename__ = tables.documents

	id = primary_key()
	filename = Column(String, nullable=False)
	mimetype = Column(String, nullable=False)
	location = Column(String, nullable=False)
	original_name = Column(String, nullable=False)
	size = Column(String, nullable=False)

	# Relationships
	supporting_documents = relationship(
		"SupportingDocument",
		back_populates="document",
		cascade="all, delete-orphan",
		foreign_keys="SupportingDocument.document_id",
	)
	invoice_documents = relationship(
		"InvoiceDocument",
		back_populates="document",
		cascade="all, delete-orphan",
		foreign_keys="InvoiceDocument.document_id",
	)
	complaint_attachments = relationship(
		"ComplaintAttachment",
		back_populates="document",
		cascade="all, delete-orphan",
		foreign_keys="ComplaintAttachment.document_id",
	)
	application_documents = relationship(
		"ApplicationDocument",
		back_populates="document",
		cascade="all, delete-orphan",
		foreign_keys="ApplicationDocument.document_id",
	)
	print_histories = relationship(
		"PrintHistory",
		back_populates="document",
		cascade="all, delete-orphan",
		foreign_keys="PrintHistory.document_id",
	)
	licence = relationship(
		"Licence",
		back_populates="document",
		uselist=False,
		foreign_keys="Licence.document_id",
	)
