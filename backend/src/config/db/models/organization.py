from enum import Enum

from sqlalchemy import Column, Float, String, Text
from sqlalchemy import Enum as SQLEnum
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.orm import relationship

from src.config.db import tables

from .base import AuditMixin, BaseModel, foreign_key, primary_key


class OrganizationStatus(str, Enum):
	"""Status for an organization"""

	DRAFT = "DRAFT"
	"""Application isn't submitted"""

	PENDING = "PENDING"
	"""An application is yet to be reviewed"""

	REVIEW = "REVIEW"
	"""An application going through review process"""

	SUSPENDED = "SUSPENDED"
	"""The Organization has been suspended"""

	REGISTERED = "REGISTERED"
	"""Organization is active"""

	RENEWAL_DRAFT = "RENEWAL_DRAFT"
	"""Organization has a draft license renewal application"""

	RENEWAL_IN_REVIEW = "RENEWAL_IN_REVIEW"
	"""Organization's license renewal is under review"""

	RENEWAL_REJECTED = "RENEWAL_REJECTED"
	"""Organization's license renewal has been rejected"""

	INACTIVE = "INACTIVE"
	"""Organization is no longer active"""


class Organization(BaseModel, AuditMixin):
	"""Organization model representing an organization in the system."""

	__tablename__ = tables.organizations

	id = primary_key()
	name = Column(Text, nullable=False, unique=True)
	abbreviation = Column(String(20), nullable=False)
	organization_type_id = foreign_key(f"{tables.loadable_items}.id", nullable=False)
	registration_number = Column(String(20), nullable=False, unique=True)
	district_id = foreign_key(f"{tables.districts}.id", nullable=False)
	financial_start_month = Column(String(10), nullable=False)
	financial_end_month = Column(String(10), nullable=False)
	charity_number = Column(String(50), unique=True, nullable=True)
	annual_income = Column(Float, default=0.0)
	registration_type_id = foreign_key(f"{tables.loadable_items}.id", nullable=False)
	account_id = foreign_key("accounts.id", nullable=False)
	biography = Column(Text, nullable=True)
	vision = Column(Text, nullable=True)
	motto = Column(Text, nullable=True)
	objectives = Column(JSONB, nullable=True)
	logo = Column(String(300), nullable=True)
	banner = Column(String(300), nullable=True)
	status = Column(SQLEnum(OrganizationStatus), default=OrganizationStatus.DRAFT)

	# Relationships
	account = relationship("Account", back_populates="organization", foreign_keys=[account_id])
	members = relationship(
		"Member",
		back_populates="organization",
		foreign_keys="Member.organization_id",
		cascade="all, delete-orphan",
	)
	location_activities = relationship(
		"LocationActivity",
		back_populates="organization",
		foreign_keys="LocationActivity.organization_id",
		cascade="all, delete-orphan",
	)
	member_invitations = relationship(
		"MemberInvitation",
		back_populates="organization",
		foreign_keys="MemberInvitation.organization_id",
		cascade="all, delete-orphan",
	)
	versions = relationship(
		"OrganizationVersion",
		back_populates="organization",
		cascade="all, delete-orphan",
	)
	district = relationship(
		"District",
		back_populates="organizations",
		uselist=False,
		foreign_keys=[district_id],
	)
	target_groups = relationship(
		"TargetGroup",
		back_populates="organization",
		foreign_keys="TargetGroup.organization_id",
		cascade="all, delete-orphan",
	)
	supporting_documents = relationship(
		"SupportingDocument",
		back_populates="organization",
		foreign_keys="SupportingDocument.organization_id",
		cascade="all, delete-orphan",
	)
	type = relationship(
		"LoadableItem",
		back_populates="organizations",
		uselist=False,
		foreign_keys=[organization_type_id],
	)
	registration_type = relationship(
		"LoadableItem",
		back_populates="organization_types",
		uselist=False,
		foreign_keys=[registration_type_id],
	)
	permits = relationship(
		"Permit",
		back_populates="organization",
		foreign_keys="Permit.organization_id",
		cascade="all, delete-orphan",
	)
	payments = relationship("Payment", back_populates="organization", foreign_keys="Payment.organization_id")
	staffs = relationship(
		"OrganizationStaff",
		back_populates="organization",
		foreign_keys="OrganizationStaff.organization_id",
		cascade="all, delete-orphan",
	)
	sectors = relationship(
		"OrganizationSector",
		back_populates="organization",
		foreign_keys="OrganizationSector.organization_id",
		cascade="all, delete-orphan",
	)
	projects = relationship(
		"OrganizationProject",
		back_populates="organization",
		foreign_keys="OrganizationProject.organization_id",
		cascade="all, delete-orphan",
	)
	auditors = relationship(
		"OrganizationAuditor",
		back_populates="organization",
		foreign_keys="OrganizationAuditor.organization_id",
		cascade="all, delete-orphan",
	)
	licences = relationship("Licence", back_populates="organization", foreign_keys="Licence.organization_id")
	licence_renewals = relationship(
		"LicenceRenewal",
		back_populates="organization",
		foreign_keys="LicenceRenewal.organization_id",
		cascade="all, delete-orphan",
	)
	invoices = relationship(
		"Invoice",
		back_populates="organization",
		foreign_keys="Invoice.organization_id",
		cascade="all, delete-orphan",
	)
	funding_sources = relationship(
		"FundingSource",
		back_populates="organization",
		foreign_keys="FundingSource.organization_id",
		cascade="all, delete-orphan",
	)
	directors = relationship(
		"Director",
		back_populates="organization",
		foreign_keys="Director.organization_id",
		cascade="all, delete-orphan",
	)
	lodged_complaints = relationship(
		"Complaint",
		back_populates="organization",
		foreign_keys="Complaint.organization_id",
		cascade="all, delete-orphan",
	)
	applications = relationship(
		"Application",
		back_populates="organization",
		foreign_keys="Application.organization_id",
		cascade="all, delete-orphan",
	)
	bank_details = relationship(
		"BankDetail",
		back_populates="organization",
		foreign_keys="BankDetail.organization_id",
		cascade="all, delete-orphan",
	)
	donors = relationship(
		"OrganizationDonor",
		back_populates="organization",
		foreign_keys="OrganizationDonor.organization_id",
		cascade="all, delete-orphan",
	)
	amendments = relationship(
		"Amendment",
		back_populates="organization",
		uselist=True,
		foreign_keys="Amendment.organization_id",
		cascade="all, delete-orphan",
	)

	@property
	def users(self):
		"""Get all active users in this organization"""
		return [membership.user for membership in self.user_memberships if membership.is_active]

	def __repr__(self):
		return f"<Organization(id={self.id}, name={self.name}, account_id={self.account_id})>"
