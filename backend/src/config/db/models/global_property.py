from sqlalchemy import Column, String

from src.config.db import tables
from src.config.db.models.base import AuditMixin, BaseModel, primary_key


class GlobalProperty(BaseModel, AuditMixin):
	__tablename__ = tables.global_properties

	id = primary_key()
	group = Column(String, nullable=False)
	property = Column(String, nullable=False)
	value = Column(String, nullable=False)

	def __repr__(self):
		return f"<GlobalProperty(group={self.group}, property={self.property}, value={self.value})>"
