from enum import Enum

from sqlalchemy import Column, Enum as SQL<PERSON><PERSON>, Text
from sqlalchemy.dialects.postgresql import <PERSON><PERSON>N<PERSON>
from sqlalchemy.orm import relationship

from src.config.db import tables
from src.config.db.models.base import BaseModel, foreign_key, primary_key
from src.config.db.models.concerns.audit_concern import AuditMixin


class AmendmentStatus(str, Enum):
	"""Status for licence renewal staging data"""

	DRAFT = "DRAFT"
	"""Renewal data is being prepared"""

	SUBMITTED = "SUBMITTED"
	"""Renewal has been submitted for review"""

	PROCESSED = "PROCESSED"
	"""Renewal has been processed and applied to organization"""


class Amendment(BaseModel, AuditMixin):
	__tablename__ = tables.amendments

	id = primary_key()
	organization_id = foreign_key(f"{tables.organizations}.id")
	application_id = foreign_key(f"{tables.applications}.id")
	form_data = Column(JSONB, nullable=False)
	status = Column(SQLEnum(AmendmentStatus), nullable=False, default=AmendmentStatus.DRAFT)
	remarks = Column(Text, nullable=True)

	# Relationships
	organization = relationship(
		"Organization",
		back_populates="amendments",
		foreign_keys=[organization_id],
		uselist=False,
	)
	application = relationship(
		"Application",
		back_populates="amendment",
		foreign_keys=[application_id],
		uselist=False,
	)

	def __repr__(self):
		return f"<Amendment organization_id={self.organization_id} application_id={self.application_id} status={self.status}>"
