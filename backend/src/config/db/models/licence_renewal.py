from enum import Enum

from sqlalchemy import Column
from sqlalchemy import Enum as SQLEnum
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.orm import relationship

from src.config.db import tables
from src.config.db.models.base import AuditMixin, BaseModel, foreign_key, primary_key


class LicenceRenewalStatus(str, Enum):
	"""Status for licence renewal staging data"""

	DRAFT = "DRAFT"
	"""Renewal data is being prepared"""

	SUBMITTED = "SUBMITTED"
	"""Renewal has been submitted for review"""

	PROCESSED = "PROCESSED"
	"""Renewal has been processed and applied to organization"""


class LicenceRenewal(BaseModel, AuditMixin):
	"""Staging table for license renewal form data"""

	__tablename__ = tables.licence_renewals

	id = primary_key()
	organization_id = foreign_key(f"{tables.organizations}.id")
	application_id = foreign_key(f"{tables.applications}.id")

	form_data = Column(JSONB, nullable=False)
	status = Column(SQLEnum(LicenceRenewalStatus), default=LicenceRenewalStatus.DRAFT)

	# Relationships
	organization = relationship(
		"Organization",
		back_populates="licence_renewals",
		uselist=False,
		foreign_keys=[organization_id],
	)
	application = relationship(
		"Application",
		back_populates="licence_renewal",
		uselist=False,
		foreign_keys=[application_id],
	)

	def __repr__(self):
		return f"<LicenceRenewal(id={self.id}, organization_id={self.organization_id}, application_id={self.application_id}, status={self.status})>"
