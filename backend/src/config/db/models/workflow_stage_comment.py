from sqlalchemy import Column, Text
from sqlalchemy.orm import relationship

from src.config.db import tables
from src.config.db.models.base import BaseModel, primary_key, foreign_key
from src.config.db.models.concerns.audit_concern import AuditMixin


class WorkflowStageComment(BaseModel, AuditMixin):
	__tablename__ = tables.workflow_stage_comments

	id = primary_key()
	text = Column(Text, nullable=True)
	workflow_stage_id = foreign_key(f"{tables.workflow_stages}.id")

	# relationships
	workflow_stage = relationship(
		"WorkflowStage",
		back_populates="comments",
		uselist=False,
	)
