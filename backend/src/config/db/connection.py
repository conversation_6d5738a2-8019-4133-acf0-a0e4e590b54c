"""
Database connection and session management.
"""

import os
from typing import Any, Generator

from dotenv import load_dotenv
from sqlalchemy import create_engine
from sqlalchemy.orm import Session, sessionmaker

load_dotenv()

DATABASE_URL = os.getenv("DATABASE_URL")

engine = create_engine(
	DATABASE_URL,
	echo=(os.getenv("ENVIRONMENT", "development") == "development"),  # Only echo in development
	pool_size=25,  # Increased from 10 for high concurrency
	max_overflow=50,  # Increased from 20 for peak loads
	pool_pre_ping=True,  # Validate connections before use
	pool_recycle=3600,  # Recycle connections every hour
	pool_timeout=30,  # Wait 30 seconds for connection from pool
	pool_reset_on_return="commit",  # Reset connection state on return
)

SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


def get_db() -> Generator[Session, Any, None]:
	"""
	Get a database session.

	Yields:
	        Session: SQLAlchemy database session

	Usage:
	        # As dependency injection (FastAPI)
	        def get_user(db: Session = Depends(get_db)):
	                return db.query(User).all()

	        # Manual usage
	        with get_db() as db:
	                users = db.query(User).all()
	"""
	db = SessionLocal()
	try:
		yield db
	finally:
		db.close()


def get_db_session() -> Session:
	"""
	Get a database session for manual management.
	Remember to close the session when done.

	Returns:
	        Session: SQLAlchemy database session

	Usage:
	        db = get_db_session()
	        try:
	                users = db.query(User).all()
	        finally:
	                db.close()
	"""
	return SessionLocal()
