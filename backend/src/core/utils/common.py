import re
from enum import Enum
from typing import List, Optional, Type, TypeVar
from uuid import UUID

from pydantic import BaseModel
from sqlalchemy import ColumnElement
from sqlalchemy.orm import Query

E = TypeVar("E", bound=Enum)


def apply_enum_filter(query: Query, column: ColumnElement, csv_string: Optional[str], enum_cls: Type[E]) -> Query:
	"""Apply a CSV string filter for Enum columns safely."""
	if not csv_string:
		return query

	members: List[E] = []
	for raw in csv_string.split(","):
		name = raw.strip().upper()
		if not name:
			continue
		try:
			members.append(enum_cls[name])
		except KeyError:
			continue

	if not members:
		return query

	return query.filter(column.in_(members))


def is_valid_username(username: str):
	"""
	Validates a username based on the following rules:
	- Only contains 0-9, a-z, A-Z, underscore (_), and dot (.)
	- Contains at most one underscore and one dot
	- Cannot contain both underscore and dot in the same string
	- Cannot start or end with _ or .

	Valid examples: hamble_123, hamble, example123, user_name, user.name
	Invalid examples: _username, username_, .username, username., user._name
	"""

	if not re.fullmatch(r"^[a-zA-Z0-9_.]+$", username):
		return False

	if username.startswith("_") or username.endswith("_") or username.startswith(".") or username.endswith("."):
		return False

	if "_" in username and "." in username:
		return False

	if username.count("_") > 1 or username.count(".") > 1:
		return False

	return True


def capitalize_words(name: str) -> str:
	"""Capitalize each word in a given word properly."""
	return " ".join(word.capitalize() for word in name.split())


def get_clean_filters(filters: dict) -> dict:
	return {k: v for k, v in filters.dict(exclude_unset=True).items() if v not in (None, "")}


def super_to_optional(model: Type[BaseModel]):
	"""
	Convert a Pydantic model to a new model where all fields are optional.

	This function takes a Pydantic model class and returns a new model class
	with the same fields, but all fields are made optional. The new model
	class is dynamically created and inherits from `BaseModel`.

	Args:
	    model (Type[BaseModel]): The Pydantic model class to convert.

	Returns:
	    Type[BaseModel]: A new Pydantic model class with all fields optional.
	"""
	annotations = {name: Optional[field.annotation] for name, field in model.model_fields.items()}
	return type(
		f"Optional{model.__name__}",
		(BaseModel,),
		{
			"__annotations__": annotations,
			**{name: None for name in model.model_fields},
		},
	)


def is_password_strong(password: str) -> bool:
	"""Validate password strength - matches frontend validation exactly"""
	import re

	if len(password) < 8:
		return False

	patterns = [r"[a-z]", r"\d", r'[!@#$%^&*()_+\-=\[\]{};\':"\\|,.<>\/?]']

	return all(re.search(pattern, password) for pattern in patterns)


def serialize_for_json(data: dict) -> dict:
	"""Convert UUID objects to strings for JSON serialization"""

	def convert_value(value):
		if isinstance(value, UUID):
			return str(value)
		elif isinstance(value, dict):
			return {k: convert_value(v) for k, v in value.items()}
		elif isinstance(value, list):
			return [convert_value(item) for item in value]
		else:
			return value

	return convert_value(data)
