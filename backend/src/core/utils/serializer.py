from src.config.db.models import Complaint, Country
from src.config.db.models.district import District
from src.config.db.models.loadable_item import LoadableItem
from src.config.db.models.organization import Organization
from src.config.db.models.region import Region
from src.core.dtos.organization_dtos import OrganizationDto
from src.modules.activities.activity_schema import ActivityDto
from src.modules.activities.activity_invitation_schema import ActivityInvitationDto
from src.modules.complaint.complaint_schema import ComplaintDTO
from src.modules.settings.settings_schema import CountryDto, DistrictDto, LoadableItemDto, RegionDto


def to_country_dto(row: Country) -> CountryDto:
	return CountryDto(id=row.id, name=row.name, dial_code=row.dial_code, short_code=row.short_code, flag=row.flag)


def to_region_dto(row: Region) -> RegionDto:
	return RegionDto(id=row.id, name=row.name, code=row.code, created_at=row.created_at, updated_at=row.updated_at)


def to_district_dto(row: District) -> DistrictDto:
	return DistrictDto(
		id=row.id,
		name=row.name,
		code=row.code,
		region_id=row.region_id,
		region_name=row.region.name if row.region else None,
		created_at=row.created_at,
		updated_at=row.updated_at,
	)


def to_loadable_item_dto(row: LoadableItem) -> LoadableItemDto:
	return LoadableItemDto(
		id=row.id,
		type=row.type,
		code=row.code,
		display_value=row.display_value,
		description=row.description,
		created_at=row.created_at,
		updated_at=row.updated_at,
	)


def to_organization_dto(row: Organization) -> OrganizationDto:
	"""Convert Organization model to OrganizationDto."""

	return OrganizationDto(
		id=row.id,
		name=row.name,
		abbreviation=row.abbreviation,
		organization_type_id=row.organization_type_id,
		registration_number=row.registration_number,
		district_id=row.district_id,
		financial_start_month=row.financial_start_month,
		financial_end_month=row.financial_end_month,
		charity_number=row.charity_number,
		annual_income=row.annual_income,
		registration_type_id=row.registration_type_id,
		biography=row.biography,
		vision=row.vision,
		motto=row.motto,
		objectives=row.objectives,
		status=row.status,
		account_id=row.account_id,
		created_at=row.created_at,
		updated_at=row.updated_at,
		created_by=row.created_by,
		updated_by=row.updated_by,
		# district=row.district if row.district else None,
		# type=row.type if row.type else None,
		# creator=row.created_by_user if row.created_by_user else None
	)


def to_activity_dto(activity) -> ActivityDto:
	return ActivityDto(
		id=activity.id,
		title=activity.title,
		category_id=activity.category_id,
		visibility=activity.visibility,
		venue=activity.venue,
		district_id=activity.district_id,
		longitude=activity.longitude,
		latitude=activity.latitude,
		map_pin=activity.map_pin,
		start_date=activity.start_date,
		end_date=activity.end_date,
		summary=activity.summary,
		created_at=activity.created_at,
		updated_at=activity.updated_at,
	)


def to_activity_invitation_dto(row) -> ActivityInvitationDto:
	return ActivityInvitationDto(
		id=row.id,
		activity_id=row.activity_id,
		status=row.status,
		type=row.type,
		account_id=row.account_id,
		email=row.email,
		name=row.name,
		account_type=row.account_type,
		created_at=row.created_at.isoformat() if row.created_at else None,
		updated_at=row.updated_at.isoformat() if row.updated_at else None,
	)


def to_complaint_dto(row: Complaint) -> ComplaintDTO:
	return ComplaintDTO(
		id=row.id,
		title=row.title,
		summary=row.summary,
		priority=row.priority,
		organization_id=row.organization_id,
		is_anonymous=row.is_anonymous,
		category_id=row.category_id,
		complainant_id=row.complainant_id,
		created_at=row.created_at,
		updated_at=row.updated_at,
	)
