from contextvars import ContextVar
from sqlalchemy.orm import Session
from typing import Optional

db_session_context: ContextVar[Optional[Session]] = ContextVar("db_session", default=None)


def get_db_session() -> Optional[Session]:
	"""Get the current database session from context"""
	return db_session_context.get()


def set_db_session(session: Session) -> None:
	"""Set the current database session in context"""
	db_session_context.set(session)


def clear_db_session() -> None:
	"""Clear the current database session from context"""
	db_session_context.set(None)


def require_db_session() -> Session:
	"""Get database session from context, raise error if not available"""
	session = get_db_session()
	if not session:
		raise RuntimeError("No database session available in context. Ensure middleware is properly configured.")
	return session
