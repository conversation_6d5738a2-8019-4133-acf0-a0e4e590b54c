from datetime import datetime
from typing import Any, Dict, List, Optional
from uuid import UUID

from src.config.db.models.account import Account
from src.config.db.models.notification import Notification, NotificationPriority, NotificationType
from src.config.db.models.notification_recipient import NotificationRecipient
from src.core.base.base_repository import BaseRepository
from src.core.logger.internal_logger import get_logger
from src.core.services.email_service import EmailMessage, EmailRecipient, EmailService, EmailTemplate


class NotificationService(BaseRepository):
	"""
	Comprehensive notification service that handles:
	- In-app notifications
	- Email notifications
	- Workflow-specific notifications
	"""

	def __init__(self):
		super().__init__()

		self.logger = get_logger(__name__)
		self.email_service = EmailService()

	async def create_notification(
		self,
		title: str,
		message: str,
		recipient_account_ids: List[UUID],
		notification_type: NotificationType = NotificationType.INFO,
		priority: NotificationPriority = NotificationPriority.MEDIUM,
		activity_id: Optional[UUID] = None,
		sender_id: Optional[UUID] = None,
		send_email: bool = True,
		email_template: Optional[EmailTemplate] = None,
		email_variables: Optional[Dict[str, Any]] = None,
	) -> Notification:
		"""Create a notification and optionally send email"""
		try:
			notification = Notification(
				title=title,
				message=message,
				type=notification_type,
				priority=priority,
				activity_id=activity_id,
				sender_id=sender_id or (self.current_user.id if hasattr(self, "current_user") else None),
				created_by=self.current_user.id if hasattr(self, "current_user") else None,
			)

			self.db.add(notification)
			self.db.flush()

			recipients = []
			email_recipients = []

			for account_id in recipient_account_ids:
				recipient = NotificationRecipient(notification_id=notification.id, account_id=account_id, is_read=False)
				recipients.append(recipient)

				if send_email:
					account = self.db.query(Account).filter(Account.id == account_id).first()
					if account and account.user and account.user.email:
						email_recipients.append(
							EmailRecipient(
								email=account.user.email, name=f"{account.user.first_name} {account.user.last_name}"
							)
						)

			self.db.add_all(recipients)
			self.db.flush()

			if send_email and email_recipients and email_template:
				try:
					email_message = EmailMessage(
						recipients=email_recipients,
						subject=title,
						template=email_template,
						template_variables=email_variables or {},
					)
					self.email_service.send_email(email_message)
				except Exception as e:
					self.logger.error(f"Failed to send email notification: {e}")
					# Don't fail the whole notification creation if email fails

			self.db.commit()

			return notification

		except Exception as e:
			self.db.rollback()
			self.logger.error(f"Failed to create notification: {e}")
			raise

	async def create_workflow_stage_notification(
		self,
		workflow_id: UUID,
		stage_name: str,
		application_code: str,
		organization_name: str,
		recipient_account_ids: List[UUID],
		action_url: str,
		notification_type: str = "STAGE_ASSIGNMENT",
	) -> Optional[Notification]:
		"""Create workflow-specific stage assignment notification"""

		notification_configs = {
			"STAGE_ASSIGNMENT": {
				"title": f"New Review Required - {application_code}",
				"message": f"Application for {organization_name} is waiting for your review at stage: {stage_name}",
				"type": NotificationType.ACTIVITY,
				"priority": NotificationPriority.HIGH,
				"email_template": EmailTemplate.ORGANIZATION_APPLICATION_REVIEW,
			},
			"STAGE_APPROVED": {
				"title": f"Application Approved - {application_code}",
				"message": f"Application for {organization_name} has been approved and moved to the next stage",
				"type": NotificationType.SUCCESS,
				"priority": NotificationPriority.MEDIUM,
				"email_template": None,
			},
			"STAGE_REJECTED": {
				"title": f"Application Rejected - {application_code}",
				"message": f"Application for {organization_name} has been rejected at stage: {stage_name}",
				"type": NotificationType.ALERT,
				"priority": NotificationPriority.HIGH,
				"email_template": EmailTemplate.REJECTION_FOR_ORGANIZATION_APPLICATION,
			},
			"WORKFLOW_COMPLETED": {
				"title": f"Application Completed - {application_code}",
				"message": f"Application for {organization_name} has been successfully completed",
				"type": NotificationType.SUCCESS,
				"priority": NotificationPriority.MEDIUM,
				"email_template": None,
			},
		}

		config = notification_configs.get(notification_type)
		if not config:
			self.logger.warning(f"Unknown notification type: {notification_type}")
			return None

		email_variables = {
			"organization_name": organization_name,
			"application_code": application_code,
			"stage_name": stage_name,
			"approve_url": action_url,
		}

		return await self.create_notification(
			title=config["title"],
			message=config["message"],
			recipient_account_ids=recipient_account_ids,
			notification_type=config["type"],
			priority=config["priority"],
			send_email=config["email_template"] is not None,
			email_template=config["email_template"],
			email_variables=email_variables,
		)

	async def create_workflow_reminder_notifications(self, days_threshold: int = 3) -> List[Notification]:
		"""Create reminder notifications for workflows pending for too long"""
		try:
			from datetime import timedelta

			from src.config.db.models.application import Application
			from src.config.db.models.template_stage_role import TemplateStageRole
			from src.config.db.models.workflow import Workflow
			from src.config.db.models.workflow_stage import WorkflowStage, WorkflowStageStatus

			cutoff_date = datetime.utcnow() - timedelta(days=days_threshold)

			overdue_stages = (
				self.db.query(WorkflowStage)
				.join(Workflow)
				.join(Application)
				.filter(WorkflowStage.status == WorkflowStageStatus.IN_REVIEW, WorkflowStage.created_at <= cutoff_date)
				.all()
			)

			notifications = []

			for stage in overdue_stages:
				stage_roles = (
					self.db.query(TemplateStageRole)
					.filter(
						TemplateStageRole.template_stage_id == stage.template_stage_id,
						TemplateStageRole.is_active,
					)
					.all()
				)

				recipient_account_ids = []
				for stage_role in stage_roles:
					for user in stage_role.role.users:
						if user.account:
							recipient_account_ids.append(user.account.id)

				if recipient_account_ids:
					notification = await self.create_workflow_stage_notification(
						workflow_id=stage.workflow_id,
						stage_name=stage.template_stage.name,
						application_code=stage.workflow.application.code,
						organization_name=stage.workflow.application.organization.name,
						recipient_account_ids=recipient_account_ids,
						action_url=f"{self.settings.FRONTEND_URL}/workflows/{stage.workflow_id}",
						notification_type="STAGE_REMINDER",
					)

					if notification:
						notifications.append(notification)

			return notifications

		except Exception as e:
			self.logger.error(f"Failed to create reminder notifications: {e}")
			return []

	async def mark_notifications_as_read(self, notification_ids: List[UUID], account_id: UUID) -> bool:
		"""Mark notifications as read for a specific account"""
		try:
			updated = (
				self.db.query(NotificationRecipient)
				.filter(
					NotificationRecipient.notification_id.in_(notification_ids),
					NotificationRecipient.account_id == account_id,
				)
				.update({"is_read": True, "read_at": datetime.utcnow()}, synchronize_session=False)
			)

			self.db.commit()
			return updated > 0

		except Exception as e:
			self.logger.error(f"Failed to mark notifications as read: {e}")
			self.db.rollback()
			return False
