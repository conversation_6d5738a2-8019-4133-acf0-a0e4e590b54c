# audit_logger.py
import contextvars
import json
import logging
from datetime import date, datetime, time
from typing import Any

from sqlalchemy import event, inspect
from sqlalchemy.orm import Session

from src.config.db import tables
from src.config.db.models.audit_log import AuditLog
from src.core.context.auth_context import current_user_context

request_context: contextvars.ContextVar = contextvars.ContextVar("request_context", default={})


def set_request_context(**kwargs):
	request_context.set(kwargs)


def get_request_context() -> dict:
	return request_context.get({})


def _serialize_value(value: Any) -> Any:
	"""
	Serialize a single value for JSON storage.
	Handles basic types, dates, and fallbacks to string.
	"""
	if isinstance(value, (str, int, float, bool, type(None))):
		return value
	elif isinstance(value, (datetime, date, time)):
		return value.isoformat()
	else:
		return str(value)


def _serialize_object(obj) -> dict:
	"""
	Serialize all column values of an ORM object (used for INSERT/DELETE).
	Excludes relationships.
	"""
	result = {}
	for col in obj.__table__.columns:
		col_name = col.name
		value = getattr(obj, col_name, None)
		result[col_name] = _serialize_value(value)
	return result


@event.listens_for(Session, "after_flush")
def after_flush(session: Session, flush_context: Any):
	"""
	Listen for all ORM changes and log them to AuditLog.
	"""
	for obj in session.new:
		_log_change(session, obj, "LOGIN" if obj.__tablename__ == tables.sessions else "CREATE")
	for obj in session.dirty:
		_log_change(session, obj, "UPDATE")
	for obj in session.deleted:
		_log_change(session, obj, "DELETE")


def _log_change(session: Session, obj, action: str):
	"""
	Log a single change (INSERT, UPDATE, DELETE) to the audit trail.
	"""
	# Prevent recursion: skip logging AuditLog changes
	if isinstance(obj, AuditLog):
		return

	# Get contextual info from request
	ctx = get_request_context()
	user = current_user_context.get()
	ip_address = ctx.get("ip_address")
	# endpoint = ctx.get("endpoint")

	table_name = obj.__tablename__
	pk = getattr(obj, "id", None)  # Assumes 'id' is the primary key

	old_values = None
	new_values = None
	new_values_dict = {}

	state = inspect(obj)
	mapper = state.mapper

	if action == "UPDATE" and session.is_modified(obj, include_collections=False):
		old_values = {}
		new_values = {}
		for key, attr in state.attrs.items():
			try:
				prop = mapper.get_property(key)
			except Exception:
				continue  # Property not found (unlikely)

			# Skip relationships (only include column-backed fields)
			if not hasattr(prop, "columns"):
				continue

			hist = attr.history
			if hist.has_changes():
				# Old value (before change)
				old_val = hist.deleted[0] if hist.deleted else None
				old_values[key] = _serialize_value(old_val)

				# New value (current state)
				new_val = getattr(obj, key)
				new_values[key] = _serialize_value(new_val)

		new_values_dict = new_values

		# Convert to JSON if not empty
		old_values = json.dumps(old_values) if old_values else None
		new_values = json.dumps(new_values) if new_values else None

	elif action == "CREATE":
		# For CREATE: old_values = null, new_values = all columns
		new_values = _serialize_object(obj)
		new_values = json.dumps(new_values)
		old_values = None

	elif action == "DELETE":
		# For DELETE: new_values = null, old_values = full object
		old_values = _serialize_object(obj)
		old_values = json.dumps(old_values)
		new_values = None
	elif action == "LOGIN":
		old_values = None
		new_values_dict = {"ip_address": ip_address}
		new_values = json.dumps(new_values_dict)

	user_id = user_id = pk if table_name == "users" else None

	if user:
		user_id = user.id

	if not user_id:
		return

	# Create audit log entry
	audit_log = AuditLog(
		user_id=user_id,
		action="DELETE" if "voided" in new_values_dict and bool(new_values_dict.get("voided")) else action,
		table_name=table_name,
		record_id=pk,
		old_values=old_values,
		new_values=new_values,
	)

	try:
		session.add(audit_log)
	except Exception as e:
		logging.error(f"Failed to log audit entry for {table_name}#{pk}: {e}")
