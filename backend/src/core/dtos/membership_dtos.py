from datetime import datetime
from typing import Optional
from pydantic import BaseModel, UUID4
from src.config.db.models.base import InvitationStatus
from src.config.db.models.member import Member, MemberRole
from src.config.db.models.member_invitation import MemberInvitation
from src.core.dtos.organization_dtos import OrganizationSummaryDto, to_organization_summary_dto


class MembershipSummaryDto(BaseModel):
	id: UUID4
	role: MemberRole
	user_id: UUID4
	organization: Optional[OrganizationSummaryDto] = None

class MemberInvitationDto(BaseModel):
	"""Data transfer object for member invitation"""

	id: UUID4
	organization_id: UUID4
	inviter_user_id: Optional[UUID4]
	invited_email: str
	code: str
	status: InvitationStatus
	role: MemberRole
	expires_at: datetime
	created_at: datetime
	updated_at: datetime

	# Organization details for context
	organization_name: Optional[str] = None
	inviter_name: Optional[str] = None

	class Config:
		from_attributes = True


class MemberDto(BaseModel):
	"""Data transfer object for organization member"""

	id: UUID4
	organization_id: UUID4
	user_id: UUID4
	role: Member<PERSON><PERSON>
	joined_at: datetime
	is_active: bool
	created_at: datetime
	updated_at: datetime

	# User details
	user_name: Optional[str] = None
	user_email: Optional[str] = None
	user_avatar: Optional[str] = None

	class Config:
		from_attributes = True


def to_member_dto(row: Member) -> MemberDto:
	"""Convert Member model to MemberDto"""
	return MemberDto(
		id=row.id,
		organization_id=row.organization_id,
		user_id=row.user_id,
		role=row.role,
		joined_at=row.joined_at,
		is_active=row.is_active,
		created_at=row.created_at,
		updated_at=row.updated_at,
		user_name=f"{row.user.first_name} {row.user.last_name}".strip() if row.user else None,
		user_email=row.user.email if row.user else None,
		user_avatar=row.user.avatar if row.user else None,
	)


def to_member_invitation_dto(row: MemberInvitation) -> MemberInvitationDto:
	"""Convert MemberInvitation model to MemberInvitationDto"""
	return MemberInvitationDto(
		id=row.id,
		organization_id=row.organization_id,
		inviter_user_id=row.inviter_user_id,
		invited_email=row.invited_email,
		code=row.code,
		status=row.status,
		role=row.role,
		expires_at=row.expires_at,
		created_at=row.created_at,
		updated_at=row.updated_at,
		organization_name=row.organization.name if row.organization else None,
		inviter_name=f"{row.inviter.first_name} {row.inviter.last_name}".strip() if row.inviter else None,
	)


def to_membership_summary_dto(row: Member) -> MembershipSummaryDto:
	return MembershipSummaryDto(
		id=row.id,
		role=row.role,
		user_id=row.user_id,
		organization=to_organization_summary_dto(row.organization) if row.organization else None
	)
