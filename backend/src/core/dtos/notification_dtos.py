from datetime import datetime
from typing import Optional

from pydantic import BaseModel, UUID4

from src.config.db.models import NotificationType, NotificationPriority, Notification, NotificationRecipient


class NotificationDto(BaseModel):
	"""DTO for Notification model"""

	id: UUID4
	title: str
	message: str
	type: NotificationType
	activity_id: Optional[UUID4]
	sender_id: Optional[UUID4]
	priority: NotificationPriority
	# Audit fields from AuditMixin
	created_at: datetime
	updated_at: datetime
	created_by: Optional[UUID4]
	updated_by: Optional[UUID4]
	voided: bool
	voided_by: Optional[UUID4]
	void_reason: Optional[str]


class NotificationRecipientDto(BaseModel):
	"""DTO for NotificationRecipient model"""

	id: UUID4
	notification_id: UUID4
	account_id: UUID4
	is_read: bool
	is_archived: bool
	read_at: Optional[datetime]
	# Audit fields from AuditMixin
	created_at: datetime
	updated_at: datetime
	created_by: Optional[UUID4]
	updated_by: Optional[UUID4]
	voided: bool
	voided_by: Optional[UUID4]
	void_reason: Optional[str]
	# Related notification
	notification: Optional[NotificationDto]


class NotificationWithRecipientDto(BaseModel):
	"""Combined DTO for notification with recipient status"""

	# Notification fields
	notification_id: UUID4
	title: str
	message: str
	type: NotificationType
	activity_id: Optional[UUID4]
	sender_id: Optional[UUID4]
	priority: NotificationPriority
	notification_created_at: datetime
	notification_updated_at: datetime
	# Recipient fields
	recipient_id: UUID4
	account_id: UUID4
	is_read: bool
	is_archived: bool
	read_at: Optional[datetime]
	recipient_created_at: datetime
	recipient_updated_at: datetime


def to_notification_dto(notification: Notification) -> NotificationDto:
	"""Convert Notification model to NotificationDto"""
	return NotificationDto(
		id=notification.id,
		title=notification.title,
		message=notification.message,
		type=notification.type,
		activity_id=notification.activity_id,
		sender_id=notification.sender_id,
		priority=notification.priority,
		created_at=notification.created_at,
		updated_at=notification.updated_at,
		created_by=notification.created_by,
		updated_by=notification.updated_by,
		voided=notification.voided,
		voided_by=notification.voided_by,
		void_reason=notification.void_reason,
	)


def to_notification_recipient_dto(
	recipient: NotificationRecipient, include_notification: bool = True
) -> NotificationRecipientDto:
	"""Convert NotificationRecipient model to NotificationRecipientDto"""
	return NotificationRecipientDto(
		id=recipient.id,
		notification_id=recipient.notification_id,
		account_id=recipient.account_id,
		is_read=recipient.is_read,
		is_archived=recipient.is_archived,
		read_at=recipient.read_at,
		created_at=recipient.created_at,
		updated_at=recipient.updated_at,
		created_by=recipient.created_by,
		updated_by=recipient.updated_by,
		voided=recipient.voided,
		voided_by=recipient.voided_by,
		void_reason=recipient.void_reason,
		notification=to_notification_dto(recipient.notification)
		if include_notification and recipient.notification
		else None,
	)


def to_notification_with_recipient_dto(recipient: NotificationRecipient) -> NotificationWithRecipientDto:
	"""Convert NotificationRecipient with loaded notification to combined DTO"""
	notification = recipient.notification
	return NotificationWithRecipientDto(
		# Notification fields
		notification_id=notification.id,
		title=notification.title,
		message=notification.message,
		type=notification.type,
		activity_id=notification.activity_id,
		sender_id=notification.sender_id,
		priority=notification.priority,
		notification_created_at=notification.created_at,
		notification_updated_at=notification.updated_at,
		# Recipient fields
		recipient_id=recipient.id,
		account_id=recipient.account_id,
		is_read=recipient.is_read,
		is_archived=recipient.is_archived,
		read_at=recipient.read_at,
		recipient_created_at=recipient.created_at,
		recipient_updated_at=recipient.updated_at,
	)
