from datetime import datetime
from typing import Optional

from pydantic import UUID4, BaseModel, EmailStr

from src.config.db.models import AccountStatus, Department, Gender, User
from src.config.db.models.role import Role
from src.modules.roles.roles_schema import RoleBaseDto


class RoleDto(BaseModel):
	id: UUID4
	name: str
	description: Optional[str] = None
	code: str
	created_at: datetime
	updated_at: datetime


class DepartmentDto(BaseModel):
	id: UUID4
	name: str
	code: str
	description: Optional[str] = None
	created_at: datetime
	updated_at: datetime


class UserDto(BaseModel):
	id: UUID4
	first_name: str
	handle: str
	account_id: UUID4
	middle_name: Optional[str] = None
	last_name: str
	email: EmailStr
	gender: Gender
	account_status: AccountStatus
	verified: bool
	role: RoleBaseDto | None = None
	is_external: bool


class UserSummaryDto(BaseModel):
	id: UUID4
	first_name: str
	last_name: str
	handle: str
	account_id: UUID4
	email: EmailStr


def to_department_dto(row: Department) -> DepartmentDto:
	return DepartmentDto(
		id=row.id,
		name=row.name,
		code=row.code,
		description=row.description,
		created_at=row.created_at,
		updated_at=row.updated_at,
	)


def to_user_dto(row: User) -> UserDto:
	return UserDto(
		id=row.id,
		first_name=row.first_name,
		handle=row.account.handle,
		account_id=row.account.id,
		middle_name=row.middle_name,
		last_name=row.last_name,
		email=row.email,
		gender=row.gender,
		role=row.roles[0],
		account_status=row.account.status,
		verified=row.verified,
		is_external=row.is_external,
	)


def to_role_dto(row: Role) -> RoleDto:
	return RoleDto(
		id=row.id,
		name=row.name,
		description=row.description,
		code=row.code,
		created_at=row.created_at,
		updated_at=row.updated_at,
	)


def to_user_summary_dto(row: User) -> UserSummaryDto:
	return UserSummaryDto(
		id=row.id,
		first_name=row.first_name,
		last_name=row.last_name,
		handle=row.account.handle,
		account_id=row.account_id,
		email=row.email,
	)
