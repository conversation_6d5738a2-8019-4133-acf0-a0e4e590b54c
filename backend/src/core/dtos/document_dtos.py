from datetime import datetime
from typing import Optional

from pydantic import BaseModel, UUID4, ConfigDict

from src.config.db.models.document import Document
from src.config.db.models.application_document import ApplicationDocument


class DocumentDto(BaseModel):
	model_config = ConfigDict(from_attributes=True)

	id: UUID4
	filename: str
	mimetype: str
	location: str
	original_name: str
	size: str
	created_at: datetime
	updated_at: Optional[datetime]


class LoadableItemDto(BaseModel):
	model_config = ConfigDict(from_attributes=True)

	id: UUID4
	display_value: str
	code: str
	description: Optional[str]


class ApplicationDocumentDto(BaseModel):
	model_config = ConfigDict(from_attributes=True)

	id: UUID4
	application_id: UUID4
	document_id: UUID4
	document_type_id: UUID4
	document: Optional[DocumentDto] = None
	document_type: Optional[LoadableItemDto] = None
	created_at: datetime
	updated_at: Optional[datetime]


def to_document_dto(row: Document) -> DocumentDto:
	return DocumentDto(
		id=row.id,
		filename=row.filename,
		mimetype=row.mimetype,
		location=row.location,
		original_name=row.original_name,
		size=row.size,
		created_at=row.created_at,
		updated_at=row.updated_at,
	)


def to_application_document_dto(row: ApplicationDocument) -> ApplicationDocumentDto:
	return ApplicationDocumentDto(
		id=row.id,
		application_id=row.application_id,
		document_id=row.document_id,
		document_type_id=row.document_type_id,
		document=to_document_dto(row.document) if row.document else None,
		document_type=LoadableItemDto(
			id=row.document_type.id,
			display_value=row.document_type.display_value,
			code=row.document_type.code,
			description=row.document_type.description,
		)
		if row.document_type
		else None,
		created_at=row.created_at,
		updated_at=row.updated_at,
	)
