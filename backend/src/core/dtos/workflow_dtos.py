from datetime import datetime
from typing import List, Optional

from pydantic import UUID4, BaseModel, ConfigDict

from src.config.db.models import Template, WorkflowStage
from src.config.db.models.template_stage import TemplateStage
from src.config.db.models.template_stage_role import TemplateStageRole
from src.config.db.models.template_stage_trigger import ActionMode, TemplateStageTrigger
from src.config.db.models.workflow import Workflow
from src.core.dtos.application_dtos import ApplicationSummaryDto
from src.core.dtos.user_dtos import to_role_dto, to_user_summary_dto, UserSummaryDto
from src.modules.roles.roles_schema import RoleBaseDto
from src.modules.users.users_schema import UserBaseDto


class TemplateStageRoleDto(BaseModel):
	model_config = ConfigDict(from_attributes=True)

	id: UUID4
	template_stage_id: UUID4
	role_id: UUID4
	is_active: bool = True
	role: Optional[RoleBaseDto] = None


class TemplateStageTriggerDto(BaseModel):
	id: UUID4
	template_stage_id: UUID4
	trigger_id: UUID4
	action_mode: ActionMode
	is_active: bool = True
	trigger: Optional[str] = None


class TemplateStageDto(BaseModel):
	model_config = ConfigDict(from_attributes=True)

	id: UUID4
	template_id: UUID4
	name: str
	description: Optional[str] = None
	is_active: bool = True
	position: int
	triggers: List[TemplateStageTriggerDto] = []
	roles: List[TemplateStageRoleDto] = []


class WorkflowStageDto(BaseModel):
	model_config = ConfigDict(from_attributes=True)

	id: UUID4
	approver: Optional[UserBaseDto] = None
	created_at: datetime
	updated_at: datetime
	updated_by: Optional[UUID4] = None
	created_by: UUID4
	starter: Optional[UserSummaryDto] = None
	stage_template: Optional[TemplateStageDto] = None


class TemplateDto(BaseModel):
	model_config = ConfigDict(from_attributes=True)

	id: UUID4
	name: str
	code: str
	description: Optional[str] = None
	is_active: bool = True
	stages: List[TemplateStageDto] = []


class WorkflowDto(BaseModel):
	model_config = ConfigDict(from_attributes=True)

	id: UUID4
	template_id: UUID4 = None
	application_id: UUID4 = None
	template: Optional[TemplateDto] = None
	next_stage: Optional[TemplateStageDto]
	approvals: List[WorkflowStageDto] = []


class WorkflowStatsDto(BaseModel):
	total: int
	pending: int
	in_review: int
	completed: int
	rejected: int
	average_time_days: float
	this_month: int
	trend_percentage: str


class TemplateSummaryDto(BaseModel):
	name: str
	description: str
	stages: Optional[List[TemplateStageDto]] = []


class CurrentStageDto(BaseModel):
	name: str
	position: int
	status: str


class WorkflowListDto(BaseModel):
	id: UUID4
	application: ApplicationSummaryDto
	template: TemplateSummaryDto
	current_stage: CurrentStageDto
	total_stages: int
	assigned_to_me: bool
	priority: str
	days_active: int


class WorkflowStageDetailDto(BaseModel):
	id: UUID4
	template_stage_name: str
	template_stage_description: str
	position: int
	status: str
	approved_by_name: Optional[str]
	created_at: datetime
	updated_at: Optional[datetime]
	template_stage: Optional[TemplateStageDto] = None
	reviewer: Optional[UserSummaryDto] = None
	approver: Optional[UserSummaryDto] = None


class WorkflowDashboardDto(BaseModel):
	id: UUID4
	application: ApplicationSummaryDto
	template: TemplateSummaryDto
	stages: List[WorkflowStageDetailDto]
	next_stage: Optional[TemplateSummaryDto]


def to_workflow_dto(row: Workflow) -> WorkflowDto:
	workflow = WorkflowDto(
		id=row.id,
		template_id=row.template_id,
		application_id=row.application_id,
		# application=to_application_dto(row.application) if row.application else None,
		template=to_template_dto(row.template) if row.template else None,
	)

	return workflow


def to_template_dto(row: Template) -> TemplateDto:
	template = TemplateDto(
		id=row.id,
		name=row.name,
		code=row.code,
		description=row.description,
		is_active=row.is_active,
	)

	return template


def to_template_stage_role_dto(row: TemplateStageRole) -> TemplateStageRoleDto:
	role = TemplateStageRoleDto(
		id=row.id,
		template_stage_id=row.template_stage_id,
		role_id=row.role_id,
		is_active=row.is_active,
		role=to_role_dto(row.role) if row.role else None,
	)

	return role


def to_template_stage_dto(row: TemplateStage) -> TemplateStageDto:
	stage = TemplateStageDto(
		id=row.id,
		template_id=row.template_id,
		name=row.name,
		description=row.description,
		is_active=row.is_active,
		position=row.position,
		roles=[to_template_stage_role_dto(row) for row in row.roles],
	)

	return stage


def to_template_stage_trigger_dto(row: TemplateStageTrigger) -> TemplateStageTriggerDto:
	trigger = TemplateStageTriggerDto(
		id=row.id,
		template_stage_id=row.template_stage_id,
		trigger_id=row.function_id,
		action_mode=row.action_mode,
		is_active=row.is_active,
		trigger=row.function.display_value if row.function else None,
	)

	return trigger

def to_workflow_stage_dto(row: WorkflowStage) -> WorkflowStageDto:
	return WorkflowStageDto(
		id=row.id,
		stage_template = to_template_stage_dto(row.template_stage) if row.template_stage else None,
		starter = to_user_summary_dto(row.updated_by_user) if row.updated_by_user else None,
		created_at=row.created_at,
		updated_by=row.updated_by,
		updated_at=row.updated_at,
		created_by=row.created_by
	)

def to_workflow_stage_detail_dto(row: WorkflowStage) -> WorkflowStageDetailDto:
	approver_name = f"{row.approver.first_name} {row.approver.last_name}" if row.approver else None

	return WorkflowStageDetailDto(
		id=row.id,
		template_stage_name=row.template_stage.name,
		template_stage_description=row.template_stage.description or "",
		position=row.template_stage.position,
		status=row.status.value,
		approved_by_name=approver_name,
		approver=to_user_summary_dto(row.approver) if row.approver else None,
		reviewer=to_user_summary_dto(row.updated_by_user) if row.updated_by_user else None,
		created_at=row.created_at,
		updated_at=row.updated_at,
		template_stage=to_template_stage_dto(row.template_stage),
	)
