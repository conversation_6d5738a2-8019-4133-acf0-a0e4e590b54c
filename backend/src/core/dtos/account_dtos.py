import json
from typing import Any, Dict

from pydantic import BaseModel
from pydantic.types import UUID4

from src.config.db.models.contact import ContactType


class ContactDto(BaseModel):
	id: UUID4
	type: ContactType
	details: Dict[str, Any]
	account_id: UUID4


def to_contact_dto(row) -> ContactDto:
	details = row.details
	if isinstance(details, str):
		details = json.loads(details)
	elif details is None:
		details = {}
	elif not isinstance(details, dict):
		details = dict(details)

	return ContactDto(
		id=row.id,
		type=row.type,
		details=details,
		account_id=row.account_id,
	)
