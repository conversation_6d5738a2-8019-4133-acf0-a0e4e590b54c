from datetime import datetime
from typing import Optional

from pydantic import BaseModel, UUID4

from src.config.db.models.fee import Fee, FeeStatus
from src.core.dtos.common_dtos import LoadableItemDto, to_loadable_item_dto


class FeeDto(BaseModel):
	id: UUID4
	amount: float
	fee_category_id: UUID4
	currency_id: UUID4
	name: str
	based_on_income: bool
	min_income: float
	max_income: float
	description: Optional[str] = None
	version: int
	effective_from: datetime
	effective_to: Optional[datetime] = None
	status: FeeStatus
	replaces_fee_id: Optional[UUID4] = None
	created_at: datetime
	updated_at: datetime
	created_by: Optional[UUID4] = None
	updated_by: Optional[UUID4] = None

	# Related data
	category: Optional[LoadableItemDto] = None
	currency_code: Optional[str] = None
	currency_name: Optional[str] = None


class FeeCreateRequest(BaseModel):
	amount: float
	fee_category_id: UUID4
	currency_id: UUID4
	name: str
	based_on_income: bool = False
	min_income: float = 0.0
	max_income: float = 0.0
	description: Optional[str] = None
	effective_from: Optional[datetime] = None
	status: FeeStatus = FeeStatus.DRAFT


class FeeUpdateRequest(BaseModel):
	amount: Optional[float] = None
	name: Optional[str] = None
	currency_id: Optional[UUID4] = None
	based_on_income: Optional[bool] = None
	min_income: Optional[float] = None
	max_income: Optional[float] = None
	description: Optional[str] = None
	effective_from: Optional[datetime] = None
	effective_to: Optional[datetime] = None
	status: Optional[FeeStatus] = None


def to_fee_dto(row: Fee, extras: str = "") -> FeeDto:
	"""Convert Fee model to FeeDto"""
	fee = FeeDto(
		id=row.id,
		amount=row.amount,
		fee_category_id=row.fee_category_id,
		currency_id=row.currency_id,
		name=row.name,
		based_on_income=row.based_on_income,
		min_income=row.min_income,
		max_income=row.max_income,
		description=row.description,
		version=row.version,
		effective_from=row.effective_from,
		effective_to=row.effective_to,
		status=row.status,
		replaces_fee_id=row.replaces_fee_id,
		created_at=row.created_at,
		updated_at=row.updated_at,
		created_by=row.created_by,
		updated_by=row.updated_by,
	)

	if extras:
		segments = extras.split(",")
		if "category" in segments and row.category:
			fee.category = to_loadable_item_dto(row.category)
		if "currency" in segments and row.currency:
			fee.currency_code = row.currency.code
			fee.currency_name = row.currency.name

	return fee
