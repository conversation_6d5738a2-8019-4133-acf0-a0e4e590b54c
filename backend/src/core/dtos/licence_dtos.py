from datetime import datetime
from typing import Optional

from pydantic import UUID4, BaseModel

from src.config.db.models.licence import Licence, LicenceType
from src.config.db.models.licence_renewal import LicenceRenewalStatus
from src.core.dtos.application_dtos import ApplicationDto, to_application_dto
from src.core.dtos.document_dtos import ApplicationDocumentDto


class LicenceDto(BaseModel):
	"""Schema for licence data"""

	id: UUID4
	licence_number: str
	organization_id: UUID4
	invoice_item_id: Optional[UUID4] = None
	expires_at: datetime
	type: LicenceType
	created_at: datetime
	updated_at: datetime

	class Config:
		from_attributes = True
		use_enum_values = True


class LicenceRenewalDto(BaseModel):
	"""Schema for licence renewal data"""

	id: UUID4
	organization_id: UUID4
	application_id: Optional[UUID4] = None
	status: LicenceRenewalStatus
	form_data: dict
	application: Optional[ApplicationDto] = None
	application_documents: Optional[list[ApplicationDocumentDto]] = None
	created_at: datetime
	updated_at: datetime


class LicenceVerificationDto(BaseModel):
	"""Schema for licence verification response"""

	licence_number: str
	is_valid: bool
	licence: Optional[LicenceDto] = None
	organization_name: Optional[str] = None
	message: str


def to_licence_dto(licence: Licence) -> LicenceDto:
	"""Convert Licence model to LicenceDto"""
	return LicenceDto(
		id=licence.id,
		licence_number=licence.licence_number,
		organization_id=licence.organization_id,
		invoice_item_id=licence.invoice_item_id,
		expires_at=licence.expires_at,
		type=licence.type,
		created_at=licence.created_at,
		updated_at=licence.updated_at,
	)


def to_licence_renewal_dto(renewal, application_documents=None) -> LicenceRenewalDto:
	"""Convert LicenceRenewal model to LicenceRenewalDto"""
	return LicenceRenewalDto(
		id=renewal.id,
		organization_id=renewal.organization_id,
		application_id=renewal.application_id,
		status=renewal.status,
		form_data=renewal.form_data,
		application=to_application_dto(renewal.application) if renewal.application else None,
		application_documents=application_documents or [],
		created_at=renewal.created_at,
		updated_at=renewal.updated_at,
	)
