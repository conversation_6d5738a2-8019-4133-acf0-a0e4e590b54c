from datetime import date, datetime
from typing import List, Optional

from pydantic import UUID4, BaseModel

from src.config.db.models import Invoice, InvoiceDocument, InvoiceItem, InvoiceStatus, Payment, PaymentStatus
from src.core.dtos.organization_dtos import OrganizationDto, to_organization_dto


class InvoiceDocumentDto(BaseModel):
	id: UUID4
	invoice_id: UUID4
	document_id: UUID4
	description: str
	filename: str
	location: str
	original_name: str
	size: str


class InvoiceDto(BaseModel):
	id: UUID4
	reference_number: str
	status: InvoiceStatus
	total_amount: float
	due_date: date
	description: str
	organization_id: UUID4
	created_at: datetime
	updated_at: datetime
	invoice_documents: List[InvoiceDocumentDto] = []
	organization: Optional[OrganizationDto] = None


class InvoiceItemDto(BaseModel):
	id: UUID4
	invoice_id: UUID4
	application_id: UUID4
	amount: float
	invoice: Optional[InvoiceDto] = None
	created_at: datetime
	updated_at: datetime


class PaymentDto(BaseModel):
	id: UUID4
	amount: float
	organization_id: UUID4
	invoice_id: UUID4
	transaction_number: str
	payment_mode_id: UUID4
	status: PaymentStatus
	paid_by: str
	organization: Optional[OrganizationDto] = None
	invoice: Optional[InvoiceDto] = None
	payment_mode: str


class CreateInvoiceRequest(BaseModel):
	organization_id: UUID4
	application_id: UUID4
	description: Optional[str] = None
	due_days: int = 30  # Days until due


class CreateInvoiceResponse(BaseModel):
	invoice: InvoiceDto
	items: List[InvoiceItemDto]


class CreatePaymentRequest(BaseModel):
	organization_id: UUID4
	invoice_id: UUID4
	amount: float
	payment_mode_id: UUID4
	paid_by: str
	transaction_number: str


class PaymentVerificationRequest(BaseModel):
	transaction_number: str
	expected_amount: float


class MarkInvoiceAsPaidRequest(BaseModel):
	payment_mode_id: UUID4


def to_invoice_document_dto(row: InvoiceDocument) -> InvoiceDocumentDto:
	invoice_document = InvoiceDocumentDto(
		id=row.id,
		invoice_id=row.invoice_id,
		document_id=row.document_id,
		description=row.description,
		filename=row.document.filename,
		location=row.document.location,
		original_name=row.document.original_name,
		size=row.document.size,
	)

	return invoice_document


def to_invoice_item_dto(row: InvoiceItem) -> InvoiceItemDto:
	return InvoiceItemDto(
		id=row.id,
		invoice_id=row.invoice_id,
		application_id=row.application_id,
		amount=row.amount,
		created_at=row.created_at,
		updated_at=row.updated_at,
		invoice=to_invoice_dto(row.invoice) if row.invoice else None,
	)


def to_payment_dto(row: Payment, extras: str = "") -> PaymentDto:
	payment = PaymentDto(
		id=row.id,
		amount=row.amount,
		organization_id=row.organization_id,
		invoice_id=row.invoice_id,
		transaction_number=row.transaction_number,
		payment_mode_id=row.payment_mode_id,
		status=row.status,
		paid_by=row.paid_by,
		payment_mode=row.payment_mode.value if row.payment_mode else "Unknown",
	)

	if extras:
		segments = extras.split(",")
		if "organization" in segments and row.organization:
			payment.organization = to_organization_dto(row.organization)
		if "invoice" in segments and row.invoice:
			payment.invoice = to_invoice_dto(row.invoice)

	return payment


class FinanceWorkflowStageDto(BaseModel):
	id: UUID4
	workflow_id: UUID4
	template_stage_id: UUID4
	status: str
	template_stage: dict
	application: dict
	invoice: Optional[InvoiceDto] = None
	organization: Optional[dict] = None
	proof_of_payment_document: Optional[dict] = None
	created_at: datetime
	updated_at: datetime


def to_invoice_dto(row: Invoice, extras: str = "") -> InvoiceDto:
	invoice = InvoiceDto(
		id=row.id,
		reference_number=row.reference_number,
		status=row.status,
		total_amount=row.total_amount,
		due_date=row.due_date,
		description=row.description,
		organization_id=row.organization_id,
		created_at=row.created_at,
		updated_at=row.updated_at,
		invoice_documents=[to_invoice_document_dto(row) for row in row.invoice_documents],
	)

	if extras:
		segments = extras.split(",")
		if "organization" in segments:
			invoice.organization = to_organization_dto(row.organization, extended=False)

	return invoice


def to_finance_workflow_stage_dto(
	workflow_stage, invoice=None, organization=None, proof_document=None
) -> FinanceWorkflowStageDto:
	return FinanceWorkflowStageDto(
		id=workflow_stage.id,
		workflow_id=workflow_stage.workflow_id,
		template_stage_id=workflow_stage.template_stage_id,
		status=workflow_stage.status.value if hasattr(workflow_stage.status, "value") else workflow_stage.status,
		template_stage={
			"id": workflow_stage.template_stage.id,
			"name": workflow_stage.template_stage.name,
			"description": workflow_stage.template_stage.description or "",
			"position": workflow_stage.template_stage.position,
		},
		application={
			"id": workflow_stage.workflow.application.id,
			"code": workflow_stage.workflow.application.code,
			"type": workflow_stage.workflow.application.type.value
			if hasattr(workflow_stage.workflow.application.type, "value")
			else workflow_stage.workflow.application.type,
		},
		invoice=to_invoice_dto(invoice) if invoice else None,
		organization={
			"id": organization.id,
			"name": organization.name,
			"code": organization.abbreviation,
			"logo": organization.logo if organization.logo else None,
		}
		if organization
		else None,
		proof_of_payment_document={
			"id": proof_document.document.id,
			"filename": proof_document.document.filename,
			"original_name": proof_document.document.original_name,
			"location": proof_document.document.location,
			"size": proof_document.document.size,
			"uploaded_at": proof_document.created_at,
		}
		if proof_document
		else None,
		created_at=workflow_stage.created_at,
		updated_at=workflow_stage.updated_at,
	)
