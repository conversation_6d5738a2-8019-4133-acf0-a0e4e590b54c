from enum import Enum
from typing import List

from pydantic import BaseModel, UUID4

from src.config.db.models import Gender
from src.core.dtos.membership_dtos import MembershipSummaryDto


class AuthType(str, Enum):
	MAIN = "MAIN"
	VERIFICATION = "VERIFICATION"
	LOGIN_2FA = "LOGIN_2FA"

class AuthDto(BaseModel):
	first_name: str
	last_name: str
	middle_name: str | None = None
	username: str
	email: str
	phone: str | None = None
	gender: Gender
	user_id: UUID4
	role_id: UUID4 | None = None
	role_name: str | None = None
	session_key: UUID4 | None = None
	permissions: List[str] = []
	auth_type: AuthType
	name: str
	is_external: bool
	memberships: List[MembershipSummaryDto] = []
