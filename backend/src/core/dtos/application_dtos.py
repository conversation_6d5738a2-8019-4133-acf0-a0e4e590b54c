from datetime import date, datetime
from typing import List, Optional

from pydantic import UUID4, BaseModel, ConfigDict

from src.config.db.models import Application, ApplicationStatus, ApplicationType
from src.core.dtos.document_dtos import ApplicationDocumentDto, DocumentDto, to_application_document_dto, to_document_dto
from src.core.dtos.organization_dtos import OrganizationDto, to_organization_dto
from src.core.dtos.user_dtos import UserSummaryDto, to_user_summary_dto


class ApplicationDto(BaseModel):
	model_config = ConfigDict(from_attributes=True)

	id: UUID4
	type: ApplicationType
	status: ApplicationStatus
	code: str
	organization: OrganizationDto = None
	documents: Optional[List[ApplicationDocumentDto]] = []
	created_at: datetime
	updated_at: datetime
	creator: Optional[UserSummaryDto] = None


class ApplicationSummaryDto(BaseModel):
	id: UUID4
	code: str
	type: str
	status: str
	organization: OrganizationDto
	documents: Optional[List[ApplicationDocumentDto]] = []
	created_at: datetime
	creator: Optional[UserSummaryDto] = None


def to_application_summary_dto(row: Application) -> ApplicationSummaryDto:
	return ApplicationSummaryDto(
		id=row.id,
		code=row.code,
		type=row.type.value,
		status=row.status.value,
		organization=to_organization_dto(row.organization, extended=False),
		documents=[to_application_document_dto(doc) for doc in row.documents] if row.documents else [],
		created_at=row.created_at,
		creator=to_user_summary_dto(row.created_by_user) if row.created_by else None,
	)


class ApplicationStatsDto(BaseModel):
	model_config = ConfigDict(from_attributes=True)
	
	total: int
	draft: int
	in_review: int
	approved: int
	rejected: int
	suspended: int


def to_application_dto(row: Application, extras=None) -> ApplicationDto:
	application = ApplicationDto(
		id=row.id,
		type=row.type,
		status=row.status,
		code=row.code,
		organization_id=row.organization_id,
		created_at=row.created_at,
		updated_at=row.updated_at,
		documents=[to_application_document_dto(doc) for doc in row.documents] if row.documents else [],
		creator=to_user_summary_dto(row.created_by_user) if row.created_by else None,
	)

	if extras:
		segments = extras.split(",")

		if "organization" in segments:
			application.organization = to_organization_dto(row.organization, extended=False)

	return application


# Permit Applicant Response DTOs
class PermitApplicantDto(BaseModel):
	model_config = ConfigDict(from_attributes=True)

	id: UUID4
	application_id: UUID4
	full_name: str
	nationality_id: UUID4
	passport_number: str
	position: Optional[str] = None
	department: Optional[str] = None
	employment_start_date: Optional[date] = None
	employment_end_date: Optional[date] = None
	letter_document: Optional[DocumentDto] = None
	passport_document: Optional[DocumentDto] = None
	created_at: datetime
	updated_at: datetime


def to_permit_applicant_dto(applicant) -> PermitApplicantDto:
	"""Convert PermitApplicant model to DTO"""
	return PermitApplicantDto(
		id=applicant.id,
		application_id=applicant.application_id,
		full_name=applicant.full_name,
		nationality_id=applicant.nationality_id,
		passport_number=applicant.passport_number,
		position=applicant.position,
		department=applicant.department,
		employment_start_date=applicant.employment_start_date,
		employment_end_date=applicant.employment_end_date,
		letter_document=to_document_dto(applicant.letter_document) if applicant.letter_document else None,
		passport_document=to_document_dto(applicant.passport_document) if applicant.passport_document else None,
		created_at=applicant.created_at,
		updated_at=applicant.updated_at,
	)
