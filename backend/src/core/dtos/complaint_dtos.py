from datetime import datetime
from typing import Optional

from pydantic import BaseModel, UUID4

from src.config.db.models import Complaint


class ComplaintDTO(BaseModel):
	id: UUID4
	tracking_code: str
	title: str
	summary: str
	priority: int
	organization_id: Optional[UUID4]
	is_anonymous: bool
	category_id: UUID4
	complainant_id: Optional[UUID4]
	complainant_name: Optional[str]
	complainant_email: Optional[str]
	complainant_phone: Optional[str]
	created_at: datetime
	updated_at: datetime


def to_complaint_dto(row: Complaint) -> ComplaintDTO:
	return ComplaintDTO(
		id=row.id,
		tracking_code=row.tracking_code,
		title=row.title,
		summary=row.summary,
		priority=row.priority,
		organization_id=row.organization_id,
		is_anonymous=row.is_anonymous,
		category_id=row.category_id,
		complainant_id=row.complainant_id,
		complainant_name=row.complainant_name,
		complainant_email=row.complainant_email,
		complainant_phone=row.complainant_phone,
		created_at=row.created_at,
		updated_at=row.updated_at,
	)
