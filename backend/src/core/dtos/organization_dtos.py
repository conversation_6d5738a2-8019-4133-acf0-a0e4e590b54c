from datetime import datetime
from typing import List, Optional

from pydantic import BaseModel
from pydantic.types import UUID4

from src.config.db.models import (
	<PERSON><PERSON><PERSON><PERSON>,
	Director,
	FundingSource,
	LocationActivity,
	OrganizationAuditor,
	OrganizationDonor,
	OrganizationProject,
	OrganizationSector,
	OrganizationStaff,
	TargetGroup,
)
from src.config.db.models.base import Gender
from src.config.db.models.organization import Organization, OrganizationStatus
from src.core.dtos.account_dtos import ContactDto, to_contact_dto
from src.core.dtos.common_dtos import (
	CountryDto,
	DistrictDto,
	LoadableItemDto,
	to_country_dto,
	to_district_dto,
	to_loadable_item_dto,
)
from src.core.dtos.user_dtos import UserDto


class OrganizationSummaryDto(BaseModel):
	id: UUID4
	name: str
	logo: Optional[str] = None
	abbreviation: str
	registration_number: str


class BankDetailsDto(BaseModel):
	id: UUID4
	organization_id: UUID4
	account_number: str
	branch_name: str
	bank_id: UUID4
	bank_name: Optional[str] = None
	bank_code: Optional[str] = None


class OrganizationAuditorDto(BaseModel):
	id: UUID4
	organization_id: UUID4
	name: str
	email: str
	phone: str
	address: str
	is_active: bool


class OrganizationDonorDto(BaseModel):
	id: UUID4
	organization_id: UUID4
	donor_id: UUID4
	is_active: bool
	amount: float
	donor_name: Optional[str] = None
	currency_id: Optional[UUID4] = None
	currency_name: Optional[str] = None


class FundingSourceDto(BaseModel):
	id: UUID4
	organization_id: UUID4
	donor_id: UUID4
	currency_id: UUID4
	contact_person: str
	amount: float
	donor: Optional[LoadableItemDto] = None


class TargetGroupDto(BaseModel):
	id: UUID4
	organization_id: UUID4
	type_id: UUID4
	is_active: bool
	created_at: datetime
	updated_at: datetime
	type: Optional[str] = None


class OrganizationStaffDto(BaseModel):
	id: UUID4
	organization_id: UUID4
	staff_type_id: UUID4
	is_active: bool
	total: int
	staff_type: Optional[str] = None


class LocationActivityDto(BaseModel):
	id: UUID4
	vdc_id: Optional[UUID4] = None
	adc_id: Optional[UUID4] = None
	organization_id: UUID4
	district_id: Optional[UUID4] = None
	district: Optional[str] = None
	created_at: datetime
	updated_at: datetime

	class Config:
		"""Pydantic configuration."""

		from_attributes = True


class OrganizationSectorDto(BaseModel):
	id: UUID4
	organization_id: UUID4
	sector_id: UUID4
	display_value: Optional[str] = None

	class Config:
		"""Pydantic configuration."""

		from_attributes = True


class OrganizationProjectDto(BaseModel):
	id: UUID4
	organization_id: UUID4
	name: str
	thematic_area_id: UUID4
	number_of_beneficiaries: int
	is_active: bool
	thematic_area: Optional[str] = None


class DirectorDto(BaseModel):
	id: UUID4
	fullname: str
	email: str
	phone: str
	avatar: Optional[str]
	national_identifier: Optional[str]
	passport_number: Optional[str]
	gender: Gender
	position: str
	country_id: UUID4
	occupation: str
	timeframe: str
	qualification_id: UUID4
	organization_id: UUID4
	country: Optional[CountryDto] = None
	qualification: Optional[str] = None
	created_at: datetime
	updated_at: datetime

	class Config:
		"""Pydantic configuration."""

		from_attributes = True
		use_enum_values = True


class OrganizationDto(BaseModel):
	"""Schema for returning organization data."""

	id: UUID4
	name: str
	abbreviation: str
	organization_type_id: UUID4
	registration_number: str
	district_id: UUID4
	financial_start_month: str
	financial_end_month: str
	charity_number: Optional[str]
	annual_income: float
	registration_type_id: UUID4
	biography: Optional[str]
	vision: Optional[str]
	motto: Optional[str]
	objectives: Optional[List[str]]
	status: OrganizationStatus
	account_id: UUID4
	logo: Optional[str] = None
	banner: Optional[str] = None
	created_at: datetime
	updated_at: datetime
	created_by: Optional[UUID4]
	updated_by: Optional[UUID4]
	projects: Optional[List[OrganizationProjectDto]] = []
	sectors: Optional[List[OrganizationSectorDto]] = []
	staff: Optional[List[OrganizationStaffDto]] = []
	donors: Optional[List[OrganizationDonorDto]] = []
	auditors: Optional[List[OrganizationAuditorDto]] = []
	funding_sources: Optional[List[FundingSourceDto]] = []
	target_groups: Optional[List[TargetGroupDto]] = []
	location_activities: Optional[List[LocationActivityDto]] = []
	directors: Optional[List[DirectorDto]] = []
	district: Optional[DistrictDto] = None
	organization_type: Optional[LoadableItemDto] = None
	creator: Optional[UserDto] = None
	registration_type: Optional[LoadableItemDto] = None
	bank_details: Optional[List[BankDetailsDto]] = []
	contacts: Optional[List[ContactDto]] = []

	class Config:
		"""Pydantic configuration."""

		from_attributes = True
		use_enum_values = True


def to_organization_project_dto(row: OrganizationProject) -> OrganizationProjectDto:
	return OrganizationProjectDto(
		id=row.id,
		organization_id=row.organization_id,
		name=row.name,
		thematic_area_id=row.thematic_area_id,
		number_of_beneficiaries=row.number_of_beneficiaries,
		is_active=row.is_active,
		thematic_area=row.thematic_area.display_value if row.thematic_area else None,
	)


def to_organization_donor_dto(row: OrganizationDonor) -> OrganizationDonorDto:
	return OrganizationDonorDto(
		id=row.id,
		organization_id=row.organization_id,
		donor_id=row.donor_id,
		is_active=row.is_active,
		amount=row.amount,
		donor_name=row.donor.display_value if row.donor else None,
		currency_id=row.currency_id,
		currency_name=row.currency.name if row.currency else None,
	)


def to_organization_staff_dto(row: OrganizationStaff) -> OrganizationStaffDto:
	return OrganizationStaffDto(
		id=row.id,
		organization_id=row.organization_id,
		staff_type_id=row.staff_type_id,
		is_active=row.is_active,
		total=row.total,
		staff_type=row.staff.display_value if row.staff else None,
	)


def to_bank_details_dto(row: BankDetail) -> BankDetailsDto:
	return BankDetailsDto(
		id=row.id,
		organization_id=row.organization_id,
		account_number=row.account_number,
		branch_name=row.branch_name,
		bank_id=row.bank_id,
		bank_name=row.bank.display_value if row.bank else None,
		bank_code=row.bank.code if row.bank else None,
	)


def to_organization_auditor_dto(row: OrganizationAuditor) -> OrganizationAuditorDto:
	return OrganizationAuditorDto(
		id=row.id,
		organization_id=row.organization_id,
		name=row.name,
		email=row.email,
		phone=row.phone,
		address=row.address,
		is_active=row.is_active,
	)


def to_funding_source_dto(row: FundingSource) -> FundingSourceDto:
	return FundingSourceDto(
		id=row.id,
		organization_id=row.organization_id,
		donor_id=row.donor_id,
		currency_id=row.currency_id,
		contact_person=row.contact_person,
		amount=row.amount,
		donor=to_loadable_item_dto(row.donor) if row.donor else None,
	)


def to_target_group_dto(row: TargetGroup) -> TargetGroupDto:
	return TargetGroupDto(
		id=row.id,
		organization_id=row.organization_id,
		type_id=row.type_id,
		is_active=row.is_active,
		created_at=row.created_at,
		updated_at=row.updated_at,
		type=row.type.display_value if row.type else None,
	)


def to_location_activity_dto(row: LocationActivity) -> LocationActivityDto:
	return LocationActivityDto(
		id=row.id,
		vdc_id=row.vdc_id,
		adc_id=row.adc_id,
		organization_id=row.organization_id,
		district_id=row.district_id,
		district=row.district.name if row.district else None,
		created_at=row.created_at,
		updated_at=row.updated_at,
	)


def to_organization_sector_dto(row: OrganizationSector) -> OrganizationSectorDto:
	return OrganizationSectorDto(
		id=row.id,
		organization_id=row.organization_id,
		sector_id=row.sector_id,
		display_value=row.sector.display_value if row.sector else None,
	)


def to_director_dto(row: Director) -> DirectorDto:
	return DirectorDto(
		id=row.id,
		fullname=row.fullname,
		email=row.email,
		phone=row.phone,
		avatar=row.avatar,
		national_identifier=row.national_identifier,
		passport_number=row.passport_number,
		gender=row.gender,
		position=row.position,
		country_id=row.country_id,
		occupation=row.occupation,
		timeframe=row.timeframe,
		qualification_id=row.qualification_id,
		organization_id=row.organization_id,
		country=to_country_dto(row.country) if row.country else None,
		qualification=row.qualification.display_value if row.qualification else None,
		created_at=row.created_at,
		updated_at=row.updated_at,
	)


def to_organization_dto(row: Organization, extended: bool = True) -> OrganizationDto:
	"""Convert Organization model to OrganizationDto."""

	dto = OrganizationDto(
		id=row.id,
		name=row.name,
		abbreviation=row.abbreviation,
		organization_type_id=row.organization_type_id,
		registration_number=row.registration_number,
		district_id=row.district_id,
		financial_start_month=row.financial_start_month,
		financial_end_month=row.financial_end_month,
		charity_number=row.charity_number,
		annual_income=row.annual_income,
		registration_type_id=row.registration_type_id,
		biography=row.biography,
		vision=row.vision,
		motto=row.motto,
		objectives=row.objectives,
		logo=row.logo,
		banner=row.banner,
		status=row.status,
		account_id=row.account_id,
		created_at=row.created_at,
		updated_at=row.updated_at,
		created_by=row.created_by,
		updated_by=row.updated_by,
	)

	if extended:
		dto.district = to_district_dto(row.district) if row.district else None
		dto.organization_type = to_loadable_item_dto(row.type) if row.type else None
		dto.registration_type = to_loadable_item_dto(row.registration_type) if row.registration_type else None
		dto.projects = [to_organization_project_dto(p) for p in row.projects] if row.projects else []
		dto.sectors = [to_organization_sector_dto(s) for s in row.sectors] if row.sectors else []
		dto.staff = [to_organization_staff_dto(s) for s in row.staffs] if row.staffs else []
		dto.donors = [to_organization_donor_dto(d) for d in row.donors] if row.donors else []
		dto.auditors = [to_organization_auditor_dto(a) for a in row.auditors] if row.auditors else []
		dto.funding_sources = [to_funding_source_dto(f) for f in row.funding_sources] if row.funding_sources else []
		dto.target_groups = [to_target_group_dto(t) for t in row.target_groups] if row.target_groups else []
		dto.location_activities = (
			[to_location_activity_dto(row) for row in row.location_activities] if row.location_activities else []
		)
		dto.directors = [to_director_dto(d) for d in row.directors] if row.directors else []
		dto.bank_details = [to_bank_details_dto(b) for b in row.bank_details] if row.bank_details else []
		dto.contacts = [to_contact_dto(c) for c in row.account.contacts] if row.account.contacts else []
	return dto


class OrganizationVerificationDto(BaseModel):
	"""Schema for organization verification response."""

	registration_number: str
	is_valid: bool
	organization_name: Optional[str] = None
	abbreviation: Optional[str] = None
	status: Optional[str] = None
	message: str


def to_organization_verification_dto(
	organization: Optional[Organization], registration_number: str
) -> OrganizationVerificationDto:
	"""Convert Organization model to OrganizationVerificationDto."""

	if not organization:
		return OrganizationVerificationDto(
			registration_number=registration_number,
			is_valid=False,
			message="Organization with this registration number not found",
		)

	is_valid = organization.status == OrganizationStatus.REGISTERED.value

	return OrganizationVerificationDto(
		registration_number=registration_number,
		is_valid=is_valid,
		organization_name=organization.name,
		abbreviation=organization.abbreviation,
		status=organization.status.value if organization.status else None,
		message="Organization found and verified"
		if is_valid
		else f"Organization found but status is {organization.status.value}",
	)

def to_organization_summary_dto(row: Organization) -> OrganizationSummaryDto:
	return OrganizationSummaryDto(
		id=row.id,
		name=row.name,
		logo=row.logo,
		abbreviation=row.abbreviation,
		registration_number=row.registration_number,
	)
