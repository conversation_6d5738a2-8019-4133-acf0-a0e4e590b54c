from typing import Optional

from sqlalchemy.orm import Session

from src.core.base.application_logger import ApplicationLogger
from src.core.context.auth_context import get_current_user
from src.core.context.db_context import require_db_session
from src.core.shared_schema import CurrentUser


class BaseRepository(ApplicationLogger):
	def __init__(self):
		super().__init__()

	@property
	def db(self) -> Session:
		"""Get the current database session from context"""
		return require_db_session()

	@property
	def current_user(self) -> Optional[CurrentUser]:
		"""Get the current user from context"""
		return get_current_user()

	@property
	def current_user_id(self) -> Optional[str]:
		"""Get the current user ID from context"""
		user = self.current_user
		return user.id if user else None
