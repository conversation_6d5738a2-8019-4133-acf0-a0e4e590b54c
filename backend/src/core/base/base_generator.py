import math
import os
import tempfile
import uuid
from abc import ABC, abstractmethod
from datetime import datetime
from io import Bytes<PERSON>
from pathlib import Path
from typing import Any, Dict, Optional

import qrcode
from reportlab.lib import colors
from reportlab.lib.enums import TA_CENTER, TA_LEFT
from reportlab.lib.pagesizes import A4
from reportlab.lib.styles import ParagraphStyle, getSampleStyleSheet
from reportlab.pdfgen import canvas
from reportlab.platypus import Paragraph, SimpleDocTemplate, Spacer, Table, TableStyle


class BaseGenerator(ABC):
	"""Base class for all PDF generators"""

	def __init__(self, output_dir: Optional[str] = None):
		self.output_dir = output_dir or self._get_default_output_dir()
		self.styles = getSampleStyleSheet()
		self._setup_custom_styles()

	def _get_default_output_dir(self) -> str:
		"""Get default output directory for generated PDFs"""
		project_root = Path(__file__).parent.parent.parent.parent.parent
		output_dir = project_root / "storage" / "generated_documents"
		output_dir.mkdir(parents=True, exist_ok=True)
		return str(output_dir)

	def _setup_custom_styles(self):
		"""Setup custom paragraph styles"""
		custom_styles = {
			"CustomTitle": {
				"parent": self.styles["Title"],
				"fontSize": 18,
				"spaceAfter": 12,
				"alignment": TA_CENTER,
				"textColor": colors.black,
			},
			"CustomHeading": {
				"parent": self.styles["Heading1"],
				"fontSize": 14,
				"spaceAfter": 6,
				"spaceBefore": 12,
				"alignment": TA_LEFT,
				"textColor": colors.black,
			},
			"CustomNormal": {
				"parent": self.styles["Normal"],
				"fontSize": 10,
				"spaceAfter": 6,
				"alignment": TA_LEFT,
			},
			"SignatureStyle": {
				"parent": self.styles["Normal"],
				"fontSize": 10,
				"alignment": TA_CENTER,
				"spaceAfter": 6,
			},
		}

		for name, props in custom_styles.items():
			self.styles.add(ParagraphStyle(name=name, **props))

	def _generate_filename(self, document_type: str, reference_id: str = None) -> str:
		"""Generate unique filename for the document"""
		timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
		unique_id = reference_id or str(uuid.uuid4())[:8]
		return f"{document_type}_{unique_id}_{timestamp}.pdf"

	def _create_header(self, title: str, subtitle: str = None) -> list:
		"""Create standard document header"""
		elements = []

		elements.append(Paragraph(title, self.styles["CustomTitle"]))
		elements.append(Spacer(1, 12))

		if subtitle:
			elements.append(Paragraph(subtitle, self.styles["CustomHeading"]))
			elements.append(Spacer(1, 12))

		return elements

	def _create_footer(self, page_info: str = None) -> list:
		"""Create standard document footer"""
		elements = []
		elements.append(Spacer(1, 24))

		elements.append(Paragraph("_" * 80, self.styles["CustomNormal"]))

		if page_info:
			elements.append(Paragraph(page_info, self.styles["CustomNormal"]))

		timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
		elements.append(Paragraph(f"Generated on: {timestamp}", self.styles["CustomNormal"]))

		return elements

	def _create_signature_section(self, signatory_title: str = "Authorized Signatory") -> list:
		"""Create signature section"""
		elements = []
		elements.append(Spacer(1, 48))

		elements.append(Paragraph("_" * 40, self.styles["SignatureStyle"]))
		elements.append(Paragraph(signatory_title, self.styles["SignatureStyle"]))
		elements.append(Paragraph(f"Date: {datetime.now().strftime('%Y-%m-%d')}", self.styles["SignatureStyle"]))

		return elements

	def _create_table(self, data: list, col_widths: list = None, style_commands: list = None) -> Table:
		"""Create formatted table"""
		table = Table(data, colWidths=col_widths)

		default_style = [
			("BACKGROUND", (0, 0), (-1, 0), colors.grey),
			("TEXTCOLOR", (0, 0), (-1, 0), colors.whitesmoke),
			("ALIGN", (0, 0), (-1, -1), "CENTER"),
			("FONTNAME", (0, 0), (-1, 0), "Helvetica-Bold"),
			("FONTSIZE", (0, 0), (-1, 0), 12),
			("BOTTOMPADDING", (0, 0), (-1, 0), 12),
			("BACKGROUND", (0, 1), (-1, -1), colors.beige),
			("GRID", (0, 0), (-1, -1), 1, colors.black),
		]

		if style_commands:
			default_style.extend(style_commands)

		table.setStyle(TableStyle(default_style))
		return table

	def generate_pdf(self, data: Dict[str, Any], filename: str = None) -> str:
		"""Generate PDF document and return file path"""
		if not filename:
			filename = self._generate_filename(self.__class__.__name__.replace("Generator", "").lower())

		file_path = os.path.join(self.output_dir, filename)

		doc = SimpleDocTemplate(file_path, pagesize=A4, rightMargin=72, leftMargin=72, topMargin=72, bottomMargin=18)

		story = self._build_content(data)

		doc.build(story)

		return file_path

	def generate_pdf_bytes(self, data: Dict[str, Any]) -> bytes:
		"""Generate PDF document and return as bytes"""
		buffer = BytesIO()

		doc = SimpleDocTemplate(buffer, pagesize=A4, rightMargin=72, leftMargin=72, topMargin=72, bottomMargin=18)

		story = self._build_content(data)

		watermark_text = self._get_watermark_text(data)
		if watermark_text:
			doc.build(
				story,
				onFirstPage=lambda canvas, doc: self._add_watermark(canvas, watermark_text),
				onLaterPages=lambda canvas, doc: self._add_watermark(canvas, watermark_text),
			)
		else:
			doc.build(story)

		buffer.seek(0)
		return buffer.getvalue()

	def _get_watermark_text(self, data: Dict[str, Any]) -> str:
		"""Override in subclasses to determine watermark text based on data"""
		return ""

	def _add_watermark(self, pdf_canvas: canvas.Canvas, text: str):
		"""Add diagonal watermark to PDF page"""
		pdf_canvas.saveState()

		pdf_canvas.setFillColor(colors.blue)
		pdf_canvas.setFillAlpha(0.3)

		page_width, page_height = A4

		center_x = page_width / 2
		center_y = page_height / 2

		angle = math.degrees(math.atan(page_height / page_width))

		pdf_canvas.setFont("Helvetica-Bold", 72)

		pdf_canvas.translate(center_x, center_y)
		pdf_canvas.rotate(angle)

		text_width = pdf_canvas.stringWidth(text, "Helvetica-Bold", 72)
		pdf_canvas.drawString(-text_width / 2, -36, text)

		pdf_canvas.restoreState()

	def _draw_qr_code(self, c: canvas.Canvas, url: str, x: float, y: float, size: float):
		"""Draw QR code for verification"""
		try:
			qr = qrcode.QRCode(version=1, box_size=3, border=1)
			qr.add_data(url)
			qr.make(fit=True)

			qr_img = qr.make_image(fill_color="black", back_color="white")

			with tempfile.NamedTemporaryFile(suffix=".png", delete=False) as tmp:
				qr_img.save(tmp.name, format="PNG")
				c.drawImage(tmp.name, x, y, width=size, height=size)
				os.unlink(tmp.name)

		except Exception as e:
			print(f"QR code generation failed: {e}")
			c.setStrokeColor(colors.black)
			c.rect(x, y, size, size)
			c.setFont("Helvetica", 8)
			c.drawString(x + 5, y + size / 2, "QR")

	def _format_date(self, date_value) -> str:
		"""Format date for document display (e.g., '15th day of August, 2025')"""
		if isinstance(date_value, str):
			try:
				date_obj = datetime.fromisoformat(date_value.replace("Z", "+00:00"))
			except ValueError:
				try:
					date_obj = datetime.strptime(date_value, "%Y-%m-%d")
				except ValueError:
					return date_value
		elif isinstance(date_value, datetime):
			date_obj = date_value
		else:
			return str(date_value)

		day = date_obj.day
		if day in [1, 21, 31]:
			suffix = "st"
		elif day in [2, 22]:
			suffix = "nd"
		elif day in [3, 23]:
			suffix = "rd"
		else:
			suffix = "th"

		return f"{day}{suffix} day of {date_obj.strftime('%B, %Y')}"

	@abstractmethod
	def _build_content(self, data: Dict[str, Any]) -> list:
		"""Build the content elements for the PDF document"""
		pass

	@abstractmethod
	def validate_data(self, data: Dict[str, Any]) -> bool:
		"""Validate input data for document generation"""
		pass
