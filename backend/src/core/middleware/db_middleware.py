"""
Database session middleware for managing per-request database sessions.
"""

from typing import Awaitable, Callable

from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.types import ASGIApp

from src.config.db.connection import get_db
from src.core.context.db_context import clear_db_session, set_db_session
from src.core.logger.internal_logger import get_logger

logger = get_logger(__name__)


class DatabaseMiddleware(BaseHTTPMiddleware):
	"""
	Middleware that creates a database session per request and stores it in context.
	Automatically handles transaction commit/rollback and session cleanup.
	"""

	def __init__(self, app: ASGIApp):
		super().__init__(app)

	async def dispatch(self, request: Request, call_next: Callable[[Request], Awaitable[Response]]) -> Response:
		"""
		Create database session for request, handle transactions, and cleanup.
		"""
		db_generator = get_db()
		db = next(db_generator)

		try:
			set_db_session(db)

			response = await call_next(request)

			if response.status_code < 400:
				db.commit()
			else:
				db.rollback()

			return response

		except Exception as e:
			db.rollback()
			logger.error(f"Database error in request: {str(e)}")
			raise

		finally:
			clear_db_session()
			db.close()
