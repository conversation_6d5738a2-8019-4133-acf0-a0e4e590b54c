"""
Startup connectivity checks for external services.
"""

import asyncio
import time
from typing import Any, Dict

from sqlalchemy import text
from sqlalchemy.exc import SQLAlchemyError

from src.config.db.connection import get_db
from src.core.context.db_context import clear_db_session, set_db_session
from src.core.logger.internal_logger import get_logger
from src.core.services.cache_service import cache_service
from src.core.services.storage_service import get_storage_service

logger = get_logger(__name__)


async def check_database_connectivity() -> Dict[str, Any]:
	"""
	Check database connectivity during startup.

	Returns:
		Dict containing connection status, latency, and error info
	"""
	start_time = time.time()

	try:
		db_generator = get_db()
		db = next(db_generator)

		try:
			set_db_session(db)

			db.execute(text("SELECT 1"))
			db.commit()

			latency_ms = int((time.time() - start_time) * 1000)

			logger.info(f"✅ Database connected successfully (latency: {latency_ms}ms)")

			return {"service": "database", "status": "connected", "latency_ms": latency_ms, "error": None}

		finally:
			clear_db_session()
			db.close()

	except SQLAlchemyError as e:
		latency_ms = int((time.time() - start_time) * 1000)
		error_msg = str(e)

		if "could not translate host name" in error_msg or "Connection refused" in error_msg:
			logger.warning(f"⚠️  Database not available - may be running without Docker (latency: {latency_ms}ms)")
		else:
			logger.error(f"❌ Database connection failed: {error_msg} (attempted in {latency_ms}ms)")

		return {"service": "database", "status": "failed", "latency_ms": latency_ms, "error": error_msg}
	except Exception as e:
		latency_ms = int((time.time() - start_time) * 1000)
		error_msg = str(e)

		logger.error(f"❌ Database connection error: {error_msg} (attempted in {latency_ms}ms)")

		return {"service": "database", "status": "error", "latency_ms": latency_ms, "error": error_msg}


async def check_redis_connectivity() -> Dict[str, Any]:
	"""
	Check Redis connectivity during startup.

	Returns:
		Dict containing connection status, latency, and error info
	"""
	start_time = time.time()

	try:
		test_key = "startup_connectivity_test"
		test_value = "test_connection"

		await cache_service.set(test_key, test_value, expire=5)
		retrieved_value = await cache_service.get(test_key)

		if retrieved_value == test_value:
			await cache_service.delete(test_key)

			latency_ms = int((time.time() - start_time) * 1000)

			logger.info(f"✅ Redis connected successfully (latency: {latency_ms}ms)")

			return {"service": "redis", "status": "connected", "latency_ms": latency_ms, "error": None}
		else:
			raise Exception("Redis set/get test failed - values don't match")

	except Exception as e:
		latency_ms = int((time.time() - start_time) * 1000)
		error_msg = str(e)

		if "Timeout connecting" in error_msg or "Connection refused" in error_msg:
			logger.warning(f"⚠️  Redis not available - may be running without Docker (latency: {latency_ms}ms)")
		else:
			logger.error(f"❌ Redis connection failed: {error_msg} (attempted in {latency_ms}ms)")

		return {"service": "redis", "status": "failed", "latency_ms": latency_ms, "error": error_msg}


async def check_minio_connectivity() -> Dict[str, Any]:
	"""
	Check MinIO connectivity during startup.

	Returns:
		Dict containing connection status, latency, and error info
	"""
	start_time = time.time()

	try:
		storage_service = get_storage_service()

		bucket_exists = storage_service.bucket_exists(storage_service.bucket)

		latency_ms = int((time.time() - start_time) * 1000)

		if bucket_exists:
			logger.info(
				f"✅ MinIO connected successfully - bucket '{storage_service.bucket}' accessible (latency: {latency_ms}ms)"
			)
		else:
			logger.warning(
				f"⚠️  MinIO connected but bucket '{storage_service.bucket}' not found (latency: {latency_ms}ms)"
			)

		return {
			"service": "minio",
			"status": "connected",
			"latency_ms": latency_ms,
			"bucket_exists": bucket_exists,
			"bucket_name": storage_service.bucket,
			"error": None,
		}

	except Exception as e:
		latency_ms = int((time.time() - start_time) * 1000)
		error_msg = str(e)

		if "Failed to resolve" in error_msg or "Connection refused" in error_msg or "Max retries exceeded" in error_msg:
			logger.warning(f"⚠️  MinIO not available - may be running without Docker (latency: {latency_ms}ms)")
		else:
			logger.error(f"❌ MinIO connection failed: {error_msg} (attempted in {latency_ms}ms)")

		return {"service": "minio", "status": "failed", "latency_ms": latency_ms, "error": error_msg}


async def perform_startup_connectivity_checks() -> Dict[str, Dict[str, Any]]:
	"""
	Perform all startup connectivity checks in parallel.

	Returns:
		Dict containing results for all services
	"""
	logger.info("🔍 Performing startup connectivity checks...")

	database_check, redis_check, minio_check = await asyncio.gather(
		check_database_connectivity(), check_redis_connectivity(), check_minio_connectivity(), return_exceptions=True
	)

	results = {}

	if isinstance(database_check, Exception):
		logger.error(f"❌ Database check failed with exception: {str(database_check)}")
		results["database"] = {"service": "database", "status": "error", "error": str(database_check)}
	else:
		results["database"] = database_check

	if isinstance(redis_check, Exception):
		logger.error(f"❌ Redis check failed with exception: {str(redis_check)}")
		results["redis"] = {"service": "redis", "status": "error", "error": str(redis_check)}
	else:
		results["redis"] = redis_check

	if isinstance(minio_check, Exception):
		logger.error(f"❌ MinIO check failed with exception: {str(minio_check)}")
		results["minio"] = {"service": "minio", "status": "error", "error": str(minio_check)}
	else:
		results["minio"] = minio_check

	# Summary logging
	connected_services = [name for name, result in results.items() if result.get("status") == "connected"]
	failed_services = [name for name, result in results.items() if result.get("status") in ["failed", "error"]]

	if connected_services:
		logger.info(f"✅ Connected services: {', '.join(connected_services)}")

	if failed_services:
		logger.warning(f"⚠️  Unavailable services: {', '.join(failed_services)} (application will still start)")
	else:
		logger.info("🎉 All external services connected successfully!")

	logger.info("🚀 Application startup connectivity checks completed")

	return results
