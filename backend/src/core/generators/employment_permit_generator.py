from datetime import datetime
from io import Bytes<PERSON>
from pathlib import Path
from typing import Any, Dict, List

from reportlab.lib import colors
from reportlab.lib.enums import TA_CENTER, TA_LEFT
from reportlab.lib.pagesizes import A4
from reportlab.lib.styles import ParagraphStyle
from reportlab.pdfgen import canvas

from src.core.base.base_generator import BaseGenerator


class EmploymentPermitGenerator(BaseGenerator):
	"""Generator for Employment Permit PDFs in portrait mode matching the official format"""

	def __init__(self, output_dir: str = None):
		super().__init__(output_dir)
		self._setup_permit_styles()

	def _setup_permit_styles(self):
		"""Setup custom styles matching the official employment permit format"""
		permit_styles = {
			"AuthorityHeader": {
				"parent": self.styles["Normal"],
				"fontSize": 14,
				"fontName": "Helvetica-Bold",
				"alignment": TA_CENTER,
				"spaceAfter": 6,
				"textColor": colors.black,
			},
			"PermitTitle": {
				"parent": self.styles["Normal"],
				"fontSize": 24,
				"fontName": "Helvetica-Bold",
				"alignment": TA_CENTER,
				"spaceAfter": 12,
				"textColor": colors.black,
			},
			"PermitSubtitle": {
				"parent": self.styles["Normal"],
				"fontSize": 16,
				"fontName": "Helvetica-Bold",
				"alignment": TA_CENTER,
				"spaceAfter": 12,
				"textColor": colors.darkred,
			},
			"PermitBody": {
				"parent": self.styles["Normal"],
				"fontSize": 12,
				"alignment": TA_CENTER,
				"spaceAfter": 12,
				"leading": 16,
			},
			"EmployeeName": {
				"parent": self.styles["Normal"],
				"fontSize": 18,
				"fontName": "Helvetica-Bold",
				"alignment": TA_CENTER,
				"spaceAfter": 12,
				"spaceBefore": 12,
			},
			"SectionHeader": {
				"parent": self.styles["Normal"],
				"fontSize": 12,
				"fontName": "Helvetica-Bold",
				"alignment": TA_LEFT,
				"spaceAfter": 6,
				"spaceBefore": 12,
				"textColor": colors.darkblue,
			},
			"InfoText": {
				"parent": self.styles["Normal"],
				"fontSize": 10,
				"alignment": TA_LEFT,
				"spaceAfter": 4,
			},
			"SignatureName": {
				"parent": self.styles["Normal"],
				"fontSize": 11,
				"fontName": "Helvetica-Bold",
				"alignment": TA_CENTER,
				"spaceAfter": 2,
			},
			"SignatureTitle": {
				"parent": self.styles["Normal"],
				"fontSize": 9,
				"alignment": TA_CENTER,
				"spaceAfter": 2,
				"fontName": "Helvetica-Oblique",
			},
		}

		for name, props in permit_styles.items():
			self.styles.add(ParagraphStyle(name=name, **props))

	def validate_data(self, data: Dict[str, Any]) -> bool:
		"""Validate employment permit data"""
		required_fields = [
			"permit_number",
			"employee_name",
			"nationality",
			"passport_number",
			"organization_name",
			"position",
			"issue_date",
			"expiry_date",
			"registrar_ceo",
			"verification_url",
		]

		missing_fields = [field for field in required_fields if field not in data]
		if missing_fields:
			raise ValueError(f"Missing required fields: {', '.join(missing_fields)}")

		return True

	def generate_pdf_bytes(self, data: Dict[str, Any]) -> bytes:
		"""Generate employment permit PDF in portrait mode"""
		self.validate_data(data)

		buffer = BytesIO()
		c = canvas.Canvas(buffer, pagesize=A4)
		width, height = A4

		self._draw_employment_permit(c, data, width, height)

		c.save()
		buffer.seek(0)
		return buffer.getvalue()

	def _draw_employment_permit(self, c: canvas.Canvas, data: Dict[str, Any], width: float, height: float):
		"""Draw the complete employment permit in portrait mode"""
		drawing_methods = [
			(self._draw_border, [width, height]),
			(self._draw_qr_code, [data["verification_url"], width - 100, height - 80, 50]),
			(self._draw_logo, [width / 2, height - 120]),
			(self._draw_authority_header, [width, height - 140]),
			(self._draw_document_title, ["EMPLOYMENT PERMIT", width, height - 180]),
			(self._draw_permit_subtitle, ["Republic of Malawi", width, height - 210]),
			(self._draw_permit_body, [width, height - 240]),
			(self._draw_employee_name, [data["employee_name"], width, height - 280]),
			(self._draw_employee_details, [data, width, height - 320]),
			(self._draw_organization_details, [data, width, height - 420]),
			(self._draw_employment_details, [data, width, height - 500]),
			(self._draw_validity_section, [data, width, height - 580]),
			(self._draw_conditions_text, [width, height - 620]),
			(self._draw_official_seal, [width / 2 - 100, height - 720]),
			(self._draw_signatures, [data, width, height - 720]),
			(self._draw_footer_info, [data, width, height - 780]),
		]

		for method, args in drawing_methods:
			method(c, *args)

	def _draw_border(self, c: canvas.Canvas, width: float, height: float):
		"""Draw double border for the employment permit"""
		c.setStrokeColor(colors.black)
		c.setLineWidth(2)
		c.rect(30, 30, width - 60, height - 60)
		c.setLineWidth(1)
		c.rect(40, 40, width - 80, height - 80)

	def _draw_logo(self, c: canvas.Canvas, x: float, y: float):
		"""Draw NGORA logo at the center top"""
		logo_path = Path(__file__).parent.parent.parent / "assets" / "ngora_logo.png"
		if logo_path.exists():
			c.drawImage(str(logo_path), x - 40, y, width=80, height=70)
		else:
			c.setStrokeColor(colors.blue)
			c.setFillColor(colors.blue)
			c.circle(x, y + 35, 35, fill=1)
			c.setFillColor(colors.white)
			c.setFont("Helvetica-Bold", 10)
			c.drawCentredString(x, y + 30, "NGORA")

	def _draw_authority_header(self, c: canvas.Canvas, width: float, y: float):
		"""Draw authority header text"""
		c.setFont("Helvetica-Bold", 14)
		c.drawCentredString(width / 2, y, "NON-GOVERNMENTAL ORGANIZATIONS REGULATORY AUTHORITY")

	def _draw_document_title(self, c: canvas.Canvas, title: str, width: float, y: float):
		"""Draw document title"""
		c.setFont("Helvetica-Bold", 24)
		c.drawCentredString(width / 2, y, title)

	def _draw_permit_subtitle(self, c: canvas.Canvas, subtitle: str, width: float, y: float):
		"""Draw permit subtitle"""
		c.setFont("Helvetica-Bold", 16)
		c.setFillColor(colors.darkred)
		c.drawCentredString(width / 2, y, subtitle)
		c.setFillColor(colors.black)

	def _draw_permit_body(self, c: canvas.Canvas, width: float, y: float):
		"""Draw main permit body text"""
		c.setFont("Helvetica", 12)
		text_lines = [
			"This permit authorizes the named individual to be employed",
			"by the specified organization in the Republic of Malawi"
		]
		
		for i, line in enumerate(text_lines):
			c.drawCentredString(width / 2, y - (i * 16), line)

	def _draw_employee_name(self, c: canvas.Canvas, employee_name: str, width: float, y: float):
		"""Draw employee name in large bold text"""
		c.setFont("Helvetica-Bold", 18)
		c.drawCentredString(width / 2, y, employee_name.upper())

	def _draw_employee_details(self, c: canvas.Canvas, data: Dict[str, Any], width: float, y: float):
		"""Draw employee personal details"""
		c.setFont("Helvetica-Bold", 12)
		c.setFillColor(colors.darkblue)
		c.drawString(60, y, "EMPLOYEE DETAILS")
		c.setFillColor(colors.black)
		
		c.setFont("Helvetica", 10)
		details = [
			f"Nationality: {data['nationality']}",
			f"Passport Number: {data['passport_number']}",
			f"Date of Birth: {data.get('date_of_birth', 'Not specified')}",
			f"Address: {data.get('employee_address', 'Not specified')}",
		]
		
		for i, detail in enumerate(details):
			c.drawString(80, y - 20 - (i * 14), detail)

	def _draw_organization_details(self, c: canvas.Canvas, data: Dict[str, Any], width: float, y: float):
		"""Draw organization details"""
		c.setFont("Helvetica-Bold", 12)
		c.setFillColor(colors.darkblue)
		c.drawString(60, y, "EMPLOYING ORGANIZATION")
		c.setFillColor(colors.black)
		
		c.setFont("Helvetica", 10)
		details = [
			f"Organization: {data['organization_name']}",
			f"Registration No: {data.get('organization_registration', 'Not specified')}",
			f"Address: {data.get('organization_address', 'Not specified')}",
		]
		
		for i, detail in enumerate(details):
			c.drawString(80, y - 20 - (i * 14), detail)

	def _draw_employment_details(self, c: canvas.Canvas, data: Dict[str, Any], width: float, y: float):
		"""Draw employment specific details"""
		c.setFont("Helvetica-Bold", 12)
		c.setFillColor(colors.darkblue)
		c.drawString(60, y, "EMPLOYMENT DETAILS")
		c.setFillColor(colors.black)
		
		c.setFont("Helvetica", 10)
		details = [
			f"Position: {data['position']}",
			f"Department: {data.get('department', 'Not specified')}",
			f"Start Date: {self._format_date(data.get('employment_start_date', data['issue_date']))}",
			f"End Date: {self._format_date(data.get('employment_end_date', data['expiry_date']))}",
		]
		
		if 'salary' in data:
			currency = data.get('currency', 'MWK')
			details.append(f"Monthly Salary: {currency} {data['salary']:,.2f}")
		
		for i, detail in enumerate(details):
			c.drawString(80, y - 20 - (i * 14), detail)

	def _draw_validity_section(self, c: canvas.Canvas, data: Dict[str, Any], width: float, y: float):
		"""Draw permit validity information"""
		c.setFont("Helvetica-Bold", 12)
		c.setFillColor(colors.darkblue)
		c.drawString(60, y, "PERMIT VALIDITY")
		c.setFillColor(colors.black)
		
		c.setFont("Helvetica", 10)
		details = [
			f"Permit Number: {data['permit_number']}",
			f"Issue Date: {self._format_date(data['issue_date'])}",
			f"Expiry Date: {self._format_date(data['expiry_date'])}",
		]
		
		for i, detail in enumerate(details):
			c.drawString(80, y - 20 - (i * 14), detail)

	def _draw_conditions_text(self, c: canvas.Canvas, width: float, y: float):
		"""Draw permit conditions and legal text"""
		c.setFont("Helvetica", 9)
		conditions = [
			"CONDITIONS:",
			"1. This permit is valid only for employment with the named organization.",
			"2. Any change in employment terms must be reported to NGORA immediately.",
			"3. The permit holder must carry this document at all times during employment.",
			"4. This permit is non-transferable and applies only to the named individual.",
		]
		
		for i, condition in enumerate(conditions):
			if i == 0:
				c.setFont("Helvetica-Bold", 9)
			else:
				c.setFont("Helvetica", 9)
			c.drawString(60, y - (i * 12), condition)

	def _draw_official_seal(self, c: canvas.Canvas, x: float, y: float):
		"""Draw official seal"""
		seal_path = Path(__file__).parent.parent.parent / "assets" / "official_seal.png"
		if seal_path.exists():
			c.drawImage(str(seal_path), x, y, width=80, height=80)
		else:
			c.setFillColor(colors.Color(218 / 255, 165 / 255, 32 / 255))  # Gold color
			c.circle(x + 40, y + 40, 40, fill=1)
			c.setFillColor(colors.black)
			c.setFont("Helvetica-Bold", 8)
			c.drawCentredString(x + 40, y + 45, "OFFICIAL")
			c.drawCentredString(x + 40, y + 35, "SEAL")

	def _draw_signatures(self, c: canvas.Canvas, data: Dict[str, Any], width: float, y: float):
		"""Draw signature section"""
		c.setStrokeColor(colors.black)
		c.setLineWidth(1)

		# Signature line (Registrar/CEO)
		sig_x = width / 2 + 50
		c.line(sig_x - 60, y + 20, sig_x + 60, y + 20)

		# Name and title
		c.setFont("Helvetica-Bold", 11)
		c.drawCentredString(sig_x, y, data["registrar_ceo"])

		c.setFont("Helvetica-Oblique", 9)
		c.drawCentredString(sig_x, y - 15, "Registrar/CEO")
		c.drawCentredString(sig_x, y - 30, "NGORA")

	def _draw_footer_info(self, c: canvas.Canvas, data: Dict[str, Any], width: float, y: float):
		"""Draw footer information"""
		c.setFont("Helvetica", 8)
		footer_text = f"For verification, visit: {data.get('verification_url', 'www.ngora.mw')}"
		c.drawCentredString(width / 2, y, footer_text)
		
		c.drawCentredString(width / 2, y - 12, "This permit is issued under the authority of the Non-Governmental Organizations Act")

	def _format_date(self, date_value) -> str:
		"""Format date for permit display (e.g., '31 December 2025')"""
		if isinstance(date_value, str):
			try:
				date_obj = datetime.fromisoformat(date_value.replace("Z", "+00:00"))
			except ValueError:
				try:
					date_obj = datetime.strptime(date_value, "%Y-%m-%d")
				except ValueError:
					return date_value
		elif isinstance(date_value, datetime):
			date_obj = date_value
		else:
			return str(date_value)

		return date_obj.strftime("%d %B %Y")

	def _build_content(self, data: Dict[str, Any]) -> List:
		"""Legacy method for compatibility - not used in canvas-based generation"""
		return []