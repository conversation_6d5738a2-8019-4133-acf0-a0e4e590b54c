from io import BytesIO
from pathlib import Path
from typing import Any, Dict, List

from reportlab.lib import colors
from reportlab.lib.enums import TA_CENTER
from reportlab.lib.pagesizes import A4, landscape
from reportlab.lib.styles import ParagraphStyle
from reportlab.pdfgen import canvas

from src.core.base.base_generator import BaseGenerator


class CertificateGenerator(BaseGenerator):
	"""Generator for NGORA Certificate of Registration PDFs matching the official format"""

	def __init__(self, output_dir: str = None):
		super().__init__(output_dir)
		self._setup_certificate_styles()

	def _setup_certificate_styles(self):
		"""Setup custom styles matching the official certificate format"""
		certificate_styles = {
			"AuthorityHeader": {
				"parent": self.styles["Normal"],
				"fontSize": 16,
				"fontName": "Helvetica-Bold",
				"alignment": TA_CENTER,
				"spaceAfter": 6,
				"textColor": colors.black,
			},
			"CertificateTitle": {
				"parent": self.styles["Normal"],
				"fontSize": 36,
				"fontName": "Helvetica-Bold",
				"alignment": TA_CENTER,
				"spaceAfter": 12,
				"textColor": colors.black,
			},
			"CertificateBody": {
				"parent": self.styles["Normal"],
				"fontSize": 14,
				"alignment": TA_CENTER,
				"spaceAfter": 12,
				"leading": 18,
			},
			"OrganizationName": {
				"parent": self.styles["Normal"],
				"fontSize": 24,
				"fontName": "Helvetica-Bold",
				"alignment": TA_CENTER,
				"spaceAfter": 12,
				"spaceBefore": 12,
			},
			"SignatureName": {
				"parent": self.styles["Normal"],
				"fontSize": 12,
				"fontName": "Helvetica-Bold",
				"alignment": TA_CENTER,
				"spaceAfter": 2,
			},
			"SignatureTitle": {
				"parent": self.styles["Normal"],
				"fontSize": 10,
				"alignment": TA_CENTER,
				"spaceAfter": 2,
				"fontName": "Helvetica-Oblique",
			},
		}

		for name, props in certificate_styles.items():
			self.styles.add(ParagraphStyle(name=name, **props))

	def validate_data(self, data: Dict[str, Any]) -> bool:
		"""Validate certificate data"""
		required_fields = [
			"organization_name",
			"issue_date",
			"board_chairperson",
			"registrar_ceo",
			"registration_number",
			"verification_url",
		]

		missing_fields = [field for field in required_fields if field not in data]
		if missing_fields:
			raise ValueError(f"Missing required fields: {', '.join(missing_fields)}")

		return True

	def generate_pdf_bytes(self, data: Dict[str, Any]) -> bytes:
		"""Generate certificate PDF matching the official format"""
		self.validate_data(data)

		buffer = BytesIO()
		page_size = landscape(A4)
		c = canvas.Canvas(buffer, pagesize=page_size)
		width, height = page_size

		self._draw_certificate(c, data, width, height)

		c.save()
		buffer.seek(0)
		return buffer.getvalue()

	def _draw_certificate(self, c: canvas.Canvas, data: Dict[str, Any], width: float, height: float):
		"""Draw the complete certificate matching the official design"""
		drawing_methods = [
			(self._draw_border, [width, height]),
			(self._draw_qr_code, [data["verification_url"], 50, height - 95, 50]),
			(self._draw_logo, [width / 2, height - 140]),
			(self._draw_authority_header, [width, height - 160]),
			(self._draw_document_title, ["CERTIFICATE OF REGISTRATION", width, height - 205]),
			(self._draw_document_body, [width, height - 230]),
			(self._draw_organization_name, [data["organization_name"], width, height - 270]),
			(self._draw_legal_text, [width, height - 300]),
			(self._draw_hands_seal_text, [data["issue_date"], width, height - 360]),
			(self._draw_official_seal, [width / 2, height - 510]),
			(self._draw_signatures, [data, width, height - 480]),
			(self._draw_registration_number, [data["registration_number"], width, height - 540]),
		]

		for method, args in drawing_methods:
			method(c, *args)

	def _draw_border(self, c: canvas.Canvas, width: float, height: float):
		"""Draw double border like the official certificate"""
		c.setStrokeColor(colors.black)
		c.setLineWidth(3)
		c.rect(30, 30, width - 60, height - 60)
		c.setLineWidth(1)
		c.rect(40, 40, width - 80, height - 80)

	def _draw_logo(self, c: canvas.Canvas, x: float, y: float):
		"""Draw NGORA logo at the center top"""
		logo_path = Path(__file__).parent.parent.parent / "assets" / "ngora_logo.png"
		if logo_path.exists():
			c.drawImage(str(logo_path), x - 50, y, width=100, height=90)
		else:
			c.setStrokeColor(colors.blue)
			c.setFillColor(colors.blue)
			c.circle(x, y + 50, 50, fill=1)
			c.setFillColor(colors.white)
			c.setFont("Helvetica-Bold", 12)
			c.drawCentredString(x, y + 45, "NGORA")

	def _draw_authority_header(self, c: canvas.Canvas, width: float, y: float):
		"""Draw authority header text"""
		c.setFont("Helvetica-Bold", 16)
		c.drawCentredString(width / 2, y, "NON-GOVERNMENTAL ORGANIZATIONS REGULATORY AUTHORITY")

	def _draw_document_title(self, c: canvas.Canvas, title: str, width: float, y: float):
		"""Draw document title"""
		c.setFont("Helvetica-Bold", 36)
		c.drawCentredString(width / 2, y, title)

	def _draw_document_body(self, c: canvas.Canvas, width: float, y: float):
		"""Draw main document body text"""
		c.setFont("Helvetica", 14)
		c.drawCentredString(width / 2, y, "This is to certify that")

	def _draw_organization_name(self, c: canvas.Canvas, org_name: str, width: float, y: float):
		"""Draw organization name in large bold text"""
		c.setFont("Helvetica-Bold", 24)
		c.drawCentredString(width / 2, y, org_name)

	def _draw_legal_text(self, c: canvas.Canvas, width: float, y: float):
		"""Draw the legal compliance text"""
		c.setFont("Helvetica", 14)
		text_lines = [
			"having complied with the requirements of Section 4 and Section 20 of the Non-Governmental",
			"Organisations Act No. 3 of 2001, is registered to operate in Malawi as a",
			"NON-GOVERNMENTAL ORGANIZATION (NGO)",
		]

		for i, line in enumerate(text_lines):
			if i == 2:
				c.setFont("Helvetica-Bold", 14)
			c.drawCentredString(width / 2, y - (i * 18), line)

	def _draw_hands_seal_text(self, c: canvas.Canvas, issue_date: str, width: float, y: float):
		"""Draw 'Given under our Hands and Seal' text with date"""
		c.setFont("Helvetica", 14)
		c.drawCentredString(width / 2, y, "Given under our Hands and Seal this")
		c.drawCentredString(width / 2, y - 18, self._format_date(issue_date))

	def _draw_official_seal(self, c: canvas.Canvas, x: float, y: float):
		"""Draw official seal in the center"""
		seal_path = Path(__file__).parent.parent.parent / "assets" / "official_seal.png"
		if seal_path.exists():
			c.drawImage(str(seal_path), x - 60, y, width=100, height=100)
		else:
			c.setFillColor(colors.Color(218 / 255, 165 / 255, 32 / 255))  # Gold color
			c.circle(x, y + 60, 60, fill=1)
			c.setFillColor(colors.black)
			c.setFont("Helvetica-Bold", 10)
			c.drawCentredString(x, y + 55, "OFFICIAL")
			c.drawCentredString(x, y + 40, "SEAL")

	def _draw_signatures(self, c: canvas.Canvas, data: Dict[str, Any], width: float, y: float):
		"""Draw signature section with board chairperson and registrar/CEO"""
		c.setStrokeColor(colors.black)
		c.setLineWidth(1)

		# Left signature line (Board Chairperson)
		left_x = width / 4
		c.line(left_x - 80, y, left_x + 80, y)

		# Right signature line (Registrar/CEO)
		right_x = 3 * width / 4
		c.line(right_x - 80, y, right_x + 80, y)

		# Names
		c.setFont("Helvetica-Bold", 12)
		c.drawCentredString(left_x, y - 15, data["board_chairperson"])
		c.drawCentredString(right_x, y - 15, data["registrar_ceo"])

		# Titles
		c.setFont("Helvetica-Oblique", 10)
		c.drawCentredString(left_x, y - 30, "Board Chairperson")
		c.drawCentredString(right_x, y - 30, "Registrar/CEO")

	def _draw_registration_number(self, c: canvas.Canvas, reg_number: str, width: float, y: float):
		"""Draw registration number at the bottom"""
		c.setFont("Helvetica", 12)
		c.drawCentredString(width / 2, y, f"Registration No. {reg_number}")

	def _build_content(self, data: Dict[str, Any]) -> List:
		"""Legacy method for compatibility"""
		return []
