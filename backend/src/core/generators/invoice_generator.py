from datetime import datetime
from typing import Any, Dict, List

from reportlab.lib import colors
from reportlab.lib.units import inch
from reportlab.platypus import Paragraph, Spacer, Table

from src.core.base.base_generator import BaseGenerator


class InvoiceGenerator(BaseGenerator):
	"""Generator for invoice PDFs"""

	def validate_data(self, data: Dict[str, Any]) -> bool:
		"""Validate invoice data"""
		required_fields = [
			"invoice_number",
			"organization_name",
			"organization_address",
			"invoice_date",
			"due_date",
			"items",
			"total_amount",
			"currency",
		]

		for field in required_fields:
			if field not in data:
				raise ValueError(f"Missing required field: {field}")

		if not isinstance(data["items"], list) or not data["items"]:
			raise ValueError("Items must be a non-empty list")

		for item in data["items"]:
			required_item_fields = ["description", "quantity", "unit_price", "amount"]
			for field in required_item_fields:
				if field not in item:
					raise ValueError(f"Missing required item field: {field}")

		return True

	def _build_content(self, data: Dict[str, Any]) -> List:
		"""Build invoice content"""
		self.validate_data(data)

		story = []

		# Header
		header_elements = self._create_header("INVOICE", "Non-Governmental Organizations Regulatory Authority (NGORA)")
		story.extend(header_elements)

		# Invoice details section
		story.extend(self._create_invoice_details(data))

		# Billing information
		story.extend(self._create_billing_info(data))

		# Items table
		story.extend(self._create_items_table(data["items"], data["currency"]))

		# Totals section
		story.extend(self._create_totals_section(data))

		# Payment information
		story.extend(self._create_payment_info(data))

		# Footer
		footer_elements = self._create_footer(f"Invoice #{data['invoice_number']}")
		story.extend(footer_elements)

		return story

	def _create_invoice_details(self, data: Dict[str, Any]) -> List:
		"""Create invoice details section"""
		elements = []

		# Invoice number and dates
		invoice_data = [
			["Invoice Number:", data["invoice_number"]],
			["Invoice Date:", self._format_date(data["invoice_date"])],
			["Due Date:", self._format_date(data["due_date"])],
		]

		if "reference_number" in data:
			invoice_data.append(["Reference:", data["reference_number"]])

		table = self._create_table(
			invoice_data,
			col_widths=[2 * inch, 3 * inch],
			style_commands=[
				("BACKGROUND", (0, 0), (-1, 0), colors.white),
				("BACKGROUND", (0, 1), (-1, -1), colors.white),
				("ALIGN", (0, 0), (0, -1), "LEFT"),
				("ALIGN", (1, 0), (1, -1), "LEFT"),
				("FONTNAME", (0, 0), (0, -1), "Helvetica-Bold"),
				("GRID", (0, 0), (-1, -1), 0, colors.white),
			],
		)

		elements.append(table)
		elements.append(Spacer(1, 24))

		return elements

	def _create_billing_info(self, data: Dict[str, Any]) -> List:
		"""Create billing information section"""
		elements = []

		elements.append(Paragraph("Bill To:", self.styles["CustomHeading"]))
		elements.append(Paragraph(data["organization_name"], self.styles["CustomNormal"]))

		address = data["organization_address"]
		if isinstance(address, dict):
			address_lines = []
			if address.get("street"):
				address_lines.append(address["street"])
			if address.get("city"):
				address_lines.append(address["city"])
			if address.get("state"):
				address_lines.append(address["state"])
			if address.get("postal_code"):
				address_lines.append(address["postal_code"])
			if address.get("country"):
				address_lines.append(address["country"])
			address_text = "<br/>".join(address_lines)
		else:
			address_text = str(address)

		elements.append(Paragraph(address_text, self.styles["CustomNormal"]))

		if "tax_id" in data:
			elements.append(Paragraph(f"Tax ID: {data['tax_id']}", self.styles["CustomNormal"]))

		elements.append(Spacer(1, 24))

		return elements

	def _create_items_table(self, items: List[Dict], currency: str) -> List:
		"""Create items table"""
		elements = []

		table_data = [["Description", "Quantity", "Unit Price", "Amount"]]

		for item in items:
			table_data.append(
				[
					item["description"],
					str(item["quantity"]),
					f"{currency} {item['unit_price']:,.2f}",
					f"{currency} {item['amount']:,.2f}",
				]
			)

		table = self._create_table(
			table_data,
			col_widths=[4 * inch, 1 * inch, 1.5 * inch, 1.5 * inch],
			style_commands=[
				("ALIGN", (1, 0), (-1, -1), "RIGHT"),
				("ALIGN", (0, 0), (0, -1), "LEFT"),
			],
		)

		elements.append(table)
		elements.append(Spacer(1, 12))

		return elements

	def _create_totals_section(self, data: Dict[str, Any]) -> List:
		"""Create totals section"""
		elements = []

		currency = data["currency"]
		totals_data = []

		# Subtotal
		if "subtotal" in data:
			totals_data.append(["Subtotal:", f"{currency} {data['subtotal']:,.2f}"])

		# Tax
		if "tax_amount" in data and data["tax_amount"] > 0:
			tax_rate = data.get("tax_rate", 0)
			tax_label = f"Tax ({tax_rate}%):" if tax_rate else "Tax:"
			totals_data.append([tax_label, f"{currency} {data['tax_amount']:,.2f}"])

		# Discount
		if "discount_amount" in data and data["discount_amount"] > 0:
			totals_data.append(["Discount:", f"-{currency} {data['discount_amount']:,.2f}"])

		# Total
		totals_data.append(["Total Amount:", f"{currency} {data['total_amount']:,.2f}"])

		# Create right-aligned totals table
		totals_table = Table(totals_data, colWidths=[2 * inch, 1.5 * inch], hAlign="RIGHT")

		totals_table.setStyle(
			[
				("ALIGN", (0, 0), (-1, -1), "RIGHT"),
				("FONTNAME", (0, -1), (-1, -1), "Helvetica-Bold"),
				("FONTSIZE", (0, -1), (-1, -1), 12),
				("LINEBELOW", (0, -1), (-1, -1), 2, colors.black),
				("GRID", (0, 0), (-1, -1), 0, colors.white),
			]
		)

		elements.append(totals_table)
		elements.append(Spacer(1, 24))

		return elements

	def _create_payment_info(self, data: Dict[str, Any]) -> List:
		"""Create payment information section"""
		elements = []

		elements.append(Paragraph("Payment Information:", self.styles["CustomHeading"]))

		# Payment terms
		if "payment_terms" in data:
			elements.append(Paragraph(f"Payment Terms: {data['payment_terms']}", self.styles["CustomNormal"]))

		# Payment methods
		if "payment_methods" in data:
			elements.append(Paragraph("Accepted Payment Methods:", self.styles["CustomNormal"]))
			for method in data["payment_methods"]:
				elements.append(Paragraph(f"• {method}", self.styles["CustomNormal"]))

		# Bank details
		if "bank_details" in data:
			elements.append(Paragraph("Bank Details:", self.styles["CustomNormal"]))
			bank = data["bank_details"]
			if "bank_name" in bank:
				elements.append(Paragraph(f"Bank: {bank['bank_name']}", self.styles["CustomNormal"]))
			if "account_number" in bank:
				elements.append(Paragraph(f"Account Number: {bank['account_number']}", self.styles["CustomNormal"]))
			if "routing_number" in bank:
				elements.append(Paragraph(f"Routing Number: {bank['routing_number']}", self.styles["CustomNormal"]))

		# Notes
		if "notes" in data:
			elements.append(Spacer(1, 12))
			elements.append(Paragraph("Notes:", self.styles["CustomHeading"]))
			elements.append(Paragraph(data["notes"], self.styles["CustomNormal"]))

		return elements

	def _get_watermark_text(self, data: Dict[str, Any]) -> str:
		"""Return watermark text based on invoice status"""
		status = data.get("status", "").upper()
		if status == "PAID":
			return "PAID"
		return ""

	def _format_date(self, date_value) -> str:
		"""Format date for display"""
		if isinstance(date_value, str):
			try:
				date_obj = datetime.fromisoformat(date_value.replace("Z", "+00:00"))
				return date_obj.strftime("%Y-%m-%d")
			except Exception:
				return date_value
		elif isinstance(date_value, datetime):
			return date_value.strftime("%Y-%m-%d")
		else:
			return str(date_value)
