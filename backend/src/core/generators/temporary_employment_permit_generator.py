from datetime import datetime
from typing import Any, Dict, List

from reportlab.lib import colors
from reportlab.lib.enums import TA_CENTER
from reportlab.lib.styles import ParagraphStyle
from reportlab.lib.units import inch
from reportlab.platypus import Paragraph, Spacer

from src.core.base.base_generator import BaseGenerator


class TemporaryEmploymentPermitGenerator(BaseGenerator):
	"""Generator for temporary employment permit PDFs"""

	def validate_data(self, data: Dict[str, Any]) -> bool:
		"""Validate temporary employment permit data"""
		required_fields = [
			"permit_number",
			"employee_name",
			"employee_nationality",
			"passport_number",
			"organization_name",
			"position",
			"issue_date",
			"expiry_date",
			"organization_address",
		]

		for field in required_fields:
			if field not in data:
				raise ValueError(f"Missing required field: {field}")

		return True

	def _setup_custom_styles(self):
		"""Setup custom styles for permits"""
		super()._setup_custom_styles()

		self.styles.add(
			ParagraphStyle(
				name="PermitTitle",
				parent=self.styles["Title"],
				fontSize=18,
				spaceAfter=18,
				alignment=TA_CENTER,
				textColor=colors.darkred,
				fontName="Helvetica-Bold",
			)
		)

		self.styles.add(
			ParagraphStyle(
				name="PermitWarning",
				parent=self.styles["Normal"],
				fontSize=10,
				spaceAfter=6,
				alignment=TA_CENTER,
				textColor=colors.red,
				fontName="Helvetica-Bold",
			)
		)

	def _build_content(self, data: Dict[str, Any]) -> List:
		"""Build temporary employment permit content"""
		self.validate_data(data)

		story = []

		header_elements = self._create_header(
			"TEMPORARY EMPLOYMENT PERMIT",
			"Republic of Malawi - Ministry of Gender, Community Development and Social Welfare",
		)
		story.extend(header_elements)

		story.extend(self._create_authority_section())

		story.extend(self._create_permit_details(data))

		story.extend(self._create_employee_info(data))

		story.extend(self._create_organization_info(data))

		story.extend(self._create_employment_details(data))

		story.extend(self._create_terms_conditions(data))

		story.extend(self._create_signature_section("Director General, NGORA"))

		story.extend(self._create_permit_footer(data))

		return story

	def _create_authority_section(self) -> List:
		"""Create authority section"""
		elements = []

		elements.append(
			Paragraph(
				"This permit is issued under the authority of the Non-Governmental Organizations Act (Cap. 5:05) "
				"and relevant employment regulations of the Laws of Malawi by the Non-Governmental Organizations "
				"Regulatory Authority (NGORA).",
				self.styles["CustomNormal"],
			)
		)
		elements.append(Spacer(1, 18))

		return elements

	def _create_permit_details(self, data: Dict[str, Any]) -> List:
		"""Create permit details section"""
		elements = []

		elements.append(Paragraph("PERMIT DETAILS", self.styles["CustomHeading"]))

		permit_data = [
			["Permit Number:", data["permit_number"]],
			["Issue Date:", self._format_date(data["issue_date"])],
			["Expiry Date:", self._format_date(data["expiry_date"])],
			["Permit Type:", "Temporary Employment"],
		]

		if "reference_number" in data:
			permit_data.append(["Reference Number:", data["reference_number"]])

		table = self._create_table(
			permit_data,
			col_widths=[2.5 * inch, 3.5 * inch],
			style_commands=[
				("BACKGROUND", (0, 0), (-1, -1), colors.white),
				("ALIGN", (0, 0), (0, -1), "LEFT"),
				("ALIGN", (1, 0), (1, -1), "LEFT"),
				("FONTNAME", (0, 0), (0, -1), "Helvetica-Bold"),
				("GRID", (0, 0), (-1, -1), 1, colors.black),
			],
		)

		elements.append(table)
		elements.append(Spacer(1, 18))

		return elements

	def _create_employee_info(self, data: Dict[str, Any]) -> List:
		"""Create employee information section"""
		elements = []

		elements.append(Paragraph("EMPLOYEE INFORMATION", self.styles["CustomHeading"]))

		employee_data = [
			["Full Name:", data["employee_name"]],
			["Nationality:", data["employee_nationality"]],
			["Passport Number:", data["passport_number"]],
		]

		if "date_of_birth" in data:
			employee_data.append(["Date of Birth:", self._format_date(data["date_of_birth"])])

		if "employee_address" in data:
			employee_data.append(["Address:", data["employee_address"]])

		if "qualification" in data:
			employee_data.append(["Qualification:", data["qualification"]])

		table = self._create_table(
			employee_data,
			col_widths=[2.5 * inch, 3.5 * inch],
			style_commands=[
				("BACKGROUND", (0, 0), (-1, -1), colors.white),
				("ALIGN", (0, 0), (0, -1), "LEFT"),
				("ALIGN", (1, 0), (1, -1), "LEFT"),
				("FONTNAME", (0, 0), (0, -1), "Helvetica-Bold"),
				("GRID", (0, 0), (-1, -1), 1, colors.black),
			],
		)

		elements.append(table)
		elements.append(Spacer(1, 18))

		return elements

	def _create_organization_info(self, data: Dict[str, Any]) -> List:
		"""Create organization information section"""
		elements = []

		elements.append(Paragraph("EMPLOYING ORGANIZATION", self.styles["CustomHeading"]))

		elements.append(Paragraph(f"Organization Name: {data['organization_name']}", self.styles["CustomNormal"]))
		elements.append(Spacer(1, 6))

		elements.append(Paragraph("Organization Address:", self.styles["CustomNormal"]))
		address = data["organization_address"]
		if isinstance(address, dict):
			address_lines = []
			if address.get("street"):
				address_lines.append(address["street"])
			if address.get("city"):
				address_lines.append(address["city"])
			if address.get("state"):
				address_lines.append(address["state"])
			if address.get("postal_code"):
				address_lines.append(address["postal_code"])
			if address.get("country"):
				address_lines.append(address["country"])
			address_text = "<br/>".join(address_lines)
		else:
			address_text = str(address)

		elements.append(Paragraph(address_text, self.styles["CustomNormal"]))
		elements.append(Spacer(1, 6))

		if "organization_registration" in data:
			elements.append(
				Paragraph(f"Registration Number: {data['organization_registration']}", self.styles["CustomNormal"])
			)
			elements.append(Spacer(1, 6))

		elements.append(Spacer(1, 12))

		return elements

	def _create_employment_details(self, data: Dict[str, Any]) -> List:
		"""Create employment details section"""
		elements = []

		elements.append(Paragraph("EMPLOYMENT DETAILS", self.styles["CustomHeading"]))

		employment_data = [
			["Position/Job Title:", data["position"]],
			["Employment Start Date:", self._format_date(data.get("employment_start_date", data["issue_date"]))],
			["Employment End Date:", self._format_date(data.get("employment_end_date", data["expiry_date"]))],
		]

		if "department" in data:
			employment_data.append(["Department:", data["department"]])

		if "salary" in data:
			currency = data.get("currency", "MWK")
			employment_data.append(["Monthly Salary:", f"{currency} {data['salary']:,.2f}"])

		if "work_location" in data:
			employment_data.append(["Work Location:", data["work_location"]])

		table = self._create_table(
			employment_data,
			col_widths=[2.5 * inch, 3.5 * inch],
			style_commands=[
				("BACKGROUND", (0, 0), (-1, -1), colors.white),
				("ALIGN", (0, 0), (0, -1), "LEFT"),
				("ALIGN", (1, 0), (1, -1), "LEFT"),
				("FONTNAME", (0, 0), (0, -1), "Helvetica-Bold"),
				("GRID", (0, 0), (-1, -1), 1, colors.black),
			],
		)

		elements.append(table)
		elements.append(Spacer(1, 18))

		return elements

	def _create_terms_conditions(self, data: Dict[str, Any]) -> List:
		"""Create terms and conditions section"""
		elements = []

		elements.append(Paragraph("TERMS AND CONDITIONS", self.styles["CustomHeading"]))

		default_conditions = [
			"This permit is valid only for the specified period and employment with the named organization.",
			"The permit holder must carry this document at all times during employment.",
			"Any change in employment terms or organization must be reported to NGORA immediately.",
			"The permit is non-transferable and applies only to the named individual and organization.",
			"The organization must ensure compliance with all labor laws and regulations of Malawi.",
			"This permit may be revoked if found to be obtained through false information.",
			"The permit holder must depart Malawi or obtain proper authorization before the expiry date.",
			"Any violation of permit conditions may result in deportation and future entry restrictions.",
		]

		conditions = data.get("conditions", default_conditions)

		for i, condition in enumerate(conditions, 1):
			elements.append(Paragraph(f"{i}. {condition}", self.styles["CustomNormal"]))
			elements.append(Spacer(1, 4))

		elements.append(Spacer(1, 18))

		return elements

	def _create_permit_footer(self, data: Dict[str, Any]) -> List:
		"""Create permit footer with warnings"""
		elements = []

		# Warning section
		elements.append(Paragraph("IMPORTANT NOTICE", self.styles["PermitWarning"]))
		elements.append(
			Paragraph(
				"This permit is issued for temporary employment purposes only. "
				"Unauthorized employment or overstaying beyond the permit period is strictly prohibited "
				"and may result in legal action including deportation.",
				self.styles["PermitWarning"],
			)
		)
		elements.append(Spacer(1, 12))

		# Validity check
		elements.append(
			Paragraph(
				"For permit verification, contact <NAME_EMAIL> or visit www.ngora.mw",
				self.styles["CustomNormal"],
			)
		)

		# Footer
		footer_elements = self._create_footer(f"Permit #{data['permit_number']}")
		elements.extend(footer_elements)

		return elements

	def _format_date(self, date_value) -> str:
		"""Format date for display"""
		if isinstance(date_value, str):
			try:
				date_obj = datetime.fromisoformat(date_value.replace("Z", "+00:00"))
				return date_obj.strftime("%d %B %Y")
			except Exception:
				return date_value
		elif isinstance(date_value, datetime):
			return date_value.strftime("%d %B %Y")
		else:
			return str(date_value)
