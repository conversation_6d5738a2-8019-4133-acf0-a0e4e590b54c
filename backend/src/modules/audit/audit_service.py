from datetime import timedelta
from fastapi_pagination import Params
from fastapi_pagination.ext.sqlalchemy import paginate
from src.core.shared_schema import Pagination
from src.config.db.models.audit_log import AuditLog
from src.core.base.base_repository import BaseRepository

from .audit_schema import AuditResponse, AuditFilter


class AuditService(BaseRepository):
	def __init__(self):
		super().__init__()

	async def find_audits(self, filters: AuditFilter) -> Pagination[AuditResponse]:
		try:
			q = self.db.query(AuditLog).order_by(AuditLog.created_at.desc())

			f = filters.model_dump(exclude_unset=True)

			if f.get("user_id"):
				q = q.filter(AuditLog.user_id == f.get("user_id"))

			if f.get("action"):
				q = q.filter(AuditLog.action == f.get("action"))

			if f.get("table_name"):
				q = q.filter(AuditLog.table_name.ilike(f"%{f.get('table_name')}%"))

			# date range
			if f.get("start_date"):
				q = q.filter(AuditLog.created_at > f.get("start_date") - timedelta(days=1))

			if f.get("end_date"):
				q = q.filter(AuditLog.created_at < f.get("end_date") + timedelta(days=1))

			# text search across old/new values
			if f.get("query"):
				q = q.filter(
					(AuditLog.old_values.ilike(f"%{f.get('query')}%"))
					| (AuditLog.new_values.ilike(f"%{f.get('query')}%"))
				)

			result = paginate(q, Params(page=filters.page, size=filters.size))
			data = [AuditResponse.model_validate(row) for row in result.items]

			return Pagination.from_query_result(data, result)
		except Exception as e:
			self.logger.error(f"Error finding audits: {e}")
			raise e

	async def get_actions(self) -> list[str]:
		try:
			rows = self.db.query(AuditLog.action).distinct().all()
			return [r[0] for r in rows]

		except Exception as e:
			self.logger.error(f"Error fetching actions: {e}")
			return []
