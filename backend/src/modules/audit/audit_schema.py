from datetime import datetime
from typing import Optional

from pydantic import BaseModel, UUID4, ConfigDict
from src.core.shared_schema import BaseRequest


class AuditDto(BaseModel):
	model_config = ConfigDict(from_attributes=True)

	id: UUID4
	table_name: str
	record_id: Optional[UUID4]
	action: str
	old_values: Optional[str]
	new_values: Optional[str]
	created_at: datetime


class AuditUserDto(BaseModel):
	model_config = ConfigDict(from_attributes=True)

	id: UUID4
	first_name: str
	last_name: str
	email: str
	gender: str


class AuditResponse(BaseModel):
	model_config = ConfigDict(from_attributes=True, arbitrary_types_allowed=True)

	table_name: str
	record_id: Optional[UUID4]
	action: str
	old_values: Optional[str]
	new_values: Optional[str]
	created_at: datetime
	user: AuditUserDto


class AuditFilter(BaseRequest):
	user_id: Optional[UUID4] = None
	action: Optional[str] = None
	table_name: Optional[str] = None
	start_date: Optional[datetime] = None
	end_date: Optional[datetime] = None
