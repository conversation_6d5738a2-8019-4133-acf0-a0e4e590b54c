from fastapi import Depends, Request

from src.core.exceptions.api import ApiException
from src.core.guards.auth_guard import auth_guard
from src.core.shared_schema import BaseResponse, Pagination

from .audit_schema import AuditResponse, AuditFilter
from .audit_service import AuditService

service = AuditService()


@auth_guard.authenticated
async def fetch_audits_handler(_: Request, filters: AuditFilter = Depends(AuditFilter)) -> Pagination[AuditResponse]:
	try:
		return await service.find_audits(filters)
	except Exception as e:
		raise ApiException(str(e))


@auth_guard.authenticated
async def get_actions_handler(_: Request) -> BaseResponse[list[str]]:
	try:
		return BaseResponse(data=await service.get_actions())
	except Exception as e:
		raise ApiException(str(e))
