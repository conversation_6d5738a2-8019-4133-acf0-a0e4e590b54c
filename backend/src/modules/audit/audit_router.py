from fastapi import APIRouter, status

from src.core.shared_schema import BaseResponse, Pagination
import src.modules.audit.audit_controller as controller
import src.modules.audit.audit_schema as schema

router = APIRouter(tags=["audits"])

router.add_api_route(
	path="",
	endpoint=controller.fetch_audits_handler,
	response_model=Pagination[schema.AuditResponse],
	methods=["GET"],
	status_code=status.HTTP_200_OK,
	description="Retrieve list of audit logs",
)

router.add_api_route(
	path="/actions",
	endpoint=controller.get_actions_handler,
	response_model=BaseResponse[list[str]],
	methods=["GET"],
	status_code=status.HTTP_200_OK,
	description="Retrieve available audit actions",
)
