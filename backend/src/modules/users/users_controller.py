from typing import List
from uuid import UUID
from fastapi import Depends, Request

from src.core.dtos.user_dtos import UserDto
from src.core.guards.auth_guard import auth_guard
from src.core.shared_schema import Pagination, BaseResponse
from src.modules.users.users_schema import (
	UserFilters,
	UserDepartmentAssignRequest,
	UserDepartmentsResponse,
	UserRoleAssignRequest,
	UserRolesResponse,
	UserRequest,
)
from src.modules.users.users_service import UsersService


def get_users_service() -> UsersService:
	"""Get UsersService instance"""
	return UsersService()


@auth_guard.can_("read.user")
async def index(_: Request, filters: UserFilters = Depends(UserFilters)) -> Pagination[UserDto]:
	service = get_users_service()
	pagination = await service.find_users(filters)
	return pagination


@auth_guard.can_("create.user")
async def create(_: Request, user: UserRequest) -> BaseResponse[UserDto]:
	service = get_users_service()
	user = await service.create_user(user)
	return BaseResponse(data=user)


@auth_guard.can_("update.user")
async def update(_: Request, id, updates: UserRequest) -> BaseResponse[UserDto]:
	service = get_users_service()
	user = service.update_user(user_id=id, updates=updates)
	return BaseResponse(data=user)


def delete(user_id: UUID):
	service = get_users_service()
	return service.delete_user(user_id)


def assign_user_to_departments(request: UserDepartmentAssignRequest) -> UserDepartmentsResponse:
	"""
	Assign a user to multiple departments.

	Args:
	    request (UserDepartmentAssignRequest): Assignment request data.

	Returns:
	    UserDepartmentsResponse: Assignment response data.
	"""
	service = get_users_service()
	return service.assign_user_to_departments(request)


def unassign_user_from_departments(user_id: UUID, department_ids: List[UUID] = None):
	"""
	Unassign a user from all their departments.

	Args:
	    user_id (UUID): User ID to unassign.
	"""
	service = get_users_service()
	service.unassign_user_from_departments(user_id, department_ids)


def assign_roles_to_user(request: UserRoleAssignRequest) -> UserRolesResponse:
	"""
	Assign multiple roles to a user.

	Args:
	    request (UserRoleAssignRequest): Assignment request data.

	Returns:
	    UserRolesResponse: Assignment response data.
	"""
	service = get_users_service()
	return service.assign_roles_to_user(request)


def unassign_roles_from_user(user_id: UUID, role_ids: List[UUID] = None) -> None:
	"""
	Unassign all roles from a user.

	Args:
	    user_id (UUID): User ID to unassign roles from.
	"""
	service = get_users_service()
	service.unassign_roles_from_user(user_id, role_ids)
