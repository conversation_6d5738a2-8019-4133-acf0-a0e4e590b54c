from fastapi import APIRouter, status

from src.modules.roles.roles_schema import RoleResponse
from src.core.shared_schema import Pagination, BaseResponse
from src.modules.roles import roles_controller as controller


router = APIRouter(tags=["roles"])

router.add_api_route(
	path="",
	endpoint=controller.index,
	name="Get and Search For Roles",
	methods=["GET"],
	status_code=status.HTTP_200_OK,
	response_model=Pagination[RoleResponse],
)

router.add_api_route(
	path="",
	name="Create Role",
	endpoint=controller.create,
	methods=["POST"],
	response_model=BaseResponse[RoleResponse],
	status_code=status.HTTP_201_CREATED,
)

router.add_api_route(
	path="/{role_id}",
	endpoint=controller.get_by_id,
	name="Get Role by ID",
	methods=["GET"],
	status_code=status.HTTP_200_OK,
	response_model=BaseResponse[RoleResponse],
)

router.add_api_route(
	name="Edit Role Attributes",
	path="/{role_id}",
	endpoint=controller.update,
	methods=["PUT"],
	response_model=BaseResponse[RoleResponse],
	status_code=status.HTTP_200_OK,
)

router.add_api_route(
	path="/{role_id}",
	endpoint=controller.delete,
	methods=["DELETE"],
	name="Delete A Role",
	status_code=status.HTTP_204_NO_CONTENT,
)

# Role-Permission Assignment Routes
router.add_api_route(
	path="/assign-permissions",
	endpoint=controller.assign_permissions_to_role,
	methods=["POST"],
	name="Assign Permissions to Role",
	response_model=BaseResponse[RoleResponse],
	status_code=status.HTTP_201_CREATED,
)

router.add_api_route(
	path="/{role_id}/unassign-permissions",
	endpoint=controller.unassign_permissions_from_role,
	methods=["DELETE"],
	name="Unassign Permissions from Role",
	status_code=status.HTTP_204_NO_CONTENT,
)
