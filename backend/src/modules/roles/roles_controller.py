from typing import List
from uuid import UUID
from fastapi import Depends, Request
from src.core.guards.auth_guard import auth_guard
from src.core.shared_schema import Pagination, BaseResponse
from src.modules.roles.roles_schema import (
	RoleResponse,
	RoleFilters,
	RoleCreate,
	RoleUpdate,
	RolePermissionAssignRequest,
)
from src.modules.roles.roles_service import RolesService

service = RolesService()


@auth_guard.authenticated
async def index(_: Request, filters: RoleFilters = Depends(RoleFilters)) -> Pagination[RoleResponse]:
	"""
	Get and search for roles with pagination.

	Args:
	    filters (RoleFilters): Query filters for roles.

	Returns:
	    Page[RoleResponse]: Paginated list of roles.
	"""
	pagination = await service.find_roles(filters)
	return pagination


@auth_guard.authenticated
async def create(_: Request, role: RoleCreate) -> BaseResponse[RoleResponse]:
	"""
	Create a new role.

	Args:
	    role (RoleCreate): Role data to create.

	Returns:
	    RoleResponse: Created role data.
	"""
	role = await service.create_role(role)
	return BaseResponse(data=role)


@auth_guard.authenticated
async def get_by_id(_: Request, role_id: UUID) -> RoleResponse:
	"""
	Get a role by ID.

	Args:
	    role_id (UUID): Role ID.

	Returns:
	    RoleResponse: Role data.
	"""
	return await service.get_role_by_id(role_id)


@auth_guard.authenticated
async def update(_: Request, role_id: UUID, updates: RoleUpdate) -> BaseResponse[RoleResponse]:
	"""
	Update a role.

	Args:
	    role_id (UUID): Role ID to update.
	    updates (RoleUpdate): Updates to apply.

	Returns:
	    RoleResponse: Updated role data.
	"""
	role = await service.update_role(role_id, updates)
	return BaseResponse(data=role)


# @auth_guard.can_("delete.role")
async def delete(role_id: UUID, delete_reason: str = None):
	"""
	Delete a role (soft delete).

	Args:
	    role_id (UUID): Role ID to delete.
	    delete_reason (str): Reason for deletion.
	"""
	return await service.delete_role(role_id, delete_reason)


@auth_guard.authenticated
async def assign_permissions_to_role(request: RolePermissionAssignRequest) -> RoleResponse:
	"""
	Assign multiple permissions to a role.

	Args:
	    request (RolePermissionAssignRequest): Assignment request data.

	Returns:
	    RolePermissionsResponse: Assignment response data.
	"""
	return await service.assign_permissions_to_role(request)


@auth_guard.authenticated
async def unassign_permissions_from_role(role_id: UUID, permission_ids: List[UUID] = None) -> None:
	"""
	Unassign permissions from a role.

	Args:
	    role_id (UUID): Role ID to unassign permissions from.
	    permission_ids (List[UUID], optional): Specific permission IDs to unassign.
	"""
	return await service.unassign_permissions_from_role(role_id, permission_ids)
