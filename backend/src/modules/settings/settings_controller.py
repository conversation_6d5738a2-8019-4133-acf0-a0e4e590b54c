from fastapi import Depends, Request
from pydantic import UUID4

from src.core.exceptions.api import ApiException
from src.core.guards.auth_guard import auth_guard
from src.core.http.responses import bad_request
from src.core.shared_schema import BaseResponse

from .settings_schema import (
	CountryFilter,
	DistrictDto,
	DistrictFilter,
	DistrictRequest,
	LoadableItemDto,
	LoadableItemFilter,
	LoadableItemRequest,
	RegionDto,
	RegionFilter,
	RegionRequest,
	SystemConfigurationRequest,
)
from .settings_service import SettingsService

settings_service = SettingsService()


def fetch_countries_handler(filter: CountryFilter = Depends(CountryFilter)):
	try:
		return settings_service.retrieve_countries(filter)
	except ApiException as e:
		return bad_request(e.message)


def fetch_districts_handler(filter: DistrictFilter = Depends(DistrictFilter)):
	try:
		return settings_service.retrieve_districts(filter)
	except ApiException as e:
		return bad_request(e.message)


def get_district_handler(district_id: UUID4):
	try:
		district = settings_service.get_district_by_id(district_id)
		return BaseResponse[DistrictDto](data=district)
	except ApiException as e:
		return bad_request(e.message)


def create_district_handler(data: DistrictRequest):
	try:
		district = settings_service.create_district(data)
		return BaseResponse[DistrictDto](data=district)
	except ApiException as e:
		return bad_request(e.message)


@auth_guard.can_("update.district")
def update_district_handler(_: Request, district_id: UUID4, data: DistrictRequest):
	try:
		district = settings_service.update_district(district_id, data)
		return BaseResponse[DistrictDto](data=district)
	except ApiException as e:
		return bad_request(e.message)


@auth_guard.can_("delete.district")
def delete_district_handler(_: Request, district_id: UUID4):
	try:
		deleted = settings_service.delete_district(district_id)
		return BaseResponse[bool](data=deleted)
	except ApiException as e:
		return bad_request(e.message)


def fetch_regions_handler(filter: RegionFilter = Depends(RegionFilter)):
	try:
		return settings_service.retrieve_regions(filter)
	except ApiException as e:
		return bad_request(e.message)


def get_region_handler(region_id: UUID4):
	try:
		region = settings_service.get_region_by_id(region_id)
		return BaseResponse[RegionDto](data=region)
	except ApiException as e:
		return bad_request(e.message)


def create_region_handler(data: RegionRequest):
	try:
		region = settings_service.create_region(data)
		return BaseResponse[RegionDto](data=region)
	except ApiException as e:
		return bad_request(e.message)


@auth_guard.can_("update.region")
def update_region_handler(_: Request, region_id: UUID4, data: RegionRequest):
	try:
		region = settings_service.update_region(region_id, data)
		return BaseResponse[RegionDto](data=region)
	except ApiException as e:
		return bad_request(e.message)


@auth_guard.can_("delete.district")
def delete_region_handler(_: Request, region_id: UUID4):
	try:
		deleted = settings_service.delete_region(region_id)
		return BaseResponse[bool](data=deleted)
	except ApiException as e:
		return bad_request(e.message)


def fetch_loadable_item_handler(filter: LoadableItemFilter = Depends(LoadableItemFilter)):
	try:
		return settings_service.retrieve_loadable_items(filter)
	except ApiException as e:
		return bad_request(e.message)


def fetch_permissions_handler():
	"""Fetch all permissions (loadable items with type='PERMISSION')"""
	try:
		filter = LoadableItemFilter(type="PERMISSION")
		return settings_service.retrieve_loadable_items(filter)
	except ApiException as e:
		raise bad_request(e.message)


@auth_guard.can_("create.loadable_item")
def create_loadable_item_handler(_: Request, data: LoadableItemRequest):
	try:
		item = settings_service.add_loadable_item(data)

		return BaseResponse[LoadableItemDto](data=item)
	except ApiException as e:
		return bad_request(e.message)


@auth_guard.can_("update.loadable_item")
def update_loadable_item_handler(_: Request, id: UUID4, data: LoadableItemRequest):
	item = settings_service.update_loadable_item(id, data)

	return BaseResponse[LoadableItemDto](data=item)


@auth_guard.can_("delete.loadable_item")
async def delete_loadable_item_handler(_: Request, id: UUID4, void_reason: str) -> BaseResponse[bool]:
	deleted = await settings_service.delete_loadable_item(id, void_reason)

	return BaseResponse[bool](data=deleted)


def fetch_loadable_item_types_handler():
	"""Fetch all distinct loadable item types"""
	types = settings_service.retrieve_loadable_item_types()
	return BaseResponse[list[str]](data=types)


def get_system_configuration_handler(group: str) -> BaseResponse[dict]:
	"""Get system configuration settings."""
	config = settings_service.get_system_configuration(group=group)
	return BaseResponse[dict](data=config)


@auth_guard.can_("update.config")
async def update_system_configuration_handler(_: Request, data: SystemConfigurationRequest):
	"""Update system configuration settings."""
	config = await settings_service.update_system_configuration(data)
	return BaseResponse[dict](data=config)
