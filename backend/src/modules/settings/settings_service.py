from fastapi_pagination import Params
from fastapi_pagination.ext.sqlalchemy import paginate
from pydantic import UUID4

from src.config.db.models import Country, GlobalProperty, LoadableItem
from src.config.db.models.district import District
from src.config.db.models.region import Region
from src.core.base.base_repository import BaseRepository
from src.core.exceptions.api import ApiException
from src.core.shared_schema import Pagination
from src.core.utils import serializer

from .settings_schema import (
	CountryDto,
	CountryFilter,
	DistrictDto,
	DistrictFilter,
	DistrictRequest,
	LoadableItemDto,
	LoadableItemFilter,
	LoadableItemRequest,
	RegionDto,
	RegionFilter,
	RegionRequest,
	SystemConfigurationRequest,
)


class SettingsService(BaseRepository):
	def __init__(self):
		super().__init__()

	def retrieve_countries(self, filter: CountryFilter) -> Pagination[CountryDto]:
		try:
			"""Retrieve countries based on the provided filter."""
			query = self.db.query(Country).order_by(Country.name.asc())

			if filter.name:
				query = query.filter(Country.name.ilike(f"%{filter.name}%"))

			paginated_result = paginate(query, Params(page=filter.page, size=filter.size))
			countries = [serializer.to_country_dto(row) for row in paginated_result.items]

			return Pagination.from_query_result(countries, paginated_result)
		except Exception as e:
			self.logger.error(f"Failed to retrieve countries: {str(e)}")
			raise ApiException("Failed to retrieve countries")

	def retrieve_regions(self, filter: RegionFilter) -> Pagination[RegionDto]:
		try:
			"""Retrieve regions based on the provided filter."""
			query = self.db.query(Region)

			if filter.name:
				query = query.filter(Region.name.ilike(f"%{filter.name}%"))

			paginated_result = paginate(query, Params(page=filter.page, size=filter.size))

			regions = [serializer.to_region_dto(row) for row in paginated_result.items]
			return Pagination.from_query_result(regions, paginated_result)
		except Exception as e:
			self.logger.error(f"Failed to retrieve regions: {str(e)}")
			raise ApiException("Failed to retrieve regions")

	def get_region_by_id(self, region_id: UUID4) -> RegionDto:
		try:
			"""Get a region by ID."""
			region = self.db.query(Region).filter(Region.id == region_id).first()

			if not region:
				raise ApiException("Region not found")

			return serializer.to_region_dto(region)
		except Exception as e:
			self.logger.error(f"Failed to retrieve region: {str(e)}")
			raise ApiException("Failed to retrieve region")

	def create_region(self, region_data: RegionRequest) -> RegionDto:
		try:
			"""Create a new region."""
			# Check if region with same name or code already exists
			existing_region = (
				self.db.query(Region)
				.filter((Region.name == region_data.name) | (Region.code == region_data.code))
				.first()
			)

			if existing_region:
				if existing_region.name == region_data.name:
					raise ApiException("Region with this name already exists")
				if existing_region.code == region_data.code:
					raise ApiException("Region with this code already exists")

			region = Region(name=region_data.name, code=region_data.code, description=region_data.description)

			self.db.add(region)
			self.db.commit()
			self.db.refresh(region)

			return serializer.to_region_dto(region)
		except Exception as e:
			self.db.rollback()
			self.logger.error(f"Failed to create region: {str(e)}")
			if isinstance(e, ApiException):
				raise e
			raise ApiException("Failed to create region")

	def update_region(self, region_id: UUID4, region_data: "RegionRequest") -> RegionDto:
		try:
			"""Update an existing region."""
			region = self.db.query(Region).filter(Region.id == region_id).first()

			if not region:
				raise ApiException("Region not found")

			# Check if another region with same name or code already exists
			existing_region = (
				self.db.query(Region)
				.filter(Region.id != region_id, (Region.name == region_data.name) | (Region.code == region_data.code))
				.first()
			)

			if existing_region:
				if existing_region.name == region_data.name:
					raise ApiException("Region with this name already exists")
				if existing_region.code == region_data.code:
					raise ApiException("Region with this code already exists")

			region.name = region_data.name
			region.code = region_data.code
			region.description = region_data.description

			self.db.commit()
			self.db.refresh(region)

			return serializer.to_region_dto(region)
		except Exception as e:
			self.db.rollback()
			self.logger.error(f"Failed to update region: {str(e)}")
			if isinstance(e, ApiException):
				raise e
			raise ApiException("Failed to update region")

	def delete_region(self, region_id: UUID4) -> bool:
		try:
			"""Delete a region."""
			region = self.db.query(Region).filter(Region.id == region_id).first()

			if not region:
				raise ApiException("Region not found")

			# Check if region has related districts
			if region.districts:
				raise ApiException("Cannot delete region with related districts")

			self.db.delete(region)
			self.db.commit()

			return True
		except Exception as e:
			self.db.rollback()
			self.logger.error(f"Failed to delete region: {str(e)}")
			if isinstance(e, ApiException):
				raise e
			raise ApiException("Failed to delete region")

	def retrieve_districts(self, filter: DistrictFilter) -> Pagination[DistrictDto]:
		try:
			"""Retrieve districts based on the provided filter."""
			query = self.db.query(District).join(Region, District.region)

			if filter.name:
				query = query.filter(District.name.ilike(f"%{filter.name}%"))

			if filter.region_id:
				query = query.filter(District.region_id == filter.region_id)

			paginated_result = paginate(query.order_by(Region.name.asc()), Params(page=filter.page, size=filter.size))
			data = [serializer.to_district_dto(row) for row in paginated_result.items]

			return Pagination.from_query_result(data, paginated_result)
		except Exception as e:
			self.logger.error(f"Failed to retrieve districts: {str(e)}")
			raise ApiException("Failed to retrieve districts")

	def get_district_by_id(self, district_id: UUID4) -> DistrictDto:
		try:
			"""Get a district by ID."""
			district = self.db.query(District).join(Region, District.region).filter(District.id == district_id).first()

			if not district:
				raise ApiException("District not found")

			return serializer.to_district_dto(district)
		except Exception as e:
			self.logger.error(f"Failed to retrieve district: {str(e)}")
			raise ApiException("Failed to retrieve district")

	def create_district(self, district_data: "DistrictRequest") -> DistrictDto:
		try:
			"""Create a new district."""
			# Check if district with same name or code already exists
			existing_district = (
				self.db.query(District)
				.filter((District.name == district_data.name) | (District.code == district_data.code))
				.first()
			)

			if existing_district:
				if existing_district.name == district_data.name:
					raise ApiException("District with this name already exists")
				if existing_district.code == district_data.code:
					raise ApiException("District with this code already exists")

			# Verify region exists
			region = self.db.query(Region).filter(Region.id == district_data.region_id).first()
			if not region:
				raise ApiException("Region not found")

			district = District(name=district_data.name, code=district_data.code, region_id=district_data.region_id)

			self.db.add(district)
			self.db.commit()
			self.db.refresh(district)

			return serializer.to_district_dto(district)
		except Exception as e:
			self.db.rollback()
			self.logger.error(f"Failed to create district: {str(e)}")
			if isinstance(e, ApiException):
				raise e
			raise ApiException("Failed to create district")

	def update_district(self, district_id: UUID4, district_data: "DistrictRequest") -> DistrictDto:
		try:
			"""Update an existing district."""
			district = self.db.query(District).filter(District.id == district_id).first()

			if not district:
				raise ApiException("District not found")

			# Check if another district with same name or code already exists
			existing_district = (
				self.db.query(District)
				.filter(
					District.id != district_id,
					(District.name == district_data.name) | (District.code == district_data.code),
				)
				.first()
			)

			if existing_district:
				if existing_district.name == district_data.name:
					raise ApiException("District with this name already exists")
				if existing_district.code == district_data.code:
					raise ApiException("District with this code already exists")

			# Verify region exists
			region = self.db.query(Region).filter(Region.id == district_data.region_id).first()
			if not region:
				raise ApiException("Region not found")

			district.name = district_data.name
			district.code = district_data.code
			district.region_id = district_data.region_id

			self.db.commit()
			self.db.refresh(district)

			return serializer.to_district_dto(district)
		except Exception as e:
			self.db.rollback()
			self.logger.error(f"Failed to update district: {str(e)}")
			if isinstance(e, ApiException):
				raise e
			raise ApiException("Failed to update district")

	def delete_district(self, district_id: UUID4) -> bool:
		try:
			"""Delete a district."""
			district = self.db.query(District).filter(District.id == district_id).first()

			if not district:
				raise ApiException("District not found")

			# Check if district has related records
			has_related_records = (
				district.traditional_authorities or district.villages or district.organizations or district.activities
			)

			if has_related_records:
				raise ApiException("Cannot delete district with related records")

			self.db.delete(district)
			self.db.commit()

			return True
		except Exception as e:
			self.db.rollback()
			self.logger.error(f"Failed to delete district: {str(e)}")
			if isinstance(e, ApiException):
				raise e
			raise ApiException("Failed to delete district")

	def retrieve_loadable_items(self, filter: LoadableItemFilter) -> Pagination[LoadableItemDto]:
		try:
			"""Retrieve loadable items based on the provided filter."""
			query = self.db.query(LoadableItem)

			if filter.type and filter.type.lower() != "all":
				query = query.filter(LoadableItem.type == filter.type)

			if filter.display_value:
				query = query.filter(LoadableItem.display_value.ilike(f"%{filter.display_value}%"))

			if filter.code:
				query = query.filter(LoadableItem.code.ilike(f"%{filter.code}%"))

			paginated_result = paginate(query, Params(page=filter.page, size=filter.size))
			items = [serializer.to_loadable_item_dto(row) for row in paginated_result.items]

			return Pagination.from_query_result(items, paginated_result)
		except Exception as e:
			self.logger.error(f"Failed to retrieve loadable items: {str(e)}")
			raise ApiException(f"Failed to retrieve loadable items: {str(e)}")

	def add_loadable_item(self, data: LoadableItemRequest) -> LoadableItemDto:
		"""Add a new loadable item."""
		try:
			item = self.db.query(LoadableItem).filter(LoadableItem.code == data.code).first()
			if item is not None:
				raise ApiException(f"Loadable item with code {data.code} already exists")

			item = (
				self.db.query(LoadableItem)
				.filter((LoadableItem.type == data.type) & (LoadableItem.display_value == data.display_value))
				.first()
			)
			if item is not None:
				raise ApiException("Loadable item with same name and type already exists")

			loadable_item = LoadableItem(
				type=data.type.upper(),
				code=data.code.upper(),
				display_value=data.display_value,
				description=data.description,
			)
			self.db.add(loadable_item)
			self.db.commit()
			self.db.refresh(loadable_item)

			return serializer.to_loadable_item_dto(loadable_item)
		except Exception as e:
			self.db.rollback()
			if isinstance(e, ApiException):
				raise
			raise ApiException(f"Failed to add loadable item: {str(e)}")

	def update_loadable_item(self, id: UUID4, data: LoadableItemRequest) -> LoadableItemDto:
		"""Update an existing loadable item."""
		try:
			loadable_item = self.db.query(LoadableItem).filter(LoadableItem.id == id).first()

			if loadable_item is None:
				raise ApiException("Could not find a loadable item to update")

			loadable_item.code = data.code.upper()
			loadable_item.type = data.type.upper()
			loadable_item.description = data.description
			loadable_item.display_value = data.display_value

			self.db.commit()
			self.db.refresh(loadable_item)

			return serializer.to_loadable_item_dto(loadable_item)
		except Exception as e:
			self.db.rollback()
			if isinstance(e, ApiException):
				raise
			raise ApiException(f"Failed to update loadable item: {str(e)}")

	async def delete_loadable_item(self, loadable_item_id: UUID4, void_reason: str) -> bool:
		"""Delete (void) a loadable item."""
		try:
			loadable_item = self.db.query(LoadableItem).filter(LoadableItem.id == loadable_item_id).first()

			if loadable_item is None:
				raise ApiException("Could not find a loadable item to delete")

			self.db.void(loadable_item, void_reason=void_reason)

			return True
		except Exception as e:
			self.db.rollback()
			if isinstance(e, ApiException):
				raise
			raise ApiException("Failed to delete loadable item")

	def get_loadable_item_by_code(self, code: str) -> LoadableItem | None:
		return self.db.query(LoadableItem).filter(LoadableItem.code == code).first()

	def get_loadable_item_by_id(self, id: UUID4) -> LoadableItem | None:
		return self.db.query(LoadableItem).filter(LoadableItem.id == id).first()

	def retrieve_loadable_item_types(self) -> list[str]:
		"""Retrieve all distinct loadable item types."""
		try:
			types = self.db.query(LoadableItem.type).distinct().all()
			return [type_tuple[0] for type_tuple in types if type_tuple[0]]
		except Exception as e:
			raise ApiException(f"Failed to retrieve loadable item types: {str(e)}")

	def get_system_configuration(self, group: str) -> dict:
		"""Retrieve system configuration from global properties."""
		try:
			# Query all configuration properties
			properties = (
				self.db.query(GlobalProperty)
				.filter(
					GlobalProperty.group == group,
				)
				.all()
			)

			# Convert to dictionary
			config_dict = {prop.property: prop.value for prop in properties}

			# Get the latest updated_at timestamp
			latest_update = None
			if properties:
				latest_update = max(prop.updated_at for prop in properties)

				config_dict["updated_at"] = latest_update

			return config_dict
		except Exception as e:
			self.logger.error(f"Failed to retrieve system configuration: {str(e)}")
			raise

	async def update_system_configuration(self, data: SystemConfigurationRequest) -> dict:
		"""Update system configuration in global properties."""
		try:
			# Convert request data to dictionary
			config_data = data.properties

			for key, value in config_data.items():
				# Convert HttpUrl to string for storage
				if hasattr(value, "__str__"):
					value = str(value)

				# Check if property already exists
				existing_property = (
					self.db.query(GlobalProperty)
					.filter(GlobalProperty.property == key)
					.filter(GlobalProperty.group == data.group)
					.first()
				)

				if existing_property:
					# Update existing property
					existing_property.value = value
				else:
					# Create new property
					new_property = GlobalProperty(group=data.group, property=key, value=value)
					self.db.add(new_property)

			self.db.commit()

			# Return updated configuration
			return self.get_system_configuration(data.group)
		except Exception as e:
			self.db.rollback()
			self.logger.error(f"Failed to update system configuration: {str(e)}")
			raise ApiException(f"Failed to update system configuration: {str(e)}")


settings_service = SettingsService()
