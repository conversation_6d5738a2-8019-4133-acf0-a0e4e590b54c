from fastapi import APIRouter, status

from src.core.shared_schema import BaseResponse, Pagination
from src.modules.complaint import complaint_controller as controller
from src.modules.complaint.complaint_schema import ComplaintDTO

complaint_router_v1 = APIRouter(tags=["complaints"])

complaint_router_v1.add_api_route(
	path="/statistics",
	methods=["GET"],
	endpoint=controller.fetch_complaint_statistics_handler,
	response_model=BaseResponse[dict],
	summary="Get complaint statistics for dashboard",
)

complaint_router_v1.add_api_route(
	path="/trends",
	methods=["GET"],
	endpoint=controller.fetch_complaint_trends_handler,
	response_model=BaseResponse[list],
	summary="Get complaint trends over time",
)

complaint_router_v1.add_api_route(
	path="/dashboard",
	methods=["GET"],
	endpoint=controller.fetch_complaint_dashboard_handler,
	response_model=Pagination[dict],
	summary="Get complaints dashboard summary",
)

complaint_router_v1.add_api_route(
	path="/",
	methods=["GET"],
	endpoint=controller.fetch_complaints_handler,
	response_model=Pagination[ComplaintDTO],
	summary="Retrieve paginated list of complaints",
)

complaint_router_v1.add_api_route(
	path="/",
	methods=["POST"],
	endpoint=controller.create_complaint_handler,
	response_model=ComplaintDTO,
	status_code=status.HTTP_201_CREATED,
	summary="Create authenticated complaint",
)

complaint_router_v1.add_api_route(
	path="/public",
	methods=["POST"],
	endpoint=controller.create_public_complaint_handler,
	response_model=BaseResponse[dict],
	status_code=status.HTTP_201_CREATED,
	summary="Submit complaint publicly (no auth required)",
)

complaint_router_v1.add_api_route(
	path="/{complaint_id}",
	methods=["GET"],
	endpoint=controller.get_single_complaint_handler,
	response_model=BaseResponse[ComplaintDTO],
	summary="Get single complaint details",
)

complaint_router_v1.add_api_route(
	path="/track/{tracking_code}",
	methods=["GET"],
	endpoint=controller.track_complaint_handler,
	response_model=BaseResponse[ComplaintDTO],
	summary="Track complaint status using tracking code",
)

complaint_router_v1.add_api_route(
	path="/{id}",
	methods=["PUT"],
	endpoint=controller.update_complaint_handler,
	response_model=BaseResponse[ComplaintDTO],
	summary="Update complaint details",
)

complaint_router_v1.add_api_route(
	path="/{complaint_id}/status",
	methods=["PUT"],
	endpoint=controller.update_complaint_status_handler,
	response_model=BaseResponse[ComplaintDTO],
	summary="Update complaint status",
)

complaint_router_v1.add_api_route(
	path="/{id}/void",
	methods=["DELETE"],
	endpoint=controller.void_complaint_handler,
	response_model=BaseResponse[bool],
	status_code=status.HTTP_200_OK,
	summary="Void complaint",
)
