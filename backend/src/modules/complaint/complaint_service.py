import random
import string
from datetime import datetime, timed<PERSON><PERSON>
from typing import List

from fastapi_pagination import Params
from fastapi_pagination.ext.sqlalchemy import paginate
from pydantic import UUID4
from sqlalchemy import func

from src.config.db.models import Complaint, LoadableItem, Organization
from src.config.db.models.complaint_status import ComplaintStatus, ComplaintStatusType
from src.core.base.base_repository import BaseRepository
from src.core.exceptions.api import ApiException
from src.core.logger import logger
from src.core.shared_schema import CurrentUser, Pagination, VoidRequest
from src.core.utils import serializer

from .complaint_schema import (
	ComplaintDTO,
	ComplaintFilter,
	ComplaintRequest,
	ComplaintStatusUpdateRequest,
	PublicComplaintRequest,
)


class ComplaintService(BaseRepository):
	def __init__(self):
		super().__init__()

	def _generate_tracking_code(self) -> str:
		now = datetime.now()
		year = now.year
		month = f"{now.month:02d}"

		while True:
			alphanumeric = "".join(random.choices(string.ascii_uppercase + string.digits, k=4))
			tracking_code = f"CPL{year}{month}{alphanumeric}"

			existing = self.db.query(Complaint).filter(Complaint.tracking_code == tracking_code).first()

			if not existing:
				return tracking_code

	async def retrieve_complaints(self, filter: ComplaintFilter) -> Pagination[ComplaintDTO]:
		try:
			query = self.db.query(Complaint)

			if filter.priority:
				query = query.filter(Complaint.priority == filter.priority)

			if filter.organization_id:
				query = query.filter(Complaint.organization_id == filter.organization_id)

			if filter.category_id:
				query = query.filter(Complaint.category_id == filter.category_id)

			if filter.tracking_code:
				query = query.filter(Complaint.tracking_code.ilike(f"%{filter.tracking_code}%"))

			paginated_result = paginate(query, Params(page=filter.page, size=filter.size))
			complaints = [serializer.to_complaint_dto(row) for row in paginated_result.items]

			return Pagination.from_query_result(complaints, paginated_result)

		except Exception as e:
			logger.error(f"Failed to retrieve complaints: {str(e)}")
			raise ApiException("Failed to retrieve complaints")

	async def create_complaint(self, payload: ComplaintRequest) -> ComplaintDTO:
		try:
			tracking_code = self._generate_tracking_code()
			complaint = Complaint(
				id=payload.id,
				tracking_code=tracking_code,
				title=payload.title,
				summary=payload.summary,
				priority=payload.priority,
				organization_id=payload.organization_id,
				is_anonymous=payload.is_anonymous,
				category_id=payload.category_id,
				complainant_id=payload.complainant_id,
				created_by=self.current_user.id if hasattr(self, "current_user") and self.current_user else None,
			)

			initial_status = ComplaintStatus(
				complaint_id=complaint.id,
				type=ComplaintStatusType.OPEN,
				created_by=self.current_user.id if hasattr(self, "current_user") and self.current_user else None,
			)

			self.db.add(complaint)
			self.db.flush()
			initial_status.complaint_id = complaint.id
			self.db.add(initial_status)
			self.db.commit()
			self.db.refresh(complaint)

			return serializer.to_complaint_dto(complaint)
		except Exception as e:
			self.db.rollback()
			logger.error(f"Failed to add complaint: {str(e)}")
			raise ApiException("Failed to add complaint")

	async def create_public_complaint(self, payload: PublicComplaintRequest) -> dict:
		try:
			tracking_code = self._generate_tracking_code()
			complaint = Complaint(
				tracking_code=tracking_code,
				title=payload.title,
				summary=payload.summary,
				priority=payload.priority,
				organization_id=payload.organization_id,
				is_anonymous=payload.complainant_name is None,
				category_id=payload.category_id,
				complainant_id=None,
				complainant_name=payload.complainant_name,
				complainant_email=payload.complainant_email,
				complainant_phone=payload.complainant_phone,
			)

			initial_status = ComplaintStatus(
				complaint_id=complaint.id,
				type=ComplaintStatusType.OPEN,
			)

			self.db.add(complaint)
			self.db.flush()
			initial_status.complaint_id = complaint.id
			self.db.add(initial_status)
			self.db.commit()

			return {
				"complaint_id": str(complaint.id),
				"tracking_code": tracking_code,
				"message": "Complaint submitted successfully",
			}
		except Exception as e:
			self.db.rollback()
			logger.error(f"Failed to create public complaint: {str(e)}")
			raise ApiException("Failed to create public complaint")

	async def get_complaint_by_id(self, complaint_id: UUID4) -> ComplaintDTO:
		complaint = self.db.query(Complaint).filter(Complaint.id == complaint_id).first()
		if not complaint:
			raise ApiException("Complaint not found")
		return serializer.to_complaint_dto(complaint)

	async def get_complaint_by_tracking_code(self, tracking_code: str) -> ComplaintDTO:
		complaint = self.db.query(Complaint).filter(Complaint.tracking_code == tracking_code).first()
		if not complaint:
			raise ApiException("Complaint not found")
		return serializer.to_complaint_dto(complaint)

	async def update_complaint_status(self, complaint_id: UUID4, payload: ComplaintStatusUpdateRequest) -> ComplaintDTO:
		try:
			complaint = self.db.query(Complaint).filter(Complaint.id == complaint_id).first()
			if not complaint:
				raise ApiException("Complaint not found")

			current_status = (
				self.db.query(ComplaintStatus)
				.filter(ComplaintStatus.complaint_id == complaint_id)
				.order_by(ComplaintStatus.created_at.desc())
				.first()
			)

			if current_status and not self._is_valid_status_transition(current_status.type, payload.status):
				raise ApiException(f"Invalid status transition from {current_status.type} to {payload.status}")

			new_status = ComplaintStatus(
				complaint_id=complaint_id,
				type=payload.status,
				resolution=payload.resolution,
				internal_notes=payload.internal_notes,
				public_response=payload.public_response,
				created_by=self.current_user.id if hasattr(self, "current_user") and self.current_user else None,
			)

			self.db.add(new_status)
			self.db.commit()
			self.db.refresh(complaint)

			return serializer.to_complaint_dto(complaint)
		except Exception as e:
			self.db.rollback()
			logger.error(f"Failed to update complaint status: {str(e)}")
			raise ApiException("Failed to update complaint status")

	def _is_valid_status_transition(self, current: ComplaintStatusType, new: ComplaintStatusType) -> bool:
		valid_transitions = {
			ComplaintStatusType.OPEN: [ComplaintStatusType.IN_PROGRESS, ComplaintStatusType.CLOSED],
			ComplaintStatusType.IN_PROGRESS: [
				ComplaintStatusType.RESOLVED,
				ComplaintStatusType.ESCALATED,
				ComplaintStatusType.CLOSED,
			],
			ComplaintStatusType.ESCALATED: [
				ComplaintStatusType.IN_PROGRESS,
				ComplaintStatusType.RESOLVED,
				ComplaintStatusType.CLOSED,
			],
			ComplaintStatusType.RESOLVED: [ComplaintStatusType.CLOSED, ComplaintStatusType.IN_PROGRESS],
			ComplaintStatusType.CLOSED: [],
		}
		return new in valid_transitions.get(current, [])

	async def get_complaint_categories(self) -> Pagination[dict]:
		categories = self.db.query(LoadableItem).filter(LoadableItem.type == "COMPLAINT_CATEGORY").all()

		category_data = [
			{"id": str(cat.id), "code": cat.code, "display_value": cat.display_value, "description": cat.description}
			for cat in categories
		]

		return Pagination(data=category_data, page=1, size=len(category_data), total=len(category_data))

	async def get_complaint_statistics(self) -> dict:
		total_complaints = self.db.query(Complaint).count()

		status_counts = {}
		for status_type in ComplaintStatusType:
			count = (
				self.db.query(ComplaintStatus)
				.join(Complaint)
				.filter(ComplaintStatus.type == status_type)
				.distinct(ComplaintStatus.complaint_id)
				.count()
			)
			status_counts[status_type.value.lower()] = count

		current_month_start = datetime.now().replace(day=1, hour=0, minute=0, second=0, microsecond=0)
		monthly_complaints = self.db.query(Complaint).filter(Complaint.created_at >= current_month_start).count()

		priority_counts = {}
		for priority in [1, 2, 3]:
			count = self.db.query(Complaint).filter(Complaint.priority == priority).count()
			priority_counts[f"priority_{priority}"] = count

		category_counts = (
			self.db.query(LoadableItem.display_value, func.count(Complaint.id).label("count"))
			.join(Complaint, LoadableItem.id == Complaint.category_id)
			.filter(LoadableItem.type == "COMPLAINT_CATEGORY")
			.group_by(LoadableItem.id, LoadableItem.display_value)
			.all()
		)

		recent_complaints_query = self.db.query(Complaint).order_by(Complaint.created_at.desc()).limit(5).all()
		recent_complaints = [serializer.to_complaint_dto(c) for c in recent_complaints_query]

		resolved_complaints = (
			self.db.query(ComplaintStatus).filter(ComplaintStatus.type == ComplaintStatusType.RESOLVED).all()
		)

		if resolved_complaints:
			total_days = sum(
				(status.created_at - status.complaint.created_at).days
				for status in resolved_complaints
				if status.complaint
			)
			avg_resolution_days = total_days / len(resolved_complaints)
		else:
			avg_resolution_days = None

		return {
			"total_complaints": total_complaints,
			"open_complaints": status_counts.get("open", 0),
			"in_progress_complaints": status_counts.get("in_progress", 0),
			"resolved_complaints": status_counts.get("resolved", 0),
			"closed_complaints": status_counts.get("closed", 0),
			"escalated_complaints": status_counts.get("escalated", 0),
			"avg_resolution_days": avg_resolution_days,
			"monthly_complaints": monthly_complaints,
			"complaints_by_priority": priority_counts,
			"complaints_by_category": {cat.display_value: cat.count for cat in category_counts},
			"recent_complaints": recent_complaints,
		}

	async def get_complaint_trends(self, period: str = "6months") -> List[dict]:
		if period == "6months":
			months_back = 6
		elif period == "12months":
			months_back = 12
		else:
			months_back = 6

		trends = []
		current_date = datetime.now()

		for i in range(months_back):
			month_start = current_date.replace(day=1) - timedelta(days=i * 30)
			month_end = (month_start + timedelta(days=31)).replace(day=1) - timedelta(days=1)

			monthly_total = (
				self.db.query(Complaint)
				.filter(Complaint.created_at >= month_start, Complaint.created_at <= month_end)
				.count()
			)

			monthly_resolved = (
				self.db.query(ComplaintStatus)
				.join(Complaint)
				.filter(
					ComplaintStatus.type == ComplaintStatusType.RESOLVED,
					Complaint.created_at >= month_start,
					Complaint.created_at <= month_end,
				)
				.count()
			)

			trends.append(
				{
					"period": month_start.strftime("%Y-%m"),
					"total_complaints": monthly_total,
					"resolved_complaints": monthly_resolved,
					"avg_resolution_time": None,
				}
			)

		return list(reversed(trends))

	async def get_complaint_dashboard_summary(self, filter: dict) -> Pagination[dict]:
		query = (
			self.db.query(
				Complaint.id,
				Complaint.tracking_code,
				Complaint.title,
				Complaint.priority,
				Complaint.created_at,
				Organization.name.label("organization_name"),
				LoadableItem.display_value.label("category_name"),
			)
			.outerjoin(Organization, Complaint.organization_id == Organization.id)
			.join(LoadableItem, Complaint.category_id == LoadableItem.id)
		)

		if filter.get("category_id"):
			query = query.filter(Complaint.category_id == filter["category_id"])

		if filter.get("organization_id"):
			query = query.filter(Complaint.organization_id == filter["organization_id"])

		complaints = query.order_by(Complaint.created_at.desc()).limit(20).all()

		summary_data = []
		for c in complaints:
			latest_status = (
				self.db.query(ComplaintStatus)
				.filter(ComplaintStatus.complaint_id == c.id)
				.order_by(ComplaintStatus.created_at.desc())
				.first()
			)

			days_open = (datetime.now() - c.created_at).days

			summary_data.append(
				{
					"id": str(c.id),
					"tracking_code": c.tracking_code,
					"title": c.title,
					"priority": c.priority,
					"status": latest_status.type.value if latest_status else "UNKNOWN",
					"organization_name": c.organization_name,
					"category_name": c.category_name,
					"created_at": c.created_at,
					"days_open": days_open,
				}
			)

		return Pagination(data=summary_data, page=1, size=len(summary_data), total=len(summary_data))

	async def update_complaint(self, id: UUID4, payload: ComplaintRequest) -> ComplaintDTO:
		try:
			complaint = self.db.query(Complaint).filter(Complaint.id == id).first()

			if complaint is None:
				raise ApiException("Complaint ID not found")

			complaint.title = payload.title
			complaint.summary = payload.summary
			complaint.priority = payload.priority
			complaint.organization_id = payload.organization_id
			complaint.is_anonymous = payload.is_anonymous
			complaint.category_id = payload.category_id
			complaint.complainant_id = payload.complainant_id

			self.db.add(complaint)
			self.db.commit()
			self.db.refresh(complaint)

			return serializer.to_complaint_dto(complaint)
		except Exception as e:
			self.db.rollback()
			logger.error(f"Failed to add complaint: {str(e)}")
			raise ApiException("Failed to add complaint")

	async def void_complaint(self, id: UUID4, user: CurrentUser, payload: VoidRequest) -> ComplaintDTO:
		try:
			complaint = self.db.query(Complaint).filter(Complaint.id == id).first()

			if complaint is None:
				raise ApiException("Complaint ID not found")

			complaint.voided = True
			complaint.voided_by = user.id
			complaint.void_reason = payload.void_reason

			self.db.add(complaint)
			self.db.commit()
			self.db.refresh(complaint)

			return serializer.to_complaint_dto(complaint)
		except Exception as e:
			self.db.rollback()
			logger.error(f"Failed to add complaint: {str(e)}")
			raise ApiException("Failed to add complaint")
