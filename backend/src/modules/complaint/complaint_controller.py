from fastapi import Depends
from pydantic import UUID4
from starlette.requests import Request

from src.core.guards.auth_guard import auth_guard
from src.core.shared_schema import BaseResponse, CurrentUser, Pagination, VoidRequest

from .complaint_schema import (
	ComplaintDTO,
	ComplaintFilter,
	ComplaintRequest,
	ComplaintStatusUpdateRequest,
	PublicComplaintRequest,
	ComplaintDashboardFilter,
)
from .complaint_service import ComplaintService


def get_complaint_service() -> ComplaintService:
	return ComplaintService()


@auth_guard.can_("read.complaint")
async def fetch_complaints_handler(
	_: Request,
	filter: ComplaintFilter = Depends(ComplaintFilter),
) -> Pagination[ComplaintDTO]:
	service = get_complaint_service()
	return await service.retrieve_complaints(filter)


@auth_guard.can_("create.complaint")
async def create_complaint_handler(
	_: Request,
	payload: ComplaintRequest,
) -> ComplaintDTO:
	service = get_complaint_service()
	return await service.create_complaint(payload)


async def create_public_complaint_handler(
	_: Request,
	payload: PublicComplaintRequest,
) -> BaseResponse[dict]:
	service = get_complaint_service()
	result = await service.create_public_complaint(payload)
	return BaseResponse[dict](data=result)


@auth_guard.can_("read.complaint")
async def get_single_complaint_handler(_: Request, complaint_id: UUID4) -> BaseResponse[ComplaintDTO]:
	service = get_complaint_service()
	complaint = await service.get_complaint_by_id(complaint_id)
	return BaseResponse[ComplaintDTO](data=complaint)


async def track_complaint_handler(_: Request, tracking_code: str) -> BaseResponse[ComplaintDTO]:
	service = get_complaint_service()
	complaint = await service.get_complaint_by_tracking_code(tracking_code)
	return BaseResponse[ComplaintDTO](data=complaint)


@auth_guard.can_("update.complaint")
async def update_complaint_handler(_: Request, id: UUID4, data: ComplaintRequest) -> BaseResponse[ComplaintDTO]:
	service = get_complaint_service()
	complaint = await service.update_complaint(id, data)
	return BaseResponse[ComplaintDTO](data=complaint)


@auth_guard.can_("update.complaint")
async def update_complaint_status_handler(
	_: Request,
	complaint_id: UUID4,
	payload: ComplaintStatusUpdateRequest,
) -> BaseResponse[ComplaintDTO]:
	service = get_complaint_service()
	updated_complaint = await service.update_complaint_status(complaint_id, payload)
	return BaseResponse[ComplaintDTO](data=updated_complaint)


@auth_guard.can_("delete.complaint")
async def void_complaint_handler(
	_: Request, id: UUID4, data: VoidRequest, user: CurrentUser = Depends(auth_guard.current_user)
) -> BaseResponse[bool]:
	service = get_complaint_service()
	await service.void_complaint(id, payload=data, user=user)
	return BaseResponse[bool](data=True)


@auth_guard.can_("read.complaint")
async def fetch_complaint_statistics_handler(
	_: Request,
) -> BaseResponse[dict]:
	service = get_complaint_service()
	stats = await service.get_complaint_statistics()
	return BaseResponse[dict](data=stats)


@auth_guard.can_("read.complaint")
async def fetch_complaint_trends_handler(_: Request, period: str = "6months") -> BaseResponse[list]:
	service = get_complaint_service()
	trends = await service.get_complaint_trends(period)
	return BaseResponse[list](data=trends)


@auth_guard.can_("read.complaint")
async def fetch_complaint_dashboard_handler(
	_: Request,
	filter: ComplaintDashboardFilter = Depends(ComplaintDashboardFilter),
) -> Pagination[dict]:
	service = get_complaint_service()
	filter_dict = {
		"category_id": filter.category_id,
		"organization_id": filter.organization_id,
		"period": filter.period,
	}
	return await service.get_complaint_dashboard_summary(filter_dict)
