from datetime import datetime
from typing import List, Optional

from pydantic import BaseModel, Field
from pydantic.types import UUID4

from src.config.db.models.complaint_status import ComplaintStatusType
from src.core.shared_schema import BaseRequest


class ComplaintRequest(BaseModel):
	id: Optional[UUID4] = None
	title: str
	summary: str
	priority: int
	organization_id: Optional[UUID4]
	is_anonymous: bool = Field(default=False)
	category_id: UUID4
	complainant_id: Optional[UUID4] = None


class PublicComplaintRequest(BaseModel):
	title: str
	summary: str
	priority: int = Field(default=1)
	organization_id: Optional[UUID4] = None
	category_id: UUID4
	complainant_name: Optional[str] = None
	complainant_email: Optional[str] = None
	complainant_phone: Optional[str] = None


class ComplaintStatusUpdateRequest(BaseModel):
	status: ComplaintStatusType
	resolution: Optional[str] = None
	internal_notes: Optional[str] = None
	public_response: Optional[str] = None


class ComplaintDTO(BaseModel):
	id: UUID4
	tracking_code: str
	title: str
	summary: str
	priority: int
	organization_id: Optional[UUID4]
	is_anonymous: bool
	category_id: UUID4
	complainant_id: Optional[UUID4]
	complainant_name: Optional[str]
	complainant_email: Optional[str]
	complainant_phone: Optional[str]
	created_at: datetime
	updated_at: datetime


class ComplaintFilter(BaseRequest):
	priority: Optional[int] = Field(default=None, description="Priority")
	category_id: Optional[UUID4] = Field(default=None, description="Category ID")
	organization_id: Optional[UUID4] = Field(default=None, description="Organisation ID")
	tracking_code: Optional[str] = Field(default=None, description="Tracking Code")
	status: Optional[ComplaintStatusType] = Field(default=None, description="Complaint Status")


class ComplaintStatsDto(BaseModel):
	total_complaints: int
	open_complaints: int
	in_progress_complaints: int
	resolved_complaints: int
	closed_complaints: int
	escalated_complaints: int
	avg_resolution_days: Optional[float]
	monthly_complaints: int
	complaints_by_priority: dict
	complaints_by_category: dict
	recent_complaints: List[ComplaintDTO]


class ComplaintDashboardFilter(BaseRequest):
	period: Optional[str] = Field(default="current_month", description="Period filter")
	category_id: Optional[UUID4] = Field(default=None, description="Category filter")
	organization_id: Optional[UUID4] = Field(default=None, description="Organization filter")


class ComplaintTrendDto(BaseModel):
	period: str
	total_complaints: int
	resolved_complaints: int
	avg_resolution_time: Optional[float]


class ComplaintSummaryDto(BaseModel):
	id: UUID4
	tracking_code: str
	title: str
	priority: int
	status: str
	organization_name: Optional[str]
	category_name: str
	created_at: datetime
	days_open: int
