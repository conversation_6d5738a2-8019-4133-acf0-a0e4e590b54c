from fastapi import APIRouter, status

from src.core.dtos.application_dtos import ApplicationDto, ApplicationStatsDto
from src.core.dtos.document_dtos import ApplicationDocumentDto
from src.core.dtos.workflow_dtos import WorkflowDto
from src.core.shared_schema import BaseResponse, Pagination
from src.modules.application import application_controller as controller

application_router_v1 = APIRouter(tags=["application"])

application_router_v1.add_api_route(
	path="",
	endpoint=controller.fetch_applications_handler,
	methods=["GET"],
	response_model=Pagination[ApplicationDto],
	status_code=status.HTTP_200_OK,
)

application_router_v1.add_api_route(
	path="/{application_id}",
	endpoint=controller.fetch_single_application_handler,
	methods=["GET"],
	response_model=ApplicationDto,
	status_code=status.HTTP_200_OK,
)

application_router_v1.add_api_route(
	path="",
	endpoint=controller.delete_application_handler,
	methods=["PATCH"],
	status_code=status.HTTP_204_NO_CONTENT,
)

application_router_v1.add_api_route(
	path="/{organization_id}/submit",
	endpoint=controller.submit_application_handler,
	methods=["POST"],
	response_model=BaseResponse[ApplicationDto],
	status_code=status.HTTP_200_OK,
)

application_router_v1.add_api_route(
	path="/{application_id}/workflow",
	endpoint=controller.fetch_application_workflow_handler,
	methods=["GET"],
	response_model=WorkflowDto,
	status_code=status.HTTP_200_OK,
)

application_router_v1.add_api_route(
	path="/{application_id}/documents",
	endpoint=controller.fetch_application_documents_handler,
	methods=["GET"],
	response_model=BaseResponse[list[ApplicationDocumentDto]],
	status_code=status.HTTP_200_OK,
)

application_router_v1.add_api_route(
	path="/history/{organization_id}",
	endpoint=controller.fetch_application_history_handler,
	methods=["GET"],
	response_model=BaseResponse[list[ApplicationDto]],
	status_code=status.HTTP_200_OK,
)

application_router_v1.add_api_route(
	path="/stats/{organization_id}",
	endpoint=controller.fetch_application_stats_handler,
	methods=["GET"],
	response_model=BaseResponse[ApplicationStatsDto],
	status_code=status.HTTP_200_OK,
)

application_router_v1.add_api_route(
	path="/permits",
	endpoint=controller.create_permit_applications_handler,
	methods=["POST"],
	response_model=BaseResponse[list[ApplicationDto]],
	status_code=status.HTTP_201_CREATED,
)
