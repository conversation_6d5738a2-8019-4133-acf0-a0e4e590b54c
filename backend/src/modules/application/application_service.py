import random
from typing import List

from fastapi_pagination import Params
from fastapi_pagination.ext.sqlalchemy import paginate
from pydantic.types import UUID4
from sqlalchemy import and_, func
from sqlalchemy.orm import joinedload

from src.config import settings
from src.config.db.models import Template
from src.config.db.models.application import Application, ApplicationStatus, ApplicationType
from src.config.db.models.application_document import ApplicationDocument
from src.config.db.models.licence_renewal import LicenceRenewal, LicenceRenewalStatus
from src.config.db.models.loadable_item import LoadableItem
from src.config.db.models.organization import OrganizationStatus
from src.config.db.models.permit_applicant import PermitApplicant
from src.config.db.models.template_stage import TemplateStage
from src.config.db.models.workflow import Workflow
from src.config.db.models.workflow_stage import WorkflowStage, WorkflowStageStatus
from src.core.base.base_repository import BaseRepository
from src.core.dtos.application_dtos import ApplicationDto, ApplicationStatsDto, to_application_dto
from src.core.dtos.document_dtos import to_application_document_dto
from src.core.dtos.financial_dtos import CreateInvoiceRequest
from src.core.dtos.workflow_dtos import WorkflowDto
from src.core.exceptions.api import ApiException
from src.core.logger.internal_logger import get_logger
from src.core.services.email_service import EmailRecipient
from src.core.services.notification_service import NotificationService
from src.core.shared_schema import Pagination, VoidRequest
from src.modules.application.application_schema import ApplicationFilter
from src.modules.document.document_service import DocumentService
from src.modules.financial.fee_service import FeeService
from src.modules.financial.invoice_service import InvoiceService


class ApplicationService(BaseRepository):
	def __init__(self):
		super().__init__()

		self._fee_service = None
		self._notification_service = None
		self._invoice_service = None
		self._document_service = None
		self.logger = get_logger(__name__)

	@property
	def fee_service(self):
		"""Lazy-loaded FeeService"""
		if self._fee_service is None:
			self._fee_service = FeeService()
		return self._fee_service

	@property
	def notification_service(self):
		"""Lazy-loaded NotificationService"""
		if self._notification_service is None:
			self._notification_service = NotificationService()
		return self._notification_service

	@property
	def document_service(self):
		"""Lazy-loaded DocumentService"""
		if self._document_service is None:
			self._document_service = DocumentService()
		return self._document_service

	@property
	def invoice_service(self):
		"""Lazy-loaded InvoiceService"""
		if self._invoice_service is None:
			self._invoice_service = InvoiceService()
		return self._invoice_service

	async def _generate_unique_application_code(self):
		"""
		Generate a unique 8-character application code without '0' and 'O'.

		Returns:
			str: Unique 8-character application code
		"""
		allowed_chars = "123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ"

		max_attempts = 200

		for _ in range(max_attempts):
			code = "".join(random.choices(allowed_chars, k=8))

			existing_application = self.db.query(Application).filter(Application.code == code).first()

			if existing_application is None:
				return code

		raise ApiException(f"Unable to generate unique application code after {max_attempts} attempts")

	async def create_organization_registration(self, org_id: UUID4, income=0.0) -> Application:
		try:
			existing_application = (
				self.db.query(Application)
				.filter(
					and_(
						Application.organization_id == org_id,
						Application.type == ApplicationType.ORGANIZATION_REGISTRATION,
					)
				)
				.first()
			)

			if existing_application is not None:
				raise ApiException("Organization already has the application")
			application_code = await self._generate_unique_application_code()
			application = Application(
				organization_id=org_id,
				status=ApplicationStatus.DRAFT,
				type=ApplicationType.ORGANIZATION_REGISTRATION,
				code=application_code,
				created_by=self.current_user.id,
			)
			self.db.add(application)
			self.db.flush()

			await self.fee_service.create_application_fee(application, income)

			if not self.db.in_transaction() or not self.db.in_nested_transaction():
				self.db.commit()

			return application
		except Exception as e:
			self.logger.error(f"Failed to create organization application: {str(e)}")
			raise e

	async def create_licence_renewal(self, org_id: UUID4, income=0.0) -> Application:
		try:
			existing_application = (
				self.db.query(Application)
				.filter(
					Application.organization_id == org_id,
					Application.type == ApplicationType.LICENCE_RENEWAL,
					Application.status.in_([ApplicationStatus.DRAFT]),
				)
				.first()
			)

			if existing_application:
				await self.fee_service.create_application_fee(existing_application, income)
				return existing_application

			application_code = await self._generate_unique_application_code()
			application = Application(
				organization_id=org_id,
				status=ApplicationStatus.DRAFT,
				type=ApplicationType.LICENCE_RENEWAL,
				code=application_code,
				created_by=self.current_user.id,
			)
			self.db.add(application)
			self.db.flush()

			await self.fee_service.create_application_fee(application, income)

			return application
		except Exception as e:
			self.logger.error(f"Failed to create licence renewal application: {str(e)}")
			raise e

	async def retrieve_applications(self, filter: ApplicationFilter) -> Pagination[ApplicationDto]:
		try:
			query = self.db.query(Application)

			if filter.organization_id:
				query = query.filter(Application.organization_id == filter.organization_id)

			if filter.status:
				query = query.filter(Application.status == filter.status)

			if filter.type:
				query = query.filter(Application.type == filter.type)

			if filter.start_date:
				query = query.filter(Application.created_at >= filter.start_date)

			if filter.end_date:
				query = query.filter(Application.created_at <= filter.end_date)

			result = paginate(query, Params(page=filter.page, size=filter.size))
			data = [ApplicationDto.model_construct(**row.__dict__) for row in result.items]

			return Pagination.from_query_result(data, result)
		except Exception as e:
			self.logger.error(f"Failed to retrieve applications: {str(e)}")
			raise

	async def _verify_application_payment(self, application: Application):
		try:
			payment_document = self.db.query(ApplicationDocument).first()

			return payment_document

		except Exception as e:
			self.logger.error(f"Payment verification failed: {str(e)}")
			raise

	async def create_initial_workflow_stage(self, workflow: Workflow) -> WorkflowStage:
		"""Create only the first workflow stage directly in IN_REVIEW status - skip PENDING"""
		first_template_stage = (
			self.db.query(TemplateStage)
			.filter(
				and_(
					TemplateStage.template_id == workflow.template_id,
					TemplateStage.is_active,
					TemplateStage.position == 1,
				)
			)
			.first()
		)

		if not first_template_stage:
			raise ApiException("ER74: No first stage found for workflow template")

		workflow_stage = WorkflowStage(
			workflow_id=workflow.id,
			template_stage_id=first_template_stage.id,
			status=WorkflowStageStatus.PENDING,
			created_by=self.current_user.id,
		)

		self.db.add(workflow_stage)
		self.db.flush()

		return workflow_stage

	async def build_stage_email_recipients(self, stage: TemplateStage) -> List[EmailRecipient]:
		"""Build email recipients for a template stage with error handling"""
		recipients = []

		for stage_role in stage.roles:
			if stage_role.is_active and stage_role.role:
				for user in stage_role.role.users:
					if user.email and user.first_name and user.last_name:
						recipients.append(EmailRecipient(email=user.email, name=f"{user.first_name} {user.last_name}"))

		if not recipients:
			self.logger.warning(f"No email recipients found for template stage {stage.id}")

		return recipients

	async def start_approval_workflow(self, application: Application) -> Workflow:
		try:
			code = "REGISTRATION"

			if application.type == ApplicationType.LICENCE_RENEWAL.value:
				code = "LICENCE_RENEWAL"

			if application.type == ApplicationType.PERMIT_APPLICATION.value:
				code = "PERMIT_APPLICATION"

			template = self.db.query(Template).filter(Template.code == code).first()

			if not template:
				raise ApiException(
					"ER73: Failed to submit application for approval, please contact system administrator."
				)

			workflow = Workflow(
				application_id=application.id,
				template_id=template.id,
				created_by=self.current_user.id,
			)

			self.db.add(workflow)
			self.db.flush()

			first_workflow_stage = await self.create_initial_workflow_stage(workflow)
			first_template_stage = first_workflow_stage.template_stage

			account_ids = []
			for stage_role in first_template_stage.roles:
				if stage_role.is_active and stage_role.role:
					for user in stage_role.role.users:
						if user.account:
							account_ids.append(user.account.id)

				if account_ids:
					await self.notification_service.create_workflow_stage_notification(
						workflow_id=workflow.id,
						stage_name=first_template_stage.name,
						application_code=application.code,
						organization_name=application.organization.name,
						recipient_account_ids=account_ids,
						action_url=f"{settings.FRONTEND_URL}/workflows/{workflow.id}",
						notification_type="STAGE_ASSIGNMENT",
					)

			application.status = ApplicationStatus.IN_REVIEW
			application.updated_by = self.current_user.id
			self.db.add(application)

			if application.type == ApplicationType.ORGANIZATION_REGISTRATION.value:
				application.organization.status = OrganizationStatus.PENDING
				self.db.add(application.organization)
			elif application.type == ApplicationType.LICENCE_RENEWAL.value:
				renewal = self.db.query(LicenceRenewal).find(LicenceRenewal.status == LicenceRenewalStatus.DRAFT)
				renewal.status = LicenceRenewalStatus.SUBMITTED
				renewal.updated_by = self.current_user.id
				self.db.add(renewal)

				application.organization.status = OrganizationStatus.RENEWAL_IN_REVIEW
				self.db.add(application.organization)

			return workflow
		except Exception as e:
			self.logger.error(f"Failed to create approval workflow: {str(e)}")
			raise

	async def submit_application_for_review(self, organization_id: UUID4):
		try:
			application = (
				self.db.query(Application)
				.filter(
					Application.organization_id == organization_id,
					Application.status == ApplicationStatus.DRAFT.value,
				)
				.first()
			)

			if not application:
				raise ApiException("Application not found or already submitted for review")

			existing_workflow = self.db.query(Workflow).filter(Workflow.application_id == application.id).first()

			if existing_workflow:
				raise ApiException("Application already has an active workflow")

			await self.start_approval_workflow(application)

			invoice_request = CreateInvoiceRequest(
				organization_id=application.organization_id,
				application_id=application.id,
				description=f"Application fees for {application.code}",
			)
			invoice_response = await self.invoice_service.create_invoice_for_application(invoice_request)
			self.logger.info(
				f"Created invoice {invoice_response.invoice.reference_number} for application {application.code}"
			)

			return to_application_dto(application)
		except Exception as e:
			self.db.rollback()
			self.logger.error(f"Failed to start application review: {str(e)}")
			raise

	async def reject_application(self, application_id: UUID4):
		try:
			application = self.db.query(Application).filter(Application.id == application_id).first()

			if application is None:
				raise ApiException("Application not found")

			if application.status != ApplicationStatus.IN_REVIEW.value:
				raise ApiException(f"Current application is in {application.status} phase")

			application.status = ApplicationStatus.REJECTED
			application.updated_by = self.current_user.id
			self.db.commit()

			#! TODO: Send rejection notification to applicant and email

			return to_application_dto(application)

		except Exception as e:
			self.logger.error(f"Failed to reject application: {str(e)}")
			raise

	async def delete_application(self, application_id: str, payload: VoidRequest):
		try:
			application = self.db.query(Application).filter(Application.id == application_id).first()

			if application is None:
				raise ApiException("Application not found")

			application.status = ApplicationStatus.SUSPENDED
			application.voided = True
			application.voided_by = self.current_user.id
			application.voided_reason = payload.reason
			self.db.commit()

		except Exception as e:
			self.logger.error(f"Failed to delete application: {str(e)}")
			raise e

	async def get_application_workflow(self, application_id: UUID4) -> WorkflowDto:
		try:
			workflow = self.db.query(Workflow).filter(Workflow.application_id == application_id).first()

			if workflow is None:
				raise ApiException("Workflow not found")

			next_stage = None

			lts_stage: WorkflowStage = (
				self.db.query(WorkflowStage)
				.join(TemplateStage)
				.filter(WorkflowStage.workflow_id == workflow.id)
				.order_by(TemplateStage.position)
				.first()
			)

			if not lts_stage:
				next_stage = (
					self.db.query(TemplateStage)
					.filter(TemplateStage.template_id == workflow.template_id)
					.order_by(TemplateStage.position)
					.first()
				)
			elif lts_stage.status == WorkflowStageStatus.REJECTED:
				next_stage = None
			else:
				next_stage = (
					self.db.query(TemplateStage)
					.filter(TemplateStage.template_id == workflow.template_id)
					.order_by(TemplateStage.position)
					.first()
				)

			approvals = (
				self.db.query(WorkflowStage)
				.join(TemplateStage)
				.filter(WorkflowStage.workflow_id == workflow.id)
				.order_by(TemplateStage.position)
				.all()
			)

			return WorkflowDto.model_construct(
				id=workflow.id, template=workflow.template, next_stage=next_stage, approvals=approvals
			)
		except Exception as e:
			self.logger.error(f"Failed to retrieve workflow: {str(e)}")
			raise e

	async def get_application_by_id(self, application_id: UUID4) -> ApplicationDto:
		try:
			application = self.db.query(Application).filter(Application.id == application_id).first()
			if application is None:
				raise ApiException("Application not found")
			return application
		except Exception as e:
			self.logger.error(f"Failed to retrieve application: {str(e)}")
			raise e

	async def get_application_documents(self, application_id: UUID4) -> List:
		"""Get documents for an application"""
		try:
			application = (
				self.db.query(Application)
				.options(
					joinedload(Application.documents).joinedload("document"),
					joinedload(Application.documents).joinedload("document_type"),
				)
				.filter(Application.id == application_id)
				.first()
			)

			if not application:
				raise ApiException("Application not found")

			return [to_application_document_dto(doc) for doc in application.documents] if application.documents else []

		except Exception as e:
			self.logger.error(f"Failed to get application documents: {e}")
			raise

	async def get_application_history(self, org_id: UUID4):
		"""Get application history for an organization"""
		try:
			applications = (
				self.db.query(Application)
				.filter(Application.organization_id == org_id)
				.order_by(Application.created_at.desc())
				.all()
			)

			return [to_application_dto(app) for app in applications]
		except Exception as e:
			self.logger.error(f"Failed to get application history: {str(e)}")
			raise

	async def submit_licence_renewal_for_review(self, organization_id: UUID4):
		"""Submit licence renewal application for review"""
		with self.db.begin():
			try:
				application = (
					self.db.query(Application)
					.filter(
						and_(
							Application.organization_id == organization_id,
							Application.type == ApplicationType.LICENCE_RENEWAL,
							Application.status == ApplicationStatus.DRAFT,
						)
					)
					.first()
				)

				if not application:
					raise ApiException("No draft licence renewal application found")

				if application.status != ApplicationStatus.DRAFT:
					raise ApiException(f"Current application is in {application.status} phase")

				await self.start_approval_workflow(application)
				return application
			except Exception as e:
				self.db.rollback()
				self.logger.error(f"Failed to submit licence renewal for review: {str(e)}")
				raise

	async def get_licence_renewal_application(self, organization_id: UUID4):
		"""Get the current licence renewal application for an organization"""
		try:
			application = (
				self.db.query(Application)
				.filter(
					and_(
						Application.organization_id == organization_id,
						Application.type == ApplicationType.LICENCE_RENEWAL,
					)
				)
				.order_by(Application.created_at.desc())
				.first()
			)

			if not application:
				return None

			return to_application_dto(application)
		except Exception as e:
			self.logger.error(f"Failed to get licence renewal application: {str(e)}")
			raise

	async def get_application_stats(self, organization_id: UUID4) -> ApplicationStatsDto:
		"""Get application statistics for an organization"""
		try:
			total_query = self.db.query(func.count(Application.id)).filter(
				Application.organization_id == organization_id
			)

			stats_query = (
				self.db.query(Application.status, func.count(Application.id).label("count"))
				.filter(Application.organization_id == organization_id)
				.group_by(Application.status)
				.all()
			)

			total = total_query.scalar() or 0

			stats = {
				"draft": 0,
				"in_review": 0,
				"approved": 0,
				"rejected": 0,
				"suspended": 0,
			}

			for status, count in stats_query:
				if status == ApplicationStatus.DRAFT:
					stats["draft"] = count
				elif status == ApplicationStatus.IN_REVIEW:
					stats["in_review"] = count
				elif status == ApplicationStatus.APPROVED:
					stats["approved"] = count
				elif status == ApplicationStatus.REJECTED:
					stats["rejected"] = count
				elif status == ApplicationStatus.SUSPENDED:
					stats["suspended"] = count

			return ApplicationStatsDto(
				total=total,
				draft=stats["draft"],
				in_review=stats["in_review"],
				approved=stats["approved"],
				rejected=stats["rejected"],
				suspended=stats["suspended"],
			)
		except Exception as e:
			self.logger.error(f"Failed to get application statistics: {str(e)}")
			raise

	async def create_permit_applications(
		self, organization_id: UUID4, applicants_data: str, supporting_documents: list
	) -> List[ApplicationDto]:
		"""Create separate permit applications for each applicant with their individual documents"""
		import json

		applicants_list = json.loads(applicants_data) if applicants_data else []

		if not applicants_list:
			raise ApiException("At least one applicant is required for permit application")

		# Ensure supporting_documents is a list and matches the number of applicants
		if not isinstance(supporting_documents, list):
			supporting_documents = []
		
		# Pad supporting_documents list if it's shorter than applicants_list
		while len(supporting_documents) < len(applicants_list):
			supporting_documents.append({})

		template = self.db.query(Template).filter(Template.code == "PERMIT_APPLICATION").first()

		if not template:
			raise ApiException("PERMIT_APPLICATION template not found. Please contact system administrator.")

		template_stages_count = (
			self.db.query(TemplateStage)
			.filter(and_(TemplateStage.template_id == template.id, TemplateStage.is_active))
			.count()
		)

		if template_stages_count == 0:
			raise ApiException("PERMIT_APPLICATION template has no active stages. Please contact system administrator.")

		doct08_type = self.db.query(LoadableItem).filter(LoadableItem.code == "DOCT08").first()
		if not doct08_type:
			raise ApiException("Document type DOCT08 not found. Please contact system administrator.")

		created_applications = []

		try:
			for index, applicant_data in enumerate(applicants_list):
				# Create separate application for each applicant
				application_code = await self._generate_unique_application_code()
				
				application = Application(
					organization_id=organization_id,
					status=ApplicationStatus.IN_REVIEW,
					type=ApplicationType.PERMIT_APPLICATION,
					code=application_code,
					created_by=self.current_user.id,
				)

				self.db.add(application)
				self.db.flush()

				# Create fee for this application
				await self.fee_service.create_application_fee(application, 0.0)

				# Create single permit applicant for this application
				permit_applicant = PermitApplicant(
					application_id=application.id,
					full_name=applicant_data.get("full_name"),
					nationality_id=applicant_data.get("nationality_id"),
					passport_number=applicant_data.get("passport_number"),
					position=applicant_data.get("position"),
					department=applicant_data.get("department"),
					employment_start_date=applicant_data.get("employment_start_date"),
					employment_end_date=applicant_data.get("employment_end_date"),
					created_by=self.current_user.id,
				)
				self.db.add(permit_applicant)
				self.db.flush()

				# Handle supporting documents for this specific applicant
				applicant_documents = supporting_documents[index] if index < len(supporting_documents) else {}
				
				if applicant_documents:
					# Handle application letter
					if "application_letter" in applicant_documents and applicant_documents["application_letter"]:
						application_letter_file = applicant_documents["application_letter"]
						await self.document_service._validate_document_file(application_letter_file)
						application_letter_doc = await self.document_service._create_document_record(
							application, application_letter_file, application.organization.account_id
						)
						permit_applicant.letter_document_id = application_letter_doc.id

						await self.document_service._create_application_document_link(
							application_letter_doc.id, application.id, doct08_type.id
						)

					# Handle passport picture
					if "passport_picture" in applicant_documents and applicant_documents["passport_picture"]:
						passport_picture_file = applicant_documents["passport_picture"]
						await self.document_service._validate_document_file(passport_picture_file)
						passport_picture_doc = await self.document_service._create_document_record(
							application, passport_picture_file, application.organization.account_id
						)
						permit_applicant.passport_document_id = passport_picture_doc.id

						await self.document_service._create_application_document_link(
							passport_picture_doc.id, application.id, doct08_type.id
						)

					self.db.add(permit_applicant)

				# Create invoice for this application
				invoice_request = CreateInvoiceRequest(
					organization_id=organization_id,
					application_id=application.id,
					description=f"Employment permit application for {applicant_data.get('full_name', 'Applicant')} - {application.code}",
				)

				await self.invoice_service.create_invoice_for_application(invoice_request)

				# Start approval workflow for this application
				await self.start_approval_workflow(application)

				created_applications.append(to_application_dto(application))

			self.db.commit()
			
			self.logger.info(f"Created {len(created_applications)} individual permit applications for organization {organization_id}")
			return created_applications

		except Exception as e:
			self.db.rollback()
			self.logger.error(f"Failed to create permit applications: {str(e)}")
			raise

	async def create_permit_application(
		self, organization_id: UUID4, applicants_data: str, supporting_documents: list
	) -> ApplicationDto:
		"""Legacy method - creates multiple applications and returns the first one for backward compatibility"""
		applications = await self.create_permit_applications(organization_id, applicants_data, supporting_documents)
		return applications[0] if applications else None
