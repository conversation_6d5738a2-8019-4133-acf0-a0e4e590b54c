from typing import List, Optional
from fastapi import File, Form, UploadFile
from fastapi.params import Depends
from pydantic import UUID4
from starlette.requests import Request

from src.core.dtos.application_dtos import ApplicationDto, ApplicationStatsDto
from src.core.dtos.document_dtos import ApplicationDocumentDto
from src.core.dtos.workflow_dtos import WorkflowDto
from src.core.guards.auth_guard import auth_guard
from src.core.shared_schema import BaseResponse, Pagination, VoidRequest
from src.modules.application.application_schema import ApplicationFilter
from src.core.logger.internal_logger import get_logger
from src.modules.application.application_service import ApplicationService

logger = get_logger(__name__)


def get_application_service() -> ApplicationService:
	"""Get ApplicationService instance"""
	return ApplicationService()


@auth_guard.can_("read.application")
async def fetch_applications_handler(
	_: Request,
	filter: ApplicationFilter = Depends(ApplicationFilter),
) -> Pagination[ApplicationDto]:
	application_service = get_application_service()
	return await application_service.retrieve_applications(filter)


@auth_guard.can_("create.application")
async def submit_application_handler(
	_: Request,
	organization_id: UUID4,
) -> BaseResponse[ApplicationDto]:
	application_service = get_application_service()
	application = await application_service.submit_application_for_review(organization_id)
	return BaseResponse(data=application)


@auth_guard.can_("delete.application")
async def delete_application_handler(_: Request, application_id: str, payload: VoidRequest) -> None:
	application_service = get_application_service()
	await application_service.delete_application(application_id, payload)
	return None


@auth_guard.can_("read.application")
async def fetch_single_application_handler(_: Request, application_id: str) -> ApplicationDto:
	application_service = get_application_service()
	application = await application_service.get_application_by_id(application_id)
	return application


async def fetch_application_workflow_handler(_: Request, application_id: str) -> WorkflowDto:
	application_service = get_application_service()
	workflow = await application_service.get_application_workflow(application_id)
	return workflow


@auth_guard.can_("read.application")
async def fetch_application_documents_handler(
	_: Request, application_id: UUID4
) -> BaseResponse[list[ApplicationDocumentDto]]:
	"""Get documents for an application"""
	application_service = get_application_service()
	documents = await application_service.get_application_documents(application_id)
	return BaseResponse(data=documents)


@auth_guard.can_("read.application")
async def fetch_application_history_handler(_: Request, organization_id: UUID4) -> BaseResponse[list[ApplicationDto]]:
	"""Get application history for an organization"""
	application_service = get_application_service()
	applications = await application_service.get_application_history(organization_id)
	return BaseResponse(data=applications)


@auth_guard.can_("read.application")
async def fetch_application_stats_handler(_: Request, organization_id: UUID4) -> BaseResponse[ApplicationStatsDto]:
	"""Get application statistics for an organization"""
	application_service = get_application_service()
	stats = await application_service.get_application_stats(organization_id)
	return BaseResponse(data=stats)


@auth_guard.can_("create.application")
async def create_permit_applications_handler(
	_: Request,
	organization_id: UUID4 = Form(...),
	applicants_data: str = Form(...),
	supporting_documents: List[UploadFile] = File(...)
) -> BaseResponse[List[ApplicationDto]]:
	"""Create permit applications for multiple applicants"""
	application_service = get_application_service()
	
	# Convert list of files to list of dictionaries
	documents_list = []
	current_applicant_docs = {}
	file_index = 0
	
	for file in supporting_documents:
		if file.filename:
			# Determine which applicant and document type based on filename pattern
			if "application_letter" in file.filename:
				if file_index > 0:
					documents_list.append(current_applicant_docs)
					current_applicant_docs = {}
				current_applicant_docs["application_letter"] = file
			elif "passport_picture" in file.filename:
				current_applicant_docs["passport_picture"] = file
				documents_list.append(current_applicant_docs)
				current_applicant_docs = {}
				file_index += 1
	
	if current_applicant_docs:
		documents_list.append(current_applicant_docs)
	
	applications = await application_service.create_permit_applications(
		organization_id, applicants_data, documents_list
	)
	return BaseResponse(data=applications)
