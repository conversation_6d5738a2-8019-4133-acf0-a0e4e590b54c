from datetime import date
from typing import List, Optional

from pydantic import UUID4, BaseModel, Field

from src.config.db.models import ApplicationStatus, ApplicationType
from src.core.shared_schema import BaseRequest


class ApplicationFilter(BaseRequest):
	organization_id: Optional[UUID4] = Field(default=None, description="Organization ID")
	status: Optional[ApplicationStatus] = Field(default=None, description="Application status")
	type: Optional[ApplicationType] = Field(default=None, description="Application type")


# Permit Application Request Schemas
class CreatePermitApplicantRequest(BaseModel):
	full_name: str
	nationality: str
	passport_number: str
	position: Optional[str] = None
	department: Optional[str] = None
	employment_start_date: Optional[date] = None
	employment_end_date: Optional[date] = None
	work_location: Optional[str] = None
	qualification: Optional[str] = None


class UpdatePermitApplicantRequest(BaseModel):
	full_name: Optional[str] = None
	nationality: Optional[str] = None
	passport_number: Optional[str] = None
	position: Optional[str] = None
	department: Optional[str] = None
	employment_start_date: Optional[date] = None
	employment_end_date: Optional[date] = None
	work_location: Optional[str] = None
	qualification: Optional[str] = None


class CreatePermitApplicationRequest(BaseModel):
	organization_id: UUID4
	applicants: List[CreatePermitApplicantRequest]
