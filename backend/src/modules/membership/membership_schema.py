from typing import Optional
from pydantic import BaseModel, EmailStr, UUID4
from src.config.db.models.member import Member<PERSON>ole
from src.config.db.models.base import Gender, InvitationStatus
from src.core.shared_schema import BaseRequest


class MemberInvitationRequest(BaseModel):
	"""Request schema for inviting a member to an organization"""

	email: EmailStr
	role: MemberRole = MemberRole.MEMBER


class AcceptInvitationRequest(BaseModel):
	"""Request schema for accepting a member invitation"""

	user_id: Optional[UUID4] = None  # For existing users


class RejectInvitationRequest(BaseModel):
	"""Request schema for rejecting a member invitation"""

	reason: Optional[str] = None


class AcceptInvitationWithAccountRequest(BaseModel):
	"""Request schema for accepting invitation and creating new account"""

	first_name: str
	last_name: str
	gender: Gender
	password: str


class ValidateInvitationTokenRequest(BaseModel):
	"""Request schema for validating invitation token"""

	token: str


class ValidateInvitationTokenResponse(BaseModel):
	"""Response schema for invitation token validation"""

	valid: bool
	email: Optional[str] = None
	organization_name: Optional[str] = None
	role: Optional[str] = None
	inviter_name: Optional[str] = None
	user_exists: bool = False
	expires_at: Optional[str] = None


class ChangeMemberRoleRequest(BaseModel):
	"""Request schema for changing a member's role"""

	role: MemberRole


class MemberFilter(BaseRequest):
	"""Filter schema for members"""
	
	role: Optional[MemberRole] = None
	search: Optional[str] = None


class MemberInvitationFilter(BaseRequest):
	"""Filter schema for member invitations"""
	
	status: Optional[InvitationStatus] = None
	search: Optional[str] = None


class OrganizationMembershipStats(BaseModel):
	"""Organization membership statistics"""
	
	active_members: int
	pending_invites: int
	total_members: int
