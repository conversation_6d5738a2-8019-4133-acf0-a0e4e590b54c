from typing import List

from fastapi import Depends
from pydantic import UUID4
from starlette.requests import Request

from src.core.dtos.membership_dtos import MemberDto, MemberInvitationDto
from src.core.guards.auth_guard import auth_guard
from src.core.shared_schema import BaseResponse, Pagination

from .membership_schema import (
	AcceptInvitationRequest,
	AcceptInvitationWithAccountRequest,
	ChangeMemberRoleRequest,
	MemberFilter,
	MemberInvitationFilter,
	MemberInvitationRequest,
	OrganizationMembershipStats,
	RejectInvitationRequest,
	ValidateInvitationTokenRequest,
	ValidateInvitationTokenResponse,
)
from .membership_service import MembershipService


def get_membership_service() -> MembershipService:
	"""Get MembershipService instance"""
	return MembershipService()


@auth_guard.can_("read.membership")
async def get_organization_members_handler(
	_: Request,
	organization_id: UUID4,
	filter_params: MemberFilter = Depends(MemberFilter),
) -> Pagination[MemberDto]:
	"""Get paginated members of an organization"""
	membership_service = get_membership_service()
	return await membership_service.get_organization_members(organization_id, filter_params)


@auth_guard.can_("create.membership")
async def invite_member_handler(
	_: Request,
	organization_id: UUID4,
	request: MemberInvitationRequest,
) -> BaseResponse[MemberInvitationDto]:
	"""Invite a new member to an organization"""
	membership_service = get_membership_service()
	invitation = await membership_service.invite_member(
		organization_id=organization_id,
		email=request.email,
		role=request.role,
	)
	return BaseResponse(
		success=True,
		message="Member invitation sent successfully",
		data=invitation,
	)


@auth_guard.can_("read.membership")
async def get_member_invitations_handler(
	_: Request,
	organization_id: UUID4,
	filter_params: MemberInvitationFilter = Depends(MemberInvitationFilter),
) -> Pagination[MemberInvitationDto]:
	"""Get paginated member invitations for an organization"""
	membership_service = get_membership_service()
	return await membership_service.get_member_invitations(organization_id, filter_params)


@auth_guard.can_("update.membership")
async def accept_member_invitation_handler(
	_: Request,
	invitation_code: str,
	request: AcceptInvitationRequest,
) -> BaseResponse[MemberDto]:
	"""Accept a member invitation"""
	membership_service = get_membership_service()
	member = await membership_service.accept_invitation(
		invitation_code=invitation_code,
		user_id=request.user_id,
	)
	return BaseResponse(
		success=True,
		message="Member invitation accepted successfully",
		data=member,
	)


@auth_guard.can_("update.membership")
async def reject_member_invitation_handler(
	_: Request,
	invitation_code: str,
	request: RejectInvitationRequest,
) -> BaseResponse[bool]:
	"""Reject a member invitation"""
	membership_service = get_membership_service()
	result = await membership_service.reject_invitation(
		invitation_code=invitation_code,
		reason=request.reason,
	)
	return BaseResponse(
		success=True,
		message="Member invitation rejected successfully",
		data=result,
	)


@auth_guard.can_("delete.membership")
async def cancel_invitation_handler(
	_: Request,
	organization_id: UUID4,
	invitation_id: UUID4,
) -> BaseResponse[bool]:
	"""Cancel a pending member invitation"""
	membership_service = get_membership_service()
	result = await membership_service.cancel_invitation(
		organization_id=organization_id,
		invitation_id=invitation_id,
	)
	return BaseResponse(
		success=True,
		message="Member invitation cancelled successfully",
		data=result,
	)


@auth_guard.can_("delete.membership")
async def remove_member_handler(
	_: Request,
	organization_id: UUID4,
	member_id: UUID4,
) -> BaseResponse[bool]:
	"""Remove a member from an organization"""
	membership_service = get_membership_service()
	result = await membership_service.remove_member(
		organization_id=organization_id,
		member_id=member_id,
	)
	return BaseResponse(
		success=True,
		message="Member removed successfully",
		data=result,
	)


@auth_guard.can_("update.membership")
async def change_member_role_handler(
	_: Request,
	organization_id: UUID4,
	member_id: UUID4,
	request: ChangeMemberRoleRequest,
) -> BaseResponse[MemberDto]:
	"""Change a member's role in an organization"""
	membership_service = get_membership_service()
	member = await membership_service.change_member_role(
		organization_id=organization_id,
		member_id=member_id,
		new_role=request.role,
	)
	return BaseResponse(
		success=True,
		message="Member role updated successfully",
		data=member,
	)


async def validate_invitation_token_handler(
	_: Request,
	request: ValidateInvitationTokenRequest,
) -> BaseResponse[ValidateInvitationTokenResponse]:
	"""Validate invitation token"""
	membership_service = get_membership_service()
	validation = await membership_service.validate_invitation_token(request.token)
	return BaseResponse(
		success=True,
		message="Token validation completed",
		data=ValidateInvitationTokenResponse(**validation),
	)


async def accept_invitation_with_account_handler(
	_: Request,
	token: str,
	request: AcceptInvitationWithAccountRequest,
) -> BaseResponse[MemberDto]:
	"""Accept invitation by creating new account"""
	membership_service = get_membership_service()
	member = await membership_service.accept_invitation_with_account_creation(
		token=token,
		first_name=request.first_name,
		last_name=request.last_name,
		gender=request.gender,
		password=request.password,
	)
	return BaseResponse(
		success=True,
		message="Account created and invitation accepted successfully",
		data=member,
	)


@auth_guard.can_("read.membership")
async def get_organization_membership_stats_handler(
	_: Request,
	organization_id: UUID4,
) -> BaseResponse[OrganizationMembershipStats]:
	"""Get organization membership statistics"""
	membership_service = get_membership_service()
	stats = await membership_service.get_organization_membership_stats(organization_id)
	
	stats_obj = OrganizationMembershipStats(
		active_members=stats["active_members"],
		pending_invites=stats["pending_invites"],
		total_members=stats["total_members"]
	)
	
	return BaseResponse(
		success=True,
		message="Membership statistics retrieved successfully",
		data=stats_obj,
	)
