from typing import List
from fastapi import APIRouter, status

from src.core.dtos.membership_dtos import MemberDto, MemberInvitationDto
from src.core.shared_schema import BaseResponse, Pagination
from src.modules.membership import membership_controller as controller
from src.modules.membership.membership_schema import OrganizationMembershipStats, ValidateInvitationTokenResponse

membership_router_v1 = APIRouter(tags=["membership"])

# ORGANIZATION MEMBERS
membership_router_v1.add_api_route(
	path="/organizations/{organization_id}/members",
	endpoint=controller.get_organization_members_handler,
	methods=["GET"],
	response_model=Pagination[MemberDto],
	status_code=status.HTTP_200_OK,
)

membership_router_v1.add_api_route(
	path="/organizations/{organization_id}/members/{member_id}",
	endpoint=controller.remove_member_handler,
	methods=["DELETE"],
	response_model=BaseResponse[bool],
	status_code=status.HTTP_200_OK,
)

membership_router_v1.add_api_route(
	path="/organizations/{organization_id}/members/{member_id}/role",
	endpoint=controller.change_member_role_handler,
	methods=["PATCH"],
	response_model=BaseResponse[MemberDto],
	status_code=status.HTTP_200_OK,
)

membership_router_v1.add_api_route(
	path="/organizations/{organization_id}/members/stats",
	endpoint=controller.get_organization_membership_stats_handler,
	methods=["GET"],
	response_model=BaseResponse[OrganizationMembershipStats],
	status_code=status.HTTP_200_OK,
)

# MEMBER INVITATIONS
membership_router_v1.add_api_route(
	path="/organizations/{organization_id}/members/invite",
	endpoint=controller.invite_member_handler,
	methods=["POST"],
	response_model=BaseResponse[MemberInvitationDto],
	status_code=status.HTTP_201_CREATED,
)

membership_router_v1.add_api_route(
	path="/organizations/{organization_id}/members/invitations",
	endpoint=controller.get_member_invitations_handler,
	methods=["GET"],
	response_model=Pagination[MemberInvitationDto],
	status_code=status.HTTP_200_OK,
)

membership_router_v1.add_api_route(
	path="/organizations/{organization_id}/members/invitations/{invitation_id}/cancel",
	endpoint=controller.cancel_invitation_handler,
	methods=["DELETE"],
	response_model=BaseResponse[bool],
	status_code=status.HTTP_200_OK,
)

# PUBLIC INVITATION ACTIONS (no auth required for external users)
membership_router_v1.add_api_route(
	path="/invitations/{invitation_code}/accept",
	endpoint=controller.accept_member_invitation_handler,
	methods=["POST"],
	response_model=BaseResponse[MemberDto],
	status_code=status.HTTP_200_OK,
)

membership_router_v1.add_api_route(
	path="/invitations/{invitation_code}/reject",
	endpoint=controller.reject_member_invitation_handler,
	methods=["POST"],
	response_model=BaseResponse[bool],
	status_code=status.HTTP_200_OK,
)

# JWT TOKEN-BASED INVITATION ROUTES (no auth required)
membership_router_v1.add_api_route(
	path="/invitations/validate",
	endpoint=controller.validate_invitation_token_handler,
	methods=["POST"],
	response_model=BaseResponse[ValidateInvitationTokenResponse],
	status_code=status.HTTP_200_OK,
)

membership_router_v1.add_api_route(
	path="/invitations/{token}/accept-with-account",
	endpoint=controller.accept_invitation_with_account_handler,
	methods=["POST"],
	response_model=BaseResponse[MemberDto],
	status_code=status.HTTP_201_CREATED,
)
