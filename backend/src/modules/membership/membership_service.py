import secrets
import string
from datetime import datetime, timedelta
from typing import List, Optional

from src.config.db.models.account import AccountStatus
from fastapi_pagination import Params
from fastapi_pagination.ext.sqlalchemy import paginate
from pydantic import UUID4
from sqlalchemy import and_, or_
from sqlalchemy.orm import joinedload

from src.config.db.models import (
	Account,
	AccountType,
	Member,
	MemberInvitation,
	Notification,
	NotificationRecipient,
	Organization,
	User,
)
from src.config.db.models.base import InvitationStatus
from src.config.db.models.member import MemberRole
from src.config.db.models.notification import NotificationPriority, NotificationType
from src.config.settings import APP_NAME, FRONTEND_URL
from src.core.base.base_repository import BaseRepository
from src.core.dtos.membership_dtos import MemberDto, MemberInvitationDto, to_member_dto, to_member_invitation_dto, \
	MembershipSummaryDto, to_membership_summary_dto
from src.core.exceptions.api import ApiException
from src.core.logger.internal_logger import get_logger
from src.core.services.email_service import EmailPriority, EmailRecipient, EmailTemplate, get_email_service
from src.core.services.encryption_service import encryption_service
from src.core.shared_schema import Pagination
from src.modules.auth.auth_service import AuthService


class MembershipService(BaseRepository):
	def __init__(self):
		super().__init__()
		self.logger = get_logger(__name__)
		self._email_service = None
		self._auth_service = None

	@property
	def email_service(self):
		"""Lazy-loaded EmailService"""
		if self._email_service is None:
			self._email_service = get_email_service()
		return self._email_service

	@property
	def auth_service(self):
		"""Lazy-loaded AuthService"""
		if self._auth_service is None:
			self._auth_service = AuthService()
		return self._auth_service

	async def get_organization_members(self, organization_id: UUID4, filter_params) -> Pagination[MemberDto]:
		"""Get paginated members of an organization"""
		try:
			query = (
				self.db.query(Member)
				.options(joinedload(Member.user))
				.filter(and_(Member.organization_id == organization_id, Member.is_active))
			)

			if filter_params.role:
				query = query.filter(Member.role == filter_params.role)

			if filter_params.search:
				query = query.filter(
					or_(
						User.first_name.ilike(f"%{filter_params.search}%"),
						User.last_name.ilike(f"%{filter_params.search}%"),
						User.email.ilike(f"%{filter_params.search}%")
					)
				).join(User, Member.user_id == User.id)

			query = query.order_by(Member.created_at.desc())

			paginated_result = paginate(query, Params(page=filter_params.page, size=filter_params.size))
			members = [to_member_dto(member) for member in paginated_result.items]

			return Pagination.from_query_result(members, paginated_result)
		except Exception as e:
			self.logger.error(f"Failed to get organization members: {str(e)}")
			raise ApiException("Failed to retrieve organization members")

	async def invite_member(self, organization_id: UUID4, email: str, role: MemberRole) -> MemberInvitationDto:
		"""Invite a new member to an organization"""
		try:
			organization = self.db.query(Organization).filter(Organization.id == organization_id).first()
			if not organization:
				raise ApiException("Organization not found")

			existing_member = (
				self.db.query(Member)
				.join(User, Member.user_id == User.id)
				.filter(and_(Member.organization_id == organization_id, User.email == email, Member.is_active))
				.first()
			)
			if existing_member:
				raise ApiException("User is already a member of this organization")

			existing_invitation = (
				self.db.query(MemberInvitation)
				.filter(
					and_(
						MemberInvitation.organization_id == organization_id,
						MemberInvitation.invited_email == email,
						MemberInvitation.status == InvitationStatus.PENDING,
						MemberInvitation.expires_at > datetime.now(),
					)
				)
				.first()
			)
			if existing_invitation:
				raise ApiException("An active invitation already exists for this email")

			code = self._generate_invitation_code()

			invitation = MemberInvitation(
				organization_id=organization_id,
				inviter_user_id=self.current_user.id,
				invited_email=email,
				code=code,
				role=role,
				status=InvitationStatus.PENDING,
				expires_at=datetime.now() + timedelta(days=7),
				created_by=self.current_user.id,
				updated_by=self.current_user.id,
			)

			self.db.add(invitation)
			self.db.flush()

			invitation = (
				self.db.query(MemberInvitation)
				.options(joinedload(MemberInvitation.organization), joinedload(MemberInvitation.inviter))
				.filter(MemberInvitation.id == invitation.id)
				.first()
			)

			existing_user = self.db.query(User).filter(User.email == email).first()

			invitation_token = encryption_service.generate_invitation_token(
				invitation_id=str(invitation.id), email=email, organization_id=str(organization_id)
			)

			invitation_link = f"{FRONTEND_URL}/invitations/accept?token={invitation_token}"

			if existing_user:
				await self._send_invitation_notification(invitation, existing_user, invitation_link)

			await self._send_invitation_email(invitation, invitation_link)

			return to_member_invitation_dto(invitation)

		except Exception as e:
			self.logger.error(f"Failed to invite member: {str(e)}")
			raise  # ApiException("Failed to send member invitation")

	async def get_member_invitations(self, organization_id: UUID4, filter_params) -> Pagination[MemberInvitationDto]:
		"""Get paginated member invitations for an organization"""
		try:
			query = (
				self.db.query(MemberInvitation)
				.options(joinedload(MemberInvitation.organization), joinedload(MemberInvitation.inviter))
				.filter(MemberInvitation.organization_id == organization_id)
			)

			if filter_params.status:
				query = query.filter(MemberInvitation.status == filter_params.status)

			if filter_params.search:
				query = query.filter(MemberInvitation.invited_email.ilike(f"%{filter_params.search}%"))

			query = query.order_by(MemberInvitation.created_at.desc())

			paginated_result = paginate(query, Params(page=filter_params.page, size=filter_params.size))
			invitations = [to_member_invitation_dto(invitation) for invitation in paginated_result.items]

			return Pagination.from_query_result(invitations, paginated_result)
		except Exception as e:
			self.logger.error(f"Failed to get member invitations: {str(e)}")
			raise ApiException("Failed to retrieve member invitations")

	async def accept_invitation(self, invitation_code: str, user_id: Optional[UUID4] = None) -> MemberDto:
		"""Accept a member invitation"""
		try:
			# Find the invitation
			invitation = (
				self.db.query(MemberInvitation)
				.options(joinedload(MemberInvitation.organization))
				.filter(
					and_(
						MemberInvitation.code == invitation_code,
						MemberInvitation.status == InvitationStatus.PENDING,
						MemberInvitation.expires_at > datetime.now(),
					)
				)
				.first()
			)

			if not invitation:
				raise ApiException("Invalid or expired invitation")

			# Use provided user_id or current user
			target_user_id = user_id or self.current_user.id

			# Check if user is already a member
			existing_member = (
				self.db.query(Member)
				.filter(
					and_(
						Member.organization_id == invitation.organization_id,
						Member.user_id == target_user_id,
						Member.is_active,
					)
				)
				.first()
			)
			if existing_member:
				raise ApiException("User is already a member of this organization")

			# Create member
			member = Member(
				organization_id=invitation.organization_id,
				user_id=target_user_id,
				role=invitation.role,
				joined_at=datetime.now(),
				is_active=True,
				created_by=target_user_id,
				updated_by=target_user_id,
			)

			# Update invitation status
			invitation.status = InvitationStatus.ACCEPTED
			invitation.updated_by = target_user_id
			invitation.updated_at = datetime.now()

			self.db.add(member)
			self.db.flush()

			# Load relationships for DTO conversion
			member = self.db.query(Member).options(joinedload(Member.user)).filter(Member.id == member.id).first()

			if not self.db.in_transaction() or not self.db.in_nested_transaction():
				self.db.commit()

			return to_member_dto(member)
		except ApiException:
			raise
		except Exception as e:
			self.logger.error(f"Failed to accept invitation: {str(e)}")
			raise ApiException("Failed to accept member invitation")

	async def reject_invitation(self, invitation_code: str, reason: Optional[str] = None) -> bool:
		"""Reject a member invitation"""
		try:
			invitation = (
				self.db.query(MemberInvitation)
				.filter(
					and_(MemberInvitation.code == invitation_code, MemberInvitation.status == InvitationStatus.PENDING)
				)
				.first()
			)

			if not invitation:
				raise ApiException("Invitation not found")

			invitation.status = InvitationStatus.REJECTED
			invitation.updated_by = self.current_user.id if self.current_user else None
			invitation.updated_at = datetime.now()

			if not self.db.in_transaction() or not self.db.in_nested_transaction():
				self.db.commit()

			return True
		except ApiException:
			raise
		except Exception as e:
			self.logger.error(f"Failed to reject invitation: {str(e)}")
			raise ApiException("Failed to reject member invitation")

	async def cancel_invitation(self, organization_id: UUID4, invitation_id: UUID4) -> bool:
		"""Cancel a pending member invitation"""
		try:
			invitation = (
				self.db.query(MemberInvitation)
				.filter(
					and_(
						MemberInvitation.id == invitation_id,
						MemberInvitation.organization_id == organization_id,
						MemberInvitation.status == InvitationStatus.PENDING,
					)
				)
				.first()
			)

			if not invitation:
				raise ApiException("Invitation not found or already processed")

			invitation.status = InvitationStatus.CANCELLED
			invitation.updated_by = self.current_user.id
			invitation.updated_at = datetime.now()

			if not self.db.in_transaction() or not self.db.in_nested_transaction():
				self.db.commit()

			return True
		except ApiException:
			raise
		except Exception as e:
			self.logger.error(f"Failed to cancel invitation: {str(e)}")
			raise ApiException("Failed to cancel member invitation")

	async def remove_member(self, organization_id: UUID4, member_id: UUID4) -> bool:
		"""Remove a member from an organization"""
		try:
			member = (
				self.db.query(Member)
				.filter(and_(Member.id == member_id, Member.organization_id == organization_id, Member.is_active))
				.first()
			)

			if not member:
				raise ApiException("Member not found")

			if member.role == MemberRole.OWNER:
				raise ApiException("Cannot remove organization owner")

			member.is_active = False
			member.voided = True
			member.updated_by = self.current_user.id
			member.updated_at = datetime.now()

			if not self.db.in_transaction() or not self.db.in_nested_transaction():
				self.db.commit()

			return True
		except ApiException:
			raise
		except Exception as e:
			self.logger.error(f"Failed to remove member: {str(e)}")
			raise ApiException("Failed to remove member")

	async def change_member_role(self, organization_id: UUID4, member_id: UUID4, new_role: MemberRole) -> MemberDto:
		"""Change a member's role in an organization"""
		try:
			member = (
				self.db.query(Member)
				.options(joinedload(Member.user))
				.filter(and_(Member.id == member_id, Member.organization_id == organization_id, Member.is_active))
				.first()
			)

			if not member:
				raise ApiException("Member not found")

			if member.role == MemberRole.OWNER:
				owner_count = (
					self.db.query(Member)
					.filter(and_(Member.organization_id == organization_id, Member.role == MemberRole.OWNER, Member.is_active))
					.count()
				)

				if owner_count <= 1:
					raise ApiException("Cannot change role of the only organization owner")

			if new_role == MemberRole.OWNER:
				current_member = (
					self.db.query(Member)
					.filter(and_(
						Member.organization_id == organization_id,
						Member.user_id == self.current_user.id,
						Member.role == MemberRole.OWNER,
						Member.is_active
					))
					.first()
				)

				if not current_member:
					raise ApiException("Only organization owners can promote members to owner")

			member.role = new_role
			member.updated_by = self.current_user.id
			member.updated_at = datetime.now()

			if not self.db.in_transaction() or not self.db.in_nested_transaction():
				self.db.commit()

			return to_member_dto(member)
		except ApiException:
			raise
		except Exception as e:
			self.logger.error(f"Failed to change member role: {str(e)}")
			raise ApiException("Failed to change member role")

	def _generate_invitation_code(self, length: int = 6) -> str:
		"""Generate a random invitation code"""
		characters = string.ascii_uppercase + string.digits
		return "".join(secrets.choice(characters) for _ in range(length))

	async def _send_invitation_email(self, invitation: MemberInvitation, invitation_link: str):
		"""Send invitation email to non-existing users"""
		try:
			template_vars = {
				"organization_name": invitation.organization.name,
				"organization_description": getattr(invitation.organization, "description", ""),
				"inviter_name": f"{invitation.inviter.first_name} {invitation.inviter.last_name}",
				"inviter_email": invitation.inviter.email,
				"role": invitation.role.value,
				"invitation_link": invitation_link,
				"expires_at": invitation.expires_at.strftime("%B %d, %Y at %I:%M %p"),
				"app_name": APP_NAME,
				"app_url": FRONTEND_URL,
				"support_url": f"{FRONTEND_URL}/support",
			}

			recipient = EmailRecipient(email=invitation.invited_email)

			result = self.email_service.send_template_email(
				template=EmailTemplate.MEMBER_INVITATION,
				recipients=[recipient],
				subject=f"You're invited to join {invitation.organization.name}",
				sender_email=invitation.inviter.email,
				template_variables=template_vars,
				priority=EmailPriority.HIGH,
			)

			self.logger.info(f"Invitation email sent to {invitation.invited_email}: {result}")

		except Exception as e:
			self.logger.error(f"Failed to send invitation email: {str(e)}")
			raise

	async def _send_invitation_notification(self, invitation: MemberInvitation, user: User, invitation_link: str):
		"""Send in-app notification to existing users"""
		try:
			notification = Notification(
				title="Organization Invitation",
				message=f"You've been invited to join {invitation.organization.name} as a {invitation.role.value}",
				type=NotificationType.ACTIVITY,
				sender_id=invitation.inviter_user_id,
				priority=NotificationPriority.HIGH,
				created_by=invitation.inviter_user_id,
			)

			self.db.add(notification)
			self.db.flush()

			# Add recipient
			recipient = NotificationRecipient(
				notification_id=notification.id,
				account_id=user.account_id,
				is_read=False,
				is_archived=False,
				created_by=invitation.inviter_user_id,
			)

			self.db.add(recipient)
			self.db.commit()

			self.logger.info(f"Invitation notification sent to user {user.email}")

		except Exception as e:
			self.logger.error(f"Failed to send invitation notification: {str(e)}")
			raise

	async def validate_invitation_token(self, token: str) -> dict:
		"""Validate invitation token and return invitation details"""
		try:
			# Verify JWT token
			payload = encryption_service.verify_invitation_token(token)
			if not payload:
				return {"valid": False}

			invitation_id = payload.get("sub")
			email = payload.get("email")

			# Get invitation details
			invitation = (
				self.db.query(MemberInvitation)
				.options(joinedload(MemberInvitation.organization), joinedload(MemberInvitation.inviter))
				.filter(
					and_(
						MemberInvitation.id == invitation_id,
						MemberInvitation.invited_email == email,
						MemberInvitation.status == InvitationStatus.PENDING,
						MemberInvitation.expires_at > datetime.now(),
					)
				)
				.first()
			)

			if not invitation:
				return {"valid": False}

			# Check if user exists
			user_exists = self.db.query(User).filter(User.email == email).first() is not None

			return {
				"valid": True,
				"email": email,
				"organization_name": invitation.organization.name,
				"role": invitation.role.value,
				"inviter_name": f"{invitation.inviter.first_name} {invitation.inviter.last_name}",
				"user_exists": user_exists,
				"expires_at": invitation.expires_at.strftime("%B %d, %Y at %I:%M %p"),
			}

		except Exception as e:
			self.logger.error(f"Failed to validate invitation token: {str(e)}")
			return {"valid": False}

	async def accept_invitation_with_account_creation(
		self, token: str, first_name: str, last_name: str, gender: str, password: str
	) -> MemberDto:
		"""Accept invitation by creating new account and adding to organization"""
		try:
			validation = await self.validate_invitation_token(token)
			if not validation["valid"]:
				raise ApiException("Invalid or expired invitation token")

			if validation["user_exists"]:
				raise ApiException("User with this email already exists. Please login and accept the invitation.")

			email = validation["email"]
			payload = encryption_service.verify_invitation_token(token)
			invitation_id = payload.get("sub")

			invitation = (
				self.db.query(MemberInvitation)
				.options(joinedload(MemberInvitation.organization))
				.filter(MemberInvitation.id == invitation_id)
				.first()
			)

			if not invitation:
				raise ApiException("Invitation not found")

			username = await self.auth_service.generate_username()

			account = Account(
				handle=username,
				type=AccountType.USER,
				created_by=invitation.inviter_user_id,
				status=AccountStatus.ACTIVE,
			)
			self.db.add(account)
			self.db.flush()

			hashed_password = self.auth_service.encryption_service.hash_password(password)

			user = User(
				first_name=first_name,
				last_name=last_name,
				email=email,
				gender=gender,
				hashed_password=hashed_password,
				account_id=account.id,
				is_external=True,
				verified=True,
			)
			self.db.add(user)
			self.db.flush()

			member = Member(
				organization_id=invitation.organization_id,
				user_id=user.id,
				role=invitation.role,
				joined_at=datetime.now(),
				is_active=True,
				created_by=user.id,
				updated_by=user.id,
			)

			invitation.status = InvitationStatus.ACCEPTED
			invitation.updated_by = user.id
			invitation.updated_at = datetime.now()

			self.db.add(member)
			self.db.flush()
			self.db.commit()

			member = self.db.query(Member).options(joinedload(Member.user)).filter(Member.id == member.id).first()

			return to_member_dto(member)

		except ApiException:
			self.db.rollback()
			raise
		except Exception as e:
			self.db.rollback()
			self.logger.error(f"Failed to accept invitation with account creation: {str(e)}")
			raise

	async def get_organization_membership_stats(self, organization_id: UUID4) -> dict:
		"""Get organization membership statistics"""
		try:
			active_members = self.db.query(Member).filter(
				Member.organization_id == organization_id,
				Member.is_active,
				~Member.voided
			).count()

			total_members = self.db.query(Member).filter(
				Member.organization_id == organization_id,
				~Member.voided
			).count()

			pending_invites = self.db.query(MemberInvitation).filter(
				MemberInvitation.organization_id == organization_id,
				MemberInvitation.status == InvitationStatus.PENDING,
				~Member.voided
			).count()

			return {
				"active_members": active_members,
				"pending_invites": pending_invites,
				"total_members": total_members
			}

		except Exception as e:
			self.logger.error(f"Failed to get organization membership stats: {str(e)}")
			raise ApiException("Failed to retrieve membership statistics")

	async def create_default_member(self, org_id: UUID4) -> Member:
		try:
			member = Member(
				organization_id=org_id,
				user_id=self.current_user.id,
				created_by=self.current_user.id,
				role=MemberRole.OWNER,
				joined_at=datetime.now(),
			)

			self.db.add(member)
			self.db.flush()

			if not self.db.in_transaction() or not self.db.in_nested_transaction():
				self.db.commit()

			return member
		except Exception as e:
			self.logger.error(f"Failed to add organization members: {str(e)}")
			raise ApiException("Failed to add organization members")

	async def retrieve_user_memberships(self, user_id: UUID4 = None) -> List[MembershipSummaryDto]:
		try:
			user_id = self.current_user.id if not user_id else user_id

			rows = self.db.query(Member).filter(Member.user_id == user_id).all()

			return [to_membership_summary_dto(row) for row in rows]
		except Exception as e:
			self.logger.error(f"Failed to fetch user memberships: {str(e)}")
			raise
