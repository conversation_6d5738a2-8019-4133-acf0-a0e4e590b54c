from typing import List

from fastapi import APIRouter, status

from src.core.dtos.organization_dtos import (
	BankDetailsDto,
	DirectorDto,
	FundingSourceDto,
	LocationActivityDto,
	OrganizationAuditorDto,
	OrganizationDonorDto,
	OrganizationDto,
	OrganizationProjectDto,
	OrganizationSectorDto,
	OrganizationStaffDto,
	OrganizationVerificationDto,
	TargetGroupDto,
)
from src.core.shared_schema import BaseResponse, Pagination
from src.modules.organization import organization_controller as controller

organization_router_v1 = APIRouter(tags=["organizations"])

# ORGANIZATION
organization_router_v1.add_api_route(
	path="",
	endpoint=controller.create_organization_handler,
	methods=["POST"],
	response_model=BaseResponse[OrganizationDto],
	status_code=status.HTTP_201_CREATED,
)
organization_router_v1.add_api_route(
	path="",
	endpoint=controller.fetch_organizations_handler,
	methods=["GET"],
	response_model=Pagination[OrganizationDto],
)
organization_router_v1.add_api_route(
	path="/{organization_id}",
	endpoint=controller.fetch_single_organization_handler,
	methods=["GET"],
	response_model=BaseResponse[OrganizationDto],
)

# DIRECTORS
organization_router_v1.add_api_route(
	path="/{organization_id}/directors",
	endpoint=controller.add_organization_directors_handler,
	methods=["POST"],
	response_model=BaseResponse[List[DirectorDto]],
	status_code=status.HTTP_201_CREATED,
)
organization_router_v1.add_api_route(
	path="/{organization_id}/directors",
	endpoint=controller.get_organization_directors_handler,
	methods=["GET"],
	response_model=BaseResponse[List[DirectorDto]],
	status_code=status.HTTP_200_OK,
)
organization_router_v1.add_api_route(
	path="/{organization_id}/directors/{director_id}",
	endpoint=controller.edit_organization_director_handler,
	methods=["PUT"],
	response_model=BaseResponse[List[DirectorDto]],
	status_code=status.HTTP_200_OK,
)
organization_router_v1.add_api_route(
	path="/{organization_id}/directors/{director_id}",
	endpoint=controller.delete_organization_director_handler,
	methods=["DELETE"],
	response_model=BaseResponse[bool],
	status_code=status.HTTP_200_OK,
)

# ORGANIZATION SECTORS
organization_router_v1.add_api_route(
	path="/{organization_id}/sectors",
	endpoint=controller.add_organization_sectors_handler,
	methods=["POST"],
	response_model=BaseResponse[OrganizationSectorDto],
	status_code=status.HTTP_201_CREATED,
)
organization_router_v1.add_api_route(
	path="/{organization_id}/sectors",
	endpoint=controller.get_organization_sectors_handler,
	methods=["GET"],
	response_model=BaseResponse[List[OrganizationSectorDto]],
	status_code=status.HTTP_200_OK,
)
organization_router_v1.add_api_route(
	path="/{organization_id}/sectors/{sector_id}",
	endpoint=controller.edit_organization_sector_handler,
	methods=["PUT"],
	response_model=BaseResponse[OrganizationSectorDto],
	status_code=status.HTTP_200_OK,
)
organization_router_v1.add_api_route(
	path="/{organization_id}/sectors/{sector_id}",
	endpoint=controller.delete_organization_sector_handler,
	methods=["DELETE"],
	response_model=BaseResponse[bool],
	status_code=status.HTTP_200_OK,
)

# LOCATION ACTIVITIES
organization_router_v1.add_api_route(
	path="/{organization_id}/location_activities",
	endpoint=controller.add_location_activities_handler,
	methods=["POST"],
	response_model=BaseResponse[LocationActivityDto],
	status_code=status.HTTP_201_CREATED,
)
organization_router_v1.add_api_route(
	path="/{organization_id}/location_activities",
	endpoint=controller.get_location_activities_handler,
	methods=["GET"],
	response_model=BaseResponse[List[LocationActivityDto]],
	status_code=status.HTTP_200_OK,
)
organization_router_v1.add_api_route(
	path="/{organization_id}/location_activities/{activity_id}",
	endpoint=controller.edit_location_activity_handler,
	methods=["PUT"],
	response_model=BaseResponse[LocationActivityDto],
	status_code=status.HTTP_200_OK,
)
organization_router_v1.add_api_route(
	path="/{organization_id}/location_activities/{activity_id}",
	endpoint=controller.delete_location_activity_handler,
	methods=["DELETE"],
	response_model=BaseResponse[bool],
	status_code=status.HTTP_200_OK,
)

# TARGET GROUPS
organization_router_v1.add_api_route(
	path="/{organization_id}/target_groups",
	endpoint=controller.add_target_groups_handler,
	methods=["POST"],
	response_model=BaseResponse[TargetGroupDto],
	status_code=status.HTTP_201_CREATED,
)
organization_router_v1.add_api_route(
	path="/{organization_id}/target_groups",
	endpoint=controller.get_target_groups_handler,
	methods=["GET"],
	response_model=BaseResponse[List[TargetGroupDto]],
	status_code=status.HTTP_200_OK,
)
organization_router_v1.add_api_route(
	path="/{organization_id}/target_groups/{group_id}",
	endpoint=controller.edit_target_group_handler,
	methods=["PUT"],
	response_model=BaseResponse[TargetGroupDto],
	status_code=status.HTTP_200_OK,
)
organization_router_v1.add_api_route(
	path="/{organization_id}/target_groups/{group_id}",
	endpoint=controller.delete_target_group_handler,
	methods=["DELETE"],
	response_model=BaseResponse[bool],
	status_code=status.HTTP_200_OK,
)

# ORGANIZATION PROJECTS
organization_router_v1.add_api_route(
	path="/{organization_id}/projects",
	endpoint=controller.add_organization_projects_handler,
	methods=["POST"],
	response_model=BaseResponse[OrganizationProjectDto],
	status_code=status.HTTP_201_CREATED,
)
organization_router_v1.add_api_route(
	path="/{organization_id}/projects",
	endpoint=controller.get_organization_projects_handler,
	methods=["GET"],
	response_model=BaseResponse[List[OrganizationProjectDto]],
	status_code=status.HTTP_200_OK,
)
organization_router_v1.add_api_route(
	path="/{organization_id}/projects/{project_id}",
	endpoint=controller.edit_organization_project_handler,
	methods=["PUT"],
	response_model=BaseResponse[OrganizationProjectDto],
	status_code=status.HTTP_200_OK,
)
organization_router_v1.add_api_route(
	path="/{organization_id}/projects/{project_id}",
	endpoint=controller.delete_organization_project_handler,
	methods=["DELETE"],
	response_model=BaseResponse[bool],
	status_code=status.HTTP_200_OK,
)

# ORGANIZATION DONORS
organization_router_v1.add_api_route(
	path="/{organization_id}/donors",
	endpoint=controller.add_organization_donor_handler,
	methods=["POST"],
	response_model=BaseResponse[OrganizationDonorDto],
	status_code=status.HTTP_201_CREATED,
)
organization_router_v1.add_api_route(
	path="/{organization_id}/donors",
	endpoint=controller.get_organization_donors_handler,
	methods=["GET"],
	response_model=BaseResponse[List[OrganizationDonorDto]],
	status_code=status.HTTP_200_OK,
)
organization_router_v1.add_api_route(
	path="/{organization_id}/donors/{donor_id}",
	endpoint=controller.edit_organization_donor_handler,
	methods=["PUT"],
	response_model=BaseResponse[OrganizationDonorDto],
	status_code=status.HTTP_200_OK,
)
organization_router_v1.add_api_route(
	path="/{organization_id}/donors/{donor_id}",
	endpoint=controller.delete_organization_donor_handler,
	methods=["DELETE"],
	response_model=BaseResponse[bool],
	status_code=status.HTTP_200_OK,
)

# FUNDING SOURCES
organization_router_v1.add_api_route(
	path="/{organization_id}/funding_sources",
	endpoint=controller.add_funding_sources_handler,
	methods=["POST"],
	response_model=BaseResponse[FundingSourceDto],
	status_code=status.HTTP_201_CREATED,
)
organization_router_v1.add_api_route(
	path="/{organization_id}/funding_sources",
	endpoint=controller.get_funding_sources_handler,
	methods=["GET"],
	response_model=BaseResponse[List[FundingSourceDto]],
	status_code=status.HTTP_200_OK,
)
organization_router_v1.add_api_route(
	path="/{organization_id}/funding_sources/{source_id}",
	endpoint=controller.edit_funding_source_handler,
	methods=["PUT"],
	response_model=BaseResponse[FundingSourceDto],
	status_code=status.HTTP_200_OK,
)
organization_router_v1.add_api_route(
	path="/{organization_id}/funding_sources/{funding_source_id}",
	endpoint=controller.delete_funding_source_handler,
	methods=["DELETE"],
	response_model=BaseResponse[bool],
	status_code=status.HTTP_200_OK,
)

# ORGANIZATION AUDITORS
organization_router_v1.add_api_route(
	path="/{organization_id}/auditors",
	endpoint=controller.add_organization_auditor_handler,
	methods=["POST"],
	response_model=BaseResponse[OrganizationAuditorDto],
	status_code=status.HTTP_201_CREATED,
)
organization_router_v1.add_api_route(
	path="/{organization_id}/auditors",
	endpoint=controller.get_organization_auditors_handler,
	methods=["GET"],
	response_model=BaseResponse[List[OrganizationAuditorDto]],
	status_code=status.HTTP_200_OK,
)
organization_router_v1.add_api_route(
	path="/{organization_id}/auditors/{auditor_id}",
	endpoint=controller.edit_organization_auditor_handler,
	methods=["PUT"],
	response_model=BaseResponse[OrganizationAuditorDto],
	status_code=status.HTTP_200_OK,
)
organization_router_v1.add_api_route(
	path="/{organization_id}/auditors/{auditor_id}",
	endpoint=controller.delete_organization_auditor_handler,
	methods=["DELETE"],
	response_model=BaseResponse[bool],
	status_code=status.HTTP_200_OK,
)

# BANK DETAILS
organization_router_v1.add_api_route(
	path="/{organization_id}/bank_details",
	endpoint=controller.add_bank_details_handler,
	methods=["POST"],
	response_model=BaseResponse[BankDetailsDto],
	status_code=status.HTTP_201_CREATED,
)
organization_router_v1.add_api_route(
	path="/{organization_id}/bank_details",
	endpoint=controller.get_bank_details_handler,
	methods=["GET"],
	response_model=BaseResponse[List[BankDetailsDto]],
	status_code=status.HTTP_200_OK,
)
organization_router_v1.add_api_route(
	path="/{organization_id}/bank_details/{detail_id}",
	endpoint=controller.edit_bank_detail_handler,
	methods=["PUT"],
	response_model=BaseResponse[BankDetailsDto],
	status_code=status.HTTP_200_OK,
)
organization_router_v1.add_api_route(
	path="/{organization_id}/bank_details/{bank_detail_id}",
	endpoint=controller.delete_bank_details_handler,
	methods=["DELETE"],
	response_model=BaseResponse[bool],
	status_code=status.HTTP_200_OK,
)

# ORGANIZATION STAFF
organization_router_v1.add_api_route(
	path="/{organization_id}/staff",
	endpoint=controller.add_organization_staff_handler,
	methods=["POST"],
	response_model=BaseResponse[OrganizationStaffDto],
	status_code=status.HTTP_201_CREATED,
)
organization_router_v1.add_api_route(
	path="/{organization_id}/staff",
	endpoint=controller.get_organization_staff_handler,
	methods=["GET"],
	response_model=BaseResponse[List[OrganizationStaffDto]],
	status_code=status.HTTP_200_OK,
)
organization_router_v1.add_api_route(
	path="/{organization_id}/staff/{staff_id}",
	endpoint=controller.edit_organization_staff_handler,
	methods=["PUT"],
	response_model=BaseResponse[OrganizationStaffDto],
	status_code=status.HTTP_200_OK,
)
organization_router_v1.add_api_route(
	path="/{organization_id}/staff/{staff_id}",
	endpoint=controller.delete_organization_staff_handler,
	methods=["DELETE"],
	response_model=BaseResponse[bool],
	status_code=status.HTTP_200_OK,
)

# PUBLIC ORGANIZATION VERIFICATION
organization_router_v1.add_api_route(
	path="/verify/{registration_number}",
	endpoint=controller.verify_organization_handler,
	methods=["GET"],
	response_model=BaseResponse[OrganizationVerificationDto],
	status_code=status.HTTP_200_OK,
)
