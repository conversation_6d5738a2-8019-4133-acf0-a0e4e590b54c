import json
from typing import List, Optional

from fastapi import Depends, File, Form, UploadFile
from pydantic import UUID4
from starlette.requests import Request

from src.core.dtos.organization_dtos import (
	BankDetailsDto,
	DirectorDto,
	FundingSourceDto,
	LocationActivityDto,
	OrganizationAuditorDto,
	OrganizationDonorDto,
	OrganizationDto,
	OrganizationProjectDto,
	OrganizationSectorDto,
	OrganizationStaffDto,
	OrganizationVerificationDto,
	TargetGroupDto,
)
from src.core.exceptions.api import ApiException
from src.core.guards.auth_guard import auth_guard
from src.core.http.responses import bad_request
from src.core.shared_schema import BaseResponse, Pagination

from .organization_schema import (
	BankDetailsRequest,
	DirectorRequest,
	FundingSourceRequest,
	LocationActivityRequest,
	OrganizationAuditorRequest,
	OrganizationDonorRequest,
	OrganizationFilter,
	OrganizationProjectRequest,
	OrganizationRequest,
	OrganizationStaffRequest,
	TargetGroupRequest,
)
from .organization_service import OrganizationService


def get_organization_service() -> OrganizationService:
	"""Get OrganizationService instance"""
	return OrganizationService()


"""
=========================================================================================
	ADD ORGANIZATION HANDLERS
=========================================================================================
"""


@auth_guard.authenticated
async def create_organization_handler(
	_: Request,
	# Basic organization fields
	name: str = Form(..., description="Official registered name of the organization"),
	abbreviation: str = Form(..., description="Commonly used abbreviation for the organization"),
	organization_type_id: str = Form(..., description="UUID representing the type of organization"),
	district_id: str = Form(..., description="UUID of the primary district"),
	financial_start_month: str = Form(..., description="Financial year start month"),
	financial_end_month: str = Form(..., description="Financial year end month"),
	registration_type_id: str = Form(..., description="UUID of the registration type"),
	charity_number: Optional[str] = Form(None, description="Official charity registration number"),
	annual_income: float = Form(default=0.0, description="Total annual income"),
	biography: Optional[str] = Form(None, description="Brief biography of the organization"),
	vision: Optional[str] = Form(None, description="Organization's vision statement"),
	motto: Optional[str] = Form(None, description="Organization's motto"),
	# Complex nested data as JSON strings
	objectives: str = Form(..., description="JSON array of objectives"),
	directors: str = Form(..., description="JSON array of director details"),
	sectors: str = Form(..., description="JSON array of sector details"),
	location_activities: str = Form(..., description="JSON array of location activities"),
	target_groups: str = Form(..., description="JSON array of target groups"),
	funding_sources: str = Form(..., description="JSON array of funding sources"),
	auditors: str = Form(..., description="JSON array of auditor details"),
	bank_details: str = Form(..., description="JSON array of bank details"),
	contacts: str = Form(..., description="JSON array of contact details"),
	staff: str = Form(..., description="JSON array of staff details"),
	donors: str = Form(..., description="JSON array of donor details"),
	projects: str = Form(..., description="JSON array of project details"),
	# File uploads
	supporting_documents: List[UploadFile] = File(..., description="List of document files"),
	document_types: str = Form(..., description="JSON array of document type UUIDs"),
) -> BaseResponse[OrganizationDto]:
	try:
		parsed_data = {
			"name": name,
			"abbreviation": abbreviation,
			"organization_type_id": organization_type_id,
			"district_id": district_id,
			"financial_start_month": financial_start_month,
			"financial_end_month": financial_end_month,
			"registration_type_id": registration_type_id,
			"charity_number": charity_number,
			"annual_income": annual_income,
			"biography": biography,
			"vision": vision,
			"motto": motto,
			"objectives": "",
		}

		try:
			parsed_data["objectives"] = json.loads(objectives) if objectives else []
			parsed_data["directors"] = json.loads(directors) if directors else []
			parsed_data["sectors"] = json.loads(sectors) if sectors else []
			parsed_data["location_activities"] = json.loads(location_activities) if location_activities else []
			parsed_data["target_groups"] = json.loads(target_groups) if target_groups else []
			parsed_data["funding_sources"] = json.loads(funding_sources) if funding_sources else []
			parsed_data["auditors"] = json.loads(auditors) if auditors else []
			parsed_data["bank_details"] = json.loads(bank_details) if bank_details else []
			parsed_data["contacts"] = json.loads(contacts) if contacts else []
			parsed_data["staff"] = json.loads(staff) if staff else []
			parsed_data["donors"] = json.loads(donors) if donors else []
			parsed_data["projects"] = json.loads(projects) if projects else []
			parsed_data["document_types"] = json.loads(document_types) if document_types else []
		except json.JSONDecodeError as e:
			raise e

		body = OrganizationRequest(**parsed_data)
		organization_service = get_organization_service()
		organization = await organization_service.create_organization(body, supporting_documents)

		return BaseResponse[OrganizationDto](data=organization)
	except ApiException as e:
		raise bad_request(e.message)


#
@auth_guard.authenticated
async def add_organization_directors_handler(
	_: Request, organization_id: str, data: List[DirectorRequest]
) -> BaseResponse[List[DirectorDto]]:
	organization_service = get_organization_service()
	directors = await organization_service.add_directors(organization_id, data)
	return BaseResponse[List[DirectorDto]](data=directors)


@auth_guard.authenticated
async def add_organization_sectors_handler(_: Request, organization_id: str, data: List[UUID4]):
	organization_service = get_organization_service()
	sectors = await organization_service.add_organization_sectors(organization_id, data)
	return BaseResponse[List[OrganizationSectorDto]](data=sectors)


@auth_guard.authenticated
async def add_location_activities_handler(_: Request, organization_id: str, data: List[LocationActivityRequest]):
	organization_service = get_organization_service()
	activities = await organization_service.add_location_activities(organization_id, data)
	return BaseResponse[List[LocationActivityDto]](data=activities)


@auth_guard.authenticated
async def add_target_groups_handler(_: Request, organization_id: str, data: List[TargetGroupRequest]):
	organization_service = get_organization_service()
	target_groups = await organization_service.add_target_groups(organization_id, data)
	return BaseResponse[List[TargetGroupDto]](data=target_groups)


@auth_guard.authenticated
async def add_organization_staff_handler(_: Request, organization_id: str, data: List[OrganizationStaffRequest]):
	organization_service = get_organization_service()
	staff = await organization_service.add_organization_staff(organization_id, data)
	return BaseResponse[List[OrganizationStaffDto]](data=staff)


@auth_guard.authenticated
async def add_organization_projects_handler(_: Request, organization_id: str, data: List[OrganizationProjectRequest]):
	try:
		organization_service = get_organization_service()
		return await organization_service.add_organization_projects(organization_id, data)
	except ApiException as e:
		raise bad_request(e.message)


@auth_guard.authenticated
async def add_organization_donor_handler(_: Request, organization_id: str, data: List[OrganizationDonorRequest]):
	organization_service = get_organization_service()
	donors = await organization_service.add_organization_donor(organization_id, data)
	return BaseResponse[List[OrganizationDonorDto]](data=donors)


@auth_guard.authenticated
async def add_organization_auditor_handler(_: Request, organization_id: str, data: List[OrganizationAuditorRequest]):
	organization_service = get_organization_service()
	auditors = await organization_service.add_organization_auditor(organization_id, data)
	return BaseResponse[List[OrganizationAuditorDto]](data=auditors)


@auth_guard.authenticated
async def add_funding_sources_handler(_: Request, organization_id: str, data: List[FundingSourceRequest]):
	organization_service = get_organization_service()
	funding_sources = await organization_service.add_funding_sources(organization_id, data)
	return BaseResponse[List[FundingSourceDto]](data=funding_sources)


@auth_guard.authenticated
async def add_bank_details_handler(_: Request, organization_id: str, data: List[BankDetailsRequest]):
	organization_service = get_organization_service()
	bank_details = await organization_service.add_bank_details(organization_id, data)
	return BaseResponse[List[BankDetailsDto]](data=bank_details)


async def fetch_organizations_handler(
	filter: OrganizationFilter = Depends(OrganizationFilter),
) -> Pagination[OrganizationDto]:
	organization_service = get_organization_service()
	organizations = await organization_service.retrieve_organizations(filter)
	return organizations


async def fetch_single_organization_handler(
	organization_id: UUID4,
) -> BaseResponse[OrganizationDto]:
	organization_service = get_organization_service()
	organization = await organization_service.get_organization_by_id(organization_id)
	return BaseResponse[OrganizationDto](data=organization)


"""
=========================================================================================
	GET ORGANIZATION HANDLERS
=========================================================================================
"""


async def get_organization_directors_handler(organization_id: str):
	organization_service = get_organization_service()
	directors = await organization_service.get_directors(organization_id)
	return BaseResponse[List[DirectorDto]](data=directors)


async def get_organization_sectors_handler(organization_id: str):
	organization_service = get_organization_service()
	sectors = await organization_service.get_organization_sectors(organization_id)
	return BaseResponse[List[OrganizationSectorDto]](data=sectors)


async def get_location_activities_handler(organization_id: str):
	organization_service = get_organization_service()
	activities = await organization_service.get_location_activities(organization_id)
	return BaseResponse[List[LocationActivityDto]](data=activities)


async def get_target_groups_handler(organization_id: str):
	organization_service = get_organization_service()
	groups = await organization_service.get_target_groups(organization_id)
	return BaseResponse[List[TargetGroupDto]](data=groups)


async def get_organization_staff_handler(organization_id: str):
	organization_service = get_organization_service()
	staff = await organization_service.get_organization_staff(organization_id)
	return BaseResponse[List[OrganizationStaffDto]](data=staff)


async def get_organization_projects_handler(organization_id: str):
	organization_service = get_organization_service()
	projects = await organization_service.get_organization_projects(organization_id)
	return BaseResponse[List[OrganizationProjectDto]](data=projects)


async def get_organization_donors_handler(organization_id: str):
	organization_service = get_organization_service()
	donors = await organization_service.get_organization_donors(organization_id)
	return BaseResponse[List[OrganizationDonorDto]](data=donors)


async def get_organization_auditors_handler(organization_id: str):
	organization_service = get_organization_service()
	auditors = await organization_service.get_organization_auditors(organization_id)
	return BaseResponse[List[OrganizationAuditorDto]](data=auditors)


async def get_funding_sources_handler(organization_id: str):
	organization_service = get_organization_service()
	sources = await organization_service.get_funding_sources(organization_id)
	return BaseResponse[List[FundingSourceDto]](data=sources)


async def get_bank_details_handler(organization_id: str):
	organization_service = get_organization_service()
	details = await organization_service.get_bank_details(organization_id)
	return BaseResponse[List[BankDetailsDto]](data=details)


"""
=========================================================================================
	EDIT ORGANIZATION HANDLERS
=========================================================================================
"""


@auth_guard.authenticated
async def edit_organization_director_handler(
	_: Request,
	organization_id: str,
	director_id: str,
	payload: DirectorRequest,
):
	organization_service = get_organization_service()
	director = await organization_service.update_director(organization_id, director_id, payload)
	return BaseResponse[DirectorDto](data=director)


@auth_guard.authenticated
async def edit_organization_sector_handler(_: Request, organization_id: str, sector_id: str, payload: UUID4):
	organization_service = get_organization_service()
	sector = await organization_service.update_organization_sector(sector_id, payload)
	return BaseResponse[DirectorDto](data=sector)


@auth_guard.authenticated
async def edit_location_activity_handler(
	_: Request,
	organization_id: str,
	activity_id: str,
	payload: LocationActivityRequest,
):
	organization_service = get_organization_service()
	location_activity = await organization_service.update_location_activity(activity_id, payload)
	return BaseResponse[LocationActivityDto](data=location_activity)


@auth_guard.authenticated
async def edit_target_group_handler(
	_: Request,
	organization_id: str,
	group_id: str,
	payload: TargetGroupRequest,
):
	organization_service = get_organization_service()
	target_group = await organization_service.update_target_group(group_id, payload)
	return BaseResponse[TargetGroupDto](data=target_group)


@auth_guard.authenticated
async def edit_organization_staff_handler(
	_: Request,
	organization_id: str,
	staff_id: str,
	payload: OrganizationStaffRequest,
):
	organization_service = get_organization_service()
	staff = await organization_service.update_organization_staff(staff_id, payload)
	return BaseResponse[OrganizationStaffDto](data=staff)


@auth_guard.authenticated
async def edit_organization_project_handler(
	_: Request,
	organization_id: str,
	project_id: str,
	payload: OrganizationProjectRequest,
):
	organization_service = get_organization_service()
	project = await organization_service.update_organization_project(project_id, payload)
	return BaseResponse[OrganizationProjectDto](data=project)


@auth_guard.authenticated
async def edit_organization_donor_handler(
	_: Request,
	organization_id: str,
	donor_id: str,
	payload: OrganizationDonorRequest,
):
	organization_service = get_organization_service()
	donor = await organization_service.update_organization_donor(donor_id, payload)
	return BaseResponse[OrganizationDonorDto](data=donor)


@auth_guard.authenticated
async def edit_organization_auditor_handler(
	_: Request,
	organization_id: str,
	auditor_id: str,
	payload: OrganizationAuditorRequest,
):
	organization_service = get_organization_service()
	auditor = await organization_service.update_organization_auditor(auditor_id, payload)
	return BaseResponse[OrganizationAuditorDto](data=auditor)


@auth_guard.authenticated
async def edit_funding_source_handler(
	_: Request,
	organization_id: str,
	source_id: str,
	payload: FundingSourceRequest,
):
	organization_service = get_organization_service()
	source = await organization_service.update_funding_source(source_id, payload)
	return BaseResponse[FundingSourceDto](data=source)


@auth_guard.authenticated
async def edit_bank_detail_handler(
	_: Request,
	organization_id: str,
	detail_id: str,
	payload: BankDetailsRequest,
):
	organization_service = get_organization_service()
	detail = await organization_service.update_bank_detail(detail_id, payload)
	return BaseResponse[BankDetailsDto](data=detail)


"""
=========================================================================================
	DELETE ORGANIZATION ENDPOINTS
=========================================================================================
"""


@auth_guard.authenticated
async def delete_organization_director_handler(_: Request, organization_id: str, director_id: str):
	organization_service = get_organization_service()
	deleted = await organization_service.delete_director(director_id)
	return BaseResponse[bool](data=deleted)


@auth_guard.authenticated
async def delete_bank_details_handler(_: Request, organization_id: str, bank_detail_id: str):
	organization_service = get_organization_service()
	deleted = await organization_service.delete_bank_detail(bank_detail_id)
	return BaseResponse[bool](data=deleted)


@auth_guard.authenticated
async def delete_funding_source_handler(_: Request, organization_id: str, funding_source_id: str):
	organization_service = get_organization_service()
	deleted = await organization_service.delete_funding_source(funding_source_id)
	return BaseResponse[bool](data=deleted)


@auth_guard.authenticated
async def delete_organization_sector_handler(_: Request, organization_id: str, sector_id: str):
	organization_service = get_organization_service()
	deleted = await organization_service.delete_organization_sector(sector_id)
	return BaseResponse[bool](data=deleted)


@auth_guard.authenticated
async def delete_location_activity_handler(_: Request, organization_id: str, activity_id: str):
	organization_service = get_organization_service()
	deleted = await organization_service.delete_location_activity(activity_id)
	return BaseResponse[bool](data=deleted)


@auth_guard.authenticated
async def delete_target_group_handler(_: Request, organization_id: str, group_id: str):
	organization_service = get_organization_service()
	deleted = await organization_service.delete_target_group(group_id)
	return BaseResponse[bool](data=deleted)


@auth_guard.authenticated
async def delete_organization_donor_handler(_: Request, organization_id: str, donor_id: str):
	organization_service = get_organization_service()
	deleted = await organization_service.delete_organization_donor(donor_id)
	return BaseResponse[bool](data=deleted)


@auth_guard.authenticated
async def delete_organization_auditor_handler(_: Request, organization_id: str, auditor_id: str):
	organization_service = get_organization_service()
	deleted = await organization_service.delete_organization_auditor(auditor_id)
	return BaseResponse[bool](data=deleted)


@auth_guard.authenticated
async def delete_organization_project_handler(_: Request, organization_id: str, project_id: str):
	organization_service = get_organization_service()
	deleted = await organization_service.delete_organization_project(project_id)
	return BaseResponse[bool](data=deleted)


@auth_guard.authenticated
async def delete_organization_staff_handler(_: Request, organization_id: str, staff_id: str):
	organization_service = get_organization_service()
	deleted = await organization_service.delete_organization_staff(staff_id)
	return BaseResponse[bool](data=deleted)


"""
=========================================================================================
	ORGANIZATION VERIFICATION HANDLERS (PUBLIC)
=========================================================================================
"""


async def verify_organization_handler(registration_number: str) -> BaseResponse[OrganizationVerificationDto]:
	"""Public organization verification endpoint - anyone can verify organization by registration number"""
	organization_service = get_organization_service()
	verification = await organization_service.verify_organization(registration_number)
	return BaseResponse[OrganizationVerificationDto](data=verification)
