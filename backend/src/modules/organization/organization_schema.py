from typing import Dict, List, Optional, Union

from fastapi import UploadFile
from pydantic import BaseModel, EmailStr, Field, field_validator
from pydantic.types import UUID4

from src.config.db.models.base import Gender
from src.config.db.models.contact import ContactType
from src.core.shared_schema import BaseRequest


class OrganizationProjectRequest(BaseModel):
	id: Optional[UUID4] = Field(None, description="Id")
	name: str = Field(..., description="Name of the project")
	thematic_area_id: UUID4 = Field(..., description="Thematic area")
	number_of_beneficiaries: int = Field(default=0, description="Number of the beneficiaries")
	is_active: bool = Field(..., description="Status of the project")


class OrganizationDonorRequest(BaseModel):
	id: Optional[UUID4] = Field(None, description="Id")
	donor_id: UUID4 = Field(..., description="ID for the donor")
	is_active: bool = Field(..., description="Status of the donor")
	amount: float = Field(..., description="Total amount that donor donates")
	currency_id: Optional[UUID4] = Field(..., description="In what currency")


class OrganizationStaffRequest(BaseModel):
	id: Optional[UUID4] = Field(None, description="Id")
	staff_type_id: UUID4 = Field(..., description="Type of the staff")
	is_active: bool = Field(..., description="Whether the staff is active")
	total_male: int = Field(..., description="Total number of males in the organization")
	total_female: int = Field(..., description="Total number of females in the organization")


class OrganizationContact(BaseModel):
	id: Optional[UUID4] = Field(None, description="Id")
	type: ContactType = Field(..., description="Type of the contact")
	details: List[Dict] = Field(..., description="Contact details")


class SupportingDocumentRequest:
	def __init__(self):
		self.document_types: List[UUID4] = []
		self.documents: List[UploadFile] = []


class BankDetailsRequest(BaseModel):
	id: Optional[UUID4] = Field(None, description="Id")
	account_number: str = Field(..., min_length=5, max_length=50, description="Bank account number.")
	branch_name: str = Field(..., min_length=1, max_length=100, description="Name of the bank branch.")
	bank_id: UUID4 = Field(..., description="UUID of the bank.")


class OrganizationAuditorRequest(BaseModel):
	id: Optional[UUID4] = Field(None, description="Id")
	name: str = Field(..., min_length=1, max_length=255, description="Full name of the organization's auditor.")
	email: EmailStr = Field(..., description="Email address of the auditor.")
	phone: str = Field(..., min_length=1, max_length=20, description="Phone number of the auditor.")
	address: str = Field(..., min_length=1, max_length=255, description="Physical address of the auditor.")
	is_active: bool = Field(..., description="Boolean indicating if the auditor is currently active.")


class FundingSourceRequest(BaseModel):
	id: Optional[UUID4] = Field(None, description="Id")
	donor_id: UUID4 = Field(..., description="UUID of the donor or funding entity.")
	currency_id: UUID4 = Field(..., description="UUID of the currency in which the amount is denominated.")
	contact_person: str = Field(
		..., min_length=1, max_length=255, description="Name of the contact person at the funding source."
	)
	amount: float = Field(..., gt=0, description="Amount of funding received. Must be greater than 0.")


class TargetGroupRequest(BaseModel):
	id: Optional[UUID4] = Field(None, description="Id")
	type_id: UUID4 = Field(..., description="UUID representing the type of target group (e.g., youth, women, elderly).")
	is_active: bool = Field(..., description="Boolean indicating if this target group definition is active.")


class LocationActivityRequest(BaseModel):
	id: Optional[UUID4] = Field(None, description="Id")
	vdc_id: Optional[Union[UUID4, str]] = Field(
		default=None, description="UUID of the Village Development Committee (VDC) if applicable."
	)
	adc_id: Optional[Union[UUID4, str]] = Field(
		default=None, description="UUID of the Area Development Committee (ADC) if applicable."
	)
	district_id: UUID4 = Field(..., description="UUID of the district where activities are conducted.")

	@field_validator("vdc_id", "adc_id")
	def allow_empty_or_uuid(cls, v):
		if v in (None, ""):
			return None
		try:
			return UUID4(str(v))
		except ValueError:
			raise ValueError("Must be a valid UUID or empty string/null")


class DirectorRequest(BaseModel):
	fullname: str = Field(..., min_length=1, max_length=255, description="Full name of the director.")
	email: EmailStr = Field(..., description="Email address of the director.")
	phone: str = Field(..., min_length=1, max_length=20, description="Phone number of the director.")
	avatar: Optional[str] = Field(None, description="URL or path to the director's avatar image (optional).")
	national_identifier: Optional[str] = Field(
		None, min_length=10, max_length=15, description="National identification number of the director (optional)."
	)
	passport_number: Optional[str] = Field(
		None, min_length=6, max_length=20, description="Passport number of the director (optional)."
	)
	gender: Gender = Field(..., description="Gender of the director.")
	position: str = Field(
		..., min_length=1, max_length=100, description="Position held by the director within the organization."
	)
	country_id: UUID4 = Field(..., description="UUID of the country where the director resides.")
	occupation: str = Field(..., min_length=1, max_length=100, description="Occupation of the director.")
	timeframe: str = Field(
		...,
		min_length=1,
		max_length=50,
		description="Timeframe related to the director's role or status (e.g., 'current', '2020-2025').",
	)
	qualification_id: UUID4 = Field(..., description="UUID of the director's highest qualification.")

	class Config:
		use_enum_values = True


class OrganizationRequest(BaseModel):
	"""Schema for creating/updating an organization."""

	name: str = Field(..., min_length=1, max_length=255, description="Official registered name of the organization.")
	abbreviation: str = Field(
		..., min_length=1, max_length=20, description="Commonly used abbreviation for the organization."
	)
	organization_type_id: UUID4 = Field(
		..., description="UUID representing the type of organization (e.g., NGO, CBO, trust)."
	)
	district_id: UUID4 = Field(
		..., description="UUID of the primary district where the organization is based or registered."
	)
	financial_start_month: str = Field(
		..., description="Month when the organization's financial year starts (e.g., 'JAN', 'APR')."
	)
	financial_end_month: str = Field(
		..., description="Month when the organization's financial year ends (e.g., 'DEC', 'MAR')."
	)
	charity_number: Optional[str] = Field(
		None, max_length=50, description="Official charity registration number, if applicable."
	)
	annual_income: float = Field(
		default=0.0, ge=0, description="Total annual income of the organization. Must be a non-negative number."
	)
	registration_type_id: UUID4 = Field(
		..., description="UUID of the type of legal registration the organization holds."
	)
	biography: Optional[str] = Field(None, description="A brief biography or overview of the organization.")
	vision: Optional[str] = Field(None, description="The organization's aspirational vision statement.")
	motto: Optional[str] = Field(None, description="The organization's guiding motto or slogan.")
	objectives: Optional[List[str]] = Field([], description="Detailed objectives of the organization.")
	directors: List[DirectorRequest] = Field(
		..., description="List of director details associated with the organization."
	)
	sectors: List[UUID4] = Field(..., description="List of sectors in which the organization operates.")
	location_activities: List[LocationActivityRequest] = Field(
		..., description="List of locations where the organization conducts activities."
	)
	target_groups: List[TargetGroupRequest] = Field(
		..., description="List of specific target demographic groups the organization serves."
	)
	funding_sources: List[FundingSourceRequest] = Field(..., description="Funding sources for the organization.")
	auditors: List[OrganizationAuditorRequest] = Field(..., description="List of auditor details for the organization.")
	bank_details: List[BankDetailsRequest] = Field(..., description="Bank account details for the organization.")
	contacts: List[OrganizationContact] = Field(..., description="List of organization contact details")
	staff: List[OrganizationStaffRequest] = Field(..., description="List of organization staff")
	donors: List[OrganizationDonorRequest] = Field(..., description="List of organization donors")
	projects: List[OrganizationProjectRequest] = Field(..., description="List of organization projects")
	document_types: List[UUID4] = Field(
		..., description="List of UUIDs representing the types of documents being uploaded."
	)

	@field_validator("financial_start_month", "financial_end_month")
	def validate_month_format(cls, v):
		"""Validate month format."""
		valid_months = ["JAN", "FEB", "MAR", "APR", "MAY", "JUN", "JUL", "AUG", "SEP", "OCT", "NOV", "DEC"]
		if v.upper() not in valid_months:
			raise ValueError(f"Month must be one of: {', '.join(valid_months)}")
		return v.upper()

	@field_validator("financial_end_month")
	def validate_financial_year_length(cls, v, info):
		"""Validate that the financial year spans exactly 12 months."""
		start_month = info.data.get("financial_start_month")
		end_month = v

		if start_month and end_month:
			month_order = ["JAN", "FEB", "MAR", "APR", "MAY", "JUN", "JUL", "AUG", "SEP", "OCT", "NOV", "DEC"]

			try:
				start_idx = month_order.index(start_month.upper())
				end_idx = month_order.index(end_month.upper())

				if end_idx >= start_idx:
					months_span = end_idx - start_idx + 1
				else:
					months_span = (12 - start_idx) + (end_idx + 1)

				if months_span != 12:
					raise ValueError(
						f"Financial year must span exactly 12 months. "
						f"Current span from {start_month} to {end_month} is {months_span} months."
					)

			except ValueError as e:
				if "not in list" in str(e):
					raise ValueError("Invalid month format in financial year validation")
				raise e
		return v

	@field_validator("name", "abbreviation")
	def validate_non_empty_strings(cls, v):
		"""Validate that string fields are not empty or just whitespace."""
		if not v or not v.strip():
			raise ValueError("Field cannot be empty or contain only whitespace")
		return v.strip()

	class Config:
		"""Pydantic configuration."""

		json_schema_extra = {
			"example": {
				"name": "Example Organization",
				"abbreviation": "EO",
				"organization_type_id": "123e4567-e89b-12d3-a456-426614174000",
				"district_id": "123e4567-e89b-12d3-a456-************",
				"financial_start_month": "APR",
				"financial_end_month": "MAR",
				"charity_number": "CHR123456789",
				"annual_income": 100000.0,
				"registration_type_id": "123e4567-e89b-12d3-a456-************",
				"biography": "Organization dedicated to community development",
				"vision": "A world where communities thrive",
				"motto": "Together we grow",
				"objectives": "Primary objective is community empowerment.",
				"directors": [],
				"sectors": [],
				"location_activities": [],
				"target_groups": [],
				"funding_sources": [],
				"auditor": [],
				"bank_details": [],
				"supporting_documents": [],
			}
		}


class OrganizationFilter(BaseRequest):
	"""Schema for filtering organizations."""

	name: Optional[str] = Field(None, description="Filter by organization name (partial match, case-insensitive).")
	abbreviation: Optional[str] = Field(None, description="Filter by abbreviation (partial match, case-insensitive).")
	organization_type_id: Optional[UUID4] = Field(None, description="Filter by organization type ID.")
	registration_number: Optional[str] = Field(None, description="Filter by registration number (partial match).")
	district_id: Optional[UUID4] = Field(None, description="Filter by district ID.")
	registration_type_id: Optional[UUID4] = Field(None, description="Filter by registration type ID.")
	status: Optional[str] = Field(None, description="Filter by organization status (e.g., ACTIVE, PENDING, INACTIVE).")

	class Config:
		"""Pydantic configuration."""

		use_enum_values = True
		json_schema_extra = {
			"example": {
				"name": "Example",
				"abbreviation": "EO",
				"district_id": "123e4567-e89b-12d3-a456-************",
				"status": "ACTIVE",
				"page": 1,
				"size": 10,
				"paginate": True,
			}
		}
