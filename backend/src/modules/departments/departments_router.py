from fastapi import APIRouter, status

from src.core.dtos.user_dtos import DepartmentDto
from src.core.shared_schema import Pagination, BaseResponse
from src.modules.departments.departments_schema import DepartmentResponse
from src.modules.departments import departments_controller as controller


router = APIRouter(tags=["departments"])

router.add_api_route(
	path="",
	endpoint=controller.index,
	name="Get and Search For Departments",
	methods=["GET"],
	status_code=status.HTTP_200_OK,
	response_model=Pagination[DepartmentDto],
)

router.add_api_route(
	path="",
	name="Create Department",
	endpoint=controller.create,
	methods=["POST"],
	response_model=BaseResponse[DepartmentDto],
	status_code=status.HTTP_201_CREATED,
)

router.add_api_route(
	path="/{department_id}",
	name="Get Department by ID",
	endpoint=controller.get_by_id,
	methods=["GET"],
	response_model=DepartmentResponse,
	status_code=status.HTTP_200_OK,
)

router.add_api_route(
	path="/{department_id}",
	name="Update Department",
	endpoint=controller.update,
	methods=["PUT"],
	response_model=BaseResponse[DepartmentDto],
	status_code=status.HTTP_200_OK,
)

router.add_api_route(
	path="/{department_id}",
	name="Delete Department",
	endpoint=controller.delete,
	methods=["DELETE"],
	status_code=status.HTTP_204_NO_CONTENT,
)
