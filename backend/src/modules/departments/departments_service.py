from typing import Any
from uuid import UUID

from fastapi_pagination import Params
from fastapi_pagination.ext.sqlalchemy import paginate
from sqlalchemy import or_
from sqlalchemy.exc import IntegrityError

from src.config.db.models.department import Department
from src.core.base.base_repository import BaseRepository
from src.core.dtos.user_dtos import to_department_dto, DepartmentDto
from src.core.exceptions.api import ApiException
from src.core.shared_schema import Pagination
from src.modules.departments.departments_schema import DepartmentCreate, DepartmentFilters, DepartmentResponse


class DepartmentsService(BaseRepository):
	def __init__(self):
		super().__init__()

	async def find_departments(self, filter: DepartmentFilters):
		"""
		Find departments based on filters.

		Args:
		    filters (DepartmentFilters): Filters to apply.

		Returns:
		    Query: SQLAlchemy query object for departments matching the criteria.
		"""
		try:
			query = self.db.query(Department)

			if filter.name:
				query = query.filter(
					or_(Department.name.ilike(f"%{filter.name}%"), Department.code.ilike(f"%{filter.name}%"))
				)

			result = paginate(query, Params(page=filter.page, size=filter.size))
			data = [to_department_dto(row) for row in result.items]

			return Pagination.from_query_result(data, result)
		except Exception as e:
			self.logger.error(f"Error finding departments: {e}")
			raise ApiException("Failed to retrieve departments")

	async def create_department(self, department_data: DepartmentCreate) -> DepartmentDto:
		"""
		Create a new department.

		Args:
		    department_data (DepartmentCreate): Department data to create.

		Returns:
		    DepartmentResponse: Created department data.
		"""
		try:
			existing_dept = (
				self.db.query(Department)
				.filter((Department.name == department_data.name) | (Department.code == department_data.code))
				.first()
			)

			if existing_dept:
				if existing_dept.name == department_data.name:
					raise ApiException("Department with this name already exists")
				if existing_dept.code == department_data.code:
					raise ApiException("Department with this code already exists")

			department = Department(
				name=department_data.name,
				code=department_data.code,
				description=department_data.description,
				created_by=self.current_user_id,
			)

			self.db.add(department)
			self.db.commit()
			self.db.refresh(department)

			return to_department_dto(department)
		except IntegrityError as e:
			self.db.rollback()
			self.logger.error(f"Integrity error creating department: {e}")
			raise ApiException("Department with this name or code already exists")
		except Exception as e:
			self.db.rollback()
			self.logger.error(f"Error creating department: {e}")
			raise ApiException("Failed to create department")

	def get_department_by_id(self, department_id: UUID) -> DepartmentResponse:
		"""
		Get a department by ID.

		Args:
		    department_id (UUID): Department ID.

		Returns:
		    DepartmentResponse: Department data.
		"""
		try:
			department = self.db.query(Department).filter(Department.id == department_id).first()

			if not department:
				raise ApiException("Department not found")

			return DepartmentResponse.model_validate(department)
		except Exception as e:
			self.logger.error(f"Error getting department: {e}")
			raise ApiException("Failed to retrieve department")

	async def update_department(self, department_id: UUID, updates: Any) -> DepartmentDto:
		"""
		Update a department.

		Args:
		    department_id (UUID): Department ID to update.
		    updates (Any): Updates to apply.

		Returns:
		    DepartmentResponse: Updated department data.
		"""
		try:
			department = self.db.query(Department).filter(Department.id == department_id).first()

			if not department:
				raise ApiException("Department not found")

			update_dict = {k: v for k, v in updates if v is not None}

			if "name" in update_dict or "code" in update_dict:
				conflict_query = self.db.query(Department).filter(Department.id != department_id)

				if "name" in update_dict:
					existing_name = conflict_query.filter(Department.name == update_dict["name"]).first()
					if existing_name:
						raise ApiException("Department with this name already exists")

				if "code" in update_dict:
					existing_code = conflict_query.filter(Department.code == update_dict["code"]).first()
					if existing_code:
						raise ApiException("Department with this code already exists")

			for k, v in update_dict.items():
				setattr(department, k, v)

			department.updated_by = self.current_user_id

			self.db.commit()
			self.db.refresh(department)

			return to_department_dto(department)
		except Exception as e:
			self.db.rollback()
			self.logger.error(f"Error updating department: {e}")
			raise ApiException("Failed to update department")

	def delete_department(self, department_id: UUID, void_reason: str) -> None:
		"""
		Soft delete a department by setting voided=True.

		Args:
		    department_id (UUID): Department ID to delete.
		    voided_reason (str): Reason for voided.
		"""
		try:
			department = self.db.query(Department).filter(Department.id == department_id).first()

			if not department:
				raise ApiException("Department not found")

			if department.users:
				active_users = [ud for ud in department.users if not ud.voided]
				if active_users:
					raise ApiException("Cannot delete department with active users. Please reassign users first.")

			self.db.void(department, void_reason=void_reason)

		except Exception as e:
			self.db.rollback()
			self.logger.error(f"Error deleting department: {e}")
			raise ApiException("Failed to delete department")
