from uuid import UUID
from fastapi import Depends, Request

from src.core.dtos.user_dtos import DepartmentDto
from src.core.guards.auth_guard import auth_guard
from src.core.shared_schema import BaseResponse
from src.modules.departments.departments_schema import DepartmentResponse, DepartmentFilters, DepartmentCreate
from src.modules.departments.departments_service import DepartmentsService

service = DepartmentsService()


@auth_guard.authenticated
async def index(_: Request, filters: DepartmentFilters = Depends(DepartmentFilters)):
	"""
	Get and search for departments with pagination.

	Args:
	    filters (DepartmentFilters): Query filters for departments.

	Returns:
	    Page[DepartmentResponse]: Paginated list of departments.
	"""
	pagination = await service.find_departments(filters)
	return pagination


@auth_guard.authenticated
async def create(_: Request, department: DepartmentCreate) -> BaseResponse[DepartmentDto]:
	"""
	Create a new department.

	Args:
	    department (DepartmentCreate): Department data to create.

	Returns:
	    DepartmentResponse: Created department data.
	"""
	department = await service.create_department(department)
	return BaseResponse(data=department)


@auth_guard.authenticated
def get_by_id(department_id: UUID) -> DepartmentResponse:
	"""
	Get a department by ID.

	Args:
	    department_id (UUID): Department ID.

	Returns:
	    DepartmentResponse: Department data.
	"""
	return service.get_department_by_id(department_id)


@auth_guard.can_("update.department")
async def update(_: Request, department_id: UUID, updates: DepartmentCreate) -> BaseResponse[DepartmentDto]:
	"""
	Update a department.

	Args:
	    department_id (UUID): Department ID to update.
	    updates (DepartmentUpdate): Updates to apply.

	Returns:
	    DepartmentResponse: Updated department data.
	"""
	department = await service.update_department(department_id=department_id, updates=updates)
	return BaseResponse(data=department)


def delete(department_id: UUID, void_reason: str):
	"""
	Delete a department (soft delete).

	Args:
	    department_id (UUID): Department ID to delete.
	"""
	service.delete_department(department_id, void_reason)
