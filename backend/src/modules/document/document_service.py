from datetime import datetime
from typing import List

from pydantic.types import UUID4

from src.config.db.models.application import Application, ApplicationType
from src.config.db.models.application_document import ApplicationDocument
from src.config.db.models.document import Document
from src.config.db.models.loadable_item import LoadableItem
from src.core.base.base_repository import BaseRepository
from src.core.exceptions.api import ApiException
from src.core.logger.internal_logger import get_logger
from src.core.services.storage_service import _get_storage_service_instance
from src.modules.organization.organization_schema import SupportingDocumentRequest


class DocumentService(BaseRepository):
	REQUIRED_DOCUMENT_TYPES = {
		ApplicationType.ORGANIZATION_REGISTRATION.value: {
			"DOCT01",
			"DOCT02",
			"DOCT03",
			"DOCT04",
			"DOCT05",
			"DOCT06",
			"DOCT07",
		},
		ApplicationType.LICENCE_RENEWAL.value: {
			"DOCT01",
			"DOCT09",
			"DOCT10",
		},
	}

	ALLOWED_TYPES = {
		"application/pdf",
		"application/msword",
		"application/vnd.openxmlformats-officedocument.wordprocessingml.document",
		"application/vnd.ms-excel",
		"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
		"application/vnd.ms-powerpoint",
		"application/vnd.openxmlformats-officedocument.presentationml.presentation",
		"image/jpeg",
		"image/png",
		"application/rtf",
		"text/csv",
		"application/vnd.oasis.opendocument.text",
		"application/vnd.oasis.opendocument.spreadsheet",
		"application/vnd.oasis.opendocument.presentation",
	}

	def __init__(self):
		super().__init__()

		self._storage = None
		self.logger = get_logger(__name__)

	@property
	def storage(self):
		"""Lazy-loaded storage service"""
		if self._storage is None:
			self._storage = _get_storage_service_instance()
		return self._storage

	def _generate_custom_filename(self, original_filename: str) -> str:
		"""Generate custom filename with format: PREFIX_year-month-date-24hr-minute-seconds.ext"""
		now = datetime.now()
		timestamp = now.strftime("%Y%m%d%H%M%S")

		file_ext = original_filename.split(".")[-1].lower() if "." in original_filename else "pdf"

		if file_ext == "pdf":
			prefix = "PDF"
		elif file_ext in ["jpg", "jpeg", "png", "gif", "bmp", "webp", "svg"]:
			prefix = "IMG"
		elif file_ext in ["mp4", "avi", "mov", "wmv", "flv", "webm", "mkv"]:
			prefix = "VID"
		else:
			prefix = "DOC"

		return f"{prefix}_{timestamp}.{file_ext}"

	async def add_application_documents(
		self, application_id: UUID4, payload: SupportingDocumentRequest, account_id: UUID4 = None
	) -> List[ApplicationDocument]:
		"""
		Add supporting documents to an application.

		Args:
			application_id: UUID of the application
			payload: Request payload containing document types and files
			account_id: UUID of the account for directory structure (optional)

		Returns:
			List of created ApplicationDocument objects

		Raises:
			ApiException: If validation fails or operation cannot be completed
		"""
		try:
			application = await self._get_application(application_id)
			await self._validate_document_requirements(application, payload)
			created_documents = await self._process_documents(application, payload, account_id)

			return created_documents

		except Exception as e:
			self.db.rollback()
			self.logger.error(f"Failed to add supporting documents for application {application_id}: {str(e)}")
			raise

	async def _get_application(self, application_id: UUID4) -> Application:
		"""Retrieve and validate application exists."""
		application = self.db.query(Application).filter(Application.id == application_id).first()
		if application is None:
			raise ApiException("Could not find organization application")
		return application

	async def _validate_document_requirements(
		self, application: Application, payload: SupportingDocumentRequest
	) -> None:
		"""Validate document requirements based on application type."""
		if application.type not in self.REQUIRED_DOCUMENT_TYPES:
			raise ApiException(f"Application type {application.type} does not require supporting documents")

		if len(payload.document_types) != len(payload.documents):
			raise ApiException("Number of document types must match number of uploaded documents")

		await self._validate_document_types(payload.document_types)

		await self._validate_required_documents(application.type, payload.document_types)

		# await self._check_existing_documents(application.id)

	async def _validate_document_types(self, document_type_ids: List[UUID4]) -> None:
		"""Validate that all provided document type IDs are valid document types."""
		for doc_type_id in document_type_ids:
			loadable_item = self.db.query(LoadableItem).filter(LoadableItem.id == doc_type_id).first()
			if loadable_item is None:
				raise ApiException(f"Document type with ID {doc_type_id} not found")
			if loadable_item.type != "DOCUMENT_TYPE":
				raise ApiException(f"Invalid document type: {loadable_item.type}")

	async def _validate_required_documents(self, application_type: str, document_type_ids: List[UUID4]) -> None:
		"""Validate that all required document types are provided."""
		required_codes = self.REQUIRED_DOCUMENT_TYPES[application_type]

		provided_codes = set()
		for doc_type_id in document_type_ids:
			loadable_item = self.db.query(LoadableItem).filter(LoadableItem.id == doc_type_id).first()
			if loadable_item:
				provided_codes.add(loadable_item.code)

		missing_codes = required_codes - provided_codes

		if missing_codes:
			doc_names = []
			for code in missing_codes.split(", "):
				print("============")
				print(code)
				print("============")
				item = self.db.query(LoadableItem).filter(LoadableItem.code == code).first()
				doc_names.append(item.display_value)

			raise ApiException(f"Missing required document types: {', '.join(doc_names)}")

	async def _check_existing_documents(self, application_id: UUID4) -> None:
		"""Check if documents already exist for this application."""
		existing_docs = (
			self.db.query(ApplicationDocument).filter(ApplicationDocument.application_id == application_id).count()
		)

		if existing_docs > 0:
			raise ApiException("Supporting documents already exist for this application")

	async def _process_documents(
		self, application: Application, payload: SupportingDocumentRequest, account_id: UUID4 = None
	) -> List[ApplicationDocument]:
		"""Process and upload all documents."""
		created_documents = []

		try:
			for index, uploaded_document in enumerate(payload.documents):
				await self._validate_document_file(uploaded_document)
				document = await self._create_document_record(application, uploaded_document, account_id)
				application_document = await self._create_application_document_link(
					document.id, application.id, payload.document_types[index]
				)

				created_documents.append(application_document)

			return created_documents

		except Exception:
			await self._cleanup_uploaded_files(created_documents)
			raise

	async def _validate_document_file(self, uploaded_document) -> None:
		"""Validate uploaded document file."""
		if not uploaded_document.filename:
			raise ApiException("Document filename is required")

		if not uploaded_document.file:
			raise ApiException("Document file content is required")

		max_size = 10 * 1024 * 1024
		if hasattr(uploaded_document, "size") and uploaded_document.size > max_size:
			raise ApiException(f"Document size exceeds maximum limit of {max_size} bytes")

		if hasattr(uploaded_document, "content_type") and uploaded_document.content_type not in self.ALLOWED_TYPES:
			raise ApiException(f"Document type {uploaded_document.content_type} is not allowed")

	async def _create_document_record(
		self, application: Application, uploaded_document, account_id: UUID4 = None
	) -> Document:
		"""Create and save document record with custom filename and account-based directory structure."""
		custom_filename = self._generate_custom_filename(uploaded_document.filename)

		directory_id = account_id if account_id else application.organization_id
		location = f"{directory_id}/{custom_filename}"

		self.storage.upload_data(location, uploaded_document.file, content_type=uploaded_document.content_type)

		document = Document(
			filename=custom_filename,
			mimetype=uploaded_document.content_type,
			location=location,
			original_name=uploaded_document.filename,
			size=getattr(uploaded_document, "size", 0),
			created_by=self.current_user.id,
		)

		self.db.add(document)
		self.db.flush()

		return document

	async def _create_application_document_link(
		self, document_id: UUID4, application_id: UUID4, document_type_id: UUID4
	) -> ApplicationDocument:
		"""Create a link between application and document."""
		application_document = ApplicationDocument(
			document_id=document_id,
			application_id=application_id,
			document_type_id=document_type_id,
			created_by=self.current_user.id,
		)

		self.db.add(application_document)
		self.db.flush()

		return application_document

	async def _cleanup_uploaded_files(self, created_documents: List[ApplicationDocument]) -> None:
		"""Cleanup uploaded files in case of failure."""
		for app_doc in created_documents:
			try:
				if hasattr(app_doc, "document") and app_doc.document:
					self.storage.delete(app_doc.document.location)
			except Exception as cleanup_error:
				self.logger.warning(f"Failed to cleanup uploaded file: {cleanup_error}")

	async def get_application_documents(self, application_id: UUID4) -> List[ApplicationDocument]:
		"""Get all documents for an application."""
		return self.db.query(ApplicationDocument).filter(ApplicationDocument.application_id == application_id).all()

	async def delete_application_document(self, application_id: UUID4, document_id: UUID4) -> bool:
		"""Delete a specific document from an application."""
		try:
			app_doc = (
				self.db.query(ApplicationDocument)
				.filter(
					ApplicationDocument.application_id == application_id, ApplicationDocument.document_id == document_id
				)
				.first()
			)

			if not app_doc:
				raise ApiException("Document not found")

			if app_doc.document:
				self.storage.delete(app_doc.document.location)

			self.db.delete(app_doc)
			if app_doc.document:
				self.db.delete(app_doc.document)
			self.db.commit()

			return True

		except Exception as e:
			self.db.rollback()
			self.logger.error(f"Failed to delete document {document_id}: {str(e)}")
			raise ApiException("Failed to delete document")


document_service = DocumentService()
