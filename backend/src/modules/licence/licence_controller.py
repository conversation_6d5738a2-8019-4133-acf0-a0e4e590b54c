import json
from typing import List, Optional

from fastapi import File, Form, UploadFile
from starlette.requests import Request

from src.core.dtos.application_dtos import ApplicationDto
from src.core.dtos.licence_dtos import LicenceDto, LicenceRenewalDto, LicenceVerificationDto
from src.core.exceptions.api import ApiException
from src.core.guards.auth_guard import auth_guard
from src.core.logger.internal_logger import get_logger
from src.core.shared_schema import BaseResponse
from src.modules.licence.licence_service import LicenceService
from src.modules.organization.organization_schema import OrganizationRequest

logger = get_logger(__name__)


def get_licence_service() -> LicenceService:
	"""Get LicenceService instance"""
	return LicenceService()


# LICENCE MANAGEMENT


@auth_guard.authenticated
async def get_organization_licences_handler(_: Request, organization_id: str) -> BaseResponse[List[LicenceDto]]:
	"""Get all licences for an organization"""
	licence_service = get_licence_service()
	licences = await licence_service.get_organization_licences(organization_id)
	return BaseResponse[List[LicenceDto]](data=licences)


@auth_guard.authenticated
async def get_active_licence_handler(_: Request, organization_id: str) -> BaseResponse[LicenceDto]:
	"""Get the active licence for an organization"""
	licence_service = get_licence_service()
	licence = await licence_service.get_active_licence(organization_id)
	if not licence:
		raise ApiException("No active licence found for this organization")
	return BaseResponse[LicenceDto](data=licence)


# LICENCE RENEWAL HANDLERS


@auth_guard.authenticated
async def create_licence_renewal_handler(
	organization_id: str,
	_: Request,
	# Basic organization fields
	name: str = Form(..., description="Official registered name of the organization"),
	abbreviation: str = Form(..., description="Commonly used abbreviation for the organization"),
	organization_type_id: str = Form(..., description="UUID representing the type of organization"),
	district_id: str = Form(..., description="UUID of the primary district"),
	financial_start_month: str = Form(..., description="Financial year start month"),
	financial_end_month: str = Form(..., description="Financial year end month"),
	registration_type_id: str = Form(..., description="UUID of the registration type"),
	charity_number: Optional[str] = Form(None, description="Official charity registration number"),
	annual_income: float = Form(default=0.0, description="Total annual income"),
	biography: Optional[str] = Form(None, description="Brief biography of the organization"),
	vision: Optional[str] = Form(None, description="Organization's vision statement"),
	motto: Optional[str] = Form(None, description="Organization's motto"),
	# Complex nested data as JSON strings
	objectives: str = Form(..., description="JSON array of objectives"),
	directors: str = Form(..., description="JSON array of director details"),
	sectors: str = Form(..., description="JSON array of sector details"),
	location_activities: str = Form(..., description="JSON array of location activities"),
	target_groups: str = Form(..., description="JSON array of target groups"),
	funding_sources: str = Form(..., description="JSON array of funding sources"),
	auditors: str = Form(..., description="JSON array of auditor details"),
	bank_details: str = Form(..., description="JSON array of bank details"),
	contacts: str = Form(..., description="JSON array of contact details"),
	staff: str = Form(..., description="JSON array of staff details"),
	donors: str = Form(..., description="JSON array of donor details"),
	projects: str = Form(..., description="JSON array of project details"),
	# File uploads
	supporting_documents: Optional[List[UploadFile]] = File(None, description="List of document files"),
	document_types: Optional[str] = Form(None, description="JSON array of document type UUIDs"),
) -> BaseResponse[ApplicationDto]:
	"""Update organization data for licence renewal (similar to create but updates existing)"""
	try:
		# Parse JSON fields
		parsed_data = {
			"name": name,
			"abbreviation": abbreviation,
			"organization_type_id": organization_type_id,
			"district_id": district_id,
			"financial_start_month": financial_start_month,
			"financial_end_month": financial_end_month,
			"registration_type_id": registration_type_id,
			"charity_number": charity_number,
			"annual_income": annual_income,
			"biography": biography,
			"vision": vision,
			"motto": motto,
			"contacts": json.loads(contacts) if contacts else [],
			"objectives": json.loads(objectives) if objectives else [],
			"directors": json.loads(directors) if directors else [],
			"sectors": json.loads(sectors) if sectors else [],
			"location_activities": json.loads(location_activities) if location_activities else [],
			"target_groups": json.loads(target_groups) if target_groups else [],
			"funding_sources": json.loads(funding_sources) if funding_sources else [],
			"auditors": json.loads(auditors) if auditors else [],
			"bank_details": json.loads(bank_details) if bank_details else [],
			"staff": json.loads(staff) if staff else [],
			"donors": json.loads(donors) if donors else [],
			"projects": json.loads(projects) if projects else [],
			"document_types": json.loads(document_types) if document_types else [],
		}

		body = OrganizationRequest(**parsed_data)

		licence_service = get_licence_service()
		application = await licence_service.create_licence_renewal(organization_id, body, supporting_documents)

		if not application:
			raise ApiException("No licence renewal application found")

		return BaseResponse(data=application, message="Organization data updated for licence renewal successfully")

	except Exception as e:
		logger.error(f"Error creating licence renewal: {str(e)}")
		raise


# @auth_guard.authenticated
# async def submit_licence_renewal_handler(_: Request, organization_id: str) -> BaseResponse[ApplicationDto]:
# 	"""Submit licence renewal application for review"""
# 	application = await organization_service.submit_licence_renewal_for_review(organization_id)
# 	return BaseResponse(data=application, message="Licence renewal submitted for review successfully")


@auth_guard.authenticated
async def get_licence_renewal_handler(_: Request, organization_id: str) -> BaseResponse[LicenceRenewalDto]:
	"""Get the current licence renewal application for an organization"""
	service = get_licence_service()
	application = await service.get_licence_renewal_applications(organization_id)

	if not application:
		raise ApiException("No licence renewal application found")
	return BaseResponse(data=application, message="Licence renewal application retrieved successfully")


@auth_guard.authenticated
async def can_create_licence_renewal_handler(_: Request, organization_id: str) -> BaseResponse[bool]:
	"""Check if organization can create a licence renewal application"""
	licence_service = get_licence_service()
	can_create = await licence_service.can_create_licence_renewal(organization_id)
	return BaseResponse[bool](data=can_create)


# LICENCE VERIFICATION HANDLERS


@auth_guard.authenticated
async def verify_licence_handler(_: Request, licence_number: str) -> BaseResponse[LicenceVerificationDto]:
	"""Verify a licence by licence number - comprehensive verification"""
	licence_service = get_licence_service()
	verification = await licence_service.verify_licence_comprehensive(licence_number)
	return BaseResponse[LicenceVerificationDto](data=verification)


@auth_guard.authenticated
async def check_licence_validity_handler(_: Request, licence_number: str) -> BaseResponse[dict]:
	"""Quick check if licence is valid (exists and not expired)"""
	licence_service = get_licence_service()
	is_valid = await licence_service.is_licence_valid(licence_number)
	result = {"licence_number": licence_number, "is_valid": is_valid}
	return BaseResponse[dict](data=result)


# PUBLIC VERIFICATION (no authentication required)
async def public_verify_licence_handler(licence_number: str) -> BaseResponse[LicenceVerificationDto]:
	"""Public licence verification endpoint - anyone can verify licence authenticity"""
	licence_service = get_licence_service()
	verification = await licence_service.verify_licence_comprehensive(licence_number)
	return BaseResponse[LicenceVerificationDto](data=verification)
