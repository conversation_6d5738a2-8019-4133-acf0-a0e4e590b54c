import json
from typing import Any, Dict, List, Optional

from pydantic import UUID4

from src.config.db.models import OrganizationProject, OrganizationSector
from src.config.db.models.application import Application
from src.config.db.models.bank_detail import BankDetail
from src.config.db.models.contact import Contact, ContactType
from src.config.db.models.director import Director
from src.config.db.models.funding_source import FundingSource
from src.config.db.models.licence_renewal import LicenceRenewal, LicenceRenewalStatus
from src.config.db.models.location_activity import LocationActivity
from src.config.db.models.organization import Organization, OrganizationStatus
from src.config.db.models.organization_auditor import OrganizationAuditor
from src.config.db.models.organization_donor import OrganizationDonor
from src.config.db.models.organization_staff import OrganizationStaff
from src.config.db.models.target_group import TargetGroup
from src.core.base.base_repository import BaseRepository
from src.core.exceptions.api import ApiException
from src.core.logger.internal_logger import get_logger


class LicenceRenewalService(BaseRepository):
	"""Service for processing licence renewals and updating organization data."""

	def __init__(self):
		super().__init__()
		self.logger = get_logger(__name__)

	async def process_licence_renewal(self, organization_id: UUID4) -> bool:
		"""Process licence renewal by updating organization with form data and marking renewal as PROCESSED."""
		try:
			with self.db.begin_nested():
				licence_renewal = await self._get_pending_licence_renewal(organization_id)
				if not licence_renewal:
					raise ApiException("No pending licence renewal found for this organization")

				organization = self.db.query(Organization).filter(Organization.id == organization_id).first()
				if not organization:
					raise ApiException("Organization not found")

				application = (
					self.db.query(Application).filter(Application.id == licence_renewal.application_id).first()
				)
				if not application:
					raise ApiException("Associated application not found")

				form_data = (
					json.loads(licence_renewal.form_data)
					if isinstance(licence_renewal.form_data, str)
					else licence_renewal.form_data
				)

				await self._update_organization_main_fields(organization, form_data, application.created_by)

				await self._update_organization_children(organization, form_data, application.created_by)

				await self._update_organization_contacts(
					organization.account_id, form_data.get("contacts", []), application.created_by
				)

				licence_renewal.status = LicenceRenewalStatus.PROCESSED
				licence_renewal.updated_by = self.current_user.id
				self.db.add(licence_renewal)

				organization.status = OrganizationStatus.REGISTERED
				organization.updated_by = application.created_by
				self.db.add(organization)

				self.db.commit()

				self.logger.info(f"Successfully processed licence renewal for organization {organization_id}")
				return True

		except Exception as e:
			self.db.rollback()
			self.logger.error(f"Failed to process licence renewal for organization {organization_id}: {str(e)}")
			raise

	async def _get_pending_licence_renewal(self, organization_id: UUID4) -> Optional[LicenceRenewal]:
		"""Get the pending licence renewal for an organization."""
		return (
			self.db.query(LicenceRenewal)
			.filter(
				LicenceRenewal.organization_id == organization_id,
				LicenceRenewal.status == LicenceRenewalStatus.SUBMITTED,
			)
			.first()
		)

	async def _update_organization_main_fields(
		self, organization: Organization, form_data: Dict[str, Any], updated_by_user_id: UUID4
	):
		"""Update main organization fields from form data."""
		main_fields = [
			"name",
			"motto",
			"vision",
			"biography",
			"abbreviation",
			"annual_income",
			"district_id",
			"charity_number",
			"financial_end_month",
			"financial_start_month",
			"organization_type_id",
			"registration_type_id",
			"objectives",
		]

		for field in main_fields:
			if field in form_data:
				setattr(organization, field, form_data[field])

		organization.updated_by = updated_by_user_id
		self.db.add(organization)
		self.logger.info(f"Updated main organization fields for {organization.id}")

	async def _update_organization_children(
		self, organization: Organization, form_data: Dict[str, Any], updated_by_user_id: UUID4
	):
		"""Update all child models of the organization."""
		child_updates = {
			"directors": self._update_directors,
			"staff": self._update_staff,
			"auditors": self._update_auditors,
			"bank_details": self._update_bank_details,
			"donors": self._update_donors,
			"target_groups": self._update_target_groups,
			"funding_sources": self._update_funding_sources,
			"location_activities": self._update_location_activities,
			"sectors": self._update_sectors,
			"projects": self._update_projects,
		}

		for form_key, update_method in child_updates.items():
			if form_key in form_data:
				await update_method(organization.id, form_data[form_key], updated_by_user_id)

	async def _update_directors(
		self, organization_id: UUID4, new_directors: List[Dict[str, Any]], updated_by_user_id: UUID4
	):
		"""Update directors with ID-based void/update/create logic."""
		existing_directors = (
			self.db.query(Director).filter(Director.organization_id == organization_id, ~Director.voided).all()
		)

		existing_by_id = {str(d.id): d for d in existing_directors}
		new_by_id = {d.get("id"): d for d in new_directors if d.get("id")}

		for director_id, director in existing_by_id.items():
			if director_id not in new_by_id:
				director.voided = True
				director.voided_by = updated_by_user_id
				director.void_reason = "Removed during licence renewal"
				self.db.add(director)
				self.logger.info(f"Voided director {director.id}")

		for director_data in new_directors:
			director_id = director_data.get("id")

			if director_id and director_id in existing_by_id:
				director = existing_by_id[director_id]
				for key, value in director_data.items():
					if hasattr(director, key) and key != "id":
						setattr(director, key, value)
				director.updated_by = updated_by_user_id
				self.db.add(director)
				self.logger.info(f"Updated director {director.id}")
			else:
				director_data_clean = {k: v for k, v in director_data.items() if k != "id"}
				new_director = Director(
					organization_id=organization_id, created_by=updated_by_user_id, **director_data_clean
				)
				self.db.add(new_director)
				self.logger.info("Created new director")

	async def _update_staff(self, organization_id: UUID4, new_staff: List[Dict[str, Any]], updated_by_user_id: UUID4):
		"""Update organization staff with ID-based logic."""
		existing_staff = (
			self.db.query(OrganizationStaff)
			.filter(OrganizationStaff.organization_id == organization_id, ~OrganizationStaff.voided)
			.all()
		)

		existing_by_id = {str(s.id): s for s in existing_staff}
		new_by_id = {s.get("id"): s for s in new_staff if s.get("id")}

		for staff_id, staff in existing_by_id.items():
			if staff_id not in new_by_id:
				staff.voided = True
				staff.voided_by = updated_by_user_id
				staff.void_reason = "Removed during licence renewal"
				self.db.add(staff)

		for staff_data in new_staff:
			staff_id = staff_data.get("id")

			if staff_id and staff_id in existing_by_id:
				staff = existing_by_id[staff_id]
				for key, value in staff_data.items():
					if hasattr(staff, key) and key != "id":
						setattr(staff, key, value)
				staff.updated_by = updated_by_user_id
				self.db.add(staff)
			else:
				staff_data_clean = {k: v for k, v in staff_data.items() if k != "id"}
				new_staff_obj = OrganizationStaff(
					organization_id=organization_id, created_by=updated_by_user_id, **staff_data_clean
				)
				self.db.add(new_staff_obj)

	async def _update_auditors(
		self, organization_id: UUID4, new_auditors: List[Dict[str, Any]], updated_by_user_id: UUID4
	):
		"""Update organization auditors with ID-based logic."""
		existing_auditors = (
			self.db.query(OrganizationAuditor)
			.filter(OrganizationAuditor.organization_id == organization_id, ~OrganizationAuditor.voided)
			.all()
		)

		existing_by_id = {str(a.id): a for a in existing_auditors}
		new_by_id = {a.get("id"): a for a in new_auditors if a.get("id")}

		for auditor_id, auditor in existing_by_id.items():
			if auditor_id not in new_by_id:
				auditor.voided = True
				auditor.voided_by = updated_by_user_id
				auditor.void_reason = "Removed during licence renewal"
				self.db.add(auditor)

		for auditor_data in new_auditors:
			auditor_id = auditor_data.get("id")

			if auditor_id and auditor_id in existing_by_id:
				auditor = existing_by_id[auditor_id]
				for key, value in auditor_data.items():
					if hasattr(auditor, key) and key != "id":
						setattr(auditor, key, value)
				auditor.updated_by = updated_by_user_id
				self.db.add(auditor)
			else:
				auditor_data_clean = {k: v for k, v in auditor_data.items() if k != "id"}
				new_auditor = OrganizationAuditor(
					organization_id=organization_id, created_by=updated_by_user_id, **auditor_data_clean
				)
				self.db.add(new_auditor)

	async def _update_bank_details(
		self, organization_id: UUID4, new_bank_details: List[Dict[str, Any]], updated_by_user_id: UUID4
	):
		"""Update bank details with ID-based logic."""
		existing_details = (
			self.db.query(BankDetail).filter(BankDetail.organization_id == organization_id, ~BankDetail.voided).all()
		)

		existing_by_id = {str(bd.id): bd for bd in existing_details}
		new_by_id = {bd.get("id"): bd for bd in new_bank_details if bd.get("id")}

		for detail_id, bank_detail in existing_by_id.items():
			if detail_id not in new_by_id:
				bank_detail.voided = True
				bank_detail.voided_by = updated_by_user_id
				bank_detail.void_reason = "Removed during licence renewal"
				self.db.add(bank_detail)

		for detail_data in new_bank_details:
			detail_id = detail_data.get("id")

			if detail_id and detail_id in existing_by_id:
				detail = existing_by_id[detail_id]
				for key, value in detail_data.items():
					if hasattr(detail, key) and key != "id":
						setattr(detail, key, value)
				detail.updated_by = updated_by_user_id
				self.db.add(detail)
			else:
				detail_data_clean = {k: v for k, v in detail_data.items() if k != "id"}
				new_detail = BankDetail(
					organization_id=organization_id, created_by=updated_by_user_id, **detail_data_clean
				)
				self.db.add(new_detail)

	async def _update_donors(self, organization_id: UUID4, new_donors: List[Dict[str, Any]], updated_by_user_id: UUID4):
		"""Update organization donors with ID-based logic."""
		existing_donors = (
			self.db.query(OrganizationDonor)
			.filter(OrganizationDonor.organization_id == organization_id, ~OrganizationDonor.voided)
			.all()
		)

		existing_by_id = {str(d.id): d for d in existing_donors}
		new_by_id = {d.get("id"): d for d in new_donors if d.get("id")}

		for donor_id, donor in existing_by_id.items():
			if donor_id not in new_by_id:
				donor.voided = True
				donor.voided_by = updated_by_user_id
				donor.void_reason = "Removed during licence renewal"
				self.db.add(donor)

		for donor_data in new_donors:
			donor_id = donor_data.get("id")

			if donor_id and donor_id in existing_by_id:
				donor = existing_by_id[donor_id]
				for key, value in donor_data.items():
					if hasattr(donor, key) and key != "id":
						setattr(donor, key, value)
				donor.updated_by = updated_by_user_id
				self.db.add(donor)
			else:
				donor_data_clean = {k: v for k, v in donor_data.items() if k != "id"}
				new_donor = OrganizationDonor(
					organization_id=organization_id, created_by=updated_by_user_id, **donor_data_clean
				)
				self.db.add(new_donor)

	async def _update_target_groups(
		self, organization_id: UUID4, new_target_groups: List[Dict[str, Any]], updated_by_user_id: UUID4
	):
		"""Update target groups with ID-based logic."""
		existing_groups = (
			self.db.query(TargetGroup).filter(TargetGroup.organization_id == organization_id, ~TargetGroup.voided).all()
		)

		existing_by_id = {str(tg.id): tg for tg in existing_groups}
		new_by_id = {tg.get("id"): tg for tg in new_target_groups if tg.get("id")}

		for group_id, group in existing_by_id.items():
			if group_id not in new_by_id:
				group.voided = True
				group.voided_by = updated_by_user_id
				group.void_reason = "Removed during licence renewal"
				self.db.add(group)

		for group_data in new_target_groups:
			group_id = group_data.get("id")

			if group_id and group_id in existing_by_id:
				group = existing_by_id[group_id]
				for key, value in group_data.items():
					if hasattr(group, key) and key != "id":
						setattr(group, key, value)
				group.updated_by = updated_by_user_id
				self.db.add(group)
			else:
				group_data_clean = {k: v for k, v in group_data.items() if k != "id"}
				new_group = TargetGroup(
					organization_id=organization_id, created_by=updated_by_user_id, **group_data_clean
				)
				self.db.add(new_group)

	async def _update_funding_sources(
		self, organization_id: UUID4, new_funding_sources: List[Dict[str, Any]], updated_by_user_id: UUID4
	):
		"""Update funding sources with ID-based logic."""
		existing_sources = (
			self.db.query(FundingSource)
			.filter(FundingSource.organization_id == organization_id, ~FundingSource.voided)
			.all()
		)

		existing_by_id = {str(fs.id): fs for fs in existing_sources}
		new_by_id = {fs.get("id"): fs for fs in new_funding_sources if fs.get("id")}

		for source_id, source in existing_by_id.items():
			if source_id not in new_by_id:
				source.voided = True
				source.voided_by = updated_by_user_id
				source.void_reason = "Removed during licence renewal"
				self.db.add(source)

		for source_data in new_funding_sources:
			source_id = source_data.get("id")

			if source_id and source_id in existing_by_id:
				source = existing_by_id[source_id]
				for key, value in source_data.items():
					if hasattr(source, key) and key != "id":
						setattr(source, key, value)
				source.updated_by = updated_by_user_id
				self.db.add(source)
			else:
				source_data_clean = {k: v for k, v in source_data.items() if k != "id"}
				new_source = FundingSource(
					organization_id=organization_id, created_by=updated_by_user_id, **source_data_clean
				)
				self.db.add(new_source)

	async def _update_location_activities(
		self, organization_id: UUID4, new_locations: List[Dict[str, Any]], updated_by_user_id: UUID4
	):
		"""Update location activities with ID-based logic."""
		existing_locations = (
			self.db.query(LocationActivity)
			.filter(LocationActivity.organization_id == organization_id, ~LocationActivity.voided)
			.all()
		)

		existing_by_id = {str(loc.id): loc for loc in existing_locations}
		new_by_id = {loc.get("id"): loc for loc in new_locations if loc.get("id")}

		for location_id, location in existing_by_id.items():
			if location_id not in new_by_id:
				location.voided = True
				location.voided_by = updated_by_user_id
				location.void_reason = "Removed during licence renewal"
				self.db.add(location)

		for location_data in new_locations:
			location_id = location_data.get("id")

			if location_id and location_id in existing_by_id:
				location = existing_by_id[location_id]
				for key, value in location_data.items():
					if hasattr(location, key) and key != "id":
						setattr(location, key, value)
				location.updated_by = updated_by_user_id
				self.db.add(location)
			else:
				location_data_clean = {k: v for k, v in location_data.items() if k != "id"}
				new_location = LocationActivity(
					organization_id=organization_id, created_by=updated_by_user_id, **location_data_clean
				)
				self.db.add(new_location)

	async def _update_sectors(self, organization_id: UUID4, new_sectors: List[str], updated_by_user_id: UUID4):
		"""Update organization sectors (expecting list of sector IDs)"""
		existing_sectors = (
			self.db.query(OrganizationSector)
			.filter(OrganizationSector.organization_id == organization_id, ~OrganizationSector.voided)
			.all()
		)

		existing_sector_ids = {s.sector_id for s in existing_sectors}
		new_sector_ids = set(new_sectors)

		for sector in existing_sectors:
			if sector.sector_id not in new_sector_ids:
				sector.voided = True
				sector.voided_by = updated_by_user_id
				sector.void_reason = "Removed during licence renewal"
				self.db.add(sector)

		for sector_id in new_sector_ids:
			if sector_id not in existing_sector_ids:
				new_sector = OrganizationSector(
					organization_id=organization_id, sector_id=sector_id, created_by=updated_by_user_id
				)
				self.db.add(new_sector)

	async def _update_projects(
		self, organization_id: UUID4, new_projects: List[Dict[str, Any]], updated_by_user_id: UUID4
	):
		"""Update organization projects with ID-based logic."""
		existing_projects = (
			self.db.query(OrganizationProject)
			.filter(OrganizationProject.organization_id == organization_id, ~OrganizationProject.voided)
			.all()
		)

		existing_by_id = {str(p.id): p for p in existing_projects}
		new_by_id = {p.get("id"): p for p in new_projects if p.get("id")}

		for project_id, project in existing_by_id.items():
			if project_id not in new_by_id:
				project.voided = True
				project.voided_by = updated_by_user_id
				project.void_reason = "Removed during licence renewal"
				self.db.add(project)

		for project_data in new_projects:
			project_id = project_data.get("id")

			if project_id and project_id in existing_by_id:
				project = existing_by_id[project_id]
				for key, value in project_data.items():
					if hasattr(project, key) and key != "id":
						setattr(project, key, value)
				project.updated_by = updated_by_user_id
				self.db.add(project)
			else:
				project_data_clean = {k: v for k, v in project_data.items() if k != "id"}
				new_project = OrganizationProject(
					organization_id=organization_id, created_by=updated_by_user_id, **project_data_clean
				)
				self.db.add(new_project)

	async def _update_organization_contacts(
		self, account_id: UUID4, new_contacts: List[Dict[str, Any]], updated_by_user_id: UUID4
	):
		"""Update organization contacts (linked to account) with ID-based logic."""
		existing_contacts = self.db.query(Contact).filter(Contact.account_id == account_id, ~Contact.voided).all()

		existing_by_id = {str(c.id): c for c in existing_contacts}
		new_by_id = {c.get("id"): c for c in new_contacts if c.get("id")}

		for contact_id, contact in existing_by_id.items():
			if contact_id not in new_by_id:
				contact.voided = True
				contact.voided_by = updated_by_user_id
				contact.void_reason = "Removed during licence renewal"
				self.db.add(contact)

		for contact_data in new_contacts:
			contact_id = contact_data.get("id")

			if contact_id and contact_id in existing_by_id:
				contact = existing_by_id[contact_id]
				contact.details = contact_data.get("details", {})
				contact.updated_by = updated_by_user_id
				self.db.add(contact)
			else:
				contact_type = ContactType(contact_data.get("type"))
				new_contact = Contact(
					account_id=account_id,
					type=contact_type,
					details=contact_data.get("details", {}),
					created_by=updated_by_user_id,
				)
				self.db.add(new_contact)
