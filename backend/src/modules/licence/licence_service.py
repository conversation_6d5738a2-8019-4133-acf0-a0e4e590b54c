import secrets
import string
from datetime import date, timedelta
from io import Bytes<PERSON>
from typing import List, Optional

from fastapi import UploadFile
from pydantic import UUID4

from src.config import settings
from src.config.db.models.application import Application, ApplicationStatus, ApplicationType
from src.config.db.models.application_document import ApplicationDocument
from src.config.db.models.document import Document
from src.config.db.models.licence import Licence, LicenceType
from src.config.db.models.licence_renewal import LicenceRenewal, LicenceRenewalStatus
from src.config.db.models.organization import Organization, OrganizationStatus
from src.core.base.base_repository import BaseRepository
from src.core.dtos.application_dtos import ApplicationDto, to_application_dto
from src.core.dtos.document_dtos import to_application_document_dto
from src.core.dtos.licence_dtos import (
	LicenceDto,
	LicenceRenewalDto,
	LicenceVerificationDto,
	to_licence_dto,
	to_licence_renewal_dto,
)
from src.core.exceptions.api import ApiException
from src.core.generators.certificate_generator import CertificateGenerator
from src.core.generators.licence_generator import LicenceGenerator
from src.core.generators.employment_permit_generator import EmploymentPermitGenerator
from src.core.logger.internal_logger import get_logger
from src.core.services.email_service import EmailAttachment, EmailRecipient, EmailTemplate, _get_email_service_instance
from src.core.utils.common import serialize_for_json
from src.modules.application.application_service import ApplicationService
from src.modules.document.document_service import DocumentService
from src.modules.organization.organization_schema import OrganizationRequest, SupportingDocumentRequest


class LicenceService(BaseRepository):
	def __init__(self):
		super().__init__()
		self.logger = get_logger(__name__)
		self._application_service = None
		self._document_service = None
		self._certificate_generator = None
		self._licence_generator = None
		self._employment_permit_generator = None
		self._email_service = None

	@property
	def application_service(self):
		"""Lazy-loaded ApplicationService"""
		if self._application_service is None:
			self._application_service = ApplicationService()
		return self._application_service

	@property
	def document_service(self):
		"""Lazy-loaded DocumentService"""
		if self._document_service is None:
			self._document_service = DocumentService()
		return self._document_service

	@property
	def certificate_generator(self):
		"""Lazy-loaded CertificateGenerator"""
		if self._certificate_generator is None:
			self._certificate_generator = CertificateGenerator()
		return self._certificate_generator

	@property
	def licence_generator(self):
		"""Lazy-loaded LicenceGenerator"""
		if self._licence_generator is None:
			self._licence_generator = LicenceGenerator()
		return self._licence_generator

	@property
	def employment_permit_generator(self):
		"""Lazy-loaded EmploymentPermitGenerator"""
		if self._employment_permit_generator is None:
			self._employment_permit_generator = EmploymentPermitGenerator()
		return self._employment_permit_generator

	@property
	def email_service(self):
		"""Lazy-loaded EmailService"""
		if self._email_service is None:
			self._email_service = _get_email_service_instance()
		return self._email_service

	def _generate_licence_number(self, licence_type: LicenceType) -> str:
		"""Generate a unique licence number"""
		if licence_type == LicenceType.CERTIFICATE:
			prefix = "CERT"
		elif licence_type == LicenceType.PERMIT:
			prefix = "EP"
		else:
			prefix = "LIC"
		year = date.today().year
		random_part = "".join(secrets.choice(string.ascii_uppercase + string.digits) for _ in range(6))
		return f"{prefix}{year}{random_part}"

	async def generate_certificate(self, organization_id: UUID4, invoice_item_id: Optional[UUID4] = None) -> Licence:
		"""Generate a certificate for organization registration"""
		try:
			existing_licence = (
				self.db.query(Licence)
				.filter(Licence.invoice_item_id == invoice_item_id, Licence.organization_id == organization_id)
				.first()
			)

			if existing_licence:
				return existing_licence

			organization = self.db.query(Organization).filter(Organization.id == organization_id).first()

			if not organization:
				raise ApiException("Organization not found")

			licence_number = self._generate_licence_number(LicenceType.CERTIFICATE)

			while self.db.query(Licence).filter(Licence.licence_number == licence_number).first():
				licence_number = self._generate_licence_number(LicenceType.CERTIFICATE)

			expires_at = date.today() + timedelta(days=365)

			certificate_data = {
				"organization_name": organization.name,
				"issue_date": date.today().isoformat(),
				"board_chairperson": "Board Chairperson",
				"registrar_ceo": "Registrar/CEO",
				"registration_number": licence_number,
				"verification_url": f"{settings.FRONTEND_URL}/verify?cert={licence_number}",
			}

			certificate_pdf_bytes = self.certificate_generator.generate_pdf_bytes(certificate_data)

			certificate_file = BytesIO(certificate_pdf_bytes)
			certificate_filename = f"certificate_{licence_number}_{date.today().isoformat()}.pdf"

			document = await self._upload_certificate_document(
				certificate_file, certificate_filename, organization.account_id
			)

			certificate = Licence(
				licence_number=licence_number,
				organization_id=organization_id,
				invoice_item_id=invoice_item_id,
				document_id=document.id,
				expires_at=expires_at,
				type=LicenceType.CERTIFICATE,
				created_by=self.current_user.id,
			)

			self.db.add(certificate)
			self.db.flush()

			await self._send_certificate_email(organization, certificate, certificate_pdf_bytes, certificate_filename)

			return certificate

		except Exception as e:
			self.logger.error(f"Failed to generate certificate: {str(e)}")
			raise

	async def can_create_licence_renewal(self, organization_id: UUID4) -> bool:
		try:
			organization = (
				self.db.query(Organization)
				.filter(
					Organization.id == organization_id,
					Organization.status.in_([OrganizationStatus.REGISTERED, OrganizationStatus.RENEWAL_DRAFT]),
				)
				.first()
			)

			return True if organization else False
		except Exception as e:
			self.logger.error(e)
			raise

	async def create_licence_renewal(
		self, organization_id: UUID4, data: OrganizationRequest, supporting_documents: List[UploadFile]
	) -> ApplicationDto:
		try:
			uploaded_documents = SupportingDocumentRequest()
			uploaded_documents.documents = supporting_documents
			uploaded_documents.document_types = data.document_types

			valid = await self.can_create_licence_renewal(organization_id)
			if not valid:
				raise ApiException("Cannot create licence renewal application")

			licence_renewal = await self.get_staged_licence_renewal_data(organization_id)
			application = await self.application_service.create_licence_renewal(organization_id, data.annual_income)

			if licence_renewal:
				# SUBSEQUENT REQUEST - Update form data and handle optional document replacement
				licence_renewal.form_data = serialize_for_json(data.model_dump())
				licence_renewal.updated_by = self.current_user.id
				self.db.add(licence_renewal)
				self.db.flush()

				# Handle optional document replacement
				if supporting_documents and len(supporting_documents) > 0:
					await self._replace_application_documents(licence_renewal.application_id, uploaded_documents)

			if not licence_renewal:
				# FIRST REQUEST - Create new renewal and require all documents
				licence_renewal = LicenceRenewal(
					application_id=application.id,
					organization_id=organization_id,
					form_data=serialize_for_json(data.model_dump()),
					created_by=self.current_user.id,
					status=LicenceRenewalStatus.DRAFT,
				)

				self.db.add(licence_renewal)
				self.db.flush()

				# For first request, validate and upload all required documents
				await self.document_service.add_application_documents(
					application.id, uploaded_documents, application.organization.account_id
				)

			application.organization.status = OrganizationStatus.RENEWAL_DRAFT
			application.organization.updated_by = self.current_user.id
			self.db.add(application.organization)

			self.db.commit()

			return to_application_dto(application)
		except Exception as e:
			self.db.rollback()
			self.logger.error(f"Failed to save renewal form data: {str(e)}")
			raise

	async def generate_licence(self, organization_id: UUID4, invoice_item_id: Optional[UUID4] = None) -> Licence:
		"""Generate a licence for licence renewal"""
		try:
			existing_licence = (
				self.db.query(Licence)
				.filter(Licence.invoice_item_id == invoice_item_id, Licence.organization_id == organization_id)
				.first()
			)

			if existing_licence:
				return existing_licence

			organization = self.db.query(Organization).filter(Organization.id == organization_id).first()

			if not organization:
				raise ApiException("Organization not found")

			licence_number = self._generate_licence_number(LicenceType.LICENCE)

			while self.db.query(Licence).filter(Licence.licence_number == licence_number).first():
				licence_number = self._generate_licence_number(LicenceType.LICENCE)

			# Licence expires after 1 year
			expires_at = date.today() + timedelta(days=365)

			# Generate licence PDF data
			licence_data = {
				"organization_name": organization.name,
				"issue_date": date.today().isoformat(),
				"valid_until": expires_at.isoformat(),
				"board_chairperson": "Board Chairperson",
				"registrar_ceo": "Registrar/CEO",
				"registration_number": licence_number,
				"verification_url": f"{settings.FRONTEND_URL}/verify?licence={licence_number}",
			}

			# Generate licence PDF
			licence_pdf_bytes = self.licence_generator.generate_pdf_bytes(licence_data)

			# Upload licence document (reuse existing function)
			licence_file = BytesIO(licence_pdf_bytes)
			licence_filename = f"licence_{licence_number}_{date.today().isoformat()}.pdf"

			document = await self._upload_certificate_document(licence_file, licence_filename, organization.account_id)

			licence = Licence(
				licence_number=licence_number,
				organization_id=organization_id,
				invoice_item_id=invoice_item_id,
				document_id=document.id,
				expires_at=expires_at,
				type=LicenceType.LICENCE,
				created_by=self.current_user.id,
			)

			self.db.add(licence)
			self.db.flush()

			# Send licence via email
			await self._send_licence_email(organization, licence, licence_pdf_bytes, licence_filename)

			self.logger.info(f"Generated licence {licence_number} for organization {organization.name}")
			return licence

		except Exception as e:
			self.logger.error(f"Failed to generate licence: {str(e)}")
			raise

	async def generate_employment_permit_for_application(self, application_id: UUID4, invoice_item_id: Optional[UUID4] = None) -> Licence:
		"""Generate employment permit for single applicant in an application"""
		try:
			from src.config.db.models.permit_applicant import PermitApplicant
			from src.config.db.models.application import Application
			from src.config.db.models.country import Country
			from sqlalchemy.orm import joinedload
			
			# Get application with related data
			application = (
				self.db.query(Application)
				.options(joinedload(Application.organization))
				.filter(Application.id == application_id)
				.first()
			)

			if not application:
				raise ApiException("Application not found")

			if application.type.value != "PERMIT_APPLICATION":
				raise ApiException("Application is not a permit application")

			# Get the single permit applicant for this application
			permit_applicant = (
				self.db.query(PermitApplicant)
				.filter(PermitApplicant.application_id == application_id)
				.first()
			)

			if not permit_applicant:
				raise ApiException("No permit applicant found for this application")

			# Generate the permit for this single applicant
			permit = await self._generate_single_employment_permit(
				application, permit_applicant, invoice_item_id
			)

			self.logger.info(f"Generated employment permit for application {application_id}")
			return permit

		except Exception as e:
			self.logger.error(f"Failed to generate employment permit for application: {str(e)}")
			raise

	async def _generate_single_employment_permit(self, application, permit_applicant, invoice_item_id: Optional[UUID4] = None) -> Licence:
		"""Generate a single employment permit for a permit applicant"""
		try:
			from src.config.db.models.country import Country
			
			# Check if permit already exists
			existing_permit = (
				self.db.query(Licence)
				.filter(
					Licence.organization_id == application.organization_id,
					Licence.type == LicenceType.PERMIT,
					Licence.invoice_item_id == invoice_item_id
				)
				.first()
			)

			if existing_permit:
				return existing_permit

			# Get nationality information
			nationality = None
			if permit_applicant.nationality_id:
				nationality = self.db.query(Country).filter(Country.id == permit_applicant.nationality_id).first()

			# Generate permit number
			permit_number = self._generate_licence_number(LicenceType.PERMIT)

			# Create permit data for PDF generation
			permit_data = {
				"permit_number": permit_number,
				"employee_name": permit_applicant.full_name,
				"nationality": nationality.name if nationality else "Unknown",
				"passport_number": permit_applicant.passport_number,
				"organization_name": application.organization.name,
				"organization_registration": getattr(application.organization, 'registration_number', 'N/A'),
				"organization_address": self._format_organization_address(application.organization),
				"position": permit_applicant.position or "Not specified",
				"department": permit_applicant.department or "Not specified",
				"issue_date": date.today().isoformat(),
				"expiry_date": (date.today() + timedelta(days=365)).isoformat(),
				"employment_start_date": permit_applicant.employment_start_date.isoformat() if permit_applicant.employment_start_date else date.today().isoformat(),
				"employment_end_date": permit_applicant.employment_end_date.isoformat() if permit_applicant.employment_end_date else (date.today() + timedelta(days=365)).isoformat(),
				"registrar_ceo": "Director General",
				"verification_url": f"{settings.FRONTEND_URL}/verify/{permit_number}",
			}

			# Generate PDF
			permit_pdf_bytes = self.employment_permit_generator.generate_pdf_bytes(permit_data)
			permit_filename = f"employment_permit_{permit_number}.pdf"

			# Create document record
			permit_document = await self.document_service.create_document_from_bytes(
				file_bytes=permit_pdf_bytes,
				filename=permit_filename,
				content_type="application/pdf",
				account_id=application.organization.account_id
			)

			# Create licence record for the permit (no expiration date for permits)
			permit = Licence(
				licence_number=permit_number,
				organization_id=application.organization_id,
				invoice_item_id=invoice_item_id,
				document_id=permit_document.id,
				expires_at=None,  # Permits have no expiration date
				type=LicenceType.PERMIT,
				created_by=self.current_user.id,
			)

			self.db.add(permit)
			self.db.commit()
			self.db.refresh(permit)

			# Send employment permit email
			await self._send_employment_permit_email(application.organization, permit, permit_applicant, permit_pdf_bytes)

			self.logger.info(f"Generated employment permit {permit_number} for applicant {permit_applicant.full_name}")
			return permit

		except Exception as e:
			self.db.rollback()
			self.logger.error(f"Failed to generate employment permit: {str(e)}")
			raise

	def _format_organization_address(self, organization) -> str:
		"""Format organization address for permit display"""
		address_parts = []
		
		if hasattr(organization, 'physical_address') and organization.physical_address:
			address_parts.append(organization.physical_address)
		if hasattr(organization, 'city') and organization.city:
			address_parts.append(organization.city)
		if hasattr(organization, 'district') and organization.district:
			address_parts.append(organization.district)
		
		return ", ".join(address_parts) if address_parts else "Address not specified"

	async def _send_employment_permit_email(self, organization, permit, permit_applicant, pdf_bytes: bytes):
		"""Send employment permit email to organization"""
		try:
			from src.core.services.email_service import send_employment_permit_email
			
			if not organization.account:
				self.logger.warning(f"No organization account found for permit {permit.licence_number}")
				return

			send_employment_permit_email(
				organization=organization,
				permit=permit,
				permit_applicant=permit_applicant,
				pdf_bytes=pdf_bytes,
				sender_email=settings.SENDER_EMAIL
			)

			self.logger.info(f"Employment permit email sent for {permit.licence_number}")

		except Exception as e:
			self.logger.error(f"Failed to send employment permit email: {str(e)}")
			# Don't raise exception as permit generation was successful

	async def get_organization_licences(self, organization_id: UUID4) -> List[LicenceDto]:
		"""Get all licences for an organization"""
		try:
			licences = (
				self.db.query(Licence)
				.filter(Licence.organization_id == organization_id)
				.order_by(Licence.created_at.desc())
				.all()
			)
			return [to_licence_dto(licence) for licence in licences]
		except Exception as e:
			self.logger.error(f"Failed to get organization licences: {str(e)}")
			raise

	async def get_active_licence(self, organization_id: UUID4) -> Optional[LicenceDto]:
		"""Get the active (non-expired) licence for an organization"""
		try:
			today = date.today()
			licence = (
				self.db.query(Licence)
				.filter(Licence.organization_id == organization_id, Licence.expires_at > today)
				.order_by(Licence.created_at.desc())
				.first()
			)
			return to_licence_dto(licence) if licence else None
		except Exception as e:
			self.logger.error(f"Failed to get active licence: {str(e)}")
			raise

	async def verify_licence_comprehensive(self, licence_number: str) -> LicenceVerificationDto:
		"""Comprehensive licence verification with detailed response"""
		try:
			licence = (
				self.db.query(Licence)
				.join(Organization, Licence.organization_id == Organization.id)
				.filter(Licence.licence_number == licence_number)
				.first()
			)

			if not licence:
				return LicenceVerificationDto(
					licence_number=licence_number, is_valid=False, message="Licence number not found"
				)

			today = date.today()
			is_expired = licence.expires_at <= today

			if is_expired:
				return LicenceVerificationDto(
					licence_number=licence_number,
					is_valid=False,
					licence=to_licence_dto(licence),
					organization_name=licence.organization.name,
					message=f"Licence expired on {licence.expires_at}",
				)

			return LicenceVerificationDto(
				licence_number=licence_number,
				is_valid=True,
				licence=to_licence_dto(licence),
				organization_name=licence.organization.name,
				message="Licence is valid and active",
			)

		except Exception as e:
			self.logger.error(f"Failed to verify licence: {str(e)}")
			raise

	async def verify_licence(self, licence_number: str) -> Optional[LicenceDto]:
		"""Simple licence verification - returns licence if exists"""
		try:
			licence = self.db.query(Licence).filter(Licence.licence_number == licence_number).first()

			if not licence:
				return None

			return to_licence_dto(licence)
		except Exception as e:
			self.logger.error(f"Failed to verify licence: {str(e)}")
			raise

	async def is_licence_valid(self, licence_number: str) -> bool:
		"""Check if a licence is valid (exists and not expired)"""
		try:
			today = date.today()
			licence = (
				self.db.query(Licence)
				.filter(Licence.licence_number == licence_number, Licence.expires_at > today)
				.first()
			)

			return licence is not None
		except Exception as e:
			self.logger.error(f"Failed to check licence validity: {str(e)}")
			return False

	async def stage_licence_renewal_data(self, organization_id: UUID4, form_data: dict) -> LicenceRenewal:
		"""Stage licence renewal form data and update organization status to RENEWAL_DRAFT"""
		with self.db.begin_nested():
			try:
				from src.modules.application.application_service import application_service

				existing_application = await application_service.get_licence_renewal_application(organization_id)

				if not existing_application:
					application = await application_service.create_licence_renewal(
						organization_id, form_data.get("annual_income", 0.0)
					)
				else:
					application = await application_service.get_application_by_id(existing_application.id)

				existing_staging = (
					self.db.query(LicenceRenewal).filter(LicenceRenewal.application_id == application.id).first()
				)

				if existing_staging:
					existing_staging.form_data = serialize_for_json(form_data)
					existing_staging.updated_by = self.current_user.id
					staging = existing_staging
				else:
					staging = LicenceRenewal(
						organization_id=organization_id,
						application_id=application.id,
						form_data=serialize_for_json(form_data),
						status=LicenceRenewalStatus.DRAFT,
						created_by=self.current_user.id,
					)
					self.db.add(staging)

				organization = self.db.query(Organization).filter(Organization.id == organization_id).first()
				if organization and organization.status != OrganizationStatus.RENEWAL_DRAFT:
					organization.status = OrganizationStatus.RENEWAL_DRAFT
					organization.updated_by = self.current_user.id

				self.db.flush()
				self.logger.info(f"Staged licence renewal data for organization {organization_id}")
				return staging

			except Exception as e:
				self.logger.error(f"Failed to stage licence renewal data: {str(e)}")
				raise

	async def get_licence_renewal_applications(self, organization_id: UUID4) -> Optional[LicenceRenewalDto]:
		"""Get staged licence renewal form data for organizations in RENEWAL_DRAFT or RENEWAL_IN_REVIEW status"""
		try:
			staging = (
				self.db.query(LicenceRenewal)
				.join(Application, LicenceRenewal.application_id == Application.id)
				.filter(
					LicenceRenewal.organization_id == organization_id,
					Application.status.in_([ApplicationStatus.DRAFT, ApplicationStatus.IN_REVIEW]),
					Application.type == ApplicationType.LICENCE_RENEWAL,
				)
				.order_by(LicenceRenewal.created_at.desc())
				.first()
			)

			if not staging:
				return None

			# Get application documents with their document types
			application_documents = (
				self.db.query(ApplicationDocument)
				.join(Document, ApplicationDocument.document_id == Document.id)
				.filter(ApplicationDocument.application_id == staging.application_id)
				.all()
			)

			# Convert to DTOs
			document_dtos = [to_application_document_dto(doc) for doc in application_documents]

			return to_licence_renewal_dto(staging, document_dtos)

		except Exception as e:
			self.logger.error(f"Failed to get staged licence renewal data: {str(e)}")
			raise

	async def get_staged_licence_renewal_data(self, organization_id: UUID4) -> Optional[LicenceRenewal]:
		"""Get staged licence renewal form data for organizations in RENEWAL_DRAFT or RENEWAL_IN_REVIEW status"""
		try:
			staging = (
				self.db.query(LicenceRenewal)
				.join(Application, LicenceRenewal.application_id == Application.id)
				.filter(
					LicenceRenewal.organization_id == organization_id,
					Application.status.in_([ApplicationStatus.DRAFT, ApplicationStatus.IN_REVIEW]),
					Application.type == ApplicationType.LICENCE_RENEWAL,
				)
				.order_by(LicenceRenewal.created_at.desc())
				.first()
			)

			return staging

		except Exception as e:
			self.logger.error(f"Failed to get staged licence renewal data: {str(e)}")
			raise

	async def submit_licence_renewal_for_review(self, organization_id: UUID4) -> None:
		"""Submit licence renewal for review and update organization status to RENEWAL_IN_REVIEW"""
		with self.db.begin_nested():
			try:
				# Use existing application service method to submit for review
				from src.modules.application.application_service import application_service

				await application_service.submit_licence_renewal_for_review(organization_id)

				# Update organization status to RENEWAL_IN_REVIEW
				organization = self.db.query(Organization).filter(Organization.id == organization_id).first()
				if organization:
					organization.status = OrganizationStatus.RENEWAL_IN_REVIEW
					organization.updated_by = self.current_user.id

				# Update staging status to SUBMITTED
				staging = (
					self.db.query(LicenceRenewal)
					.join(Application, LicenceRenewal.application_id == Application.id)
					.filter(
						LicenceRenewal.organization_id == organization_id,
						LicenceRenewal.status == LicenceRenewalStatus.DRAFT,
					)
					.first()
				)

				if staging:
					staging.status = LicenceRenewalStatus.SUBMITTED
					staging.updated_by = self.current_user.id

				self.db.flush()
				self.logger.info(f"Submitted licence renewal for review for organization {organization_id}")

			except Exception as e:
				self.logger.error(f"Failed to submit licence renewal for review: {str(e)}")
				raise

	async def complete_licence_renewal(self, organization_id: UUID4) -> None:
		"""Complete licence renewal process and reset organization status to REGISTERED"""
		with self.db.begin_nested():
			try:
				organization = self.db.query(Organization).filter(Organization.id == organization_id).first()
				if organization:
					organization.status = OrganizationStatus.REGISTERED
					organization.updated_by = self.current_user.id

				# Update staging status to PROCESSED
				staging = (
					self.db.query(LicenceRenewal)
					.join(Application, LicenceRenewal.application_id == Application.id)
					.filter(
						LicenceRenewal.organization_id == organization_id,
						LicenceRenewal.status == LicenceRenewalStatus.SUBMITTED,
					)
					.first()
				)

				if staging:
					staging.status = LicenceRenewalStatus.PROCESSED
					staging.updated_by = self.current_user.id

				self.db.flush()
				self.logger.info(f"Completed licence renewal for organization {organization_id}")

			except Exception as e:
				self.logger.error(f"Failed to complete licence renewal: {str(e)}")
				raise

	async def reject_licence_renewal(self, organization_id: UUID4) -> None:
		"""Reject licence renewal and update organization status"""
		try:
			organization = self.db.query(Organization).filter(Organization.id == organization_id).first()
			if organization:
				organization.status = OrganizationStatus.RENEWAL_REJECTED
				organization.updated_by = self.current_user.id

			self.db.flush()

			if not self.db.in_transaction() or not self.db.in_nested_transaction():
				self.db.commit()

			self.logger.info(f"Rejected licence renewal for organization {organization_id}")

		except Exception as e:
			self.logger.error(f"Failed to reject licence renewal: {str(e)}")
			raise

	async def _upload_certificate_document(
		self, certificate_file: BytesIO, filename: str, account_id: UUID4
	) -> Document:
		"""Upload certificate document to MinIO and create document record"""
		try:
			custom_filename = self.document_service._generate_custom_filename(filename)
			location = f"{account_id}/{custom_filename}"

			self.document_service.storage.upload_data(
				location, certificate_file.getvalue(), content_type="application/pdf"
			)

			document = Document(
				filename=custom_filename,
				mimetype="application/pdf",
				location=location,
				original_name=filename,
				size=len(certificate_file.getvalue()),
				created_by=self.current_user.id,
			)

			self.db.add(document)
			self.db.flush()

			return document

		except Exception as e:
			self.logger.error(f"Failed to upload certificate document: {str(e)}")
			raise

	async def _send_certificate_email(
		self, organization: Organization, certificate: Licence, certificate_pdf_bytes: bytes, filename: str
	):
		"""Send certificate via email to organization creator"""
		try:
			if not organization.created_by_user or not organization.created_by_user.email:
				self.logger.warning(f"No email found for organization creator: {organization.id}")
				return

			attachment = EmailAttachment(
				filename=filename, content=certificate_pdf_bytes, content_type="application/pdf"
			)
			recipient_name = f"{organization.created_by_user.first_name} {organization.created_by_user.last_name}"
			template_variables = {
				"recipient_name": recipient_name,
				"organization_name": organization.name,
				"licence_number": certificate.licence_number,
				"issue_date": certificate.created_at.strftime("%B %d, %Y"),
				"expires_at": certificate.expires_at.strftime("%B %d, %Y"),
				"verification_url": f"{settings.FRONTEND_URL}/verify?cert={certificate.licence_number}",
				"sender_email": settings.SENDER_EMAIL,
			}

			self.email_service.send_template_email(
				template=EmailTemplate.CERTIFICATE_DELIVERY,
				recipients=[
					EmailRecipient(
						email=organization.created_by_user.email,
						name=recipient_name,
					)
				],
				subject=f"Certificate of Registration - {organization.name}",
				sender_email=settings.SENDER_EMAIL,
				template_variables=template_variables,
				attachments=[attachment],
			)
		except Exception as e:
			self.logger.error(f"Failed to send certificate email: {str(e)}")
			# Don't raise the exception as this is not critical to certificate generation

	async def _send_licence_email(
		self, organization: Organization, licence: Licence, licence_pdf_bytes: bytes, filename: str
	):
		"""Send licence via email to organization creator"""
		try:
			if not organization.created_by_user or not organization.created_by_user.email:
				self.logger.warning(f"No email found for organization creator: {organization.id}")
				return

			attachment = EmailAttachment(filename=filename, content=licence_pdf_bytes, content_type="application/pdf")
			recipient_name = f"{organization.created_by_user.first_name} {organization.created_by_user.last_name}"
			template_variables = {
				"recipient_name": recipient_name,
				"organization_name": organization.name,
				"licence_number": licence.licence_number,
				"issue_date": licence.created_at.strftime("%B %d, %Y"),
				"expires_at": licence.expires_at.strftime("%B %d, %Y"),
				"verification_url": f"{settings.FRONTEND_URL}/verify?licence={licence.licence_number}",
				"sender_email": settings.SENDER_EMAIL,
			}

			self.email_service.send_template_email(
				template=EmailTemplate.LICENCE_DELIVERY,
				recipients=[
					EmailRecipient(
						email=organization.created_by_user.email,
						name=recipient_name,
					)
				],
				subject=f"Licence Renewal Approved - {organization.name}",
				sender_email=settings.SENDER_EMAIL,
				template_variables=template_variables,
				attachments=[attachment],
			)
		except Exception as e:
			self.logger.error(f"Failed to send licence email: {str(e)}")
			# Don't raise the exception as this is not critical to licence generation

	async def _replace_application_documents(
		self, application_id: UUID4, new_documents: SupportingDocumentRequest
	) -> None:
		"""Replace existing documents by voiding old ones and uploading new ones"""
		try:
			for index, doc_type_id in enumerate(new_documents.document_types):
				existing_app_doc = (
					self.db.query(ApplicationDocument)
					.filter(
						ApplicationDocument.application_id == application_id,
						ApplicationDocument.document_type_id == doc_type_id,
						~ApplicationDocument.voided,
					)
					.first()
				)

				if existing_app_doc:
					# Void the application document
					existing_app_doc.voided = True
					existing_app_doc.voided_by = self.current_user.id
					existing_app_doc.void_reason = "deprecated"
					self.db.add(existing_app_doc)

					# Also void the associated document
					if existing_app_doc.document:
						existing_app_doc.document.voided = True
						existing_app_doc.document.voided_by = self.current_user.id
						existing_app_doc.document.void_reason = "deprecated"
						self.db.add(existing_app_doc.document)

					self.logger.info(f"Voided existing document {existing_app_doc.id} of type {doc_type_id}")

			application = self.db.query(Application).filter(Application.id == application_id).first()
			if not application:
				raise ApiException("Application not found")

			for index, uploaded_document in enumerate(new_documents.documents):
				await self.document_service._validate_document_file(uploaded_document)
				document = await self.document_service._create_document_record(
					application, uploaded_document, application.organization.account_id
				)
				await self.document_service._create_application_document_link(
					document.id, application_id, new_documents.document_types[index]
				)
				self.logger.info(
					f"Created replacement document {document.id} of type {new_documents.document_types[index]}"
				)

			self.db.flush()

		except Exception as e:
			self.logger.error(f"Failed to replace application documents: {str(e)}")
			raise
