from typing import List

from fastapi import APIRouter, status

from src.core.dtos.application_dtos import ApplicationDto
from src.core.dtos.licence_dtos import LicenceDto, LicenceRenewalDto, LicenceVerificationDto
from src.core.shared_schema import BaseResponse
from src.modules.licence import licence_controller as controller

licence_router_v1 = APIRouter(tags=["licences"])

# LICENCE MANAGEMENT
licence_router_v1.add_api_route(
	path="/organizations/{organization_id}/licences",
	endpoint=controller.get_organization_licences_handler,
	methods=["GET"],
	response_model=BaseResponse[List[LicenceDto]],
	status_code=status.HTTP_200_OK,
)

licence_router_v1.add_api_route(
	path="/organizations/{organization_id}/licences/active",
	endpoint=controller.get_active_licence_handler,
	methods=["GET"],
	response_model=BaseResponse[LicenceDto],
	status_code=status.HTTP_200_OK,
)

# LICENCE RENEWAL
licence_router_v1.add_api_route(
	path="/organizations/{organization_id}/renewal",
	endpoint=controller.create_licence_renewal_handler,
	methods=["POST"],
	response_model=BaseResponse[ApplicationDto],
	status_code=status.HTTP_201_CREATED,
)

# licence_router_v1.add_api_route(
# 	path="/organizations/{organization_id}/renewal/submit",
# 	endpoint=controller.submit_licence_renewal_handler,
# 	methods=["POST"],
# 	response_model=BaseResponse[ApplicationDto],
# 	status_code=status.HTTP_200_OK,
# )

licence_router_v1.add_api_route(
	path="/organizations/{organization_id}/renewal",
	endpoint=controller.get_licence_renewal_handler,
	methods=["GET"],
	response_model=BaseResponse[LicenceRenewalDto],
	status_code=status.HTTP_200_OK,
)

licence_router_v1.add_api_route(
	path="/organizations/{organization_id}/renewal/eligible",
	endpoint=controller.can_create_licence_renewal_handler,
	methods=["GET"],
	response_model=BaseResponse[bool],
	status_code=status.HTTP_200_OK,
)

# LICENCE VERIFICATION
licence_router_v1.add_api_route(
	path="/verify/{licence_number}",
	endpoint=controller.verify_licence_handler,
	methods=["GET"],
	response_model=BaseResponse[LicenceVerificationDto],
	status_code=status.HTTP_200_OK,
)

licence_router_v1.add_api_route(
	path="/check/{licence_number}",
	endpoint=controller.check_licence_validity_handler,
	methods=["GET"],
	response_model=BaseResponse[dict],
	status_code=status.HTTP_200_OK,
)

# PUBLIC VERIFICATION (no authentication required)
licence_router_v1.add_api_route(
	path="/public/verify/{licence_number}",
	endpoint=controller.public_verify_licence_handler,
	methods=["GET"],
	response_model=BaseResponse[LicenceVerificationDto],
	status_code=status.HTTP_200_OK,
)
