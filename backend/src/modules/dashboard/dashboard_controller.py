from typing import List
from starlette.requests import Request

from src.core.guards.auth_guard import auth_guard
from src.core.shared_schema import BaseResponse
from .dashboard_schema import OrganizationStatisticsDto, RecentOrganizationDto
from .dashboard_service import DashboardService

dashboard_service = DashboardService()


@auth_guard.authenticated
async def get_organization_statistics_handler(_: Request) -> BaseResponse[OrganizationStatisticsDto]:
	"""
	Get comprehensive organization statistics for dashboard
	"""
	statistics = await dashboard_service.get_organization_statistics()
	return BaseResponse[OrganizationStatisticsDto](data=statistics)


@auth_guard.authenticated
async def get_recent_organizations_handler(_: Request, limit: int = 10) -> BaseResponse[List[RecentOrganizationDto]]:
	"""
	Get recently created organizations
	"""
	recent_organizations = await dashboard_service.get_recent_organizations(limit)
	return BaseResponse[List[RecentOrganizationDto]](data=recent_organizations)
