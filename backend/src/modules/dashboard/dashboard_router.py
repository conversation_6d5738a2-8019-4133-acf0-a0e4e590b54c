from fastapi import APIRouter
from typing import List

from src.core.shared_schema import BaseResponse
from .dashboard_schema import OrganizationStatisticsDto, RecentOrganizationDto
from .dashboard_controller import (
	get_organization_statistics_handler,
	get_recent_organizations_handler,
)

dashboard_router_v1 = APIRouter(tags=["dashboard"])


dashboard_router_v1.add_api_route(
	"/organizations/statistics",
	endpoint=get_organization_statistics_handler,
	methods=["GET"],
	response_model=BaseResponse[OrganizationStatisticsDto],
	summary="Get organization statistics",
	description="Get comprehensive organization statistics for dashboard including status distribution, income stats, and trends",
)

dashboard_router_v1.add_api_route(
	"/organizations/recent",
	endpoint=get_recent_organizations_handler,
	methods=["GET"],
	response_model=BaseResponse[List[RecentOrganizationDto]],
	summary="Get recent organizations",
	description="Get recently created organizations for dashboard",
)
