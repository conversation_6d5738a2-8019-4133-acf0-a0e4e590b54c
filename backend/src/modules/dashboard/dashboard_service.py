from datetime import datetime, timedelta
from typing import Any, Dict

from sqlalchemy import case, func

from src.config.db.models.application import Application, ApplicationStatus, ApplicationType
from src.config.db.models.licence import Licence
from src.config.db.models.licence_renewal import LicenceRenewal, LicenceRenewalStatus
from src.config.db.models.organization import Organization, OrganizationStatus
from src.config.db.models.workflow import Workflow
from src.core.base.base_repository import BaseRepository
from src.core.logger.internal_logger import get_logger


class DashboardService(BaseRepository):
	def __init__(self):
		super().__init__()
		self.logger = get_logger(__name__)

	async def get_organization_statistics(self) -> Dict[str, Any]:
		"""
		Get comprehensive organization statistics for dashboard
		"""
		try:
			thirty_days_ago = datetime.now() - timedelta(days=30)
			twelve_months_ago = datetime.now() - timedelta(days=365)

			total_organizations = self.db.query(Organization).count()

			status_counts = (
				self.db.query(Organization.status, func.count(Organization.id).label("count"))
				.group_by(Organization.status)
				.all()
			)

			recent_organizations = (
				self.db.query(func.count(Organization.id)).filter(Organization.created_at >= thirty_days_ago).scalar()
				or 0
			)

			total_applications = (
				self.db.query(Application).filter(Application.type == ApplicationType.ORGANIZATION_REGISTRATION).count()
			)

			application_status_counts = (
				self.db.query(Application.status, func.count(Application.id).label("count"))
				.filter(Application.type == ApplicationType.ORGANIZATION_REGISTRATION)
				.group_by(Application.status)
				.all()
			)

			active_workflows = (
				self.db.query(Workflow)
				.join(Application, Workflow.application_id == Application.id)
				.filter(Application.type == ApplicationType.ORGANIZATION_REGISTRATION)
				.filter(Application.status == ApplicationStatus.IN_REVIEW)
				.count()
			)

			organization_types = (
				self.db.query(Organization.organization_type_id, func.count(Organization.id).label("count"))
				.group_by(Organization.organization_type_id)
				.all()
			)

			district_distribution = (
				self.db.query(Organization.district_id, func.count(Organization.id).label("count"))
				.group_by(Organization.district_id)
				.all()
			)

			income_stats = self.db.query(
				func.count(Organization.id).label("total"),
				func.avg(Organization.annual_income).label("average"),
				func.sum(Organization.annual_income).label("total_income"),
				func.min(Organization.annual_income).label("minimum"),
				func.max(Organization.annual_income).label("maximum"),
			).first()

			monthly_registrations = (
				self.db.query(
					func.date_trunc("month", Organization.created_at).label("month"),
					func.count(Organization.id).label("count"),
				)
				.filter(Organization.created_at >= twelve_months_ago)
				.group_by(func.date_trunc("month", Organization.created_at))
				.order_by(func.date_trunc("month", Organization.created_at))
				.all()
			)

			processing_stats = (
				self.db.query(
					func.avg(func.extract("epoch", Application.updated_at - Application.created_at) / 86400).label(
						"avg_processing_days"
					),
					func.count(case((Application.status == ApplicationStatus.APPROVED, 1))).label("approved_count"),
					func.count(case((Application.status == ApplicationStatus.REJECTED, 1))).label("rejected_count"),
				)
				.filter(Application.type == ApplicationType.ORGANIZATION_REGISTRATION)
				.filter(Application.status.in_([ApplicationStatus.APPROVED, ApplicationStatus.REJECTED]))
				.first()
			)

			# License renewal statistics
			current_date = datetime.now().date()
			ninety_days_from_now = current_date + timedelta(days=90)
			current_year_start = datetime(current_date.year, 1, 1).date()

			# Organizations due for renewal (licenses expiring in next 90 days)
			due_for_renewal = (
				self.db.query(func.count(Licence.id))
				.join(Organization, Licence.organization_id == Organization.id)
				.filter(Organization.status == OrganizationStatus.REGISTERED)
				.filter(Licence.expires_at <= ninety_days_from_now)
				.filter(Licence.expires_at >= current_date)
				.scalar()
				or 0
			)

			# Pending renewals (organizations with renewal applications in progress)
			pending_renewals = (
				self.db.query(func.count(LicenceRenewal.id))
				.filter(LicenceRenewal.status == LicenceRenewalStatus.SUBMITTED)
				.scalar()
				or 0
			)

			# Completed renewals this year
			completed_this_year = (
				self.db.query(func.count(LicenceRenewal.id))
				.filter(LicenceRenewal.status == LicenceRenewalStatus.PROCESSED)
				.filter(LicenceRenewal.created_at >= current_year_start)
				.scalar()
				or 0
			)

			statistics = {
				"overview": {
					"total_organizations": total_organizations,
					"recent_organizations": recent_organizations,
					"total_applications": total_applications,
					"active_workflows": active_workflows,
				},
				"organization_status": {
					status.value if hasattr(status, "value") else status: count for status, count in status_counts
				},
				"application_status": {
					status.value if hasattr(status, "value") else status: count
					for status, count in application_status_counts
				},
				"organization_types": {str(type_id): count for type_id, count in organization_types},
				"district_distribution": {str(district_id): count for district_id, count in district_distribution},
				"income_statistics": {
					"total_organizations": income_stats.total or 0 if income_stats else 0,
					"average_income": float(income_stats.average or 0)
					if income_stats and income_stats.average
					else 0.0,
					"total_income": float(income_stats.total_income or 0)
					if income_stats and income_stats.total_income
					else 0.0,
					"minimum_income": float(income_stats.minimum or 0)
					if income_stats and income_stats.minimum
					else 0.0,
					"maximum_income": float(income_stats.maximum or 0)
					if income_stats and income_stats.maximum
					else 0.0,
				},
				"monthly_registrations": [
					{"month": month.strftime("%Y-%m") if month else None, "count": count}
					for month, count in monthly_registrations
				],
				"processing_statistics": {
					"average_processing_days": float(processing_stats.avg_processing_days or 0)
					if processing_stats
					else 0.0,
					"approved_count": processing_stats.approved_count or 0 if processing_stats else 0,
					"rejected_count": processing_stats.rejected_count or 0 if processing_stats else 0,
					"approval_rate": (
						(
							(processing_stats.approved_count or 0)
							/ max((processing_stats.approved_count or 0) + (processing_stats.rejected_count or 0), 1)
							* 100
						)
						if processing_stats
						else 0.0
					),
				},
				"renewal_statistics": {
					"due_for_renewal": due_for_renewal,
					"pending_renewals": pending_renewals,
					"completed_this_year": completed_this_year,
				},
			}

			return statistics

		except Exception as e:
			self.logger.error(f"Failed to get organization statistics: {str(e)}")
			if self.db.in_transaction():
				self.db.rollback()

			return {
				"overview": {
					"total_organizations": 0,
					"recent_organizations": 0,
					"total_applications": 0,
					"active_workflows": 0,
				},
				"organization_status": {},
				"application_status": {},
				"organization_types": {},
				"district_distribution": {},
				"income_statistics": {
					"total_organizations": 0,
					"average_income": 0.0,
					"total_income": 0.0,
					"minimum_income": 0.0,
					"maximum_income": 0.0,
				},
				"monthly_registrations": [],
				"processing_statistics": {
					"average_processing_days": 0.0,
					"approved_count": 0,
					"rejected_count": 0,
					"approval_rate": 0.0,
				},
				"renewal_statistics": {
					"due_for_renewal": 0,
					"pending_renewals": 0,
					"completed_this_year": 0,
				},
			}

	async def get_recent_organizations(self, limit: int = 10) -> list:
		"""
		Get recently created organizations
		"""
		try:
			recent_orgs = self.db.query(Organization).order_by(Organization.created_at.desc()).limit(limit).all()

			return [
				{
					"id": org.id,
					"name": org.name,
					"abbreviation": org.abbreviation,
					"status": org.status.value if hasattr(org.status, "value") else org.status,
					"created_at": org.created_at.isoformat() if org.created_at else None,
					"annual_income": float(org.annual_income or 0),
					"registration_number": org.registration_number,
				}
				for org in recent_orgs
			]

		except Exception as e:
			self.logger.error(f"Failed to get recent organizations: {str(e)}")
			if self.db.in_transaction():
				self.db.rollback()
			return []
