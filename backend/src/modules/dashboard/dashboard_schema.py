from typing import Dict, List, Optional
from pydantic import BaseModel
from datetime import datetime


class OrganizationOverviewDto(BaseModel):
	total_organizations: int
	recent_organizations: int
	total_applications: int
	active_workflows: int


class IncomeStatisticsDto(BaseModel):
	total_organizations: int
	average_income: float
	total_income: float
	minimum_income: float
	maximum_income: float


class MonthlyRegistrationDto(BaseModel):
	month: Optional[str]
	count: int


class ProcessingStatisticsDto(BaseModel):
	average_processing_days: float
	approved_count: int
	rejected_count: int
	approval_rate: float


class OrganizationStatisticsDto(BaseModel):
	overview: OrganizationOverviewDto
	organization_status: Dict[str, int]
	application_status: Dict[str, int]
	organization_types: Dict[str, int]
	district_distribution: Dict[str, int]
	income_statistics: IncomeStatisticsDto
	monthly_registrations: List[MonthlyRegistrationDto]
	processing_statistics: ProcessingStatisticsDto


class RecentOrganizationDto(BaseModel):
	id: str
	name: str
	abbreviation: str
	status: str
	created_at: Optional[str]
	annual_income: float
	registration_number: str


class DashboardFilter(BaseModel):
	"""Filter for dashboard data"""

	start_date: Optional[datetime] = None
	end_date: Optional[datetime] = None
	status: Optional[str] = None
	district_id: Optional[str] = None
	organization_type_id: Optional[str] = None
