from fastapi import APIRouter, status

from src.config.db.models.base import InvitationStatus
from .activity_invitation_controller import (
    create_activity_invitations_handler,
    delete_activity_invitation_handler,
    list_activity_invitations_handler,
    update_activity_invitation_resend_handler,
    update_activity_invitation_status_handler,
)
from .activity_invitation_schema import ActivityInvitationDto

router = APIRouter(tags=["activity_invitations"])

# GET /v1/activities/{activity_id}/invitations
router.add_api_route(
    path="/activities/{activity_id}/invitations",
    endpoint=list_activity_invitations_handler,
    response_model=list[ActivityInvitationDto],
    methods=["GET"],
    status_code=status.HTTP_200_OK,
    description="List invitations for an activity",
)

# POST /v1/activities/{activity_id}/invitations
router.add_api_route(
    path="/activities/{activity_id}/invitations",
    endpoint=create_activity_invitations_handler,
    response_model=list[ActivityInvitationDto],
    methods=["POST"],
    status_code=status.HTTP_201_CREATED,
    description="Create invitations for an activity",
)

# PUT /v1/activities/{activity_id}/invitations/{invitation_id}/status/{status_value}
router.add_api_route(
    path="/activities/{activity_id}/invitations/{invitation_id}/status/{status_value}",
    endpoint=update_activity_invitation_status_handler,
    response_model=ActivityInvitationDto,
    methods=["PUT"],
    status_code=status.HTTP_200_OK,
    description="Update invitation status",
)


router.add_api_route(
    path="/activities/{activity_id}/invitations/{invitation_id}/resend",
    endpoint=update_activity_invitation_resend_handler,
    response_model=ActivityInvitationDto,
    methods=["POST"],
    status_code=status.HTTP_200_OK,
    description="Update invitation status",
)

# DELETE /v1/activities/{activity_id}/invitations/{invitation_id}
router.add_api_route(
    path="/activities/{activity_id}/invitations/{invitation_id}",
    endpoint=delete_activity_invitation_handler,
    methods=["DELETE"],
    status_code=status.HTTP_204_NO_CONTENT,
    description="Delete an invitation",
)
