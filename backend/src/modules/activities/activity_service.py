from uuid import UUID

from fastapi_pagination import Params
from fastapi_pagination.ext.sqlalchemy import paginate
from sqlalchemy import and_

from src.config.db.models import Activity
from src.core.base.base_repository import BaseRepository
from src.core.dtos.activity_dtos import to_activity_dto
from src.core.exceptions.api import ApiException
from src.core.shared_schema import CurrentUser, Pagination
from src.core.utils import serializer

from .activity_schema import ActivityDto, ActivityFilter, ActivityRequest


class ActivityService(BaseRepository):
	def __init__(self):
		super().__init__()

	async def find_activities(self, filter: ActivityFilter) -> Pagination[ActivityDto]:
		"""
		Find activities based on filters.

		Args:
		    filters (ActivityFilter): Filters to apply.

		Returns:
		    list: List of activities matching the criteria.
		"""
		partial_filters = ["title", "venue"]

		try:
			filter_dict = filter.model_dump(exclude_unset=True)
			pfilters = {k: v for k, v in filter_dict.items() if k in partial_filters and v is not None}

			query = self.db.query(Activity).order_by(Activity.start_date.desc())

			for field in partial_filters:
				if field in pfilters:
					value = pfilters.pop(field)
					query = query.filter(getattr(Activity, field).ilike(f"%{value}%"))

			result = paginate(query, Params(page=filter.page, size=filter.size))
			data = [to_activity_dto(row) for row in result.items]

			return Pagination.from_query_result(data, result)

		except Exception as e:
			self.logger.error(f"Error finding activities: {e}")
			raise e

	def get_activity_by_id(self, activity_id: UUID) -> ActivityDto:
		try:
			activity = self.db.query(Activity).filter(and_(Activity.id == activity_id, not Activity.voided)).first()

			if not activity:
				raise ApiException("Activity not found")

			return serializer.to_activity_dto(activity)
		except Exception as e:
			self.logger.error(f"Error finding activities: {e}")
			raise e

	def create_activity(self, data: ActivityRequest) -> ActivityDto:
		try:
			activity_data = data.model_dump()
			activity = Activity(**activity_data)
			self.db.add(activity)
			self.db.commit()
			self.db.refresh(activity)
			return serializer.to_activity_dto(activity)
		except Exception as e:
			self.db.rollback()
			self.logger.error(f"Error creating activity: {e}")
			raise e

	async def update_activity(self, id: UUID, data: ActivityRequest) -> ActivityDto:
		try:
			activity = self.db.query(Activity).filter(and_(Activity.id == id)).first()

			if not activity:
				raise ApiException("Activity not found")

			for key, value in data.model_dump().items():
				if key == "id":
					continue
				setattr(activity, key, value)

			self.db.commit()
			self.db.refresh(activity)
			return serializer.to_activity_dto(activity)

		except Exception as e:
			self.db.rollback()
			self.logger.error(f"Error updating activity: {e}")
			raise e

	async def delete_activity(self, activity_id: UUID) -> bool:
		try:
			activity = self.db.query(Activity).filter(and_(Activity.id == activity_id)).first()
			if not activity:
				raise ApiException("Activity not found")

			activity.voided = True
			self.db.commit()
			return True
		except Exception as e:
			self.db.rollback()
			self.logger.error(f"Error deleting activity: {e}")
			raise e
