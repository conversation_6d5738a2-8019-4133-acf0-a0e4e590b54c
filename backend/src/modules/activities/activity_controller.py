from uuid import UUID

from fastapi import Depends, HTTPException, Request, status
from fastapi_pagination.ext.sqlalchemy import paginate

from src.core.exceptions.api import ApiException
from src.core.guards.auth_guard import auth_guard
from src.core.shared_schema import CurrentUser, Pagination

from .activity_schema import ActivityDto, ActivityFilter, ActivityRequest
from .activity_service import ActivityService

activity_service = ActivityService()


async def fetch_activities_handler(
    _: Request,
	filters: ActivityFilter = Depends(ActivityFilter)
) -> Pagination[ActivityDto]:
	try:
		return await activity_service.find_activities(filters)
	except Exception as e:
		raise ApiException(str(e))


def get_activity_handler(
	id: UUID,
	user: CurrentUser = Depends(auth_guard.current_user),
) -> ActivityDto:
	try:
		return activity_service.get_activity_by_id(id)
	except Exception as e:
		raise HTTPException(status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))


def create_activity_handler(
	_: Request, data: ActivityRequest
) -> ActivityDto:
	try:
		return activity_service.create_activity(data)
	except Exception as e:
		raise ApiException(f"Failed to create activity: {str(e)}")


async def update_activity_handler(
    _: Request,
	id: UUID,
	data: ActivityRequest
) -> ActivityDto:
	try:
		return await activity_service.update_activity(id, data)
	except Exception as e:
		raise ApiException(str(e))


async def delete_activity_handler(
    _: Request,
	id: UUID,
) -> None:
	try:
		return await activity_service.delete_activity(id)
	except Exception as e:
		raise ApiException(str(e))
