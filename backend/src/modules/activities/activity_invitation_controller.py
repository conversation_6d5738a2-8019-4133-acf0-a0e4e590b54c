from typing import List
from uuid import UUID

from fastapi import Depends, HTTPException, Request, status

from src.config.db.models.base import InvitationStatus
from src.core.exceptions.api import ApiException
from src.core.guards.auth_guard import auth_guard
from src.core.shared_schema import CurrentUser
from .activity_invitation_schema import ActivityInvitationDto, ActivityInvitationFilter, ActivityInvitationRequest
from .activity_invitation_service import ActivityInvitationService

service = ActivityInvitationService()


def list_activity_invitations_handler(
    _: Request,
    activity_id: UUID,
    filters: ActivityInvitationFilter = Depends(ActivityInvitationFilter),
) -> List[ActivityInvitationDto]:
    try:
        return service.list_invitations(activity_id, filters)
    except Exception as e:
        raise ApiException(str(e))


async def create_activity_invitations_handler(
    _: Request,
    activity_id: UUID,
    invitations: List[ActivityInvitationRequest]
) -> List[ActivityInvitationDto]:
    try:
        return await service.create_invitations(activity_id, invitations)
    except Exception as e:
        raise ApiException(str(e))


def update_activity_invitation_status_handler(
    _: Request,
    activity_id: UUID,
    invitation_id: UUID,
    status_value: InvitationStatus,
) -> ActivityInvitationDto:
    try:
        return service.update_invitation_status(activity_id, invitation_id, status_value)
    except Exception as e:
        raise ApiException(str(e))


async def delete_activity_invitation_handler(
    _: Request,
    activity_id: UUID,
    invitation_id: UUID,
) -> None:
    try:
        return await service.delete_invitation(activity_id, invitation_id)
    except Exception as e:
        raise ApiException(str(e))
    
    
async def update_activity_invitation_resend_handler(
    _: Request,
    activity_id: UUID,
    invitation_id: UUID,
) -> ActivityInvitationDto:
    try:
        return await service.resend_invitation(activity_id, invitation_id)
    except Exception as e:
        raise ApiException(str(e))
