from enum import Enum
from typing import Optional

from pydantic import BaseModel, UUID4, EmailStr, Field

from src.config.db.models.account import AccountType
from src.config.db.models.base import InvitationStatus
from src.core.shared_schema import BaseRequest


class InviteeType(str, Enum):
    INTERNAL = "INTERNAL"
    EXTERNAL = "EXTERNAL"


class ActivityInvitationDto(BaseModel):
    id: UUID4
    activity_id: UUID4
    status: InvitationStatus
    type: InviteeType
    account_id: Optional[UUID4] = None
    email: Optional[EmailStr] = None
    name: Optional[str] = None
    account_type: Optional[AccountType] = None
    created_at: Optional[str] = None
    updated_at: Optional[str] = None


class ActivityInvitationRequest(BaseModel):
    """
    Create/Update request for an activity invitation.
    For INTERNAL invitations, provide one of: account_id | user_id | organization_id
    For EXTERNAL invitations, provide: email, optional name and account_type (ORG/USER)
    """

    type: InviteeType = Field(..., description="INTERNAL or EXTERNAL")

    # INTERNAL - any entity within the system
    account_id: Optional[UUID4] = Field(default=None, description="Account ID for internal invitation")
    user_id: Optional[UUID4] = Field(default=None, description="User ID for internal invitation")
    organization_id: Optional[UUID4] = Field(default=None, description="Organization ID for internal invitation")

    # EXTERNAL - outsider invite via email
    email: Optional[EmailStr] = None
    name: Optional[str] = None
    account_type: Optional[AccountType] = Field(default=AccountType.USER, description="ORG or USER")


class ActivityInvitationFilter(BaseRequest):
    status: Optional[InvitationStatus] = None
    search: Optional[str] = Field(default=None, description="Search by email or name for external invites")
