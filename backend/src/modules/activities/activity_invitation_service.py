from datetime import datetime
from typing import List, Optional
from uuid import UUID

from sqlalchemy import and_, literal, or_
from sqlalchemy.orm import joinedload

from src.config import settings
from src.config.db.models import Account, Activity, ActivityInvitation, InviteeType, Organization, User
from src.config.db.models.account import AccountType, AccountType
from src.config.db.models.base import InvitationStatus
from src.config.db.models.contact import Contact, ContactType
from src.core.base.base_repository import BaseRepository
from src.core.exceptions.api import ApiException
from src.core.logger.internal_logger import get_logger
from src.core.services.email_service import EmailRecipient, EmailTemplate, _get_email_service_instance
from src.core.services.notification_service import NotificationService
from src.core.utils import serializer
from .activity_invitation_schema import ActivityInvitationDto, ActivityInvitationFilter, ActivityInvitationRequest


class ActivityInvitationService(BaseRepository):
    def __init__(self):
        super().__init__()
        self.logger = get_logger(__name__)
        self._email_service = None
        self.notification_service = NotificationService()
        
    @property
    def email_service(self):
        """Lazy-loaded EmailService"""
        if self._email_service is None:
            self._email_service = _get_email_service_instance()
        return self._email_service

    def _get_account_id(self, user_id: Optional[UUID], organization_id: Optional[UUID], account_id: Optional[UUID]) -> UUID:
        if account_id:
            acc = self.db.query(Account).filter(Account.id == account_id).first()
            if not acc:
                raise ApiException("Account not found")
            return acc.id

        if user_id:
            user = self.db.query(User).filter(User.id == user_id).first()
            if not user:
                raise ApiException("User not found")
            return user.account_id

        if organization_id:
            org = self.db.query(Organization).filter(Organization.id == organization_id).first()
            if not org:
                raise ApiException("Organization not found")
            return org.account_id

        raise ApiException("Missing account reference for INTERNAL invitation")

    def _ensure_activity(self, activity_id: UUID) -> Activity:
        activity = self.db.query(Activity).filter(and_(Activity.id == activity_id, ~Activity.voided)).first()
        if not activity:
            raise ApiException("Activity not found")
        return activity

    def list_invitations(self, activity_id: UUID, filters: ActivityInvitationFilter) -> List[ActivityInvitationDto]:
        try:
            self._ensure_activity(activity_id)

            query = (
                self.db.query(ActivityInvitation)
                .options(joinedload(ActivityInvitation.account))
                .filter(and_(ActivityInvitation.activity_id == activity_id, ~ActivityInvitation.voided))
                .order_by(ActivityInvitation.created_at.desc())
            )

            if filters.status:
                query = query.filter(ActivityInvitation.status == filters.status)

            if filters.search:
                s = f"%{filters.search}%"
                query = query.filter(
                    or_(ActivityInvitation.email.ilike(s), ActivityInvitation.name.ilike(s))
                )

            rows = query.all()
            return [serializer.to_activity_invitation_dto(row) for row in rows]
        except Exception as e:
            self.logger.error(f"Failed to list invitations: {e}")
            raise

    async def create_invitations(self, activity_id: UUID, requests: List[ActivityInvitationRequest]) -> List[ActivityInvitationDto]:
        try:
            activity = self._ensure_activity(activity_id)

            created: List[ActivityInvitation] = []

            for req in requests:
                if req.type == InviteeType.INTERNAL:
                    acc_id = self._get_account_id(req.user_id, req.organization_id, req.account_id)
                    
                    # First attempt: try getting from User
                    account = (
                        self.db.query(
                            User.first_name.label("first_name"),
                            User.last_name.label("last_name"),
                            User.email.label("email")
                        )
                        .filter(User.account_id == acc_id)
                        .first()
                    )

                    # If no User, fall back to Organization + Contact
                    if not account:
                        account = (
                            self.db.query(
                                Organization.name.label("first_name"),  
                                literal(None).label("last_name"),       
                                Contact.details.label("email")          
                            )
                            .outerjoin(
                                Contact,
                                and_(
                                    Organization.account_id == Contact.account_id,
                                    Contact.type == ContactType.EMAIL
                                )
                            )
                            .filter(Organization.account_id == acc_id)
                            .first()
                        )
                    
                    email = account.email
                    first_name = account.first_name
                    last_name = account.last_name
                    
                    if not email:
                        self.logger.warning(f"Account {first_name} has no email. Skipping invitation.")
                        continue
                    

                    # de-duplicate pending invites to same account
                    existing = (
                        self.db.query(ActivityInvitation)
                        .filter(
                            and_(
                                ActivityInvitation.activity_id == activity_id,
                                ActivityInvitation.type == InviteeType.INTERNAL,
                                ActivityInvitation.account_id == acc_id,
                                ActivityInvitation.status == InvitationStatus.PENDING,
                            )
                        )
                        .first()
                    )
                    if existing:
                        continue

                    inv = ActivityInvitation(
                        activity_id=activity_id,
                        type=InviteeType.INTERNAL,
                        status=InvitationStatus.PENDING,
                        account_id=acc_id,
                        created_by=self.current_user_id,
                        updated_by=self.current_user_id,
                        account_type=AccountType.ORG if req.organization_id else AccountType.USER,
                        email=email,
                        name=f"{first_name} {last_name}",
                    )
                    self.db.add(inv)
                    self.db.flush()
                    created.append(inv)

                    # Notify internal account
                    try:
                        self.notification_service.create_notification(
                            title=f"Invitation to activity: {activity.title}",
                            message=f"You have been invited to attend '{activity.title}'.",
                            recipient_account_ids=[acc_id],
                        )
                    except Exception as ne:
                        self.logger.warning(f"Failed to send notification: {ne}")

                else:  # EXTERNAL
                    if not req.email:
                        raise ApiException("External invitation requires email")

                    # prevent duplicate pending invite for same external email
                    existing_ext = (
                        self.db.query(ActivityInvitation)
                        .filter(
                            and_(
                                ActivityInvitation.activity_id == activity_id,
                                ActivityInvitation.type == InviteeType.EXTERNAL,
                                ActivityInvitation.email == req.email,
                                ActivityInvitation.status == InvitationStatus.PENDING,
                            )
                        )
                        .first()
                    )
                    if existing_ext:
                        continue

                    inv = ActivityInvitation(
                        activity_id=activity_id,
                        type=InviteeType.EXTERNAL,
                        status=InvitationStatus.PENDING,
                        email=req.email,
                        name=req.name,
                        account_type=AccountType.USER if req.user_id else AccountType.ORG,
                        created_by=self.current_user_id,
                        updated_by=self.current_user_id,
                    )
                    self.db.add(inv)
                    self.db.flush()
                    created.append(inv)

            if not self.db.in_transaction() or not self.db.in_nested_transaction():
                self.db.commit()

            return [serializer.to_activity_invitation_dto(row) for row in created]
        except ApiException:
            self.db.rollback()
            raise
        except Exception as e:
            self.db.rollback()
            self.logger.error(f"Failed to create invitations: {e}")
            raise

    def update_invitation_status(self, activity_id: UUID, invitation_id: UUID, status: InvitationStatus) -> ActivityInvitationDto:
        try:
            self._ensure_activity(activity_id)
            inv = (
                self.db.query(ActivityInvitation)
                .filter(
                    and_(
                        ActivityInvitation.id == invitation_id,
                        ActivityInvitation.activity_id == activity_id,
                        ~ActivityInvitation.voided,
                    )
                )
                .first()
            )
            if not inv:
                raise ApiException("Invitation not found")

            inv.status = status
            inv.updated_by = self.current_user_id
            inv.updated_at = datetime.utcnow()
            self.db.flush()
            self.db.commit()

            return serializer.to_activity_invitation_dto(inv)
        except ApiException:
            self.db.rollback()
            raise
        except Exception as e:
            self.db.rollback()
            self.logger.error(f"Failed to update invitation: {e}")
            raise

    async def delete_invitation(self, activity_id: UUID, invitation_id: UUID) -> bool:
        try:
            self._ensure_activity(activity_id)
            inv = (
                self.db.query(ActivityInvitation)
                .filter(
                    and_(
                        ActivityInvitation.id == invitation_id,
                        ActivityInvitation.activity_id == activity_id,
                    )
                )
                .first()
            )
            if not inv:
                raise ApiException("Invitation not found")

            inv.voided = True
            inv.voided_by = self.current_user_id
            inv.void_reason = "Deleted by user"
            self.db.flush()
            self.db.commit()
            return True
        except Exception as e:
            self.db.rollback()
            self.logger.error(f"Failed to delete invitation: {e}")
            raise
        
    async def resend_invitation(self, activity_id: UUID, invitation_id: UUID) -> ActivityInvitationDto:
        try:
            self._ensure_activity(activity_id)
            inv = (
                self.db.query(ActivityInvitation)
                .filter(
                    and_(
                        ActivityInvitation.id == invitation_id,
                        ActivityInvitation.activity_id == activity_id,
                    )
                )
                .first()
            )
            if not inv:
                raise ApiException("Invitation not found")
            if inv.status != InvitationStatus.PENDING:
                raise ApiException("Cannot resend invitation")
            
            self.email_service.send_template_email(
                template=EmailTemplate.ACTIVITY_INVITATION,
                recipients=[EmailRecipient(email=inv.email)],
                subject=f"Invitation to activity: {inv.activity.title}",
                sender_email=settings.SENDER_EMAIL,
                template_variables={
                    "activity_title": inv.activity.title,
                    "activity_date": inv.activity.start_date.strftime("%B %d, %Y"),
                    "activity_start_time": inv.activity.start_date.strftime("%I:%M %p"),
                    "activity_end_time": inv.activity.end_date.strftime("%I:%M %p"),
                    "activity_venue": inv.activity.venue,
                    "activity_summary": inv.activity.summary,
                    "activity_link": f"{settings.FRONTEND_URL}/activities/{inv.activity.id}",
                    "app_name": settings.APP_NAME,
                    "app_url": settings.FRONTEND_URL,
                    "support_url": f"{settings.FRONTEND_URL}/support",
                    "year": datetime.now().year
                },
            )
            
            return serializer.to_activity_invitation_dto(inv)
        except Exception as e:
            self.logger.error(f"Failed to resend invitation: {e}")
            raise
        
