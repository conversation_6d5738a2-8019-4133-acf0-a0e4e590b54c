from datetime import date
from typing import Dict, List, Optional

from fastapi import Depends, Query
from pydantic import UUID4
from starlette.requests import Request

from src.core.dtos.financial_dtos import (
	CreatePaymentRequest,
	InvoiceDto,
	InvoiceItemDto,
	MarkInvoiceAsPaidRequest,
	PaymentDto,
	PaymentVerificationRequest,
)
from src.core.exceptions.api import ApiException
from src.core.guards.auth_guard import auth_guard
from src.core.logger.internal_logger import get_logger
from src.core.shared_schema import BaseResponse, Pagination
from src.modules.financial.fee_service import FeeService
from src.modules.financial.financial_analytics_service import FinancialAnalyticsService
from src.modules.financial.financial_reports_service import FinancialReportsService
from src.modules.financial.financial_schema import InvoiceFilter, PaymentFilter
from src.modules.financial.invoice_service import InvoiceService
from src.modules.financial.organization_financial_service import OrganizationFinancialService
from src.modules.financial.payment_service import PaymentService

logger = get_logger(__name__)


def get_invoice_service() -> InvoiceService:
	"""Get InvoiceService instance"""
	return InvoiceService()


def get_payment_service() -> PaymentService:
	"""Get PaymentService instance"""
	return PaymentService()


def get_financial_analytics_service() -> FinancialAnalyticsService:
	"""Get FinancialAnalyticsService instance"""
	return FinancialAnalyticsService()


def get_organization_financial_service() -> OrganizationFinancialService:
	"""Get OrganizationFinancialService instance"""
	return OrganizationFinancialService()


def get_financial_reports_service() -> FinancialReportsService:
	"""Get FinancialReportsService instance"""
	return FinancialReportsService()


def get_fee_service() -> FeeService:
	"""Get FeeService instance"""
	return FeeService()


# INVOICE MANAGEMENT


# @auth_guard.authenticated
async def fetch_invoices_handler(
	_: Request,
	filter: InvoiceFilter = Depends(InvoiceFilter),
) -> Pagination[InvoiceDto]:
	invoice_service = get_invoice_service()
	pagination = await invoice_service.retrieve_invoices(filter)
	return pagination


@auth_guard.authenticated
async def get_invoice_by_application_handler(_: Request, application_id: UUID4) -> BaseResponse[InvoiceDto]:
	"""Get invoice for a specific application"""
	invoice_service = get_invoice_service()
	invoice = await invoice_service.get_invoice_by_application_id(application_id)
	if not invoice:
		raise ApiException("Invoice not found for this application")
	return BaseResponse[InvoiceDto](data=invoice)


@auth_guard.authenticated
async def get_invoice_items_by_application_handler(
	_: Request, application_id: UUID4
) -> BaseResponse[List[InvoiceItemDto]]:
	"""Get all invoice items for a specific application"""
	invoice_service = get_invoice_service()
	invoice_item = await invoice_service.get_first_invoice_item_by_application_id(application_id)
	if not invoice_item:
		return BaseResponse[List[InvoiceItemDto]](data=[])
	return BaseResponse[List[InvoiceItemDto]](data=[invoice_item])


@auth_guard.authenticated
async def generate_invoice_pdf_handler(_: Request, invoice_id: UUID4) -> BaseResponse[Dict]:
	"""Generate PDF for an invoice"""
	invoice_service = get_invoice_service()
	pdf_bytes, invoice_reference = await invoice_service.generate_invoice_pdf(invoice_id)

	import base64

	pdf_base64 = base64.b64encode(pdf_bytes).decode("utf-8")

	return BaseResponse[Dict](
		data={
			"invoice_id": str(invoice_id),
			"pdf_content": pdf_base64,
			"content_type": "application/pdf",
			"filename": f"{invoice_reference}.pdf",
		}
	)


@auth_guard.authenticated
async def mark_invoice_as_paid_handler(
	_: Request, invoice_id: UUID4, request: MarkInvoiceAsPaidRequest
) -> BaseResponse[InvoiceDto]:
	"""Mark invoice as paid with payment mode"""
	invoice_service = get_invoice_service()
	invoice = await invoice_service.mark_invoice_as_paid(invoice_id, request.payment_mode_id)
	return BaseResponse[InvoiceDto](data=invoice)


# PAYMENT MANAGEMENT
@auth_guard.authenticated
async def get_payments_handler(_: Request, filter: PaymentFilter = Depends(PaymentFilter)) -> Pagination[PaymentDto]:
	"""Get all payments with optional filters"""
	payment_service = get_payment_service()
	payments = await payment_service.retrieve_payments(filter)
	return payments


@auth_guard.authenticated
async def create_payment_handler(_: Request, request: CreatePaymentRequest) -> BaseResponse[PaymentDto]:
	"""Create a new payment record"""
	payment_service = get_payment_service()
	payment = await payment_service.create_payment(request)
	return BaseResponse[PaymentDto](data=payment)


@auth_guard.authenticated
async def verify_payment_handler(_: Request, request: PaymentVerificationRequest) -> BaseResponse[PaymentDto]:
	"""Verify a payment transaction"""
	payment_service = get_payment_service()
	payment = await payment_service.verify_payment(request)
	return BaseResponse[PaymentDto](data=payment)


@auth_guard.authenticated
async def get_payments_by_invoice_handler(_: Request, invoice_id: UUID4) -> BaseResponse[List[PaymentDto]]:
	"""Get all payments for an invoice"""
	payment_service = get_payment_service()
	payments = await payment_service.get_payments_by_invoice(invoice_id)
	return BaseResponse[List[PaymentDto]](data=payments)


@auth_guard.authenticated
async def get_payments_by_organization_handler(_: Request, organization_id: UUID4) -> BaseResponse[List[PaymentDto]]:
	"""Get all payments for an organization"""
	payment_service = get_payment_service()
	payments = await payment_service.get_payments_by_organization(organization_id)
	return BaseResponse[List[PaymentDto]](data=payments)


# WEBHOOK HANDLING


async def payment_webhook_handler(
	_: Request, transaction_number: str, status: str, amount: float
) -> BaseResponse[PaymentDto]:
	"""Handle payment webhook from external payment provider"""
	payment_service = get_payment_service()
	payment = await payment_service.process_payment_webhook(transaction_number, status, amount)
	return BaseResponse[PaymentDto](data=payment)


# FINANCIAL ANALYTICS & DASHBOARD ENDPOINTS


@auth_guard.authenticated
async def get_financial_dashboard_handler(
	_: Request,
	start_date: Optional[date] = Query(None, description="Start date for analysis"),
	end_date: Optional[date] = Query(None, description="End date for analysis"),
) -> BaseResponse[Dict]:
	"""Get financial dashboard data"""
	analytics_service = get_financial_analytics_service()

	# Default to current month if no dates provided
	if not start_date:
		today = date.today()
		start_date = today.replace(day=1)
	if not end_date:
		end_date = date.today()

	dashboard_data = await analytics_service.get_revenue_dashboard(start_date, end_date)
	return BaseResponse[Dict](data=dashboard_data)


@auth_guard.authenticated
async def get_payment_analytics_handler(
	_: Request, period: str = Query("monthly", description="Analysis period: daily, weekly, monthly, yearly")
) -> BaseResponse[Dict]:
	"""Get payment analytics and trends"""
	analytics_service = get_financial_analytics_service()
	analytics_data = await analytics_service.get_payment_analytics(period)
	return BaseResponse[Dict](data=analytics_data)


@auth_guard.authenticated
async def get_organization_financial_profile_handler(_: Request, organization_id: UUID4) -> BaseResponse[Dict]:
	"""Get comprehensive financial profile for an organization"""
	org_financial_service = get_organization_financial_service()
	profile = await org_financial_service.get_organization_financial_profile(organization_id)
	return BaseResponse[Dict](data=profile)


@auth_guard.authenticated
async def get_organization_payment_timeline_handler(_: Request, organization_id: UUID4) -> BaseResponse[List[Dict]]:
	"""Get payment timeline for organization visualization"""
	org_financial_service = get_organization_financial_service()
	timeline = await org_financial_service.get_organization_payment_timeline(organization_id)
	return BaseResponse[List[Dict]](data=timeline)


@auth_guard.authenticated
async def compare_organizations_financially_handler(
	_: Request, organization_ids: List[UUID4] = Query(..., description="List of organization IDs to compare")
) -> BaseResponse[Dict]:
	"""Compare multiple organizations financially"""
	org_financial_service = get_organization_financial_service()
	comparison = await org_financial_service.compare_organizations_financially(organization_ids)
	return BaseResponse[Dict](data=comparison)


# PAYMENT RECONCILIATION ENDPOINTS


@auth_guard.authenticated
async def reconcile_payment_with_fees_handler(_: Request, application_code: str) -> BaseResponse[Dict]:
	"""Reconcile payment deposit with calculated fees for an application"""
	payment_service = get_payment_service()
	reconciliation = await payment_service.reconcile_deposit_with_calculated_fee(application_code)
	return BaseResponse[Dict](data=reconciliation)


@auth_guard.authenticated
async def get_payment_discrepancies_handler(
	_: Request, organization_id: Optional[UUID4] = Query(None, description="Filter by organization ID")
) -> BaseResponse[List[Dict]]:
	"""Get all payment discrepancies requiring attention"""
	payment_service = get_payment_service()
	discrepancies = await payment_service.handle_payment_discrepancies(organization_id)
	return BaseResponse[List[Dict]](data=discrepancies)


@auth_guard.authenticated
async def get_payment_reconciliation_summary_handler(
	_: Request, organization_id: Optional[UUID4] = Query(None, description="Filter by organization ID")
) -> BaseResponse[Dict]:
	"""Get payment reconciliation summary"""
	payment_service = get_payment_service()
	summary = await payment_service.get_payment_reconciliation_summary(organization_id)
	return BaseResponse[Dict](data=summary)


# FEE ANALYTICS ENDPOINTS


@auth_guard.authenticated
async def analyze_fee_effectiveness_handler(_: Request) -> BaseResponse[Dict]:
	"""Analyze effectiveness of current fee structure"""
	fee_service = get_fee_service()
	analysis = await fee_service.analyze_fee_effectiveness()
	return BaseResponse[Dict](data=analysis)


@auth_guard.authenticated
async def simulate_fee_change_impact_handler(
	_: Request, fee_id: str, new_amount: float = Query(..., description="New fee amount to simulate")
) -> BaseResponse[Dict]:
	"""Simulate impact of changing a fee amount"""
	fee_service = get_fee_service()
	impact = await fee_service.simulate_fee_change_impact(fee_id, new_amount)
	return BaseResponse[Dict](data=impact)


@auth_guard.authenticated
async def get_fee_utilization_report_handler(_: Request) -> BaseResponse[Dict]:
	"""Get detailed fee utilization report"""
	fee_service = get_fee_service()
	report = await fee_service.get_fee_utilization_report()
	return BaseResponse[Dict](data=report)


# FINANCIAL REPORTS ENDPOINTS


@auth_guard.authenticated
async def generate_monthly_revenue_report_handler(
	_: Request, month: int = Query(..., description="Month (1-12)"), year: int = Query(..., description="Year")
) -> BaseResponse[Dict]:
	"""Generate monthly revenue report"""
	reports_service = get_financial_reports_service()
	report = await reports_service.generate_monthly_revenue_report(month, year)
	return BaseResponse[Dict](data=report)


@auth_guard.authenticated
async def generate_payment_reconciliation_report_handler(
	_: Request,
	start_date: date = Query(..., description="Start date for report"),
	end_date: date = Query(..., description="End date for report"),
	organization_id: Optional[UUID4] = Query(None, description="Filter by organization ID"),
) -> BaseResponse[Dict]:
	"""Generate payment reconciliation report"""
	reports_service = get_financial_reports_service()
	report = await reports_service.generate_payment_reconciliation_report(start_date, end_date, organization_id)
	return BaseResponse[Dict](data=report)


@auth_guard.authenticated
async def generate_organization_payment_history_report_handler(
	_: Request, organization_id: UUID4, include_pending: bool = Query(True, description="Include pending payments")
) -> BaseResponse[Dict]:
	"""Generate organization payment history report"""
	reports_service = get_financial_reports_service()
	report = await reports_service.generate_organization_payment_history_report(organization_id, include_pending)
	return BaseResponse[Dict](data=report)


@auth_guard.authenticated
async def generate_fee_collection_efficiency_report_handler(
	_: Request,
	start_date: date = Query(..., description="Start date for report"),
	end_date: date = Query(..., description="End date for report"),
) -> BaseResponse[Dict]:
	"""Generate fee collection efficiency report"""
	reports_service = get_financial_reports_service()
	report = await reports_service.generate_fee_collection_efficiency_report(start_date, end_date)
	return BaseResponse[Dict](data=report)


@auth_guard.authenticated
async def generate_outstanding_receivables_report_handler(
	_: Request, as_of_date: Optional[date] = Query(None, description="As of date for report (defaults to today)")
) -> BaseResponse[Dict]:
	"""Generate outstanding receivables report with aging analysis"""
	reports_service = get_financial_reports_service()
	report = await reports_service.generate_outstanding_receivables_report(as_of_date)
	return BaseResponse[Dict](data=report)


@auth_guard.authenticated
async def generate_payment_method_analysis_report_handler(
	_: Request,
	start_date: date = Query(..., description="Start date for report"),
	end_date: date = Query(..., description="End date for report"),
) -> BaseResponse[Dict]:
	"""Generate payment method usage analysis report"""
	reports_service = get_financial_reports_service()
	report = await reports_service.generate_payment_method_analysis_report(start_date, end_date)
	return BaseResponse[Dict](data=report)
