from typing import Dict, List

from fastapi import APIRouter

from src.core.dtos.financial_dtos import InvoiceDto, InvoiceItemDto, PaymentDto
from src.core.shared_schema import BaseResponse, Pagination
from src.modules.financial import financial_controller as controller
from src.modules.financial.currency_router import currency_router_v1
from src.modules.financial.fee_router import fee_router_v1

financial_router_v1 = APIRouter(tags=["financial"])

# INVOICE ROUTES
financial_router_v1.add_api_route(
	path="/invoices",
	methods=["GET"],
	endpoint=controller.fetch_invoices_handler,
	response_model=Pagination[InvoiceDto],
)

financial_router_v1.add_api_route(
	path="/invoices/application/{application_id}",
	methods=["GET"],
	endpoint=controller.get_invoice_by_application_handler,
	response_model=BaseResponse[InvoiceDto],
)

financial_router_v1.add_api_route(
	path="/invoice-items/application/{application_id}",
	methods=["GET"],
	endpoint=controller.get_invoice_items_by_application_handler,
	response_model=BaseResponse[List[InvoiceItemDto]],
)

financial_router_v1.add_api_route(
	path="/invoices/{invoice_id}/pdf",
	methods=["GET"],
	endpoint=controller.generate_invoice_pdf_handler,
	response_model=BaseResponse[Dict],
)

financial_router_v1.add_api_route(
	path="/invoices/{invoice_id}/mark-as-paid",
	methods=["POST"],
	endpoint=controller.mark_invoice_as_paid_handler,
	response_model=BaseResponse[InvoiceDto],
)

# PAYMENT ROUTES
financial_router_v1.add_api_route(
	path="/payments",
	methods=["POST"],
	endpoint=controller.create_payment_handler,
	response_model=BaseResponse[PaymentDto],
)

financial_router_v1.add_api_route(
	path="/payments",
	methods=["GET"],
	endpoint=controller.get_payments_handler,
	response_model=Pagination[PaymentDto],
)

financial_router_v1.add_api_route(
	path="/payments/verify",
	methods=["POST"],
	endpoint=controller.verify_payment_handler,
	response_model=BaseResponse[PaymentDto],
)

financial_router_v1.add_api_route(
	path="/payments/invoice/{invoice_id}",
	methods=["GET"],
	endpoint=controller.get_payments_by_invoice_handler,
	response_model=BaseResponse[List[PaymentDto]],
)

financial_router_v1.add_api_route(
	path="/payments/organization/{organization_id}",
	methods=["GET"],
	endpoint=controller.get_payments_by_organization_handler,
	response_model=BaseResponse[List[PaymentDto]],
)

# WEBHOOK ROUTES
financial_router_v1.add_api_route(
	path="/webhooks/payment/{transaction_number}/{status}/{amount}",
	methods=["POST"],
	endpoint=controller.payment_webhook_handler,
	response_model=BaseResponse[PaymentDto],
)

# FINANCIAL ANALYTICS & DASHBOARD ROUTES
financial_router_v1.add_api_route(
	path="/dashboard",
	methods=["GET"],
	endpoint=controller.get_financial_dashboard_handler,
	response_model=BaseResponse[Dict],
)

financial_router_v1.add_api_route(
	path="/analytics/payments",
	methods=["GET"],
	endpoint=controller.get_payment_analytics_handler,
	response_model=BaseResponse[Dict],
)

# ORGANIZATION FINANCIAL ROUTES
financial_router_v1.add_api_route(
	path="/organizations/{organization_id}/profile",
	methods=["GET"],
	endpoint=controller.get_organization_financial_profile_handler,
	response_model=BaseResponse[Dict],
)

financial_router_v1.add_api_route(
	path="/organizations/{organization_id}/timeline",
	methods=["GET"],
	endpoint=controller.get_organization_payment_timeline_handler,
	response_model=BaseResponse[List[Dict]],
)

financial_router_v1.add_api_route(
	path="/organizations/compare",
	methods=["GET"],
	endpoint=controller.compare_organizations_financially_handler,
	response_model=BaseResponse[Dict],
)

# PAYMENT RECONCILIATION ROUTES
financial_router_v1.add_api_route(
	path="/reconciliation/application/{application_code}",
	methods=["GET"],
	endpoint=controller.reconcile_payment_with_fees_handler,
	response_model=BaseResponse[Dict],
)

financial_router_v1.add_api_route(
	path="/reconciliation/discrepancies",
	methods=["GET"],
	endpoint=controller.get_payment_discrepancies_handler,
	response_model=BaseResponse[List[Dict]],
)

financial_router_v1.add_api_route(
	path="/reconciliation/summary",
	methods=["GET"],
	endpoint=controller.get_payment_reconciliation_summary_handler,
	response_model=BaseResponse[Dict],
)

# FEE ANALYTICS ROUTES
financial_router_v1.add_api_route(
	path="/fees/effectiveness",
	methods=["GET"],
	endpoint=controller.analyze_fee_effectiveness_handler,
	response_model=BaseResponse[Dict],
)

financial_router_v1.add_api_route(
	path="/fees/{fee_id}/simulate-change",
	methods=["GET"],
	endpoint=controller.simulate_fee_change_impact_handler,
	response_model=BaseResponse[Dict],
)

financial_router_v1.add_api_route(
	path="/fees/utilization-report",
	methods=["GET"],
	endpoint=controller.get_fee_utilization_report_handler,
	response_model=BaseResponse[Dict],
)

# FINANCIAL REPORTS ROUTES
financial_router_v1.add_api_route(
	path="/reports/revenue/monthly",
	methods=["GET"],
	endpoint=controller.generate_monthly_revenue_report_handler,
	response_model=BaseResponse[Dict],
)

financial_router_v1.add_api_route(
	path="/reports/reconciliation",
	methods=["GET"],
	endpoint=controller.generate_payment_reconciliation_report_handler,
	response_model=BaseResponse[Dict],
)

financial_router_v1.add_api_route(
	path="/reports/organizations/{organization_id}/payment-history",
	methods=["GET"],
	endpoint=controller.generate_organization_payment_history_report_handler,
	response_model=BaseResponse[Dict],
)

financial_router_v1.add_api_route(
	path="/reports/fee-collection-efficiency",
	methods=["GET"],
	endpoint=controller.generate_fee_collection_efficiency_report_handler,
	response_model=BaseResponse[Dict],
)

financial_router_v1.add_api_route(
	path="/reports/outstanding-receivables",
	methods=["GET"],
	endpoint=controller.generate_outstanding_receivables_report_handler,
	response_model=BaseResponse[Dict],
)

financial_router_v1.add_api_route(
	path="/reports/payment-methods",
	methods=["GET"],
	endpoint=controller.generate_payment_method_analysis_report_handler,
	response_model=BaseResponse[Dict],
)

# Include fee routes
financial_router_v1.include_router(fee_router_v1)

# Include currency routes
financial_router_v1.include_router(currency_router_v1)
