from datetime import date
from typing import Dict, List

from sqlalchemy import func, desc
from sqlalchemy.orm import joinedload
from pydantic import UUID4

from src.config.db.models import Payment, Invoice, Organization, PaymentStatus, InvoiceStatus, LoadableItem
from src.core.base.base_repository import BaseRepository
from src.core.exceptions import ApiException
from src.core.logger.internal_logger import get_logger


class OrganizationFinancialService(BaseRepository):
	"""Service for organization-specific financial profiling and analysis"""

	def __init__(self):
		super().__init__()
		self.logger = get_logger(__name__)

	async def get_organization_financial_profile(self, organization_id: UUID4) -> Dict:
		"""Get comprehensive financial profile for an organization"""
		try:
			return {
				"organization_id": str(organization_id),
				"payment_history": await self._get_payment_history(organization_id),
				"financial_summary": await self._get_financial_summary(organization_id),
				"payment_compliance": await self._calculate_payment_compliance(organization_id),
				"payment_behavior": await self._analyze_payment_behavior(organization_id),
				"outstanding_balances": await self._get_outstanding_balances(organization_id),
				"payment_methods_used": await self._get_payment_method_preferences(organization_id),
				"recent_transactions": await self._get_recent_transactions(organization_id, limit=10),
				"risk_assessment": await self._assess_financial_risk(organization_id),
			}
		except Exception as e:
			self.logger.error(f"Failed to get organization financial profile: {str(e)}")
			raise ApiException("Failed to retrieve organization financial profile")

	async def get_organization_payment_timeline(self, organization_id: UUID4) -> List[Dict]:
		"""Get payment timeline for visualization"""
		try:
			payments = (
				self.db.query(Payment)
				.filter(Payment.organization_id == organization_id, Payment.status == PaymentStatus.SUCCESS)
				.order_by(Payment.created_at)
				.all()
			)

			timeline = []
			for payment in payments:
				timeline.append(
					{
						"date": payment.created_at.date().isoformat(),
						"amount": float(payment.amount),
						"transaction_number": payment.transaction_number,
						"payment_method": payment.payment_mode.value if payment.payment_mode else "Unknown",
						"invoice_id": str(payment.invoice_id) if payment.invoice_id else None,
					}
				)

			return timeline
		except Exception as e:
			self.logger.error(f"Failed to get payment timeline: {str(e)}")
			raise ApiException("Failed to retrieve payment timeline")

	async def compare_organizations_financially(self, organization_ids: List[UUID4]) -> Dict:
		"""Compare multiple organizations financially"""
		try:
			comparison = {}

			for org_id in organization_ids:
				org_data = await self._get_financial_summary(org_id)
				org = self.db.query(Organization).filter(Organization.id == org_id).first()

				comparison[str(org_id)] = {
					"name": org.name if org else "Unknown",
					"total_paid": org_data["total_fees_paid"],
					"payment_count": org_data["total_payments"],
					"average_payment": org_data["average_payment_amount"],
					"compliance_score": (await self._calculate_payment_compliance(org_id))["compliance_score"],
					"latest_payment": org_data["latest_payment_date"],
				}

			# Add rankings
			sorted_by_total = sorted(comparison.items(), key=lambda x: x[1]["total_paid"], reverse=True)
			sorted_by_compliance = sorted(comparison.items(), key=lambda x: x[1]["compliance_score"], reverse=True)

			return {
				"organizations": comparison,
				"rankings": {
					"by_total_paid": [
						{"org_id": k, "rank": i + 1, "amount": v["total_paid"]}
						for i, (k, v) in enumerate(sorted_by_total)
					],
					"by_compliance": [
						{"org_id": k, "rank": i + 1, "score": v["compliance_score"]}
						for i, (k, v) in enumerate(sorted_by_compliance)
					],
				},
			}
		except Exception as e:
			self.logger.error(f"Failed to compare organizations: {str(e)}")
			raise ApiException("Failed to compare organizations financially")

	async def _get_payment_history(self, organization_id: UUID4) -> List[Dict]:
		"""Get detailed payment history"""
		payments = (
			self.db.query(Payment)
			.options(joinedload(Payment.payment_mode))
			.filter(Payment.organization_id == organization_id)
			.order_by(desc(Payment.created_at))
			.limit(50)
			.all()
		)

		return [
			{
				"payment_id": str(payment.id),
				"amount": float(payment.amount),
				"transaction_number": payment.transaction_number,
				"status": payment.status.value,
				"payment_method": payment.payment_mode.value if payment.payment_mode else "Unknown",
				"paid_by": payment.paid_by,
				"date": payment.created_at.date().isoformat(),
				"invoice_id": str(payment.invoice_id) if payment.invoice_id else None,
			}
			for payment in payments
		]

	async def _get_financial_summary(self, organization_id: UUID4) -> Dict:
		"""Get financial summary statistics"""
		# Total payments
		total_paid = (
			self.db.query(func.sum(Payment.amount))
			.filter(Payment.organization_id == organization_id, Payment.status == PaymentStatus.SUCCESS)
			.scalar()
			or 0
		)

		# Payment count
		payment_count = (
			self.db.query(func.count(Payment.id))
			.filter(Payment.organization_id == organization_id, Payment.status == PaymentStatus.SUCCESS)
			.scalar()
			or 0
		)

		# Average payment
		avg_payment = (
			self.db.query(func.avg(Payment.amount))
			.filter(Payment.organization_id == organization_id, Payment.status == PaymentStatus.SUCCESS)
			.scalar()
			or 0
		)

		# Latest payment
		latest_payment = (
			self.db.query(Payment)
			.filter(Payment.organization_id == organization_id, Payment.status == PaymentStatus.SUCCESS)
			.order_by(desc(Payment.created_at))
			.first()
		)

		# Outstanding invoices
		outstanding = (
			self.db.query(func.sum(Invoice.total_amount))
			.filter(
				Invoice.organization_id == organization_id,
				Invoice.status.in_([InvoiceStatus.PENDING, InvoiceStatus.PARTIALLY_PAID]),
			)
			.scalar()
			or 0
		)

		return {
			"total_fees_paid": float(total_paid),
			"total_payments": int(payment_count),
			"average_payment_amount": float(avg_payment),
			"latest_payment_date": latest_payment.created_at.date().isoformat() if latest_payment else None,
			"outstanding_balance": float(outstanding),
		}

	async def _calculate_payment_compliance(self, organization_id: UUID4) -> Dict:
		"""Calculate payment compliance metrics"""
		# Get all invoices for organization
		total_invoices = (
			self.db.query(func.count(Invoice.id)).filter(Invoice.organization_id == organization_id).scalar() or 0
		)

		# Get paid invoices
		paid_invoices = (
			self.db.query(func.count(Invoice.id))
			.filter(Invoice.organization_id == organization_id, Invoice.status == InvoiceStatus.PAID)
			.scalar()
			or 0
		)

		# Get overdue invoices (past due date and not paid)
		overdue_invoices = (
			self.db.query(func.count(Invoice.id))
			.filter(
				Invoice.organization_id == organization_id,
				Invoice.due_date < date.today(),
				Invoice.status != InvoiceStatus.PAID,
			)
			.scalar()
			or 0
		)

		compliance_score = 0
		if total_invoices > 0:
			compliance_score = (paid_invoices / total_invoices) * 100

		# Calculate average payment delay
		avg_delay = await self._calculate_average_payment_delay(organization_id)

		return {
			"total_invoices": total_invoices,
			"paid_invoices": paid_invoices,
			"overdue_invoices": overdue_invoices,
			"compliance_score": round(compliance_score, 2),
			"compliance_rating": self._get_compliance_rating(compliance_score),
			"average_payment_delay_days": avg_delay,
		}

	async def _analyze_payment_behavior(self, organization_id: UUID4) -> Dict:
		"""Analyze payment patterns and behavior"""
		# Payment frequency analysis
		payments = (
			self.db.query(Payment)
			.filter(Payment.organization_id == organization_id, Payment.status == PaymentStatus.SUCCESS)
			.all()
		)

		if not payments:
			return {"payment_frequency": "No payments", "seasonal_pattern": "No data"}

		# Calculate time between payments
		payment_dates = sorted([p.created_at.date() for p in payments])
		intervals = []
		for i in range(1, len(payment_dates)):
			interval = (payment_dates[i] - payment_dates[i - 1]).days
			intervals.append(interval)

		avg_interval = sum(intervals) / len(intervals) if intervals else 0

		# Seasonal analysis
		monthly_payments = {}
		for payment in payments:
			month = payment.created_at.month
			monthly_payments[month] = monthly_payments.get(month, 0) + 1

		peak_month = max(monthly_payments, key=monthly_payments.get) if monthly_payments else None
		month_names = ["", "Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"]

		return {
			"average_payment_interval_days": round(avg_interval, 1),
			"payment_frequency": self._categorize_payment_frequency(avg_interval),
			"total_payments_made": len(payments),
			"peak_payment_month": month_names[peak_month] if peak_month else "No data",
			"monthly_distribution": {month_names[k]: v for k, v in monthly_payments.items()},
		}

	async def _get_outstanding_balances(self, organization_id: UUID4) -> Dict:
		"""Get detailed outstanding balance information"""
		outstanding_invoices = (
			self.db.query(Invoice)
			.filter(
				Invoice.organization_id == organization_id,
				Invoice.status.in_([InvoiceStatus.PENDING, InvoiceStatus.PARTIALLY_PAID]),
			)
			.all()
		)

		total_outstanding = sum(invoice.total_amount for invoice in outstanding_invoices)

		aging_buckets = {
			"current": 0,  # 0-30 days
			"30_days": 0,  # 31-60 days
			"60_days": 0,  # 61-90 days
			"90_plus_days": 0,  # 90+ days
		}

		today = date.today()
		for invoice in outstanding_invoices:
			days_overdue = (today - invoice.due_date).days
			if days_overdue <= 30:
				aging_buckets["current"] += invoice.total_amount
			elif days_overdue <= 60:
				aging_buckets["30_days"] += invoice.total_amount
			elif days_overdue <= 90:
				aging_buckets["60_days"] += invoice.total_amount
			else:
				aging_buckets["90_plus_days"] += invoice.total_amount

		return {
			"total_outstanding": float(total_outstanding),
			"invoice_count": len(outstanding_invoices),
			"aging_analysis": {k: float(v) for k, v in aging_buckets.items()},
			"oldest_invoice_days": max([(today - inv.due_date).days for inv in outstanding_invoices])
			if outstanding_invoices
			else 0,
		}

	async def _get_payment_method_preferences(self, organization_id: UUID4) -> List[Dict]:
		"""Get payment method usage statistics"""
		results = (
			self.db.query(
				LoadableItem.value.label("method"),
				func.count(Payment.id).label("count"),
				func.sum(Payment.amount).label("total_amount"),
			)
			.join(LoadableItem, Payment.payment_mode_id == LoadableItem.id)
			.filter(Payment.organization_id == organization_id, Payment.status == PaymentStatus.SUCCESS)
			.group_by(LoadableItem.value)
			.order_by(desc(func.count(Payment.id)))
			.all()
		)

		return [
			{
				"payment_method": row.method,
				"usage_count": int(row.count),
				"total_amount": float(row.total_amount),
				"is_preferred": i == 0,  # Most used method
			}
			for i, row in enumerate(results)
		]

	async def _get_recent_transactions(self, organization_id: UUID4, limit: int = 10) -> List[Dict]:
		"""Get recent transactions with details"""
		transactions = (
			self.db.query(Payment)
			.options(joinedload(Payment.invoice), joinedload(Payment.payment_mode))
			.filter(Payment.organization_id == organization_id)
			.order_by(desc(Payment.created_at))
			.limit(limit)
			.all()
		)

		return [
			{
				"transaction_id": str(transaction.id),
				"date": transaction.created_at.date().isoformat(),
				"amount": float(transaction.amount),
				"status": transaction.status.value,
				"transaction_number": transaction.transaction_number,
				"payment_method": transaction.payment_mode.value if transaction.payment_mode else "Unknown",
				"invoice_reference": transaction.invoice.reference_number if transaction.invoice else None,
			}
			for transaction in transactions
		]

	async def _assess_financial_risk(self, organization_id: UUID4) -> Dict:
		"""Assess financial risk level for the organization"""
		compliance = await self._calculate_payment_compliance(organization_id)
		outstanding = await self._get_outstanding_balances(organization_id)

		risk_score = 0
		risk_factors = []

		# Compliance risk
		if compliance["compliance_score"] < 70:
			risk_score += 3
			risk_factors.append("Low payment compliance")
		elif compliance["compliance_score"] < 85:
			risk_score += 1
			risk_factors.append("Moderate payment compliance")

		# Outstanding balance risk
		if outstanding["total_outstanding"] > 10000:
			risk_score += 2
			risk_factors.append("High outstanding balance")
		elif outstanding["total_outstanding"] > 5000:
			risk_score += 1
			risk_factors.append("Moderate outstanding balance")

		# Overdue risk
		if outstanding["aging_analysis"]["90_plus_days"] > 0:
			risk_score += 3
			risk_factors.append("Long overdue payments")
		elif outstanding["aging_analysis"]["60_days"] > 0:
			risk_score += 2
			risk_factors.append("Moderately overdue payments")

		# Payment delay risk
		if compliance["average_payment_delay_days"] > 60:
			risk_score += 2
			risk_factors.append("Consistently late payments")
		elif compliance["average_payment_delay_days"] > 30:
			risk_score += 1
			risk_factors.append("Occasionally late payments")

		risk_level = "Low"
		if risk_score >= 6:
			risk_level = "High"
		elif risk_score >= 3:
			risk_level = "Medium"

		return {
			"risk_level": risk_level,
			"risk_score": risk_score,
			"risk_factors": risk_factors,
			"recommendation": self._get_risk_recommendation(risk_level, risk_factors),
		}

	async def _calculate_average_payment_delay(self, organization_id: UUID4) -> float:
		"""Calculate average payment delay in days"""
		# This would require tracking payment due dates vs actual payment dates
		# For now, return a placeholder calculation
		payments_with_invoices = (
			self.db.query(Payment, Invoice)
			.join(Invoice, Payment.invoice_id == Invoice.id)
			.filter(Payment.organization_id == organization_id, Payment.status == PaymentStatus.SUCCESS)
			.all()
		)

		delays = []
		for payment, invoice in payments_with_invoices:
			delay = (payment.created_at.date() - invoice.due_date).days
			if delay > 0:  # Only count actual delays
				delays.append(delay)

		return sum(delays) / len(delays) if delays else 0

	def _categorize_payment_frequency(self, avg_interval: float) -> str:
		"""Categorize payment frequency based on average interval"""
		if avg_interval <= 30:
			return "Frequent"
		elif avg_interval <= 90:
			return "Regular"
		elif avg_interval <= 180:
			return "Occasional"
		else:
			return "Infrequent"

	def _get_compliance_rating(self, score: float) -> str:
		"""Get compliance rating based on score"""
		if score >= 95:
			return "Excellent"
		elif score >= 85:
			return "Good"
		elif score >= 70:
			return "Fair"
		else:
			return "Poor"

	def _get_risk_recommendation(self, risk_level: str, risk_factors: List[str]) -> str:
		"""Get recommendation based on risk assessment"""
		if risk_level == "High":
			return "Immediate attention required. Consider payment plan or follow-up."
		elif risk_level == "Medium":
			return "Monitor closely. Send payment reminders for overdue amounts."
		else:
			return "Low risk organization. Continue standard procedures."


organization_financial_service = OrganizationFinancialService()
