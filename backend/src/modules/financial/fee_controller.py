from fastapi import Depends
from pydantic import UUID4
from starlette.requests import Request

from src.core.dtos.fee_dtos import FeeD<PERSON>, FeeCreateRequest, FeeUpdateRequest, to_fee_dto
from src.core.exceptions.api import ApiException
from src.core.guards.auth_guard import auth_guard
from src.core.shared_schema import BaseResponse, Pagination
from src.modules.financial.fee_schema import FeeFilter
from src.modules.financial.fee_service import fee_service


@auth_guard.authenticated
async def fetch_fees_handler(
	_: Request,
	filter: FeeFilter = Depends(FeeFilter),
) -> Pagination[FeeDto]:
	"""Retrieve paginated list of fees with filtering"""
	return await fee_service.retrieve_fees(filter)


@auth_guard.authenticated
async def get_fee_handler(
	fee_id: UUID4,
	_: Request,
) -> FeeDto:
	"""Get a specific fee by ID"""
	fee = await fee_service.get_fee_by_id(fee_id)
	if not fee:
		raise ApiException("Fee not found")

	fee_dto = to_fee_dto(fee, extras="category,currency")
	return fee_dto


@auth_guard.authenticated
async def create_fee_handler(
	request: FeeCreateRequest,
	_: Request,
) -> BaseResponse[FeeDto]:
	"""Create a new fee"""
	fee = await fee_service.create_fee(request)
	fee_dto = to_fee_dto(fee, extras="category,currency")
	return BaseResponse[FeeDto](data=fee_dto)


@auth_guard.authenticated
async def update_fee_handler(
	fee_id: UUID4,
	updates: FeeUpdateRequest,
	_: Request,
) -> BaseResponse[FeeDto]:
	"""Update an existing fee"""
	fee = await fee_service.update_fee(fee_id, updates)
	fee_dto = to_fee_dto(fee, extras="category,currency")
	return BaseResponse[FeeDto](data=fee_dto)


@auth_guard.authenticated
async def delete_fee_handler(
	fee_id: UUID4,
	_: Request,
) -> BaseResponse[bool]:
	"""Delete a fee (soft delete)"""
	await fee_service.delete_fee(fee_id)
	return BaseResponse(success=True)


@auth_guard.authenticated
def activate_fee_handler(fee_id: UUID4) -> BaseResponse[FeeDto]:
	"""Activate a scheduled fee"""
	fee = fee_service.activate_fee(fee_id)
	if not fee:
		raise ApiException("Fee not found or not schedulable")

	fee_dto = to_fee_dto(fee, extras="category,currency")
	return BaseResponse[FeeDto](data=fee_dto, message="Fee activated successfully")
