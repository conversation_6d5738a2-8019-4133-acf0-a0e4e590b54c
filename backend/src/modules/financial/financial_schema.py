from typing import Optional

from pydantic import UUID4, Field

from src.core.shared_schema import BaseRequest


class InvoiceFilter(BaseRequest):
	type: Optional[str] = Field(default=None, description="Invoice type")
	organization_id: Optional[UUID4] = Field(default=None, description="Organization ID")


class PaymentFilter(BaseRequest):
	status: Optional[str] = Field(default=None, description="Payment status")
