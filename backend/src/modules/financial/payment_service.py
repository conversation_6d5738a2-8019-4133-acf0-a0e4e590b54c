from typing import Dict, List, Optional

from fastapi_pagination import Params
from fastapi_pagination.ext.sqlalchemy import paginate
from pydantic import UUID4
from sqlalchemy import desc, func

from src.config.db.models import ApplicationFee, Invoice, InvoiceItem, InvoiceStatus, Payment, PaymentStatus
from src.config.db.models.application import Application
from src.core.base.base_repository import BaseRepository
from src.core.dtos.financial_dtos import CreatePaymentRequest, PaymentDto, PaymentVerificationRequest, to_payment_dto
from src.core.exceptions.api import ApiException
from src.core.logger.internal_logger import get_logger
from src.core.shared_schema import Pagination
from src.modules.financial.financial_schema import PaymentFilter


class PaymentService(BaseRepository):
	def __init__(self):
		super().__init__()
		self.logger = get_logger(__name__)

	async def retrieve_payments(self, filter: PaymentFilter = None) -> Pagination[PaymentDto]:
		"""Retrieve payments with optional filtering"""
		try:
			query = self.db.query(Payment)

			# if filter.status:
			# 	query = query.filter(Payment.status == filter.status)

			query = query.order_by(desc(Payment.created_at))

			query_result = paginate(query, Params(page=filter.page, size=filter.size))
			data = [to_payment_dto(payment) for payment in query_result.items]

			return Pagination.from_query_result(data, query_result)
		except Exception as e:
			self.logger.error(f"Failed to retrieve payments: {str(e)}")
			raise

	async def create_payment(self, request: CreatePaymentRequest) -> PaymentDto:
		"""Create a new payment record"""
		try:
			invoice = self.db.query(Invoice).filter(Invoice.id == request.invoice_id).first()
			if not invoice:
				raise ApiException("Invoice not found")

			if invoice.status == InvoiceStatus.PAID:
				raise ApiException("Invoice is already paid")

			existing_payment = (
				self.db.query(Payment).filter(Payment.transaction_number == request.transaction_number).first()
			)
			if existing_payment:
				raise ApiException("Transaction number already exists")

			payment = Payment(
				amount=request.amount,
				organization_id=request.organization_id,
				invoice_id=request.invoice_id,
				transaction_number=request.transaction_number,
				payment_mode_id=request.payment_mode_id,
				status=PaymentStatus.SUCCESS,
				paid_by=request.paid_by,
				created_by=self.current_user.id,
			)

			self.db.add(payment)
			self.db.flush()

			# Update invoice status based on payment amount
			await self._update_invoice_status(invoice, request.amount)

			self.logger.info(f"Created payment {payment.transaction_number} for invoice {invoice.reference_number}")

			return to_payment_dto(payment)

		except Exception as e:
			self.logger.error(f"Failed to create payment: {str(e)}")
			raise

	async def verify_payment(self, request: PaymentVerificationRequest) -> PaymentDto:
		"""Verify a payment transaction"""
		try:
			payment = self.db.query(Payment).filter(Payment.transaction_number == request.transaction_number).first()

			if not payment:
				raise ApiException("Payment not found")

			if abs(payment.amount - request.expected_amount) > 0.01:
				self.logger.warning(
					f"Payment amount mismatch: expected {request.expected_amount}, got {payment.amount}"
				)
				payment.status = PaymentStatus.FAILED
			else:
				payment.status = PaymentStatus.SUCCESS

				invoice = self.db.query(Invoice).filter(Invoice.id == payment.invoice_id).first()
				if invoice:
					await self._update_invoice_status(invoice, payment.amount)

			self.db.add(payment)
			self.db.flush()

			return to_payment_dto(payment)

		except Exception as e:
			self.logger.error(f"Failed to verify payment: {str(e)}")
			raise

	async def get_payments_by_invoice(self, invoice_id: UUID4) -> List[PaymentDto]:
		"""Get all payments for an invoice"""
		try:
			payments = (
				self.db.query(Payment).filter(Payment.invoice_id == invoice_id).order_by(desc(Payment.created_at)).all()
			)

			return [to_payment_dto(payment) for payment in payments]

		except Exception as e:
			self.logger.error(f"Failed to get payments by invoice: {str(e)}")
			raise

	async def get_payments_by_organization(self, organization_id: UUID4) -> List[PaymentDto]:
		"""Get all payments for an organization"""
		try:
			payments = (
				self.db.query(Payment)
				.filter(Payment.organization_id == organization_id)
				.order_by(desc(Payment.created_at))
				.all()
			)

			return [to_payment_dto(payment, "invoice") for payment in payments]

		except Exception as e:
			self.logger.error(f"Failed to get payments by organization: {str(e)}")
			raise

	async def _update_invoice_status(self, invoice: Invoice, payment_amount: float):
		"""Update invoice status based on payment amount"""
		try:
			total_payments = (
				self.db.query(Payment)
				.filter(Payment.invoice_id == invoice.id, Payment.status == PaymentStatus.SUCCESS)
				.all()
			)

			total_paid = sum(p.amount for p in total_payments) + payment_amount

			if total_paid >= invoice.total_amount:
				invoice.status = InvoiceStatus.PAID
				self.logger.info(f"Invoice {invoice.reference_number} marked as PAID")
			elif total_paid > 0:
				invoice.status = InvoiceStatus.PARTIALLY_PAID
				self.logger.info(f"Invoice {invoice.reference_number} marked as PARTIALLY_PAID")

			self.db.add(invoice)

		except Exception as e:
			self.logger.error(f"Failed to update invoice status: {str(e)}")
			raise

	async def process_payment_webhook(self, transaction_number: str, status: str, amount: float) -> PaymentDto:
		"""Process payment webhook from external payment provider"""
		try:
			payment = self.db.query(Payment).filter(Payment.transaction_number == transaction_number).first()

			if not payment:
				raise ApiException(f"Payment with transaction number {transaction_number} not found")

			if status.upper() == "SUCCESS":
				payment.status = PaymentStatus.SUCCESS

				invoice = self.db.query(Invoice).filter(Invoice.id == payment.invoice_id).first()
				if invoice:
					await self._update_invoice_status(invoice, amount)

			elif status.upper() == "FAILED":
				payment.status = PaymentStatus.FAILED

			self.db.add(payment)
			self.db.flush()

			self.logger.info(f"Processed webhook for payment {transaction_number}: status={status}")

			return to_payment_dto(payment)

		except Exception as e:
			self.logger.error(f"Failed to process payment webhook: {str(e)}")
			raise

	async def reconcile_deposit_with_calculated_fee(self, code: str) -> Dict:
		"""Reconcile pre-deposit with calculated application fees"""
		try:
			application = self.db.query(Application).filter(Application.code == code).first()
			if not application:
				raise ApiException(f"Application with code {code} not found")

			payment = await self._get_verified_payment_for_application(code)
			if not payment:
				raise ApiException("No verified payment found for this application")

			calculated_fees = await self._get_total_application_fees(application.id)
			if calculated_fees is None:
				raise ApiException("No calculated fees found for this application")

			difference = payment.amount - calculated_fees
			tolerance = 0.01

			status = "exact"
			if difference > tolerance:
				status = "overpaid"
			elif difference < -tolerance:
				status = "underpaid"

			return {
				"application_id": str(application.id),
				"payment_amount": float(payment.amount),
				"calculated_fees": float(calculated_fees),
				"difference": float(difference),
				"status": status,
				"requires_action": abs(difference) > tolerance,
				"payment_id": str(payment.id),
				"reconciliation_notes": self._generate_reconciliation_notes(difference, status),
			}

		except Exception as e:
			self.logger.error(f"Failed to reconcile deposit with fees: {str(e)}")
			raise ApiException("Failed to reconcile payment with calculated fees")

	async def handle_payment_discrepancies(self, organization_id: Optional[UUID4] = None) -> List[Dict]:
		"""Identify and return all payment discrepancies requiring attention"""
		try:
			query = self.db.query(Payment).filter(Payment.status == PaymentStatus.SUCCESS)

			if organization_id:
				query = query.filter(Payment.organization_id == organization_id)

			payments = query.all()
			discrepancies = []

			for payment in payments:
				if payment.invoice_id:
					try:
						invoice = self.db.query(Invoice).filter(Invoice.id == payment.invoice_id).first()
						if invoice:
							invoice_item = (
								self.db.query(InvoiceItem).filter(InvoiceItem.invoice_id == invoice.id).first()
							)

							if invoice_item and invoice_item.application_id:
								reconciliation = await self.reconcile_deposit_with_calculated_fee(
									invoice_item.application_id
								)

								if reconciliation["requires_action"]:
									discrepancies.append(
										{
											"payment_id": str(payment.id),
											"organization_id": str(payment.organization_id),
											"application_id": str(invoice_item.application_id),
											"invoice_id": str(payment.invoice_id),
											"discrepancy_amount": reconciliation["difference"],
											"discrepancy_type": reconciliation["status"],
											"payment_amount": reconciliation["payment_amount"],
											"calculated_fees": reconciliation["calculated_fees"],
											"created_at": payment.created_at.isoformat(),
											"requires_follow_up": True,
										}
									)
					except Exception as item_error:
						self.logger.warning(f"Could not process payment {payment.id}: {str(item_error)}")
						continue

			return discrepancies

		except Exception as e:
			self.logger.error(f"Failed to handle payment discrepancies: {str(e)}")
			raise ApiException("Failed to identify payment discrepancies")

	async def get_payment_reconciliation_summary(self, organization_id: Optional[UUID4] = None) -> Dict:
		"""Get summary of payment reconciliation status"""
		try:
			discrepancies = await self.handle_payment_discrepancies(organization_id)

			overpayments = [d for d in discrepancies if d["discrepancy_type"] == "overpaid"]
			underpayments = [d for d in discrepancies if d["discrepancy_type"] == "underpaid"]

			total_overpayment = sum(d["discrepancy_amount"] for d in overpayments)
			total_underpayment = sum(abs(d["discrepancy_amount"]) for d in underpayments)

			return {
				"total_discrepancies": len(discrepancies),
				"overpayments_count": len(overpayments),
				"underpayments_count": len(underpayments),
				"total_overpayment_amount": float(total_overpayment),
				"total_underpayment_amount": float(total_underpayment),
				"requires_immediate_attention": len([d for d in discrepancies if abs(d["discrepancy_amount"]) > 100]),
				"discrepancies": discrepancies,
			}

		except Exception as e:
			self.logger.error(f"Failed to get reconciliation summary: {str(e)}")
			raise ApiException("Failed to get payment reconciliation summary")

	async def _get_verified_payment_for_application(self, code: str) -> Optional[Payment]:
		"""Get the verified payment for a specific application"""
		payment = (
			self.db.query(Payment)
			.join(Invoice, Payment.invoice_id == Invoice.id)
			.join(InvoiceItem, InvoiceItem.invoice_id == Invoice.id)
			.join(Application, InvoiceItem.application_id == Application.id)
			.filter(Application.code == code)
			.first()
		)

		return payment

	async def _get_total_application_fees(self, application_id: UUID4) -> Optional[float]:
		"""Get total calculated fees for an application"""
		result = (
			self.db.query(func.sum(ApplicationFee.amount))
			.filter(ApplicationFee.application_id == application_id)
			.scalar()
		)

		return float(result) if result else None

	def _generate_reconciliation_notes(self, difference: float, status: str) -> str:
		"""Generate human-readable reconciliation notes"""
		if status == "exact":
			return "Payment matches calculated fees exactly"
		elif status == "overpaid":
			return f"Payment exceeds calculated fees by {abs(difference):.2f}. Consider refund or credit."
		else:
			return f"Payment is {abs(difference):.2f} less than calculated fees. Follow-up required."
