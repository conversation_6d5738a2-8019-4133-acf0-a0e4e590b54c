from datetime import date, timed<PERSON>ta
from typing import Dict, List, Optional

from pydantic import UUID4
from sqlalchemy import desc, extract, func

from src.config.db.models import (
	Application,
	ApplicationFee,
	Invoice,
	LoadableItem,
	Organization,
	Payment,
	PaymentStatus,
)
from src.core.base.base_repository import BaseRepository
from src.core.exceptions import ApiException
from src.core.logger.internal_logger import get_logger


class FinancialAnalyticsService(BaseRepository):
	"""Service for financial analytics and reporting"""

	def __init__(self):
		super().__init__()
		self.logger = get_logger(__name__)

	async def get_revenue_dashboard(
		self, start_date: date, end_date: date, department_id: Optional[UUID4] = None
	) -> Dict:
		"""Get comprehensive revenue dashboard data"""
		try:
			return {
				"total_revenue": await self._calculate_total_revenue(start_date, end_date),
				"revenue_by_month": await self._get_monthly_revenue_trend(start_date, end_date),
				"revenue_by_application_type": await self._get_revenue_by_app_type(start_date, end_date),
				"collection_efficiency": await self._calculate_collection_rate(start_date, end_date),
				"top_paying_organizations": await self._get_top_payers(start_date, end_date, limit=10),
				"revenue_growth": await self._calculate_revenue_growth(start_date, end_date),
				"average_payment_amount": await self._get_average_payment_amount(start_date, end_date),
				"payment_count": await self._get_payment_count(start_date, end_date),
			}
		except Exception as e:
			self.logger.error(f"Failed to get revenue dashboard: {str(e)}")
			raise ApiException("Failed to retrieve revenue dashboard data")

	async def get_payment_analytics(self, period: str = "monthly") -> Dict:
		"""Get payment analytics and trends"""
		try:
			end_date = date.today()
			if period == "daily":
				start_date = end_date - timedelta(days=30)
			elif period == "weekly":
				start_date = end_date - timedelta(weeks=12)
			elif period == "yearly":
				start_date = end_date - timedelta(days=365 * 2)
			else:  # monthly
				start_date = end_date - timedelta(days=365)

			return {
				"payment_volume_trends": await self._get_payment_volume_trends(start_date, end_date, period),
				"payment_method_breakdown": await self._get_payment_method_stats(start_date, end_date),
				"payment_success_rate": await self._get_payment_success_rate(start_date, end_date),
				"payment_verification_metrics": await self._get_verification_metrics(start_date, end_date),
				"deposit_accuracy_analysis": await self._analyze_deposit_accuracy(start_date, end_date),
				"seasonal_patterns": await self._analyze_seasonal_patterns(start_date, end_date),
				"average_verification_time": await self._get_average_verification_time(start_date, end_date),
			}
		except Exception as e:
			self.logger.error(f"Failed to get payment analytics: {str(e)}")
			raise ApiException("Failed to retrieve payment analytics")

	async def _calculate_total_revenue(self, start_date: date, end_date: date) -> float:
		"""Calculate total revenue for the period"""
		result = (
			self.db.query(func.sum(Payment.amount))
			.filter(
				Payment.status == PaymentStatus.SUCCESS,
				Payment.created_at >= start_date,
				Payment.created_at <= end_date,
			)
			.scalar()
		)
		return float(result or 0)

	async def _get_monthly_revenue_trend(self, start_date: date, end_date: date) -> List[Dict]:
		"""Get monthly revenue trends"""
		results = (
			self.db.query(
				extract("year", Payment.created_at).label("year"),
				extract("month", Payment.created_at).label("month"),
				func.sum(Payment.amount).label("revenue"),
				func.count(Payment.id).label("payment_count"),
			)
			.filter(
				Payment.status == PaymentStatus.SUCCESS,
				Payment.created_at >= start_date,
				Payment.created_at <= end_date,
			)
			.group_by(extract("year", Payment.created_at), extract("month", Payment.created_at))
			.order_by(extract("year", Payment.created_at), extract("month", Payment.created_at))
			.all()
		)

		return [
			{
				"year": int(row.year),
				"month": int(row.month),
				"revenue": float(row.revenue or 0),
				"payment_count": int(row.payment_count or 0),
				"period": f"{int(row.year)}-{int(row.month):02d}",
			}
			for row in results
		]

	async def _get_revenue_by_app_type(self, start_date: date, end_date: date) -> Dict[str, float]:
		"""Get revenue breakdown by application type"""
		results = (
			self.db.query(Application.type.label("app_type"), func.sum(Payment.amount).label("revenue"))
			.join(Invoice, Payment.invoice_id == Invoice.id)
			.join(Application, Invoice.organization_id == Application.organization_id)
			.filter(
				Payment.status == PaymentStatus.SUCCESS,
				Payment.created_at >= start_date,
				Payment.created_at <= end_date,
			)
			.group_by(Application.type)
			.all()
		)

		return {row.app_type: float(row.revenue or 0) for row in results}

	async def _calculate_collection_rate(self, start_date: date, end_date: date) -> float:
		"""Calculate collection efficiency (paid vs invoiced)"""
		total_invoiced = (
			self.db.query(func.sum(Invoice.total_amount))
			.filter(Invoice.created_at >= start_date, Invoice.created_at <= end_date)
			.scalar()
			or 0
		)

		total_collected = (
			self.db.query(func.sum(Payment.amount))
			.filter(
				Payment.status == PaymentStatus.SUCCESS,
				Payment.created_at >= start_date,
				Payment.created_at <= end_date,
			)
			.scalar()
			or 0
		)

		if total_invoiced > 0:
			return float((total_collected / total_invoiced) * 100)
		return 0.0

	async def _get_top_payers(self, start_date: date, end_date: date, limit: int = 10) -> List[Dict]:
		"""Get top paying organizations"""
		results = (
			self.db.query(
				Organization.id,
				Organization.name,
				func.sum(Payment.amount).label("total_paid"),
				func.count(Payment.id).label("payment_count"),
			)
			.join(Payment, Organization.id == Payment.organization_id)
			.filter(
				Payment.status == PaymentStatus.SUCCESS,
				Payment.created_at >= start_date,
				Payment.created_at <= end_date,
			)
			.group_by(Organization.id, Organization.name)
			.order_by(desc(func.sum(Payment.amount)))
			.limit(limit)
			.all()
		)

		return [
			{
				"organization_id": str(row.id),
				"organization_name": row.name,
				"total_paid": float(row.total_paid),
				"payment_count": int(row.payment_count),
			}
			for row in results
		]

	async def _calculate_revenue_growth(self, start_date: date, end_date: date) -> float:
		"""Calculate revenue growth compared to previous period"""
		period_days = (end_date - start_date).days
		previous_start = start_date - timedelta(days=period_days)
		previous_end = start_date

		current_revenue = await self._calculate_total_revenue(start_date, end_date)
		previous_revenue = await self._calculate_total_revenue(previous_start, previous_end)

		if previous_revenue > 0:
			growth = ((current_revenue - previous_revenue) / previous_revenue) * 100
			return round(growth, 2)
		return 0.0

	async def _get_average_payment_amount(self, start_date: date, end_date: date) -> float:
		"""Get average payment amount for the period"""
		result = (
			self.db.query(func.avg(Payment.amount))
			.filter(
				Payment.status == PaymentStatus.SUCCESS,
				Payment.created_at >= start_date,
				Payment.created_at <= end_date,
			)
			.scalar()
		)
		return float(result or 0)

	async def _get_payment_count(self, start_date: date, end_date: date) -> int:
		"""Get total payment count for the period"""
		result = (
			self.db.query(func.count(Payment.id))
			.filter(
				Payment.status == PaymentStatus.SUCCESS,
				Payment.created_at >= start_date,
				Payment.created_at <= end_date,
			)
			.scalar()
		)
		return int(result or 0)

	async def _get_payment_volume_trends(self, start_date: date, end_date: date, period: str) -> List[Dict]:
		"""Get payment volume trends by period"""
		if period == "daily":
			date_part = func.date(Payment.created_at)
			# date_format = "daily"
		elif period == "weekly":
			date_part = func.date_trunc("week", Payment.created_at)
			# date_format = "weekly"
		elif period == "yearly":
			date_part = extract("year", Payment.created_at)
			# date_format = "yearly"
		else:  # monthly
			date_part = func.date_trunc("month", Payment.created_at)
			# date_format = "monthly"

		results = (
			self.db.query(
				date_part.label("period"),
				func.count(Payment.id).label("volume"),
				func.sum(Payment.amount).label("amount"),
			)
			.filter(
				Payment.status == PaymentStatus.SUCCESS,
				Payment.created_at >= start_date,
				Payment.created_at <= end_date,
			)
			.group_by(date_part)
			.order_by(date_part)
			.all()
		)

		return [
			{"period": str(row.period), "volume": int(row.volume), "amount": float(row.amount or 0)} for row in results
		]

	async def _get_payment_method_stats(self, start_date: date, end_date: date) -> Dict[str, int]:
		"""Get payment method breakdown"""
		results = (
			self.db.query(LoadableItem.value.label("payment_method"), func.count(Payment.id).label("count"))
			.join(LoadableItem, Payment.payment_mode_id == LoadableItem.id)
			.filter(
				Payment.status == PaymentStatus.SUCCESS,
				Payment.created_at >= start_date,
				Payment.created_at <= end_date,
			)
			.group_by(LoadableItem.value)
			.all()
		)

		return {row.payment_method: int(row.count) for row in results}

	async def _get_payment_success_rate(self, start_date: date, end_date: date) -> float:
		"""Calculate payment success rate"""
		total_payments = (
			self.db.query(func.count(Payment.id))
			.filter(Payment.created_at >= start_date, Payment.created_at <= end_date)
			.scalar()
			or 0
		)

		successful_payments = (
			self.db.query(func.count(Payment.id))
			.filter(
				Payment.status == PaymentStatus.SUCCESS,
				Payment.created_at >= start_date,
				Payment.created_at <= end_date,
			)
			.scalar()
			or 0
		)

		if total_payments > 0:
			return float((successful_payments / total_payments) * 100)
		return 0.0

	async def _get_verification_metrics(self, start_date: date, end_date: date) -> Dict:
		"""Get payment verification metrics"""
		total_payments = (
			self.db.query(func.count(Payment.id))
			.filter(Payment.created_at >= start_date, Payment.created_at <= end_date)
			.scalar()
			or 0
		)

		verified_payments = (
			self.db.query(func.count(Payment.id))
			.filter(
				Payment.created_at >= start_date,
				Payment.created_at <= end_date,
				Payment.status == PaymentStatus.SUCCESS,
			)
			.scalar()
			or 0
		)

		pending_verification = total_payments - verified_payments

		return {
			"total_payments": total_payments,
			"verified_payments": verified_payments,
			"pending_verification": pending_verification,
			"verification_rate": float((verified_payments / total_payments) * 100) if total_payments > 0 else 0.0,
		}

	async def _analyze_deposit_accuracy(self, start_date: date, end_date: date) -> Dict:
		"""Analyze how accurate pre-deposits are compared to calculated fees"""
		results = (
			self.db.query(Payment.amount.label("deposit_amount"), ApplicationFee.amount.label("calculated_fee"))
			.join(Invoice, Payment.invoice_id == Invoice.id)
			.join(
				ApplicationFee,
				ApplicationFee.application_id == Invoice.organization_id,
			)
			.filter(
				Payment.status == PaymentStatus.SUCCESS,
				Payment.created_at >= start_date,
				Payment.created_at <= end_date,
			)
			.all()
		)

		exact_matches = 0
		overpayments = 0
		underpayments = 0
		total_difference = 0

		for row in results:
			difference = row.deposit_amount - row.calculated_fee
			if abs(difference) < 0.01:
				exact_matches += 1
			elif difference > 0:
				overpayments += 1
			else:
				underpayments += 1
			total_difference += abs(difference)

		total_records = len(results)

		return {
			"total_analyzed": total_records,
			"exact_matches": exact_matches,
			"overpayments": overpayments,
			"underpayments": underpayments,
			"accuracy_rate": float((exact_matches / total_records) * 100) if total_records > 0 else 0.0,
			"average_difference": float(total_difference / total_records) if total_records > 0 else 0.0,
		}

	async def _analyze_seasonal_patterns(self, start_date: date, end_date: date) -> List[Dict]:
		"""Analyze seasonal payment patterns"""
		results = (
			self.db.query(
				extract("month", Payment.created_at).label("month"),
				func.count(Payment.id).label("payment_count"),
				func.sum(Payment.amount).label("total_amount"),
			)
			.filter(
				Payment.status == PaymentStatus.SUCCESS,
				Payment.created_at >= start_date,
				Payment.created_at <= end_date,
			)
			.group_by(extract("month", Payment.created_at))
			.order_by(extract("month", Payment.created_at))
			.all()
		)

		months = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"]

		return [
			{
				"month": months[int(row.month) - 1],
				"month_number": int(row.month),
				"payment_count": int(row.payment_count),
				"total_amount": float(row.total_amount or 0),
			}
			for row in results
		]

	async def _get_average_verification_time(self, start_date: date, end_date: date) -> float:
		"""Get average time for payment verification (placeholder - would need verification timestamps)"""
		# This would require additional fields in Payment model to track verification time
		# For now, return a placeholder value
		return 2.5  # Average of 2.5 days


financial_analytics_service = FinancialAnalyticsService()
