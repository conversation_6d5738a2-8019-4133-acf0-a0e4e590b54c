from datetime import date, datetime, timedelta
from typing import Dict, List, Optional

from pydantic import UUID4
from sqlalchemy import desc, extract, func
from sqlalchemy.orm import joinedload

from src.config.db.models import (
	Application,
	ApplicationFee,
	Fee,
	Invoice,
	InvoiceItem,
	InvoiceStatus,
	LoadableItem,
	Organization,
	Payment,
	PaymentStatus,
)
from src.core.base.base_repository import BaseRepository
from src.core.exceptions import ApiException
from src.core.logger.internal_logger import get_logger


class FinancialReportsService(BaseRepository):
	"""Service for generating standardized financial reports"""

	def __init__(self):
		super().__init__()
		self.logger = get_logger(__name__)

	async def generate_monthly_revenue_report(self, month: int, year: int) -> Dict:
		"""Generate comprehensive monthly revenue report"""
		try:
			start_date = date(year, month, 1)
			if month == 12:
				end_date = date(year + 1, 1, 1) - timedelta(days=1)
			else:
				end_date = date(year, month + 1, 1) - timedelta(days=1)

			return {
				"report_period": {
					"month": month,
					"year": year,
					"start_date": start_date.isoformat(),
					"end_date": end_date.isoformat(),
					"period_name": start_date.strftime("%B %Y"),
				},
				"revenue_summary": await self._get_revenue_summary(start_date, end_date),
				"payment_breakdown": await self._get_payment_breakdown(start_date, end_date),
				"application_type_analysis": await self._get_application_type_revenue(start_date, end_date),
				"top_contributors": await self._get_top_revenue_contributors(start_date, end_date),
				"comparison_data": await self._get_monthly_comparison(month, year),
				"collection_efficiency": await self._get_collection_efficiency(start_date, end_date),
				"generated_at": datetime.now().isoformat(),
				"generated_by": self.current_user.email if self.current_user else "System",
			}
		except Exception as e:
			self.logger.error(f"Failed to generate monthly revenue report: {str(e)}")
			raise ApiException("Failed to generate monthly revenue report")

	async def generate_payment_reconciliation_report(
		self, start_date: date, end_date: date, organization_id: Optional[UUID4] = None
	) -> Dict:
		"""Generate payment reconciliation report showing discrepancies"""
		try:
			from src.modules.financial.payment_service import PaymentService

			payment_service = PaymentService()
			discrepancies = await payment_service.handle_payment_discrepancies(organization_id)
			# reconciliation_summary = await payment_service.get_payment_reconciliation_summary(organization_id)

			# Filter discrepancies by date range
			filtered_discrepancies = []
			for discrepancy in discrepancies:
				payment_date = datetime.fromisoformat(discrepancy["created_at"]).date()
				if start_date <= payment_date <= end_date:
					filtered_discrepancies.append(discrepancy)

			return {
				"report_period": {"start_date": start_date.isoformat(), "end_date": end_date.isoformat()},
				"organization_filter": str(organization_id) if organization_id else "All Organizations",
				"reconciliation_summary": {
					"total_discrepancies": len(filtered_discrepancies),
					"overpayments": len([d for d in filtered_discrepancies if d["discrepancy_type"] == "overpaid"]),
					"underpayments": len([d for d in filtered_discrepancies if d["discrepancy_type"] == "underpaid"]),
					"total_overpayment_amount": sum(
						d["discrepancy_amount"] for d in filtered_discrepancies if d["discrepancy_type"] == "overpaid"
					),
					"total_underpayment_amount": sum(
						abs(d["discrepancy_amount"])
						for d in filtered_discrepancies
						if d["discrepancy_type"] == "underpaid"
					),
					"high_value_discrepancies": len(
						[d for d in filtered_discrepancies if abs(d["discrepancy_amount"]) > 1000]
					),
				},
				"detailed_discrepancies": filtered_discrepancies,
				"recommendations": await self._generate_reconciliation_recommendations(filtered_discrepancies),
				"generated_at": datetime.now().isoformat(),
				"generated_by": self.current_user.email if self.current_user else "System",
			}
		except Exception as e:
			self.logger.error(f"Failed to generate reconciliation report: {str(e)}")
			raise ApiException("Failed to generate payment reconciliation report")

	async def generate_organization_payment_history_report(
		self, organization_id: UUID4, include_pending: bool = True
	) -> Dict:
		"""Generate complete payment history report for an organization"""
		try:
			organization = self.db.query(Organization).filter(Organization.id == organization_id).first()
			if not organization:
				raise ApiException("Organization not found")

			# Get payment history
			payment_query = self.db.query(Payment).filter(Payment.organization_id == organization_id)
			if not include_pending:
				payment_query = payment_query.filter(Payment.status == PaymentStatus.SUCCESS)

			payments = payment_query.order_by(desc(Payment.created_at)).all()

			# Get invoice history
			invoices = (
				self.db.query(Invoice)
				.filter(Invoice.organization_id == organization_id)
				.order_by(desc(Invoice.created_at))
				.all()
			)

			return {
				"organization": {
					"id": str(organization.id),
					"name": organization.name,
					"registration_number": getattr(organization, "registration_number", "N/A"),
					"contact_email": getattr(organization, "email", "N/A"),
				},
				"summary": await self._get_organization_payment_summary(organization_id),
				"payment_history": [
					{
						"payment_id": str(payment.id),
						"date": payment.created_at.date().isoformat(),
						"amount": float(payment.amount),
						"status": payment.status.value,
						"transaction_number": payment.transaction_number,
						"payment_method": payment.payment_mode.value if payment.payment_mode else "Unknown",
						"paid_by": payment.paid_by,
						"invoice_id": str(payment.invoice_id) if payment.invoice_id else None,
					}
					for payment in payments
				],
				"invoice_history": [
					{
						"invoice_id": str(invoice.id),
						"reference_number": invoice.reference_number,
						"date": invoice.created_at.date().isoformat(),
						"due_date": invoice.due_date.isoformat(),
						"amount": float(invoice.total_amount),
						"status": invoice.status.value,
						"description": invoice.description,
					}
					for invoice in invoices
				],
				"financial_health": await self._assess_organization_financial_health(organization_id),
				"generated_at": datetime.now().isoformat(),
				"generated_by": self.current_user.email if self.current_user else "System",
			}
		except Exception as e:
			self.logger.error(f"Failed to generate organization payment history: {str(e)}")
			raise ApiException("Failed to generate organization payment history report")

	async def generate_fee_collection_efficiency_report(self, start_date: date, end_date: date) -> Dict:
		"""Generate fee collection efficiency report by category and income bracket"""
		try:
			# Get fee collection data by category
			category_results = (
				self.db.query(
					LoadableItem.value.label("category"),
					func.count(ApplicationFee.id).label("applications"),
					func.sum(ApplicationFee.amount).label("total_fees"),
					func.avg(ApplicationFee.amount).label("avg_fee"),
				)
				.join(Fee, ApplicationFee.fee_id == Fee.id)
				.join(LoadableItem, Fee.fee_category_id == LoadableItem.id)
				.filter(ApplicationFee.created_at >= start_date, ApplicationFee.created_at <= end_date)
				.group_by(LoadableItem.value)
				.all()
			)

			# Get income bracket analysis
			income_bracket_results = (
				self.db.query(
					Fee.min_income,
					Fee.max_income,
					func.count(ApplicationFee.id).label("applications"),
					func.sum(ApplicationFee.amount).label("total_fees"),
					func.avg(ApplicationFee.organization_income).label("avg_org_income"),
				)
				.join(ApplicationFee, Fee.id == ApplicationFee.fee_id)
				.filter(
					Fee.based_on_income,
					ApplicationFee.created_at >= start_date,
					ApplicationFee.created_at <= end_date,
				)
				.group_by(Fee.min_income, Fee.max_income)
				.all()
			)

			return {
				"report_period": {"start_date": start_date.isoformat(), "end_date": end_date.isoformat()},
				"collection_by_category": [
					{
						"category": row.category,
						"applications_count": int(row.applications),
						"total_fees_collected": float(row.total_fees or 0),
						"average_fee": float(row.avg_fee or 0),
					}
					for row in category_results
				],
				"income_bracket_performance": [
					{
						"income_bracket": f"{row.min_income}-{row.max_income or 'unlimited'}",
						"applications_count": int(row.applications),
						"total_fees_collected": float(row.total_fees or 0),
						"average_organization_income": float(row.avg_org_income or 0),
					}
					for row in income_bracket_results
				],
				"efficiency_metrics": await self._calculate_collection_efficiency_metrics(start_date, end_date),
				"trends_analysis": await self._analyze_collection_trends(start_date, end_date),
				"generated_at": datetime.now().isoformat(),
				"generated_by": self.current_user.email if self.current_user else "System",
			}
		except Exception as e:
			self.logger.error(f"Failed to generate fee collection efficiency report: {str(e)}")
			raise ApiException("Failed to generate fee collection efficiency report")

	async def generate_outstanding_receivables_report(self, as_of_date: Optional[date] = None) -> Dict:
		"""Generate outstanding receivables report with aging analysis"""
		try:
			if not as_of_date:
				as_of_date = date.today()

			# Get all outstanding invoices
			outstanding_invoices = (
				self.db.query(Invoice)
				.options(joinedload(Invoice.organization))
				.filter(Invoice.status.in_([InvoiceStatus.PENDING, InvoiceStatus.PARTIALLY_PAID]))
				.all()
			)

			aging_analysis = {
				"current": [],  # 0-30 days
				"30_days": [],  # 31-60 days
				"60_days": [],  # 61-90 days
				"90_plus_days": [],  # 90+ days
			}

			total_outstanding = 0

			for invoice in outstanding_invoices:
				days_overdue = (as_of_date - invoice.due_date).days
				invoice_data = {
					"invoice_id": str(invoice.id),
					"reference_number": invoice.reference_number,
					"organization_name": invoice.organization.name if invoice.organization else "Unknown",
					"organization_id": str(invoice.organization_id),
					"amount": float(invoice.total_amount),
					"due_date": invoice.due_date.isoformat(),
					"days_overdue": days_overdue,
					"status": invoice.status.value,
				}

				total_outstanding += invoice.total_amount

				if days_overdue <= 30:
					aging_analysis["current"].append(invoice_data)
				elif days_overdue <= 60:
					aging_analysis["30_days"].append(invoice_data)
				elif days_overdue <= 90:
					aging_analysis["60_days"].append(invoice_data)
				else:
					aging_analysis["90_plus_days"].append(invoice_data)

			aging_summary = {
				bucket: {"count": len(invoices), "total_amount": sum(inv["amount"] for inv in invoices)}
				for bucket, invoices in aging_analysis.items()
			}

			return {
				"as_of_date": as_of_date.isoformat(),
				"summary": {
					"total_outstanding_amount": float(total_outstanding),
					"total_outstanding_invoices": len(outstanding_invoices),
					"aging_summary": aging_summary,
				},
				"aging_analysis": aging_analysis,
				"priority_follow_ups": [
					inv
					for inv in aging_analysis["90_plus_days"] + aging_analysis["60_days"]
					if inv["amount"] > 1000  # High value overdue
				],
				"recommendations": await self._generate_receivables_recommendations(aging_analysis),
				"generated_at": datetime.now().isoformat(),
				"generated_by": self.current_user.email if self.current_user else "System",
			}
		except Exception as e:
			self.logger.error(f"Failed to generate outstanding receivables report: {str(e)}")
			raise ApiException("Failed to generate outstanding receivables report")

	async def generate_payment_method_analysis_report(self, start_date: date, end_date: date) -> Dict:
		"""Generate payment method usage analysis report"""
		try:
			# Payment method usage statistics
			payment_method_stats = (
				self.db.query(
					LoadableItem.value.label("payment_method"),
					func.count(Payment.id).label("transaction_count"),
					func.sum(Payment.amount).label("total_amount"),
					func.avg(Payment.amount).label("average_amount"),
					func.min(Payment.amount).label("min_amount"),
					func.max(Payment.amount).label("max_amount"),
				)
				.join(LoadableItem, Payment.payment_mode_id == LoadableItem.id)
				.filter(
					Payment.status == PaymentStatus.SUCCESS,
					Payment.created_at >= start_date,
					Payment.created_at <= end_date,
				)
				.group_by(LoadableItem.value)
				.order_by(desc(func.count(Payment.id)))
				.all()
			)

			# Monthly trend analysis
			monthly_trends = (
				self.db.query(
					extract("month", Payment.created_at).label("month"),
					LoadableItem.value.label("payment_method"),
					func.count(Payment.id).label("count"),
					func.sum(Payment.amount).label("amount"),
				)
				.join(LoadableItem, Payment.payment_mode_id == LoadableItem.id)
				.filter(
					Payment.status == PaymentStatus.SUCCESS,
					Payment.created_at >= start_date,
					Payment.created_at <= end_date,
				)
				.group_by(extract("month", Payment.created_at), LoadableItem.value)
				.all()
			)

			# Organize monthly trends
			trends_by_method = {}
			for trend in monthly_trends:
				method = trend.payment_method
				if method not in trends_by_method:
					trends_by_method[method] = []

				trends_by_method[method].append(
					{
						"month": int(trend.month),
						"transaction_count": int(trend.count),
						"total_amount": float(trend.amount),
					}
				)

			return {
				"report_period": {"start_date": start_date.isoformat(), "end_date": end_date.isoformat()},
				"payment_method_statistics": [
					{
						"payment_method": row.payment_method,
						"transaction_count": int(row.transaction_count),
						"total_amount": float(row.total_amount or 0),
						"average_amount": float(row.average_amount or 0),
						"min_amount": float(row.min_amount or 0),
						"max_amount": float(row.max_amount or 0),
						"market_share_by_volume": 0,  # Will be calculated below
						"market_share_by_value": 0,  # Will be calculated below
					}
					for row in payment_method_stats
				],
				"monthly_trends": trends_by_method,
				"insights": await self._generate_payment_method_insights(payment_method_stats),
				"generated_at": datetime.now().isoformat(),
				"generated_by": self.current_user.email if self.current_user else "System",
			}
		except Exception as e:
			self.logger.error(f"Failed to generate payment method analysis: {str(e)}")
			raise ApiException("Failed to generate payment method analysis report")

	# Helper methods for report generation

	async def _get_revenue_summary(self, start_date: date, end_date: date) -> Dict:
		"""Get revenue summary for a period"""
		total_revenue = (
			self.db.query(func.sum(Payment.amount))
			.filter(
				Payment.status == PaymentStatus.SUCCESS,
				Payment.created_at >= start_date,
				Payment.created_at <= end_date,
			)
			.scalar()
			or 0
		)

		payment_count = (
			self.db.query(func.count(Payment.id))
			.filter(
				Payment.status == PaymentStatus.SUCCESS,
				Payment.created_at >= start_date,
				Payment.created_at <= end_date,
			)
			.scalar()
			or 0
		)

		avg_payment = total_revenue / payment_count if payment_count > 0 else 0

		return {
			"total_revenue": float(total_revenue),
			"payment_count": int(payment_count),
			"average_payment": float(avg_payment),
		}

	async def _get_payment_breakdown(self, start_date: date, end_date: date) -> Dict:
		"""Get payment breakdown by status and method"""
		status_breakdown = (
			self.db.query(
				Payment.status, func.count(Payment.id).label("count"), func.sum(Payment.amount).label("amount")
			)
			.filter(Payment.created_at >= start_date, Payment.created_at <= end_date)
			.group_by(Payment.status)
			.all()
		)

		return {
			"by_status": [
				{"status": row.status.value, "count": int(row.count), "amount": float(row.amount or 0)}
				for row in status_breakdown
			]
		}

	async def _get_application_type_revenue(self, start_date: date, end_date: date) -> Dict:
		"""Get revenue breakdown by application type"""
		results = (
			self.db.query(
				Application.type, func.sum(Payment.amount).label("revenue"), func.count(Payment.id).label("count")
			)
			.join(Invoice, Payment.invoice_id == Invoice.id)
			.join(InvoiceItem, InvoiceItem.invoice_id == Invoice.id)
			.join(Application, Application.id == InvoiceItem.application_id)
			.filter(
				Payment.status == PaymentStatus.SUCCESS,
				Payment.created_at >= start_date,
				Payment.created_at <= end_date,
			)
			.group_by(Application.type)
			.all()
		)

		return {
			app_type: {"revenue": float(revenue or 0), "transaction_count": int(count)}
			for app_type, revenue, count in results
		}

	async def _get_top_revenue_contributors(self, start_date: date, end_date: date, limit: int = 10) -> List[Dict]:
		"""Get top revenue contributing organizations"""
		results = (
			self.db.query(
				Organization.id,
				Organization.name,
				func.sum(Payment.amount).label("total_revenue"),
				func.count(Payment.id).label("payment_count"),
			)
			.join(Payment, Organization.id == Payment.organization_id)
			.filter(
				Payment.status == PaymentStatus.SUCCESS,
				Payment.created_at >= start_date,
				Payment.created_at <= end_date,
			)
			.group_by(Organization.id, Organization.name)
			.order_by(desc(func.sum(Payment.amount)))
			.limit(limit)
			.all()
		)

		return [
			{
				"organization_id": str(row.id),
				"organization_name": row.name,
				"total_revenue": float(row.total_revenue),
				"payment_count": int(row.payment_count),
			}
			for row in results
		]

	async def _get_monthly_comparison(self, month: int, year: int) -> Dict:
		"""Get comparison with previous month and same month last year"""
		current_start = date(year, month, 1)

		# Previous month
		if month == 1:
			prev_month_start = date(year - 1, 12, 1)
			prev_month_end = date(year, 1, 1) - timedelta(days=1)
		else:
			prev_month_start = date(year, month - 1, 1)
			prev_month_end = date(year, month, 1) - timedelta(days=1)

		# Same month last year
		same_month_last_year_start = date(year - 1, month, 1)
		if month == 12:
			same_month_last_year_end = date(year, 1, 1) - timedelta(days=1)
		else:
			same_month_last_year_end = date(year - 1, month + 1, 1) - timedelta(days=1)

		current_revenue = await self._get_revenue_summary(
			current_start, current_start.replace(day=28) + timedelta(days=4)
		)
		prev_month_revenue = await self._get_revenue_summary(prev_month_start, prev_month_end)
		last_year_revenue = await self._get_revenue_summary(same_month_last_year_start, same_month_last_year_end)

		return {
			"vs_previous_month": {
				"revenue_change": current_revenue["total_revenue"] - prev_month_revenue["total_revenue"],
				"percentage_change": (
					(current_revenue["total_revenue"] - prev_month_revenue["total_revenue"])
					/ prev_month_revenue["total_revenue"]
					* 100
				)
				if prev_month_revenue["total_revenue"] > 0
				else 0,
			},
			"vs_same_month_last_year": {
				"revenue_change": current_revenue["total_revenue"] - last_year_revenue["total_revenue"],
				"percentage_change": (
					(current_revenue["total_revenue"] - last_year_revenue["total_revenue"])
					/ last_year_revenue["total_revenue"]
					* 100
				)
				if last_year_revenue["total_revenue"] > 0
				else 0,
			},
		}

	async def _get_collection_efficiency(self, start_date: date, end_date: date) -> Dict:
		"""Calculate collection efficiency metrics"""
		total_invoiced = (
			self.db.query(func.sum(Invoice.total_amount))
			.filter(Invoice.created_at >= start_date, Invoice.created_at <= end_date)
			.scalar()
			or 0
		)

		total_collected = (
			self.db.query(func.sum(Payment.amount))
			.filter(
				Payment.status == PaymentStatus.SUCCESS,
				Payment.created_at >= start_date,
				Payment.created_at <= end_date,
			)
			.scalar()
			or 0
		)

		efficiency = (total_collected / total_invoiced * 100) if total_invoiced > 0 else 0

		return {
			"total_invoiced": float(total_invoiced),
			"total_collected": float(total_collected),
			"collection_efficiency_percentage": round(efficiency, 2),
		}

	async def _generate_reconciliation_recommendations(self, discrepancies: List[Dict]) -> List[str]:
		"""Generate recommendations based on reconciliation discrepancies"""
		recommendations = []

		if not discrepancies:
			recommendations.append("No payment discrepancies found - excellent payment accuracy")
			return recommendations

		high_value_count = len([d for d in discrepancies if abs(d["discrepancy_amount"]) > 1000])
		overpayment_count = len([d for d in discrepancies if d["discrepancy_type"] == "overpaid"])
		underpayment_count = len([d for d in discrepancies if d["discrepancy_type"] == "underpaid"])

		if high_value_count > 0:
			recommendations.append(f"Immediate attention required for {high_value_count} high-value discrepancies")

		if overpayment_count > underpayment_count:
			recommendations.append("Consider implementing refund process for overpayments")
		elif underpayment_count > overpayment_count:
			recommendations.append("Focus on collection of underpayments through follow-up procedures")

		if len(discrepancies) > 10:
			recommendations.append("Review fee calculation process to reduce payment discrepancies")

		return recommendations

	async def _get_organization_payment_summary(self, organization_id: UUID4) -> Dict:
		"""Get payment summary for an organization"""
		total_paid = (
			self.db.query(func.sum(Payment.amount))
			.filter(Payment.organization_id == organization_id, Payment.status == PaymentStatus.SUCCESS)
			.scalar()
			or 0
		)

		payment_count = (
			self.db.query(func.count(Payment.id))
			.filter(Payment.organization_id == organization_id, Payment.status == PaymentStatus.SUCCESS)
			.scalar()
			or 0
		)

		outstanding = (
			self.db.query(func.sum(Invoice.total_amount))
			.filter(
				Invoice.organization_id == organization_id,
				Invoice.status.in_([InvoiceStatus.PENDING, InvoiceStatus.PARTIALLY_PAID]),
			)
			.scalar()
			or 0
		)

		return {
			"total_paid": float(total_paid),
			"payment_count": int(payment_count),
			"outstanding_balance": float(outstanding),
			"average_payment": float(total_paid / payment_count) if payment_count > 0 else 0,
		}

	async def _assess_organization_financial_health(self, organization_id: UUID4) -> Dict:
		"""Assess financial health of an organization"""
		# This is a simplified assessment - could be expanded with more sophisticated metrics
		summary = await self._get_organization_payment_summary(organization_id)

		health_score = 100
		health_factors = []

		if summary["outstanding_balance"] > 5000:
			health_score -= 30
			health_factors.append("High outstanding balance")
		elif summary["outstanding_balance"] > 1000:
			health_score -= 15
			health_factors.append("Moderate outstanding balance")

		if summary["payment_count"] < 3:
			health_score -= 20
			health_factors.append("Limited payment history")

		if health_score >= 80:
			health_rating = "Excellent"
		elif health_score >= 60:
			health_rating = "Good"
		elif health_score >= 40:
			health_rating = "Fair"
		else:
			health_rating = "Poor"

		return {"health_score": health_score, "health_rating": health_rating, "health_factors": health_factors}

	async def _calculate_collection_efficiency_metrics(self, start_date: date, end_date: date) -> Dict:
		"""Calculate collection efficiency metrics"""
		# Implementation similar to _get_collection_efficiency but more detailed
		return await self._get_collection_efficiency(start_date, end_date)

	async def _analyze_collection_trends(self, start_date: date, end_date: date) -> Dict:
		"""Analyze collection trends over the period"""
		# Weekly collection analysis
		weekly_data = (
			self.db.query(
				func.date_trunc("week", ApplicationFee.created_at).label("week"),
				func.sum(ApplicationFee.amount).label("amount"),
				func.count(ApplicationFee.id).label("count"),
			)
			.filter(ApplicationFee.created_at >= start_date, ApplicationFee.created_at <= end_date)
			.group_by(func.date_trunc("week", ApplicationFee.created_at))
			.all()
		)

		return {
			"weekly_trends": [
				{
					"week": row.week.strftime("%Y-%m-%d") if row.week else None,
					"amount": float(row.amount or 0),
					"count": int(row.count),
				}
				for row in weekly_data
			]
		}

	async def _generate_receivables_recommendations(self, aging_analysis: Dict) -> List[str]:
		"""Generate recommendations for receivables management"""
		recommendations = []

		high_priority_count = len(aging_analysis["90_plus_days"])
		medium_priority_count = len(aging_analysis["60_days"])

		if high_priority_count > 0:
			recommendations.append(
				f"Immediate collection action required for {high_priority_count} invoices over 90 days overdue"
			)

		if medium_priority_count > 0:
			recommendations.append(f"Send payment reminders for {medium_priority_count} invoices 60-90 days overdue")

		total_overdue = high_priority_count + medium_priority_count + len(aging_analysis["30_days"])
		if total_overdue > 10:
			recommendations.append("Consider implementing automated payment reminder system")

		return recommendations

	async def _generate_payment_method_insights(self, payment_method_stats) -> List[str]:
		"""Generate insights about payment method usage"""
		insights = []

		if not payment_method_stats:
			return ["No payment data available for analysis"]

		# Find most popular method
		most_popular = max(payment_method_stats, key=lambda x: x.transaction_count)
		insights.append(
			f"Most popular payment method: {most_popular.payment_method} ({most_popular.transaction_count} transactions)"
		)

		# Find highest value method
		highest_value = max(payment_method_stats, key=lambda x: x.total_amount)
		if highest_value.payment_method != most_popular.payment_method:
			insights.append(
				f"Highest value payment method: {highest_value.payment_method} (${highest_value.total_amount:.2f} total)"
			)

		return insights


financial_reports_service = FinancialReportsService()
