from datetime import datetime
from typing import Optional

from pydantic import Field, UUID4

from src.config.db.models.fee import FeeStatus
from src.core.shared_schema import BaseRequest


class FeeFilter(BaseRequest):
	"""Filter schema for fee queries"""

	name: Optional[str] = Field(default=None, description="Fee name filter")
	status: Optional[FeeStatus] = Field(default=None, description="Fee status filter")
	fee_category_id: Optional[UUID4] = Field(default=None, description="Fee category ID filter")
	currency_id: Optional[UUID4] = Field(default=None, description="Currency ID filter")
	based_on_income: Optional[bool] = Field(default=None, description="Filter by income-based fees")
	effective_from_start: Optional[datetime] = Field(default=None, description="Effective from start date")
	effective_from_end: Optional[datetime] = Field(default=None, description="Effective from end date")
	active_only: Optional[bool] = Field(default=False, description="Show only active fees")
	include_expired: Optional[bool] = Field(default=False, description="Include expired fees")
	version: Optional[int] = Field(default=None, description="Fee version filter")

	class Config:
		extra = "forbid"
