import secrets
import string
from datetime import date, datetime, timedelta
from typing import Optional

from fastapi_pagination import Params
from fastapi_pagination.ext.sqlalchemy import paginate
from pydantic import UUID4

from src.config.db.models import (
	Application,
	Invoice,
	InvoiceItem,
	InvoiceStatus,
	LoadableItem,
	Organization,
	Payment,
	PaymentStatus,
)
from src.core.base.base_repository import BaseRepository
from src.core.dtos.financial_dtos import (
	CreateInvoiceRequest,
	CreateInvoiceResponse,
	InvoiceDto,
	InvoiceItemDto,
	to_invoice_dto,
	to_invoice_item_dto,
)
from src.core.exceptions import ApiException
from src.core.logger.internal_logger import get_logger
from src.core.shared_schema import Pagination
from src.modules.financial.financial_schema import InvoiceFilter


class InvoiceService(BaseRepository):
	def __init__(self):
		super().__init__()

		self.logger = get_logger(__name__)

	async def retrieve_invoices(self, filter: InvoiceFilter) -> Pagination[InvoiceDto]:
		try:
			query = self.db.query(Invoice).order_by(Invoice.created_at.desc())

			if filter.organization_id:
				query = query.filter(Invoice.organization_id == filter.organization_id)

			if filter.start_date:
				query = query.filter(Invoice.created_at >= filter.start_date)

			if filter.end_date:
				query = query.filter(Invoice.created <= filter.end_date)

			query_result = paginate(query, Params(page=filter.page, size=filter.size))
			data = [to_invoice_dto(row, filter.segments) for row in query_result.items]

			return Pagination.from_query_result(data, query_result)
		except Exception as e:
			self.logger.error(f"Failed to fetch invoice items : {str(e)}")
			raise

	async def _generate_invoice_reference(self) -> str:
		"""Generate unique invoice reference number"""
		timestamp = datetime.now().strftime("%Y%m%d")
		random_suffix = "".join(secrets.choice(string.ascii_uppercase + string.digits) for _ in range(4))
		return f"INV{timestamp}{random_suffix}"

	async def get_invoice_by_application_id(self, application_id: UUID4) -> Optional[InvoiceDto]:
		"""Get existing invoice for an application"""
		try:
			invoice = (
				self.db.query(Invoice).join(InvoiceItem).filter(InvoiceItem.application_id == application_id).first()
			)

			if not invoice:
				return None

			return to_invoice_dto(invoice)
		except Exception as e:
			self.logger.error(f"Failed to get invoice by application ID: {str(e)}")
			raise

	async def get_first_invoice_item_by_application_id(self, application_id: UUID4) -> Optional[InvoiceItemDto]:
		"""Get the first invoice item for an application (for licence linking)"""
		try:
			invoice_item = self.db.query(InvoiceItem).filter(InvoiceItem.application_id == application_id).first()

			if not invoice_item:
				return None

			return to_invoice_item_dto(invoice_item)
		except Exception as e:
			self.logger.error(f"Failed to get invoice item by application ID: {str(e)}")
			raise

	async def create_invoice_for_application(self, request: CreateInvoiceRequest) -> CreateInvoiceResponse:
		"""Create invoice with items based on application fees"""
		try:
			existing_invoice = await self.get_invoice_by_application_id(request.application_id)
			if existing_invoice:
				items = self.db.query(InvoiceItem).filter(InvoiceItem.application_id == request.application_id).all()
				return CreateInvoiceResponse(
					invoice=existing_invoice, items=[to_invoice_item_dto(item) for item in items]
				)

			application = self.db.query(Application).filter(Application.id == request.application_id).first()

			if not application:
				raise ApiException("Application not found")

			if not application.application_fees:
				raise ApiException("No fees found for this application")

			total_amount = sum(fee.amount for fee in application.application_fees)

			if total_amount < 0:
				raise ApiException("Invalid fee amount")

			invoice = Invoice(
				reference_number=await self._generate_invoice_reference(),
				organization_id=request.organization_id,
				total_amount=total_amount,
				due_date=date.today() + timedelta(days=request.due_days),
				description=request.description or f"Application fees for {application.code}",
				status=InvoiceStatus.PENDING if total_amount > 0 else InvoiceStatus.PAID,
				created_by=self.current_user.id,
			)

			self.db.add(invoice)
			self.db.flush()

			invoice_items = []
			for app_fee in application.application_fees:
				invoice_item = InvoiceItem(
					invoice_id=invoice.id,
					application_id=request.application_id,
					amount=app_fee.amount,
					created_by=self.current_user.id,
				)
				self.db.add(invoice_item)
				invoice_items.append(invoice_item)

			self.db.flush()

			invoice_dto = to_invoice_dto(invoice)
			items_dto = [to_invoice_item_dto(item) for item in invoice_items]

			self.logger.info(f"Created invoice {invoice.reference_number} for application {application.code}")

			return CreateInvoiceResponse(invoice=invoice_dto, items=items_dto)

		except Exception as e:
			self.logger.error(f"Failed to create invoice for application: {str(e)}")
			raise

	async def generate_invoice_pdf(self, invoice_id: UUID4) -> tuple[bytes, str]:
		"""Generate professional PDF invoice with NGORA branding"""
		try:
			invoice = self.db.query(Invoice).filter(Invoice.id == invoice_id).first()
			if not invoice:
				raise ApiException("Invoice not found")

			organization = self.db.query(Organization).filter(Organization.id == invoice.organization_id).first()
			if not organization:
				raise ApiException("Organization not found")

			invoice_items = (
				self.db.query(InvoiceItem)
				.join(Application, InvoiceItem.application_id == Application.id, isouter=True)
				.filter(InvoiceItem.invoice_id == invoice_id)
				.all()
			)

			invoice_data = await self._prepare_invoice_data(invoice, organization, invoice_items)

			from src.core.generators.invoice_generator import InvoiceGenerator

			generator = InvoiceGenerator()

			pdf_bytes = generator.generate_pdf_bytes(invoice_data)
			return pdf_bytes, invoice.reference_number

		except Exception as e:
			self.logger.error(f"Failed to generate invoice PDF: {str(e)}")
			raise ApiException("Failed to generate invoice PDF")

	async def _prepare_invoice_data(self, invoice, organization, invoice_items) -> dict:
		"""Prepare data structure for invoice generator"""
		org_address = getattr(organization, "address", "") or ""
		if hasattr(organization, "physical_address") and organization.physical_address:
			org_address = organization.physical_address

		items = []
		for item in invoice_items:
			description = "Application Fee"
			if hasattr(item, "application") and item.application:
				description = f"Application Fee - {item.application.code}"

			items.append(
				{
					"description": description,
					"quantity": 1,
					"unit_price": float(item.amount),
					"amount": float(item.amount),
				}
			)

		subtotal = float(invoice.total_amount)
		total_amount = subtotal

		payment_modes = (
			self.db.query(LoadableItem).filter(LoadableItem.type == "PAYMENT_MODE", LoadableItem.is_active).all()
		)
		payment_methods = (
			[mode.display_value for mode in payment_modes]
			if payment_modes
			else ["Bank Transfer", "Mobile Money", "Cash Payment at NGORA Office"]
		)

		return {
			"invoice_number": invoice.reference_number,
			"organization_name": organization.name,
			"organization_address": org_address,
			"invoice_date": invoice.created_at.isoformat(),
			"due_date": invoice.due_date.isoformat(),
			"items": items,
			"subtotal": subtotal,
			"total_amount": total_amount,
			"currency": "MWK",
			"payment_terms": "Payment due within 30 days",
			"payment_methods": payment_methods,
			"notes": invoice.description or "Thank you for choosing NGORA services.",
			"status": invoice.status.value if hasattr(invoice.status, "value") else str(invoice.status),
		}

	async def generate_enhanced_invoice_number(self, year: Optional[int] = None) -> str:
		"""Generate enhanced invoice number with year-based sequence"""
		if not year:
			year = datetime.now().year

		last_invoice = (
			self.db.query(Invoice)
			.filter(Invoice.reference_number.like(f"INV-{year}-%"))
			.order_by(Invoice.created_at.desc())
			.first()
		)

		if last_invoice:
			parts = last_invoice.reference_number.split("-")
			if len(parts) >= 3:
				try:
					last_sequence = int(parts[2])
					new_sequence = last_sequence + 1
				except ValueError:
					new_sequence = 1
			else:
				new_sequence = 1
		else:
			new_sequence = 1

		return f"INV-{year}-{new_sequence:04d}"

	async def mark_invoice_as_paid(self, invoice_id: UUID4, payment_mode_id: UUID4) -> InvoiceDto:
		"""Mark invoice as paid by creating a payment record and updating invoice status"""
		try:
			invoice = self.db.query(Invoice).filter(Invoice.id == invoice_id).first()
			if not invoice:
				raise ApiException("Invoice not found")

			if invoice.status == InvoiceStatus.PAID:
				self.logger.warning(f"Invoice {invoice.reference_number} is already marked as PAID")
				return to_invoice_dto(invoice)

			existing_payment = self.db.query(Payment).filter(Payment.invoice_id == invoice_id).first()

			if not existing_payment:
				transaction_number = await self._generate_transaction_number()

				payment = Payment(
					amount=invoice.total_amount,
					organization_id=invoice.organization_id,
					invoice_id=invoice_id,
					transaction_number=transaction_number,
					payment_mode_id=payment_mode_id,
					status=PaymentStatus.SUCCESS,
					paid_by="Admin",
					created_by=self.current_user.id,
				)

				self.db.add(payment)
				self.logger.info(f"Created payment record {transaction_number} for invoice {invoice.reference_number}")

			invoice.status = InvoiceStatus.PAID
			invoice.updated_by = self.current_user.id

			self.db.flush()

			self.logger.info(f"Marked invoice {invoice.reference_number} as PAID")

			return to_invoice_dto(invoice)

		except Exception as e:
			self.logger.error(f"Failed to mark invoice as paid: {str(e)}")
			raise

	async def _generate_transaction_number(self) -> str:
		"""Generate unique transaction number"""
		import uuid

		prefix = "PAY"
		timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
		unique_suffix = str(uuid.uuid4())[:6].upper()
		return f"{prefix}{timestamp}{unique_suffix}"


invoice_service = InvoiceService()
