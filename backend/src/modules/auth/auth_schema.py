from datetime import datetime
from typing import Dict, Optional

from pydantic import UUID4, BaseModel, EmailStr

from src.config.db.models.base import Gender


class UserBase(BaseModel):
	first_name: str
	last_name: str
	middle_name: str | None = None
	username: str
	email: EmailStr
	gender: Gender


class UserCreate(UserBase):
	password: str
	phone: str | None = None


class UserResponse(UserBase):
	user_id: UUID4
	created_at: datetime
	updated_at: datetime

	class Config:
		from_attributes = True


class LoginResponse(UserResponse):
	auth: Dict[str, str]
	session_id: str
	session_type: str


class LoginRequest(BaseModel):
	identifier: str
	password: str
	remember_me: bool = False

	class Config:
		json_schema_extra = {
			"example": {"identifier": "<EMAIL>", "password": "securepassword123", "remember_me": True}
		}


class VerificationRetryRequest(BaseModel):
	email: EmailStr


class Token(BaseModel):
	access_token: str
	refresh_token: str
	token_type: str
	remember_me: bool = False
	expires_in: Optional[int] = None


class PasswordResetRequest(BaseModel):
	email: EmailStr


class EmailVerificationResponse(BaseModel):
	"""Schema for email verification response"""

	success: bool
	message: str
	user_id: Optional[str] = None

	class Config:
		json_schema_extra = {"example": {"success": True, "message": "Email verified successfully", "user_id": "12345"}}


class ResendVerificationRequest(BaseModel):
	"""Schema for resending verification email"""

	email: EmailStr

	class Config:
		json_schema_extra = {"example": {"email": "<EMAIL>"}}


class LogoutRequest(BaseModel):
	"""Request to logout - can be empty body or include session info"""

	all_sessions: bool = False
	session_id: Optional[str] = None
	revoke_remember_me: bool = False

	class Config:
		json_schema_extra = {"example": {"all_sessions": False, "session_id": "abc123", "revoke_remember_me": True}}


class LogoutResponse(BaseModel):
	success: bool
	message: str
	sessions_logged_out: int
	remember_me_sessions_revoked: Optional[int] = None


class SessionInfo(BaseModel):
	session_id: str
	device: Optional[str]
	ip_address: Optional[str]
	user_agent: Optional[str]
	created_at: datetime
	last_activity: datetime
	is_current: bool
	remember_me: bool = False


class UserSessionsResponse(BaseModel):
	sessions: list[SessionInfo]
	total_sessions: int
	remember_me_sessions: int = 0
	regular_sessions: int = 0


class PasswordConfirmationRequest(BaseModel):
	token: str
	password: str


class PasswordResetTokenResponse(BaseModel):
	token: str


# New schemas for remember me specific operations
class RememberMeStatusRequest(BaseModel):
	"""Request to check remember me status"""

	session_id: Optional[str] = None


class RememberMeStatusResponse(BaseModel):
	"""Response for remember me status"""

	is_remember_me: bool
	expires_at: Optional[datetime] = None
	days_remaining: Optional[int] = None


class RevokeRememberMeRequest(BaseModel):
	"""Request to revoke remember me sessions"""

	user_id: Optional[str] = None  # If admin operation
	all_devices: bool = True
	specific_session_id: Optional[str] = None


class RevokeRememberMeResponse(BaseModel):
	"""Response for revoking remember me sessions"""

	success: bool
	message: str
	sessions_revoked: int


# Enhanced refresh token request
class RefreshTokenRequest(BaseModel):
	refresh_token: str
	maintain_remember_me: bool = True


class RefreshTokenResponse(BaseModel):
	access_token: str
	token_type: str
	expires_in: int
	remember_me: bool = False


class RegistrationRequest(BaseModel):
	first_name: str
	last_name: str
	email: EmailStr
	handle: Optional[str] = None
	gender: Gender
	password: str
	is_external: bool = True
	created_by: Optional[UUID4] = None


class TwoFactorRequest(BaseModel):
	code: str | None = None
	email: EmailStr | None = None
	token: str | None = None
