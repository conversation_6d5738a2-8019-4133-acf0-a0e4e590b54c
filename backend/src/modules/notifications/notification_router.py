from fastapi import APIRouter

from src.core.dtos.notification_dtos import NotificationDto, NotificationWithRecipientDto
from src.core.shared_schema import BaseResponse, Pagination
from src.modules.notifications import notification_controller as controller

notification_router_v1 = APIRouter(tags=["notifications"])

notification_router_v1.add_api_route(
	path="/",
	methods=["GET"],
	endpoint=controller.fetch_notifications_handler,
	response_model=Pagination[NotificationWithRecipientDto],
	summary="Get user notifications",
	description="Fetch notifications for the authenticated user with filtering and pagination",
)

notification_router_v1.add_api_route(
	path="/all",
	methods=["GET"],
	endpoint=controller.fetch_all_notifications_handler,
	response_model=Pagination[NotificationDto],
	summary="Get all notifications (admin)",
	description="Fetch all notifications - requires admin permissions",
)

notification_router_v1.add_api_route(
	path="/",
	methods=["POST"],
	endpoint=controller.create_notification_handler,
	response_model=BaseResponse[NotificationDto],
	summary="Create notification",
	description="Create a new notification and send to recipients",
)

notification_router_v1.add_api_route(
	path="/mark-all-read",
	methods=["POST"],
	endpoint=controller.mark_all_as_read_handler,
	response_model=BaseResponse[bool],
	summary="Mark all notifications as read",
	description="Mark all unread notifications as read for the current user",
)

notification_router_v1.add_api_route(
	path="/unread-count",
	methods=["GET"],
	endpoint=controller.get_unread_count_handler,
	response_model=BaseResponse[int],
	summary="Get unread notification count",
	description="Get the count of unread, non-archived notifications for the current user",
)

notification_router_v1.add_api_route(
	path="/{notification_id}",
	methods=["GET"],
	endpoint=controller.get_notification_handler,
	response_model=BaseResponse[NotificationDto],
	summary="Get notification by ID",
	description="Retrieve a specific notification by its ID",
)

notification_router_v1.add_api_route(
	path="/{notification_id}/status",
	methods=["PATCH"],
	endpoint=controller.update_notification_status_handler,
	response_model=BaseResponse[NotificationWithRecipientDto],
	summary="Update notification status",
	description="Update notification read/archived status for the current user",
)

notification_router_v1.add_api_route(
	path="/{notification_id}",
	methods=["DELETE"],
	endpoint=controller.delete_notification_handler,
	response_model=BaseResponse[bool],
	summary="Delete notification",
	description="Delete (void) a notification - requires admin permissions",
)
