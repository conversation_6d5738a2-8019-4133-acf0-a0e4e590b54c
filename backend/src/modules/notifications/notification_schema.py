from typing import Optional, List

from pydantic import BaseModel, Field, UUID4

from src.config.db.models import NotificationType, NotificationPriority
from src.core.shared_schema import BaseRequest


class NotificationFilter(BaseRequest):
	type: Optional[NotificationType] = Field(default=None, description="Type of the notification")
	priority: Optional[NotificationPriority] = Field(default=None, description="Priority of the notification")
	is_read: Optional[bool] = Field(default=None, description="Filter by read status")
	is_archived: Optional[bool] = Field(default=None, description="Filter by archived status")
	sender_id: Optional[UUID4] = Field(default=None, description="Filter by sender ID")


class CreateNotificationRequest(BaseModel):
	title: str = Field(..., description="Title of the notification")
	message: str = Field(..., description="Message content of the notification")
	type: NotificationType = Field(default=NotificationType.INFO, description="Type of the notification")
	activity_id: Optional[UUID4] = Field(default=None, description="Related activity ID")
	sender_id: Optional[UUID4] = Field(default=None, description="Sender user ID")
	priority: NotificationPriority = Field(
		default=NotificationPriority.MEDIUM, description="Priority of the notification"
	)
	recipient_ids: List[UUID4] = Field(..., description="List of recipient account IDs")


class UpdateNotificationStatusRequest(BaseModel):
	is_read: Optional[bool] = Field(default=None, description="Mark as read/unread")
	is_archived: Optional[bool] = Field(default=None, description="Mark as archived/unarchived")
