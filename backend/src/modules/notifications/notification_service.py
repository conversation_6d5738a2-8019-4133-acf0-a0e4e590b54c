from datetime import datetime, timezone
from typing import Optional

from fastapi_pagination import Params
from fastapi_pagination.ext.sqlalchemy import paginate
from pydantic import UUID4
from sqlalchemy.orm import joinedload

from src.config.db.models import Notification, NotificationRecipient
from src.config.db.models.user import User
from src.core.base.base_repository import BaseRepository
from src.core.dtos.notification_dtos import (
	NotificationDto,
	NotificationWithRecipientDto,
	to_notification_dto,
	to_notification_with_recipient_dto,
)
from src.core.logger.internal_logger import get_logger
from src.core.shared_schema import Pagination
from src.modules.notifications.notification_schema import (
	CreateNotificationRequest,
	NotificationFilter,
	UpdateNotificationStatusRequest,
)


class NotificationService(BaseRepository):
	def __init__(self):
		super().__init__()

		self.logger = get_logger(__name__)

	async def retrieve_notifications_for_account(
		self, filter: NotificationFilter
	) -> Pagination[NotificationWithRecipientDto]:
		"""Retrieve notifications for a specific account with recipient info"""
		try:
			user = self.db.query(User.account_id).filter(User.id == self.current_user.id).first()

			query = (
				self.db.query(NotificationRecipient)
				.join(Notification, NotificationRecipient.notification_id == Notification.id)
				.options(joinedload(NotificationRecipient.notification))
				.filter(NotificationRecipient.account_id == user.account_id)
			)

			if filter.type is not None:
				query = query.filter(Notification.type == filter.type)

			if filter.priority is not None:
				query = query.filter(Notification.priority == filter.priority)

			if filter.is_read is not None:
				query = query.filter(NotificationRecipient.is_read == filter.is_read)

			if filter.is_archived is not None:
				query = query.filter(NotificationRecipient.is_archived == filter.is_archived)

			if filter.sender_id is not None:
				query = query.filter(Notification.sender_id == filter.sender_id)

			if filter.start_date:
				query = query.filter(Notification.created_at >= filter.start_date)

			if filter.end_date:
				query = query.filter(Notification.created_at <= filter.end_date)

			query = query.order_by(Notification.created_at.desc())

			query_result = paginate(query, Params(page=filter.page, size=filter.size))
			data = [to_notification_with_recipient_dto(row) for row in query_result.items]

			return Pagination.from_query_result(data, query_result)
		except Exception as e:
			self.logger.error(f"Failed to retrieve notifications: {str(e)}")
			raise

	async def retrieve_notifications(self, filter: NotificationFilter) -> Pagination[NotificationDto]:
		"""Retrieve all notifications (admin view)"""
		try:
			query = self.db.query(Notification)

			if filter.type is not None:
				query = query.filter(Notification.type == filter.type)

			if filter.priority is not None:
				query = query.filter(Notification.priority == filter.priority)

			if filter.sender_id is not None:
				query = query.filter(Notification.sender_id == filter.sender_id)

			if filter.start_date:
				query = query.filter(Notification.created_at >= filter.start_date)

			if filter.end_date:
				query = query.filter(Notification.created_at <= filter.end_date)

			query = query.order_by(Notification.created_at.desc())

			query_result = paginate(query, Params(page=filter.page, size=filter.size))
			data = [to_notification_dto(row) for row in query_result.items]

			return Pagination.from_query_result(data, query_result)
		except Exception as e:
			self.logger.error(f"Failed to retrieve notifications: {str(e)}")
			raise

	async def create_notification(self, request: CreateNotificationRequest) -> NotificationDto:
		"""Create a new notification and send to recipients"""
		try:
			notification = Notification(
				title=request.title,
				message=request.message,
				type=request.type,
				activity_id=request.activity_id,
				sender_id=request.sender_id,
				priority=request.priority,
				created_by=self.current_user.id,
			)

			self.db.add(notification)
			self.db.flush()

			for recipient_id in request.recipient_ids:
				recipient = NotificationRecipient(
					notification_id=notification.id,
					account_id=recipient_id,
					is_read=False,
					is_archived=False,
					created_by=self.current_user.id,
				)
				self.db.add(recipient)

			self.db.commit()

			return to_notification_dto(notification)
		except Exception as e:
			self.db.rollback()
			self.logger.error(f"Failed to create notification: {str(e)}")
			raise

	async def update_notification_status(
		self, notification_id: UUID4, request: UpdateNotificationStatusRequest
	) -> NotificationWithRecipientDto:
		"""Update notification status for a specific recipient"""
		try:
			user = self.db.query(User.account_id).filter(User.id == self.current_user.id).first()
			recipient = (
				self.db.query(NotificationRecipient)
				.options(joinedload(NotificationRecipient.notification))
				.filter(
					NotificationRecipient.notification_id == notification_id,
					NotificationRecipient.account_id == user.account_id,
				)
				.first()
			)

			if not recipient:
				raise ValueError("Notification recipient not found")

			if request.is_read is not None:
				recipient.is_read = request.is_read
				if request.is_read:
					recipient.read_at = datetime.now(timezone.utc)
				else:
					recipient.read_at = None

			if request.is_archived is not None:
				recipient.is_archived = request.is_archived

			recipient.updated_by = self.current_user.id

			self.db.commit()

			return to_notification_with_recipient_dto(recipient)
		except Exception as e:
			self.db.rollback()
			self.logger.error(f"Failed to update notification status: {str(e)}")
			raise

	async def mark_all_as_read(self, account_id: UUID4) -> bool:
		"""Mark all unread notifications as read for an account"""
		try:
			current_time = datetime.now(timezone.utc)
			updated_count = (
				self.db.query(NotificationRecipient)
				.filter(NotificationRecipient.account_id == account_id, NotificationRecipient.is_read.is_(False))
				.update(
					{
						"is_read": True,
						"read_at": current_time,
						"updated_by": self.current_user.id,
					}
				)
			)

			self.db.commit()
			return updated_count > 0
		except Exception as e:
			self.db.rollback()
			self.logger.error(f"Failed to mark all notifications as read: {str(e)}")
			raise

	async def get_unread_count(self) -> int:
		"""Get count of unread notifications for an account"""
		try:
			user = self.db.query(User.account_id).filter(User.id == self.current_user.id).first()
			count = (
				self.db.query(NotificationRecipient)
				.filter(
					NotificationRecipient.account_id == user.account_id,
					NotificationRecipient.is_read.is_(False),
					NotificationRecipient.is_archived.is_(False),
				)
				.count()
			)
			return count
		except Exception as e:
			self.logger.error(f"Failed to get unread count: {str(e)}")
			raise

	async def get_notification_by_id(self, notification_id: UUID4) -> Optional[NotificationDto]:
		"""Get a specific notification by ID"""
		try:
			notification = self.db.query(Notification).filter(Notification.id == notification_id).first()
			return to_notification_dto(notification) if notification else None
		except Exception as e:
			self.logger.error(f"Failed to get notification by ID: {str(e)}")
			raise

	async def delete_notification(self, notification_id: UUID4) -> bool:
		"""Soft delete a notification (void it)"""
		try:
			notification = self.db.query(Notification).filter(Notification.id == notification_id).first()
			if not notification:
				return False

			notification.voided = True
			notification.voided_by = self.current_user.id
			notification.void_reason = "Notification deleted"

			self.db.commit()
			return True
		except Exception as e:
			self.db.rollback()
			self.logger.error(f"Failed to delete notification: {str(e)}")
			raise


notification_service = NotificationService()
