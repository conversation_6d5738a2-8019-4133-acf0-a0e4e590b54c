from fastapi import Depends, HTTPException, Request, status
from pydantic import UUID4

from src.core.dtos.notification_dtos import NotificationDto, NotificationWithRecipientDto
from src.core.guards.auth_guard import auth_guard
from src.core.shared_schema import BaseResponse, Pagination
from src.modules.notifications.notification_schema import (
	CreateNotificationRequest,
	NotificationFilter,
	UpdateNotificationStatusRequest,
)
from src.modules.notifications.notification_service import notification_service


@auth_guard.authenticated
async def fetch_notifications_handler(
	_: Request,
	filter: NotificationFilter = Depends(NotificationFilter),
) -> Pagination[NotificationWithRecipientDto]:
	notifications = await notification_service.retrieve_notifications_for_account(filter)
	return notifications


@auth_guard.authenticated
async def fetch_all_notifications_handler(
	_: Request,
	filter: NotificationFilter = Depends(NotificationFilter),
) -> Pagination[NotificationDto]:
	"""Fetch all notifications (admin view)"""
	notifications = await notification_service.retrieve_notifications(filter)
	return notifications


@auth_guard.authenticated
async def create_notification_handler(
	_: Request,
	request_data: CreateNotificationRequest,
) -> BaseResponse[NotificationDto]:
	"""Create a new notification"""
	notification = await notification_service.create_notification(request_data)
	return BaseResponse(data=notification)


@auth_guard.authenticated
async def update_notification_status_handler(
	_: Request,
	notification_id: UUID4,
	request_data: UpdateNotificationStatusRequest,
) -> BaseResponse[NotificationWithRecipientDto]:
	"""Update notification status (read/archived)"""
	try:
		updated_notification = await notification_service.update_notification_status(notification_id, request_data)
		return BaseResponse(data=updated_notification)
	except ValueError as e:
		raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))


@auth_guard.authenticated
async def mark_all_as_read_handler(
	_: Request,
) -> BaseResponse[bool]:
	"""Mark all notifications as read for the authenticated user"""
	success = await notification_service.mark_all_as_read()
	return BaseResponse(data=success)


@auth_guard.authenticated
async def get_unread_count_handler(
	_: Request,
) -> BaseResponse[int]:
	"""Get unread notification count for the authenticated user"""
	count = await notification_service.get_unread_count()
	return BaseResponse(data=count)


@auth_guard.authenticated
async def get_notification_handler(
	_: Request,
	notification_id: UUID4,
) -> BaseResponse[NotificationDto]:
	"""Get a specific notification by ID"""
	notification = await notification_service.get_notification_by_id(notification_id)
	if not notification:
		raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Notification not found")
	return BaseResponse(data=notification)


@auth_guard.authenticated
async def delete_notification_handler(
	_: Request,
	notification_id: UUID4,
) -> BaseResponse[bool]:
	"""Delete (void) a notification"""
	success = await notification_service.delete_notification(notification_id)
	if not success:
		raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Notification not found")
	return BaseResponse(data=success)
