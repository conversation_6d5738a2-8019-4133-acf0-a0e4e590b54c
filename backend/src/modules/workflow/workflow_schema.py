from typing import List, Optional

from pydantic import UUID4, BaseModel, Field

from src.config.db.models.application import ApplicationStatus
from src.config.db.models.template_stage_trigger import ActionMode
from src.core.shared_schema import BaseRequest


class TemplateFilter(BaseRequest):
	name: Optional[str] = Field(None, description="Workflow template name")


class TemplateStageRoleRequest(BaseModel):
	role_id: UUID4 = Field(..., description="ID of the role associated with this stage")


class TemplateStageTriggerRequest(BaseModel):
	trigger_id: UUID4 = Field(..., description="ID of the trigger associated with this stage")
	action_mode: ActionMode = Field(..., description="Action mode for the trigger (BEFORE or AFTER)")


class TemplateStageRequest(BaseModel):
	name: str = Field(..., min_length=1, description="Name of the template stage")
	description: Optional[str] = Field(None, description="Description of the template stage")
	is_active: bool = Field(True, description="Is the template stage active")
	position: int = Field(..., description="Position of the template stage in the workflow")
	roles: List[str] = Field(default_factory=list, description="List of role IDs associated with this stage")
	roles: List[str] = Field(default_factory=list, description="List of role IDs associated with this stage")
	triggers: List[TemplateStageTriggerRequest] = Field(
		default_factory=list, description="List of trigger IDs associated with this stage"
	)


class TemplateRequest(BaseModel):
	name: str = Field(min_length=5, description="Workflow template name")
	code: str = Field(min_length=3, description="Workflow template code")
	description: Optional[str] = Field(None, description="Workflow template description")
	is_active: bool = Field(True, description="Workflow template is active")
	stages: List[TemplateStageRequest] = Field(
		default_factory=list, description="List of stages associated with this template"
	)


class TemplateStageFilter(BaseRequest):
	name: Optional[str] = Field(None, description="Template stage name")
	is_active: Optional[bool] = Field(default=True, description="Is the template stage active")


class TemplateStageRoleFilter(BaseRequest):
	is_active: Optional[bool] = Field(default=True, description="Is the template stage role active")


class TemplateStageTriggerFilter(BaseRequest):
	action_mode: Optional[ActionMode] = Field(None, description="Action mode for the trigger (BEFORE or AFTER)")


class WorkflowDashboardFilter(BaseRequest):
	search: Optional[str] = Field(None, description="Search by organization name or application code")
	status: Optional[ApplicationStatus] = Field(None, description="Filter by workflow status")
	priority: Optional[str] = Field(None, description="Filter by priority (HIGH, MEDIUM, LOW)")
	assigned_to_me: Optional[bool] = Field(None, description="Filter workflows assigned to current user")
	application_type: Optional[str] = Field(None, description="Filter by application type")
	role_code: Optional[str] = Field(None, description="Filter by role code")


class WorkflowActionRequest(BaseModel):
	comment: Optional[str] = Field(None, description="Comments for the action")
	reason: Optional[str] = Field(None, description="Reason for rejection (required for reject)")


class WorkflowStageFilter(BaseRequest):
	stage_id: Optional[UUID4] = Field(None, description="Filter by specific stage ID")
	status: Optional[str] = Field(None, description="Filter by stage status (e.g., PENDING, APPROVED, REJECTED)")
	role_code: Optional[str] = Field(None, description="Filter by role code associated with the stage")
	search: Optional[str] = Field(None, description="Search by organization name or application code")
