from datetime import datetime, timedelta, timezone
from typing import List, Optional

from fastapi_pagination import Params
from fastapi_pagination.ext.sqlalchemy import paginate
from pydantic import UUID4
from sqlalchemy import and_, desc, func, or_
from sqlalchemy.orm import joinedload

from src.config import settings
from src.config.db.models import (
	Application,
	ApplicationStatus,
	ApplicationType,
	Document,
	LoadableItem,
	Template,
	User,
	Workflow,
	WorkflowStageComment,
)
from src.config.db.models.application_document import ApplicationDocument
from src.config.db.models.invoice import Invoice, InvoiceStatus
from src.config.db.models.invoice_item import InvoiceItem
from src.config.db.models.organization import Organization, OrganizationStatus
from src.config.db.models.role import Role
from src.config.db.models.template_stage import TemplateStage
from src.config.db.models.template_stage_role import TemplateStageRole
from src.config.db.models.template_stage_trigger import TemplateStageTrigger
from src.config.db.models.workflow_stage import WorkflowStage, WorkflowStageStatus
from src.core.base.base_repository import BaseRepository
from src.core.dtos.application_dtos import to_application_summary_dto
from src.core.dtos.financial_dtos import FinanceWorkflowStageDto, to_finance_workflow_stage_dto
from src.core.dtos.workflow_dtos import (
	CurrentStageDto,
	TemplateDto,
	TemplateStageDto,
	TemplateStageRoleDto,
	TemplateStageTriggerDto,
	TemplateSummaryDto,
	WorkflowDashboardDto,
	WorkflowListDto,
	WorkflowStageDetailDto,
	WorkflowStatsDto,
	to_template_dto,
	to_template_stage_dto,
	to_template_stage_role_dto,
	to_template_stage_trigger_dto, to_workflow_stage_dto, WorkflowStageDto, to_workflow_stage_detail_dto,
)
from src.core.exceptions.api import ApiException
from src.core.services.notification_service import NotificationService
from src.core.shared_schema import Pagination, VoidRequest
from src.core.utils.common import capitalize_words
from src.modules.financial.invoice_service import InvoiceService
from src.modules.licence.licence_renewal_service import LicenceRenewalService
from src.modules.licence.licence_service import LicenceService
from src.modules.workflow.workflow_schema import (
	TemplateFilter,
	TemplateRequest,
	TemplateStageFilter,
	TemplateStageRequest,
	TemplateStageRoleFilter,
	TemplateStageRoleRequest,
	TemplateStageTriggerFilter,
	TemplateStageTriggerRequest,
	WorkflowActionRequest,
	WorkflowDashboardFilter,
	WorkflowStageFilter,
)


class WorkflowService(BaseRepository):
	"""
	Service class for managing workflow templates.
	Inherits from BaseRepository for database operations.
	"""

	def __init__(self):
		super().__init__()
		self._notification_service = None
		self._licence_service = None
		self._licence_renewal_service = None
		self._invoice_service = None

	@property
	def notification_service(self):
		"""Lazy-loaded NotificationService"""
		if self._notification_service is None:
			self._notification_service = NotificationService()
		return self._notification_service

	@property
	def licence_service(self):
		"""Lazy-loaded LicenceService"""
		if self._licence_service is None:
			self._licence_service = LicenceService()
		return self._licence_service

	@property
	def licence_renewal_service(self):
		"""Lazy-loaded LicenceRenewalService"""
		if self._licence_renewal_service is None:
			self._licence_renewal_service = LicenceRenewalService()
		return self._licence_renewal_service

	@property
	def invoice_service(self):
		"""Lazy-loaded InvoiceService"""
		if self._invoice_service is None:
			self._invoice_service = InvoiceService()
		return self._invoice_service


	async def retrieve_templates(self, filter: TemplateFilter) -> Pagination[TemplateDto]:
		try:
			query = self.db.query(Template)

			if filter.name:
				query = query.filter(Template.name.ilike(f"%{filter.name}%"))

			result = paginate(query, Params(page=filter.page, size=filter.size))
			data = [TemplateDto.model_construct(**row.__dict__) for row in result.items]

			return Pagination.from_query_result(data, result)
		except Exception as e:
			self.logger.error(f"Failed to retrieve workflow template: {e}")
			raise

	async def get_template_by_id(self, template_id: UUID4) -> TemplateDto:
		try:
			template = self.db.query(Template).filter(Template.id == template_id).first()

			if template is None:
				raise ApiException("Workflow template not found")

			return template
		except Exception as e:
			self.logger.error(f"Failed to retrieve workflow template: {e}")
			raise ApiException("Failed to retrieve workflow template")

	async def create_template(self, payload: TemplateRequest) -> TemplateDto:
		try:
			with self.db.begin_nested():
				existing_template = (
					self.db.query(Template)
					.filter(or_(Template.name == payload.name, Template.code == payload.code))
					.first()
				)

				if existing_template:
					raise ApiException("Workflow template with the same code or name already exists")

				template = Template(
					name=capitalize_words(payload.name), code=payload.code.upper(), description=payload.description
				)

				self.db.add(template)
				self.db.flush()

				if payload.stages:
					await self.add_template_stage(template.id, payload.stages)

				self.db.commit()

			return template
		except ApiException:
			self.db.rollback()
			raise
		except Exception as e:
			self.db.rollback()
			self.logger.error(f"Failed to create workflow template: {e}")
			raise

	async def update_template(self, template_id: UUID4, payload: TemplateRequest) -> TemplateDto:
		try:
			template = self.db.query(Template).filter(Template.id == template_id).first()

			if not template:
				raise ApiException("Workflow template does not exists")

			template.name = capitalize_words(payload.name)
			template.description = payload.description
			template.updated_by = self.current_user.id

			self.db.add(template)
			self.db.commit()
			self.db.refresh(template)

			return to_template_dto(template)
		except ApiException:
			raise
		except Exception as e:
			self.logger.error(f"Failed to update workflow template: {e}")
			raise

	async def void_template(self, template_id: UUID4, payload: VoidRequest) -> bool:
		try:
			template = self.db.query(Template).filter(Template.id == template_id).first()

			if not template:
				raise ApiException("Workflow template does not exists")

			template.void_reason = payload.void_reason

			self.db.add(template)
			self.db.commit()
			self.db.refresh(template)

			return True
		except ApiException:
			raise
		except Exception as e:
			self.logger.error(f"Failed to void workflow template: {e}")
			raise

	async def add_template_stage_roles(
		self, stage_id: UUID4, roles: List[TemplateStageRoleRequest]
	) -> List[TemplateStageRoleDto]:
		try:
			stage = self.db.query(TemplateStage).filter(TemplateStage.id == stage_id).first()

			self.db.query(TemplateStageRole).filter(TemplateStageRole.template_stage_id == stage_id).delete()

			if not stage:
				raise ApiException("Workflow template stage does not exists")

			new_roles = []
			for role_id in roles:
				existing_role = (
					self.db.query(TemplateStageRole)
					.filter(and_(TemplateStageRole.template_stage_id == stage_id, TemplateStageRole.role_id == role_id))
					.first()
				)
				if existing_role:
					raise ApiException(f"Role with ID '{role_id}' already exists for this stage")

				new_role = TemplateStageRole(
					template_stage_id=stage_id,
					role_id=role_id,
					is_active=True,
				)
				new_roles.append(new_role)

			self.db.add_all(new_roles)
			self.db.flush()

			if not self.db.in_transaction() or not self.db.in_nested_transaction():
				self.db.commit()

			return [to_template_stage_role_dto(role) for role in new_roles]
		except ApiException:
			raise
		except Exception as e:
			self.logger.error(f"Failed to add roles to workflow template stage: {e}")
			raise

	async def add_template_stage_triggers(
		self, stage_id: UUID4, triggers: List[TemplateStageTriggerRequest]
	) -> List[TemplateStageTriggerDto]:
		try:
			stage = self.db.query(TemplateStage).filter(TemplateStage.id == stage_id).first()

			if not stage:
				raise ApiException("Workflow template stage does not exists")

			new_triggers = []
			for trigger in triggers:
				existing_trigger = (
					self.db.query(TemplateStageTrigger)
					.filter(
						and_(
							TemplateStageTrigger.template_stage_id == stage_id,
							TemplateStageTrigger.function_id == trigger.function_id,
						)
					)
					.first()
				)
				if existing_trigger:
					raise ApiException(
						f"Trigger with ID '{existing_trigger.function.display_value}' already exists for this stage"
					)

				new_trigger = TemplateStageTrigger(
					template_stage_id=stage_id,
					function_id=trigger.function_id,
					action_mode=trigger.action_mode,
					created_by=self.current_user.id,
				)
				new_triggers.append(new_trigger)

			self.db.add_all(new_triggers)
			self.db.flush()

			if not self.db.in_transaction() or not self.db.in_nested_transaction():
				self.db.commit()

			return [to_template_stage_trigger_dto(trigger) for trigger in new_triggers]
		except ApiException:
			raise
		except Exception as e:
			self.logger.error(f"Failed to add triggers to workflow template stage: {e}")
			raise

	async def retrieve_template_stages(
		self, template_id: UUID4, filter: TemplateStageFilter
	) -> Pagination[TemplateStageDto]:
		try:
			query = self.db.query(TemplateStage).filter(TemplateStage.template_id == template_id)

			if filter.name:
				query = query.filter(TemplateStage.name.ilike(f"%{filter.name}%"))

			if filter.is_active:
				query = query.filter(TemplateStage.is_active == filter.is_active)

			query_result = paginate(query, Params(page=filter.page, size=filter.size))
			data = [row for row in query_result.items]

			return Pagination.from_query_result(data, query_result)
		except Exception as e:
			self.logger.error(f"Failed to retrieve template stages: {e}")
			raise

	async def update_template_stage(self, stage_id: UUID4, payload: TemplateStageRequest) -> TemplateStageDto:
		try:
			stage = self.db.query(TemplateStage).filter(TemplateStage.id == stage_id).first()

			if not stage:
				raise ApiException("Workflow template stage does not exists")

			current_position = stage.position
			new_position = payload.position

			total_stages = self.db.query(TemplateStage).filter(TemplateStage.template_id == stage.template_id).count()

			if new_position > total_stages:
				new_position = total_stages
			elif new_position < 1:
				new_position = 1

			if current_position != new_position:
				if new_position > current_position:
					self.db.query(TemplateStage).filter(
						and_(
							TemplateStage.template_id == stage.template_id,
							TemplateStage.position > current_position,
							TemplateStage.position <= new_position,
						)
					).update({TemplateStage.position: TemplateStage.position - 1})
				else:
					self.db.query(TemplateStage).filter(
						and_(
							TemplateStage.template_id == stage.template_id,
							TemplateStage.position >= new_position,
							TemplateStage.position < current_position,
						)
					).update({TemplateStage.position: TemplateStage.position + 1})

			stage.name = capitalize_words(payload.name)
			stage.description = payload.description
			stage.is_active = payload.is_active
			stage.position = new_position

			if payload.roles:
				await self.add_template_stage_roles(stage_id, payload.roles)

			self.db.add(stage)
			self.db.commit()
			self.db.refresh(stage)

			return to_template_stage_dto(stage)

		except ApiException:
			raise
		except Exception as e:
			self.logger.error(f"Failed to update workflow template stage: {e}")
			raise

	async def void_template_stage(self, stage_id: UUID4, payload: VoidRequest) -> bool:
		try:
			stage = self.db.query(TemplateStage).filter(TemplateStage.id == stage_id).first()

			if not stage:
				raise ApiException("Workflow template stage does not exists")

			stage.void_reason = payload.void_reason
			stage.voided = True

			self.db.commit()

			self.db.commit()

			return True
		except ApiException:
			raise
		except Exception as e:
			self.logger.error(f"Failed to void workflow template stage: {e}")
			raise

	async def retrieve_template_stage_roles(
		self, stage_id: UUID4, filter: TemplateStageRoleFilter
	) -> Pagination[TemplateStageRoleDto]:
		try:
			query = self.db.query(TemplateStageRole).filter(TemplateStageRole.template_stage_id == stage_id)

			if filter.is_active:
				query = query.filter(TemplateStageRole.is_active == filter.is_active)

			query_result = paginate(query, Params(page=filter.page, size=filter.size))
			data = [to_template_stage_role_dto(row) for row in query_result.items]

			return Pagination.from_query_result(data, query_result)
		except Exception as e:
			self.logger.error(f"Failed to retrieve template stage roles: {e}")
			raise

	async def void_template_stage_role(self, stage_role_id: UUID4, payload: VoidRequest) -> bool:
		try:
			role = self.db.query(TemplateStageRole).filter(TemplateStageRole.id == stage_role_id).first()

			if not role:
				raise ApiException("Template stage role does not exists")

			role.voided = True
			role.voided_by = self.current_user.id
			role.void_reason = payload.void_reason

			self.db.add(role)
			self.db.commit()
			self.db.refresh(role)

			return True
		except ApiException:
			raise
		except Exception as e:
			self.logger.error(f"Failed to void template stage role: {e}")
			raise

	async def retrieve_template_stage_triggers(
		self, stage_id: UUID4, filter: TemplateStageTriggerFilter
	) -> Pagination[TemplateStageTriggerDto]:
		try:
			query = self.db.query(TemplateStageTrigger).filter(TemplateStageTrigger.template_stage_id == stage_id)

			if filter.action_mode:
				query = query.filter(TemplateStageTrigger.action_mode == filter.action_mode)

			query_result = paginate(query, Params(page=filter.page, size=filter.size))
			data = [to_template_stage_trigger_dto(row) for row in query_result.items]

			return Pagination.from_query_result(data, query_result)
		except Exception as e:
			self.logger.error(f"Failed to retrieve template stage triggers: {e}")
			raise

	async def void_template_stage_trigger(self, stage_trigger_id: UUID4, payload: VoidRequest) -> bool:
		try:
			trigger = self.db.query(TemplateStageTrigger).filter(TemplateStageTrigger.id == stage_trigger_id).first()

			if not trigger:
				raise ApiException("Template stage trigger does not exists")

			trigger.voided = True
			trigger.voided_by = self.current_user.id
			trigger.void_reason = payload.void_reason

			self.db.add(trigger)
			self.db.commit()
			self.db.refresh(trigger)

			return True
		except ApiException:
			raise
		except Exception as e:
			self.logger.error(f"Failed to void template stage trigger: {e}")
			raise

	async def add_template_stage(
		self, template_id: UUID4, payload: (TemplateStageRequest | List[TemplateStageRequest])
	) -> List[TemplateStageDto]:
		try:
			template = self.db.query(Template).filter(Template.id == template_id).first()

			if not template:
				raise ApiException("Workflow template does not exists")

			stages_to_create = payload if isinstance(payload, list) else [payload]

			stages = []
			for stage in stages_to_create:
				existing_stage = (
					self.db.query(TemplateStage)
					.filter(
						or_(
							and_(TemplateStage.name == stage.name, TemplateStage.template_id == template_id),
							and_(TemplateStage.template_id == template_id, TemplateStage.position == stage.position),
						)
					)
					.first()
				)
				if existing_stage:
					raise ApiException(f"Stage with name '{stage.name}' or position '{stage.position}' already exists")

				created_stage = TemplateStage(
					template_id=template_id,
					name=capitalize_words(stage.name),
					description=stage.description,
					is_active=stage.is_active,
					position=stage.position,
				)

				self.db.add(created_stage)
				self.db.flush()

				stages.append(created_stage)

				await self.add_template_stage_roles(created_stage.id, stage.roles)
				await self.add_template_stage_triggers(created_stage.id, stage.triggers)

			self.db.commit()
			return stages
		except ApiException:
			raise
		except Exception as e:
			self.logger.error(f"Failed to add stage to workflow template: {e}")
			raise

	async def retrieve_workflow_dashboard(self, filter: WorkflowDashboardFilter) -> Pagination[WorkflowListDto]:
		"""Retrieve workflows for dashboard with filtering - simplified to avoid transaction issues"""
		try:
			query = self.db.query(Workflow).options(
				joinedload(Workflow.application).joinedload(Application.organization),
				joinedload(Workflow.template),
				joinedload(Workflow.stages),
			)

			if filter.search:
				search_term = f"%{filter.search}%"
				application_ids = self.db.query(Application.id).filter(Application.code.ilike(search_term)).subquery()
				query = query.filter(Workflow.application_id.in_(application_ids))

			if filter.status:
				application_ids = self.db.query(Application.id).filter(Application.status == filter.status).subquery()
				query = query.filter(Workflow.application_id.in_(application_ids))

			if filter.application_type:
				application_ids = (
					self.db.query(Application.id).filter(Application.type == filter.application_type.upper()).subquery()
				)
				query = query.filter(Workflow.application_id.in_(application_ids))

			if filter.assigned_to_me:
				query = (
					query.join(WorkflowStage)
					.join(TemplateStageRole, WorkflowStage.template_stage_id == TemplateStageRole.template_stage_id)
					.filter(
						and_(
							WorkflowStage.status == WorkflowStageStatus.IN_REVIEW,
							TemplateStageRole.role_id == self.current_user.role_id,
						)
					)
				)
			query = query.order_by(desc(Workflow.created_at))

			result = paginate(query, Params(page=filter.page, size=filter.size))

			workflow_dtos = []
			for workflow in result.items:
				current_stage = None
				stages_count = len([s for s in workflow.template.stages if s.is_active])

				active_stages = [
					s
					for s in workflow.stages
					if s.status in [WorkflowStageStatus.IN_REVIEW, WorkflowStageStatus.PENDING]
				]
				if active_stages:
					current_stage_obj = active_stages[0]
					current_stage = CurrentStageDto(
						name=current_stage_obj.template_stage.name,
						position=current_stage_obj.template_stage.position,
						status=current_stage_obj.status.value,
					)
				else:
					last_stage = max(workflow.stages, key=lambda s: s.template_stage.position)
					current_stage = CurrentStageDto(
						name=last_stage.template_stage.name,
						position=last_stage.template_stage.position,
						status=last_stage.status.value,
					)

				now_aware = datetime.now(timezone.utc)
				created_at_aware = workflow.created_at.astimezone(timezone.utc)

				days_active = (now_aware - created_at_aware).days

				assigned_to_me = self._is_workflow_assigned_to_user(workflow)

				priority = self._determine_workflow_priority(workflow)

				workflow_dto = WorkflowListDto(
					id=workflow.id,
					application=to_application_summary_dto(workflow.application),
					template=TemplateSummaryDto(
						name=workflow.template.name, description=workflow.template.description or ""
					),
					current_stage=current_stage,
					total_stages=stages_count,
					assigned_to_me=assigned_to_me,
					priority=priority,
					days_active=days_active,
				)
				workflow_dtos.append(workflow_dto)

			return Pagination.from_query_result(workflow_dtos, result)

		except Exception as e:
			self.logger.error(f"Failed to retrieve workflow dashboard: {e}")
			raise

	async def get_workflow_statistics(self) -> WorkflowStatsDto:
		"""Get workflow statistics for dashboard"""
		try:
			total = self.db.query(Workflow).count()

			status_counts_raw = (
				self.db.query(Application.status, func.count(Workflow.id).label("count"))
				.join(Application, Workflow.application_id == Application.id)
				.group_by(Application.status)
				.all()
			)

			stats = {status: count for status, count in status_counts_raw}

			start_of_month = datetime.now().replace(day=1, hour=0, minute=0, second=0, microsecond=0)
			this_month = self.db.query(Workflow).filter(Workflow.created_at >= start_of_month).count()

			start_of_last_month = (start_of_month - timedelta(days=1)).replace(day=1)
			end_of_last_month = start_of_month - timedelta(seconds=1)
			last_month = (
				self.db.query(Workflow)
				.filter(and_(Workflow.created_at >= start_of_last_month, Workflow.created_at <= end_of_last_month))
				.count()
			)

			if last_month > 0:
				trend_percentage = f"+{((this_month - last_month) / last_month * 100):.1f}%"
			else:
				trend_percentage = "+0.0%" if this_month == 0 else "+100.0%"

			completed_workflows = (
				self.db.query(Workflow)
				.join(Application)
				.filter(Application.status == ApplicationStatus.APPROVED.value)
				.all()
			)

			total_days = sum(
				[
					(workflow.updated_at - workflow.created_at).days
					for workflow in completed_workflows
					if workflow.updated_at
				]
			)

			average_time_days = total_days / len(completed_workflows) if completed_workflows else 0

			return WorkflowStatsDto(
				total=total,
				pending=stats.get(ApplicationStatus.DRAFT.value, 0),
				in_review=stats.get(ApplicationStatus.IN_REVIEW.value, 0),
				completed=stats.get(ApplicationStatus.APPROVED.value, 0),
				rejected=stats.get(ApplicationStatus.REJECTED.value, 0),
				average_time_days=round(average_time_days, 1),
				this_month=this_month,
				trend_percentage=trend_percentage,
			)

		except Exception as e:
			self.logger.error(f"Failed to get workflow statistics: {e}")
			self.db.rollback()
			raise

	async def retrieve_workflow_stage_by_role(self, filter: WorkflowStageFilter) -> Pagination[FinanceWorkflowStageDto]:
		"""Retrieve workflow stages for a specific role, specifically FINANCE_OFFICER"""
		try:
			query = (
				self.db.query(WorkflowStage)
				.join(TemplateStage, WorkflowStage.template_stage_id == TemplateStage.id)
				.join(TemplateStageRole, TemplateStage.id == TemplateStageRole.template_stage_id)
				.join(Role, TemplateStageRole.role_id == Role.id)
				.join(Workflow, WorkflowStage.workflow_id == Workflow.id)
				.join(Application, Workflow.application_id == Application.id)
				.join(Organization, Application.organization_id == Organization.id)
				.filter(Role.code == "FINANCE_OFFICER")
				.options(
					joinedload(WorkflowStage.template_stage),
					joinedload(WorkflowStage.workflow)
					.joinedload(Workflow.application)
					.joinedload(Application.organization),
				)
			)

			if filter.status:
				query = query.filter(WorkflowStage.status == filter.status)

			query_result = paginate(query, Params(page=filter.page, size=filter.size))

			stages = []
			for workflow_stage in query_result.items:
				application = workflow_stage.workflow.application
				organization = application.organization

				invoice_item = (
					self.db.query(InvoiceItem)
					.join(Invoice, InvoiceItem.invoice_id == Invoice.id)
					.filter(InvoiceItem.application_id == application.id)
					.first()
				)
				invoice = invoice_item.invoice if invoice_item else None

				proof_document = (
					self.db.query(ApplicationDocument)
					.join(Document, ApplicationDocument.document_id == Document.id)
					.join(LoadableItem, ApplicationDocument.document_type_id == LoadableItem.id)
					.filter(and_(ApplicationDocument.application_id == application.id, LoadableItem.code == "DOCT01"))
					.first()
				)

				finance_stage_dto = to_finance_workflow_stage_dto(
					workflow_stage=workflow_stage,
					invoice=invoice,
					organization=organization,
					proof_document=proof_document,
				)

				stages.append(finance_stage_dto)

			return Pagination.from_query_result(stages, query_result)
		except Exception as e:
			self.logger.error(f"Failed to retrieve workflow stages by role: {e}")
			raise

	async def get_workflow_by_id(self, workflow_id: UUID4) -> WorkflowDashboardDto:
		"""Get detailed workflow information by ID"""
		try:
			workflow = (
				self.db.query(Workflow)
				.options(
					joinedload(Workflow.application).joinedload(Application.organization),
					joinedload(Workflow.template).joinedload(Template.stages),
					joinedload(Workflow.stages).joinedload(WorkflowStage.template_stage),
				)
				.filter(Workflow.id == workflow_id)
				.first()
			)

			if not workflow:
				raise ApiException("Workflow not found")

			stage_details = []
			for stage in sorted(workflow.stages, key=lambda s: s.template_stage.position):
				stage_details.append(to_workflow_stage_detail_dto(stage))

			next_stage = None
			current_position = max([s.template_stage.position for s in workflow.stages], default=0)
			next_template_stage = (
				self.db.query(TemplateStage)
				.filter(
					and_(
						TemplateStage.template_id == workflow.template_id,
						TemplateStage.position == current_position + 1,
						TemplateStage.is_active,
					)
				)
				.first()
			)

			if next_template_stage:
				next_stage = TemplateSummaryDto(
					name=next_template_stage.name, description=next_template_stage.description or ""
				)

			return WorkflowDashboardDto(
				id=workflow.id,
				application=to_application_summary_dto(workflow.application),
				template=TemplateSummaryDto(
					name=workflow.template.name,
					description=workflow.template.description or "",
					stages=[to_template_stage_dto(stage) for stage in workflow.template.stages],
				),
				stages=stage_details,
				next_stage=next_stage,
			)

		except Exception as e:
			self.logger.error(f"Failed to get workflow by ID: {e}")
			raise

	async def _get_stage_account_ids(self, stage: TemplateStage) -> List[str]:
		"""Get account IDs for users assigned to a template stage"""
		account_ids = []
		for stage_role in stage.roles:
			if stage_role.is_active and stage_role.role:
				for user in stage_role.role.users:
					if user.account:
						account_ids.append(user.account.id)
		return account_ids

	async def _send_stage_notifications(
		self,
		stage: TemplateStage,
		application: Application,
		workflow_id: str,
		notification_type: str = "STAGE_ASSIGNMENT",
	):
		"""Send notifications for workflow stage events"""
		account_ids = await self._get_stage_account_ids(stage)
		if account_ids:
			await self.notification_service.create_workflow_stage_notification(
				workflow_id=workflow_id,
				stage_name=stage.name,
				application_code=application.code,
				organization_name=application.organization.name,
				recipient_account_ids=account_ids,
				action_url=f"{settings.FRONTEND_URL}/workflows/{workflow_id}",
				notification_type=notification_type,
			)

	async def _generate_licence_for_approved_application(self, application: Application):
		"""Generate licence or certificate when application is approved"""
		try:
			invoice_item = await self.invoice_service.get_first_invoice_item_by_application_id(application.id)
			invoice_item_id = invoice_item.id if invoice_item else None

			if not invoice_item_id:
				self.logger.warning(
					f"No invoice item found for application {application.code}, licence will be created without payment link"
				)

			if application.type == ApplicationType.ORGANIZATION_REGISTRATION:
				await self.licence_service.generate_certificate(
					organization_id=application.organization_id, invoice_item_id=invoice_item_id
				)
				self.logger.info(f"Generated certificate for organization registration: {application.code}")

			elif application.type == ApplicationType.LICENCE_RENEWAL:
				await self.licence_service.generate_licence(
					organization_id=application.organization_id, invoice_item_id=invoice_item_id
				)
				self.logger.info(f"Generated licence for renewal: {application.code}")

		except Exception as e:
			self.logger.error(f"Failed to generate licence for application {application.code}: {str(e)}")
			raise

	async def _send_completion_notifications(self, application: Application):
		"""Send notifications when workflow is completed"""
		organization_account_ids = []
		if application.organization.account:
			organization_account_ids.append(application.organization.account.id)

		if organization_account_ids:
			await self.notification_service.create_workflow_stage_notification(
				workflow_id=str(application.workflows[0].id) if application.workflows else "",
				stage_name="Application Approved",
				application_code=application.code,
				organization_name=application.organization.name,
				recipient_account_ids=organization_account_ids,
				action_url=f"{settings.FRONTEND_URL}/organizations/{application.organization.id}",
				notification_type="WORKFLOW_COMPLETED",
			)

	async def _send_rejection_notifications(self, application: Application, stage_name: str):
		"""Send notifications when workflow is rejected"""
		organization_account_ids = []
		if application.organization.account:
			organization_account_ids.append(application.organization.account.id)

		if organization_account_ids:
			await self.notification_service.create_workflow_stage_notification(
				workflow_id=str(application.workflows[0].id) if application.workflows else "",
				stage_name=stage_name,
				application_code=application.code,
				organization_name=application.organization.name,
				recipient_account_ids=organization_account_ids,
				action_url=f"{settings.FRONTEND_URL}/organizations/{application.organization.id}",
				notification_type="STAGE_REJECTED",
			)

	async def approve_workflow_stage(self, workflow_id: UUID4, stage_id: UUID4, payload: WorkflowActionRequest):
		"""
		Approves the current stage and moves the workflow to the next stage.
		If this is the last stage, validates invoice items before approval.
		"""
		try:
			workflow_stage = (
				self.db.query(WorkflowStage)
				.options(joinedload(WorkflowStage.workflow).joinedload(Workflow.application))
				.filter(and_(WorkflowStage.id == stage_id, WorkflowStage.workflow_id == workflow_id))
				.first()
			)

			if not workflow_stage:
				raise ApiException("Workflow stage not found.")

			if workflow_stage.status != WorkflowStageStatus.IN_REVIEW.value:
				raise ApiException("The current stage is not in review and cannot be approved.")

			application = workflow_stage.workflow.application

			next_stage = await self.create_next_workflow_stage(
				workflow_stage.workflow,
				workflow_stage.template_stage.position,
			)

			if not next_stage:
				invoice_item = self.db.query(InvoiceItem).filter(InvoiceItem.application_id == application.id).first()

				if not invoice_item:
					raise ApiException("No invoice items found for this application. Cannot approve final stage.")

				if invoice_item.invoice.status != InvoiceStatus.PAID.value:
					raise ApiException("Invoice must be paid before final approval.")

			workflow_stage.status = WorkflowStageStatus.APPROVED
			workflow_stage.updated_at = datetime.now()
			workflow_stage.approved_by = self.current_user.id
			self.db.add(workflow_stage)

			if payload.comment:
				comment = WorkflowStageComment(
					workflow_stage_id=workflow_stage.id,
					text=payload.comment,
					created_by=self.current_user.id,
				)
				self.db.add(comment)

			self.db.flush()

			# Handle final approval or next stage
			if not next_stage:
				# Final stage - handle completion
				application.status = ApplicationStatus.APPROVED
				application.updated_by = self.current_user.id
				self.db.add(application)

				application.organization.status = OrganizationStatus.REGISTERED
				self.db.add(application.organization)

				licence = None

				if application.type == ApplicationType.ORGANIZATION_REGISTRATION.value:
					licence = await self.licence_service.generate_certificate(
						application.organization_id, invoice_item.id
					)

				if application.type == ApplicationType.LICENCE_RENEWAL.value:
					licence = await self.licence_service.generate_licence(application.organization_id, invoice_item.id)
					await self.licence_renewal_service.process_licence_renewal(application.organization_id)

				if application.type == ApplicationType.PERMIT_APPLICATION.value:
					permit = await self.licence_service.generate_employment_permit_for_application(
						application.id, invoice_item.id
					)
					self.logger.info(f"Generated employment permit for application: {application.code}")

				if not licence and application.type != ApplicationType.PERMIT_APPLICATION.value:
					self.logger.error(f"Certificate/Licence not generated for application: {application.id}")

				await self._send_completion_notifications(application)
			else:
				# Not final stage - notify next stage
				await self._send_stage_notifications(
					next_stage.template_stage,
					application,
					str(workflow_stage.workflow.id),
					notification_type="STAGE_ASSIGNMENT",
				)

			self.db.commit()
			self.db.refresh(workflow_stage)

		except Exception as e:
			self.db.rollback()
			self.logger.error(f"Failed to handle workflow action: {e}")
			raise

	async def create_next_workflow_stage(
		self, workflow: Workflow, current_stage_position: int
	) -> Optional[WorkflowStage]:
		"""Create next workflow stage dynamically after current stage approval"""
		next_template_stage = (
			self.db.query(TemplateStage)
			.filter(
				and_(
					TemplateStage.template_id == workflow.template_id,
					TemplateStage.position == current_stage_position + 1,
				)
			)
			.first()
		)

		if not next_template_stage:
			return None

		existing_stage = (
			self.db.query(WorkflowStage)
			.filter(
				and_(
					WorkflowStage.workflow_id == workflow.id, WorkflowStage.template_stage_id == next_template_stage.id
				)
			)
			.first()
		)

		if existing_stage:
			existing_stage.status = WorkflowStageStatus.IN_REVIEW
			existing_stage.updated_by = self.current_user.id
			existing_stage.updated_at = datetime.now()
			self.db.add(existing_stage)
			self.db.flush()
			return existing_stage

		new_stage = WorkflowStage(
			workflow_id=workflow.id,
			template_stage_id=next_template_stage.id,
			status=WorkflowStageStatus.IN_REVIEW,
			created_by=self.current_user.id,
		)
		self.db.add(new_stage)
		self.db.flush()
		return new_stage

	def _is_workflow_assigned_to_user(self, workflow: Workflow) -> bool:
		"""Check if workflow is assigned to current user"""
		try:
			current_user_roles = [role.id for role in self.current_user.roles]

			for stage in workflow.stages:
				if stage.status == WorkflowStageStatus.IN_REVIEW:
					stage_roles = [role.role_id for role in stage.roles]
					if any(role_id in current_user_roles for role_id in stage_roles):
						return True
			return False
		except Exception:
			return False

	def _determine_workflow_priority(self, workflow: Workflow) -> str:
		"""Determine workflow priority based on business logic"""
		now_aware = datetime.now(timezone.utc)
		created_at_aware = workflow.created_at.astimezone(timezone.utc)

		days_active = (now_aware - created_at_aware).days

		if days_active > 7:
			return "HIGH"
		elif days_active > 3:
			return "MEDIUM"
		else:
			return "LOW"

	def start_stage_review(self, stage_id: UUID4) -> WorkflowStageDto:
		"""Start the approval review"""
		try:
			stage = self.db.query(WorkflowStage).filter(WorkflowStage.id == stage_id).first()

			if not stage:
				raise ApiException("Workflow stage does not exists")

			stage.status = WorkflowStageStatus.IN_REVIEW
			stage.updated_by = self.current_user.id
			stage.updated_at = datetime.now()

			self.db.add(stage)
			self.db.flush()

			return to_workflow_stage_dto(stage)

		except Exception as e:
			self.logger.error(f"Failed to start the review: {str(e)}")
			raise

	def cancel_stage_review(self, stage_id: UUID4) -> dict:
		"""Start the approval review"""
		try:
			stage = self.db.query(WorkflowStage).filter(WorkflowStage.id == stage_id).first()

			if not stage:
				raise ApiException("Workflow stage does not exists")

			if stage.status != WorkflowStageStatus.IN_REVIEW.value:
				raise ApiException("Stage can only be reversed when in review mode")

			stage.status = WorkflowStageStatus.PENDING
			stage.updated_by = None
			stage.updated_at = datetime.now()

			self.db.add(stage)
			self.db.flush()

			return to_workflow_stage_dto(stage)

		except Exception as e:
			self.logger.error(f"Failed to start the review: {str(e)}")
			raise
