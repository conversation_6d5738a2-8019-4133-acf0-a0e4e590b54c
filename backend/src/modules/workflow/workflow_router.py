from typing import Optional

from fastapi import APIRouter, status

from src.core.dtos.financial_dtos import FinanceWorkflowStageDto
from src.core.dtos.workflow_dtos import (
	TemplateDto,
	TemplateStageDto,
	WorkflowDashboardDto,
	WorkflowListDto,
	WorkflowStatsDto, WorkflowStageDto,
)
from src.core.shared_schema import BaseResponse, Pagination
from src.modules.workflow import workflow_controller as controller

workflow_router_v1 = APIRouter(tags=["workflows"])

workflow_router_v1.add_api_route(
	path="/templates",
	methods=["GET"],
	endpoint=controller.fetch_templates_handler,
	response_model=Pagination[TemplateDto],
	summary="Retrieve a paginated list of workflow templates",
)

workflow_router_v1.add_api_route(
	path="/templates/{template_id}",
	methods=["GET"],
	endpoint=controller.get_template_handler,
	response_model=TemplateDto,
	summary="Retrieve a specific workflow template by ID",
)

workflow_router_v1.add_api_route(
	path="/templates",
	methods=["POST"],
	endpoint=controller.create_template_handler,
	response_model=TemplateDto,
	status_code=status.HTTP_201_CREATED,
	summary="Create a new workflow template",
)

workflow_router_v1.add_api_route(
	path="/templates/{template_id}",
	methods=["PUT"],
	endpoint=controller.update_template_handler,
	response_model=TemplateDto,
	summary="Update an existing workflow template",
)

workflow_router_v1.add_api_route(
	path="/templates/{template_id}/void",
	methods=["DELETE"],
	endpoint=controller.void_template_handler,
	status_code=status.HTTP_204_NO_CONTENT,
	summary="Void an existing workflow template",
)

# stages
workflow_router_v1.add_api_route(
	path="/templates/{template_id}/stages",
	methods=["POST"],
	endpoint=controller.create_template_stage_handler,
	response_model=BaseResponse[list[TemplateStageDto]],
	status_code=status.HTTP_201_CREATED,
	summary="Create a new stage for a workflow template",
)

workflow_router_v1.add_api_route(
	path="/templates/{template_id}/stages",
	methods=["GET"],
	endpoint=controller.fetch_template_stages_handler,
	response_model=Pagination[TemplateStageDto],
	summary="Retrieve paginated list of stages for a workflow template",
)

workflow_router_v1.add_api_route(
	path="/templates/{template_id}/stages/{stage_id}",
	methods=["PUT"],
	endpoint=controller.update_template_stage_handler,
	response_model=TemplateStageDto,
	summary="Update an existing workflow template stage",
)

workflow_router_v1.add_api_route(
	path="/templates/{template_id}/stages/{stage_id}",
	methods=["DELETE"],
	endpoint=controller.void_template_stage_handler,
	status_code=status.HTTP_204_NO_CONTENT,
	summary="Void an existing workflow template stage",
)

# Template stage roles CRUD
# workflow_router_v1.add_api_route(
# 	path="/templates/{template_id}/stages/{stage_id}/roles",
# 	methods=["GET"],
# 	endpoint=controller.fetch_template_stage_roles_handler,
# 	response_model=Pagination[TemplateStageRoleDto],
# 	summary="Retrieve paginated list of roles for a workflow template stage",
# )

# workflow_router_v1.add_api_route(
# 	path="/templates/{template_id}/stages/{stage_id}/roles",
# 	methods=["POST"],
# 	endpoint=controller.create_template_stage_roles_handler,
# 	response_model=BaseResponse[List[TemplateStageRoleDto]],
# 	status_code=status.HTTP_201_CREATED,
# 	summary="Create roles for a workflow template stage",
# )

# workflow_router_v1.add_api_route(
# 	path="/templates/{template_id}/stages/{stage_id}/roles/{role_id}",
# 	methods=["DELETE"],
# 	endpoint=controller.void_template_stage_role_handler,
# 	status_code=status.HTTP_204_NO_CONTENT,
# 	summary="Void a workflow template stage role",
# )

# Template stage triggers CRUD
# workflow_router_v1.add_api_route(
# 	path="/templates/{template_id}/stages/{stage_id}/triggers",
# 	methods=["GET"],
# 	endpoint=controller.fetch_template_stage_triggers_handler,
# 	response_model=Pagination[TemplateStageTriggerDto],
# 	summary="Retrieve paginated list of triggers for a workflow template stage",
# )

# workflow_router_v1.add_api_route(
# 	path="/templates/{template_id}/stages/{stage_id}/triggers",
# 	methods=["POST"],
# 	endpoint=controller.create_template_stage_triggers_handler,
# 	response_model=BaseResponse[List[TemplateStageTriggerDto]],
# 	status_code=status.HTTP_201_CREATED,
# 	summary="Create triggers for a workflow template stage",
# )

# workflow_router_v1.add_api_route(
# 	path="/templates/{template_id}/stages/{stage_id}/triggers/{trigger_id}",
# 	methods=["DELETE"],
# 	endpoint=controller.void_template_stage_trigger_handler,
# 	status_code=status.HTTP_204_NO_CONTENT,
# 	summary="Void a workflow template stage trigger",
# )

workflow_router_v1.add_api_route(
	path="/dashboard",
	methods=["GET"],
	endpoint=controller.fetch_workflow_dashboard_handler,
	response_model=Pagination[WorkflowListDto],
	summary="Get workflows for dashboard with filtering",
)

workflow_router_v1.add_api_route(
	path="/stats",
	methods=["GET"],
	endpoint=controller.fetch_workflow_stats_handler,
	response_model=BaseResponse[WorkflowStatsDto],
	summary="Get workflow statistics for dashboard",
)

workflow_router_v1.add_api_route(
	path="/{workflow_id}",
	methods=["GET"],
	endpoint=controller.fetch_single_workflow_handler,
	response_model=BaseResponse[WorkflowDashboardDto],
	summary="Get detailed workflow information",
)

workflow_router_v1.add_api_route(
	path="/{workflow_id}/stages/{stage_id}/start_stage_review",
	methods=["POST"],
	endpoint=controller.start_approval_process_handler,
	response_model=BaseResponse[Optional[WorkflowStageDto]],
	status_code=status.HTTP_200_OK,
	summary="Approve a workflow stage",
)

workflow_router_v1.add_api_route(
	path="/{workflow_id}/stages/{stage_id}/cancel_stage_review",
	methods=["POST"],
	endpoint=controller.cancel_approval_process_handler,
	response_model=BaseResponse[Optional[WorkflowStageDto]],
	status_code=status.HTTP_200_OK,
	summary="Approve a workflow stage",
)

workflow_router_v1.add_api_route(
	path="/{workflow_id}/stages/{stage_id}/approve",
	methods=["POST"],
	endpoint=controller.approve_workflow_stage_handler,
	response_model=BaseResponse[Optional[WorkflowDashboardDto]],
	status_code=status.HTTP_200_OK,
	summary="Approve a workflow stage",
)

workflow_router_v1.add_api_route(
	path="/{workflow_id}/stages/{stage_id}/reject",
	methods=["POST"],
	endpoint=controller.reject_workflow_stage_handler,
	response_model=BaseResponse[WorkflowDashboardDto],
	status_code=status.HTTP_200_OK,
	summary="Reject a workflow stage",
)

workflow_router_v1.add_api_route(
	path="/stages/finance",
	methods=["GET"],
	endpoint=controller.fetch_finance_workflow_stages_handler,
	response_model=Pagination[FinanceWorkflowStageDto],
	summary="Get workflow stages assigned to Finance Officer role with complete details",
)
