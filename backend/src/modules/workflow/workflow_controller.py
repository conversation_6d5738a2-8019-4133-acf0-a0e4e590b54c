from fastapi import Depends
from pydantic import UUID4
from starlette.requests import Request

from src.core.dtos.financial_dtos import FinanceWorkflowStageDto
from src.core.dtos.workflow_dtos import (
	TemplateDto,
	TemplateStageDto,
	WorkflowDashboardDto,
	WorkflowListDto,
	WorkflowStatsDto, WorkflowStageDto,
)
from src.core.guards.auth_guard import auth_guard
from src.core.shared_schema import BaseResponse, Pagination, VoidRequest
from src.modules.workflow.workflow_schema import (
	TemplateFilter,
	TemplateRequest,
	TemplateStageFilter,
	TemplateStageRequest,
	WorkflowActionRequest,
	WorkflowDashboardFilter,
	WorkflowStageFilter,
)
from src.modules.workflow.workflow_service import WorkflowService


def get_workflow_service() -> WorkflowService:
	"""Get WorkflowService instance"""
	return WorkflowService()


@auth_guard.authenticated
async def fetch_templates_handler(
	_: Request,
	filter: TemplateFilter = Depends(TemplateFilter),
) -> Pagination[TemplateDto]:
	service = get_workflow_service()
	return await service.retrieve_templates(filter)


@auth_guard.authenticated
async def get_template_handler(
	template_id: UUID4,
	_: Request,
) -> Pagination[TemplateDto]:
	service = get_workflow_service()
	return await service.get_template_by_id(template_id)


@auth_guard.authenticated
async def create_template_handler(
	_: Request,
	payload: TemplateRequest,
) -> TemplateDto:
	service = get_workflow_service()
	new_template = await service.create_template(payload)
	return new_template


@auth_guard.authenticated
async def start_approval_process_handler(
	_: Request,
	stage_id: UUID4,
) -> BaseResponse[WorkflowStageDto]:
	service = get_workflow_service()
	stage = await service.start_stage_review(stage_id)
	return stage


@auth_guard.authenticated
async def cancel_approval_process_handler(
	_: Request,
	stage_id: UUID4,
) -> BaseResponse[WorkflowStageDto]:
	service = get_workflow_service()
	stage = await service.cancel_stage_review(stage_id)
	return stage


@auth_guard.authenticated
async def update_template_handler(
	_: Request,
	template_id: UUID4,
	payload: TemplateRequest,
) -> TemplateDto:
	service = get_workflow_service()
	updated_template = await service.update_template(template_id, payload)
	return updated_template


@auth_guard.can_("delete.template")
async def void_template_handler(_: Request, template_id: UUID4, payload: VoidRequest):
	service = get_workflow_service()
	success = await service.void_template(template_id, payload)
	return {"success": success}


@auth_guard.can_("create.template")
async def create_template_stage_handler(
	_: Request,
	template_id: UUID4,
	payload: TemplateStageRequest,
) -> BaseResponse[list[TemplateStageDto]]:
	service = get_workflow_service()
	return BaseResponse(data=await service.add_template_stage(template_id, payload))


@auth_guard.can_("update.template")
async def update_template_stage_handler(
	_: Request,
	stage_id: UUID4,
	payload: TemplateStageRequest,
) -> BaseResponse[list[TemplateStageDto]]:
	service = get_workflow_service()
	return BaseResponse(data=await service.update_template_stage(stage_id, payload))


@auth_guard.authenticated
async def fetch_template_stages_handler(
	_: Request,
	template_id: UUID4,
	filter: TemplateStageFilter = Depends(TemplateStageFilter),
) -> Pagination[TemplateStageDto]:
	service = get_workflow_service()
	return await service.retrieve_template_stages(template_id, filter)


@auth_guard.can_("delete.template_stage")
async def void_template_stage_handler(_: Request, stage_id: UUID4, payload: VoidRequest):
	service = get_workflow_service()
	return await service.void_template_stage(stage_id, payload)


@auth_guard.can_("read.workflow")
async def fetch_workflow_dashboard_handler(
	_: Request,
	filter: WorkflowDashboardFilter = Depends(WorkflowDashboardFilter),
) -> Pagination[WorkflowListDto]:
	"""Get all workflows for dashboard with filtering and search"""
	service = get_workflow_service()
	return await service.retrieve_workflow_dashboard(filter)


@auth_guard.can_("read.workflow")
async def fetch_workflow_stats_handler(_: Request) -> BaseResponse[WorkflowStatsDto]:
	"""Get workflow statistics for dashboard"""
	service = get_workflow_service()
	stats = await service.get_workflow_statistics()
	return BaseResponse[WorkflowStatsDto](data=stats)


@auth_guard.can_("read.workflow")
async def fetch_single_workflow_handler(_: Request, workflow_id: UUID4) -> BaseResponse[WorkflowDashboardDto]:
	"""Get detailed workflow information by ID"""
	service = get_workflow_service()
	workflow = await service.get_workflow_by_id(workflow_id)
	return BaseResponse(data=workflow)


@auth_guard.can_("update.workflow")
async def approve_workflow_stage_handler(
	_: Request, workflow_id: UUID4, stage_id: UUID4, payload: WorkflowActionRequest
) -> BaseResponse[WorkflowDashboardDto]:
	"""Approve a workflow stage"""
	service = get_workflow_service()
	workflow = await service.approve_workflow_stage(workflow_id, stage_id, payload)
	return BaseResponse(data=workflow)


@auth_guard.can_("update.workflow")
async def reject_workflow_stage_handler(
	_: Request, workflow_id: UUID4, stage_id: UUID4, payload: WorkflowActionRequest
) -> BaseResponse[WorkflowDashboardDto]:
	"""Reject a workflow stage"""
	service = get_workflow_service()
	workflow = await service.reject_workflow_stage(workflow_id, stage_id, payload)
	return BaseResponse(data=workflow)


@auth_guard.authenticated
async def fetch_finance_workflow_stages_handler(
	_: Request,
	filter: WorkflowStageFilter = Depends(WorkflowStageFilter),
) -> Pagination[FinanceWorkflowStageDto]:
	"""Get workflow stages assigned to FINANCE_OFFICER role with all required details"""
	service = get_workflow_service()
	pagination = await service.retrieve_workflow_stage_by_role(filter)
	return pagination
