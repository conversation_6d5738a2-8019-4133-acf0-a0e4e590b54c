from contextlib import asynccontextmanager

from fastapi import <PERSON><PERSON><PERSON>
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import RedirectResponse
from fastapi_pagination import add_pagination

from src.config.middleware import AuditMiddleware, CurrentUserMiddleware
from src.config.metadata import API_METADATA
from src.config.routes import configure_routes
from src.config.settings import IS_PRODUCTION, get_trusted_hosts
from src.core.exceptions.handlers import register_exception_handlers
from src.core.middleware.db_middleware import DatabaseMiddleware
from src.core.shared_schema import BaseResponse
from src.core.startup.connectivity_checks import perform_startup_connectivity_checks
from src.modules.system.system_service import SystemService


@asynccontextmanager
async def lifespan(app: FastAPI):
	"""Application lifespan manager for startup and shutdown events."""
	await perform_startup_connectivity_checks()
	yield


def create_app() -> FastAPI:
	app = FastAPI(lifespan=lifespan, **API_METADATA)

	app.add_middleware(DatabaseMiddleware)

	app.add_middleware(
		CORSMiddleware,
		allow_origins=get_trusted_hosts(),
		allow_credentials=True,
		allow_methods=["*"],
		allow_headers=["*"],
	)

	app.add_middleware(CurrentUserMiddleware)
	app.add_middleware(AuditMiddleware)

	add_pagination(app)

	register_exception_handlers(app)

	@app.get("/", include_in_schema=False, summary="Redirect to API documentation")
	async def root():
		if IS_PRODUCTION:
			return BaseResponse(data={"message": "myNGO API is operational."}, success=True, errors=[])
		return RedirectResponse(url="/v1/docs")

	@app.get("/health", tags=["System Health"], summary="Comprehensive API Health Check")
	async def health_check():
		try:
			from src.config.db.connection import get_db
			from src.core.context.db_context import clear_db_session, set_db_session

			db_generator = get_db()
			db = next(db_generator)

			try:
				set_db_session(db)
				system_service = SystemService()
				health_data = system_service.get_health_status()

				return BaseResponse(data=health_data, success=True, errors=[])
			finally:
				clear_db_session()
				db.close()

		except Exception as e:
			error_message = f"Health check failed due to an internal error: {str(e)}"
			print(f"Error during health check: {e}")
			return BaseResponse(
				data=None, success=False, errors=[{"message": error_message, "code": "system_health_error"}]
			)

	configure_routes(app)

	return app
