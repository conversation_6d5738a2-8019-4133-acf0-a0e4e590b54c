[project]
name = "backend"
version = "0.1.0"
description = "myNGO - manage NGOs online is a platform for capturing, storing, processing and reporting of NGO activities"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "alembic>=1.15.2",
    "fastapi-pagination>=0.13.3",
    "fastapi[standard]>=0.115.12",
    "mailjet-rest>=1.4.0",
    "minio>=7.2.15",
    "pandas>=2.2.3",
    "passlib>=1.7.4",
    "psutil>=7.0.0",
    "psycopg2>=2.9.10",
    "pyjwt>=2.10.1",
    "python-dotenv>=1.1.0",
    "qrcode[pil]>=8.2",
    "redis>=6.2.0",
    "reportlab>=4.4.0",
    "rich>=14.0.0",
    "sqlalchemy>=2.0.40",
    "sqlparse>=0.5.3",
    "debugpy>=1.8.16"
]

[project.optional-dependencies]
test = [
    "pytest>=8.0.0",
    "pytest-asyncio>=0.23.5",
    "pytest-cov>=4.1.0",
    "httpx>=0.27.0",
    "pytest-mock>=3.12.0",
    "factory-boy>=3.3.0",
    "ruff",
]

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = "test_*.py"
python_functions = "test_*"
asyncio_mode = "auto"

# Ruff Configuration
[tool.ruff]
line-length = 120
exclude = [
    ".bzr",
    ".direnv",
    ".eggs",
    ".git",
    ".git-rewrite",
    ".hg",
    ".ipynb_checkpoints",
    ".mypy_cache",
    ".nox",
    ".pants.d",
    ".pyenv",
    ".pytest_cache",
    ".pytype",
    ".ruff_cache",
    ".svn",
    ".tox",
    ".venv",
    ".vscode",
    "__pypackages__",
    "_build",
    "buck-out",
    "build",
    "dist",
    "node_modules",
    "site-packages",
    "venv",
]

[tool.ruff.lint]
# Enable Pyflakes (`F`) and a subset of the pycodestyle (`E`) codes by default.
# Unlike Flake8, Ruff doesn't enable pycodestyle warnings (`W`) or
# McCabe complexity (`C901`) by default.
select = ["E4", "E7", "E9", "F"]
ignore = []

# Allow fix for all enabled rules (when `--fix`) is provided.
fixable = ["ALL"]
unfixable = []

# Allow unused variables when underscore-prefixed.
dummy-variable-rgx = "^(_+|(_+[a-zA-Z0-9_]*[a-zA-Z0-9]+?))$"


# Ruff formatter specific settings
[tool.ruff.format]
indent-style = "tab"
quote-style = "double"
docstring-code-format = true
line-ending = "auto"
skip-magic-trailing-comma = false

[dependency-groups]
dev = [
    "pre-commit>=4.2.0",
    "ruff>=0.12.1",
]
test = ["pre-commit>=4.2.0", "ruff>=0.12.1"]
