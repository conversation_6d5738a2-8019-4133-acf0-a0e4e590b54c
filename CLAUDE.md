# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Backend (FastAPI + PostgreSQL)

Located in `/backend/` directory using Python 3.12+ with uv dependency management.

**Local Development:**

```bash
cd backend
uv venv .venv
source .venv/bin/activate
uv sync
```

**Database Management:**

```bash
# Initialize fresh database (create + migrate + seed)
python -m src.cli db init

# Individual commands
python -m src.cli db create
python -m src.cli db migrate  # or: alembic upgrade head
python -m src.cli db seed
python -m src.cli db drop     # destructive operation with confirmation

# Generate new migrations
alembic revision --autogenerate -m "description"
```

**Running the API:**

```bash
# Local development
uvicorn main:app --reload

# Docker development stack (recommended)
docker compose up --build
```

**Code Quality:**

```bash
# Linting and formatting with Ruff
ruff check --fix
ruff format
```

**Testing:**

```bash
pytest
pytest -v
pytest --cov
```

### Frontend (Next.js + React + Shadcn)

Located in `/frontend/` directory using Next.js 14 with pnpm package management.

**Development:**

```bash
cd frontend
pnpm install
pnpm run dev  # Runs on port 4000 with Turbopack
```

**Build & Deploy:**

```bash
pnpm run build
pnpm run start
```

**Code Quality:**

```bash
pnpm run lint  # ESLint with --fix
```

## Architecture Overview

### Backend Architecture

- **Framework:** FastAPI with async/await support
- **Database:** PostgreSQL with SQLAlchemy ORM and Alembic migrations
- **Authentication:** JWT-based auth with 2FA support using cookies
- **API Structure:** Modular design with Controllers, Services, and Repositories
- **External Services:** Redis (caching), MinIO (object storage), Mailjet (email)

**Module Structure Pattern:**

```
src/modules/{module_name}/
├── {module_name}_controller.py    # HTTP request handlers
├── {module_name}_service.py       # Business logic
├── {module_name}_router.py        # FastAPI route definitions
└── {module_name}_schema.py        # Pydantic models
```

**Key Base Classes:**

- `BaseController` in `src/core/base/base_controller.py` - Currently not being used by anything, we are ignoring it
- `BaseRepository` in `src/core/base/base_repository.py` - Database operations
- All models inherit from `Base` in `src/config/db/models/base.py`

**API Endpoints:**
All routes are prefixed with `/v1/` and organized by module:

- `/v1/auth` - Authentication & user management
- `/v1/organizations` - NGO organization management
- `/v1/workflows` - Application approval workflows
- `/v1/applications` - Organization applications
- `/v1/financials` - Fee and payment management
- `/v1/users` - User administration
- `/v1/departments` - Government departments
- `/v1/settings` - System configuration

### Frontend Architecture

- **Framework:** Next.js 14 App Router with TypeScript
- **UI Library:** Shadcn extending Radix UI components with Tailwind v3 CSS
- **State Management:** Zustand for global state, React Hook Form for forms
- **Data Fetching:** TanStack Query (React Query) with Axios
- **Authentication:** JWT tokens with protected routes
- **Things to remember:** Not using useEffect as much as possible, only use it when necessary

**Key Directories:**

- `app/` - Next.js App Router pages and layouts
- `components/` - Reusable UI components organized by feature
- `services/` - API service layer with typed endpoints
- `types/` - TypeScript type definitions for DTOs
- `hooks/` - Custom React hooks
- `config/api.config.ts` - Centralized API endpoint definitions

**Component Organization:**

- `components/ui/` - Base UI primitives (buttons, inputs, dialogs)
- `components/forms/` - Complex form components and wizards
- `components/organization/` - NGO-specific form sections
- `components/workflow/` - Workflow management UI

## Key Features & Business Logic

### Organization Registration Workflow

Multi-step wizard for NGO registration with document uploads, director information, and supporting documents. Applications flow through configurable approval workflows.

### Workflow Management System

Template-based workflow engine where applications progress through stages with role-based approvals. Each stage can have multiple reviewers and configurable triggers.

### Financial Management

Fee scheduling system with currency support, invoice generation, and payment tracking.

### User & Permission System

Role-based access control with department-based user management and activity tracking.

## Environment Configuration

### Backend Environment Variables

Key variables in `.env` file:

- Database: `POSTGRES_*` variables for connection
- Auth: `SECRET_KEY`, `ALGORITHM` for JWT
- External services: Mailjet, MinIO, Redis configurations
- Environment: `ENVIRONMENT=development|production`

### Frontend Environment Variables

- `NEXT_PUBLIC_API_BASE_URL` - Backend API endpoint

## Development Workflow

### Git Workflow

- No direct pushes to `main` branch
- Use feature branches: `feature/description`, `bugfix/description`
- All changes require Pull Request reviews
- Target branch for PRs: `development`

### Docker Development

The backend includes a complete Docker Compose setup with PostgreSQL, Redis, and MinIO services. Use `docker compose up --build` for full-stack development.

### Testing Strategy

- Backend: pytest with async support, factory-boy for test data
- Frontend: Component-based testing approach
- Database: Test database with migrations and seeding

## HTTP Status Code Standards

- **200 OK** - Successful read/update operations
- **201 Created** - Resource creation
- **204 No Content** - Successful deletion
- **400 Bad Request** - Validation errors, malformed requests
- **401 Unauthorized** - Authentication required/failed
- **403 Forbidden** - Insufficient permissions
- **404 Not Found** - Resource not found
- **500 Internal Server Error** - Server errors

### UI design

- We are using everything to reflect background and foreground colors

### Project scope

This project is for https://ngora.mw. The Non-Governmental Organizations (NGO) Regulatory Authority was established as a statutory Body by an act of Parliament. Our Mandate is to register and regulate the operations of all NGOs in Malawi. As a government agency, NGORA is under the Ministry of Gender, Community Development and Social Welfare.
So it's a platform for NGOs, now anyone anywhere can register organization. That organization should go through approval process.
